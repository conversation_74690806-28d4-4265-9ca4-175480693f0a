import React, { useState } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import Button from '@albertsons/uds/molecule/Button';
import { Radio } from '@albertsons/uds/molecule/Radio';
import EditUserDetailsModal from './editUserDetailsModal';
import DeactivateUserModal from './deactivateUserModal';
import DeactivateAndReplaceUserModal from './deactivateAndReplaceUserModal';

interface EditUserAccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData?: any;
}

const EditUserAccessModal: React.FC<EditUserAccessModalProps> = ({ 
  isOpen, 
  onClose, 
  userData 
}) => {
  const [value, setValue] = useState<string>();
  const [isDeactivateAndReplaceModalOpen, setDeactivateAndReplaceModalOpen] = useState<boolean>(false);
  const [isDeactivateUserModalOpen, setDeactivateUserModalOpen] = useState<boolean>(false);
  const [showEditUserDetailsModal, setShowEditUserDetailsModal] = useState<boolean>(false);
  const [parentModalVisible, setParentModalVisible] = useState<boolean>(true);
  const userDataToUse = userData || {
    role: 'Manager',
    ldap: 'john.doe',
    userName: 'John Doe',
    manager: 'Jane Smith',
    department: 'IT Department',
    desk: 'Desk-101',
    effectiveStartDate: '2023-01-15'
  };

  const handleNextClick = () => {
    switch (value) {
      case 'deactivateAndReplaceUser':
        setParentModalVisible(false); 
        setDeactivateAndReplaceModalOpen(true);
        break;
      case 'deactivateUser':
        setParentModalVisible(false); 
        setDeactivateUserModalOpen(true);
        break;
      case 'editUserDetails':
        setParentModalVisible(false);
        setShowEditUserDetailsModal(true);
        break;
      default:
        console.log('Please select an option');
    }
  };

  const handleBackToParent = () => {
    setShowEditUserDetailsModal(false);
    setDeactivateAndReplaceModalOpen(false);
    setDeactivateUserModalOpen(false);
    setParentModalVisible(true); 
  };

  return (
    <>
      <Modal isOpen={isOpen && parentModalVisible} onClose={onClose} minHeight={60}>
        <div className='select-none font-bold text-[28px] mt-4 ml-6'>
          Edit User Access
        </div>
        <div className='select-none ml-6'>
         Please choose an option below for changing the user access
        </div>
        <div className="w-[800px] h-px bg-[#c8daeb]" />
        <div className='p-4'>
          <div className="flex items-start gap-1 label text-black  text-sm font-bold">
            What would you like to do?
          </div>
          <div className='flex flex-row gap-4'>
            <Radio.Group value={value} onChange={setValue} horizontal={true}>
              <Radio label='Deactivate and Replace User' value='deactivateAndReplaceUser' />
              <Radio label='Deactivate User' value='deactivateUser' />
              <Radio label='Edit User details' value='editUserDetails' />
            </Radio.Group>
          </div>
          <div className="flex justify-end gap-[8px] mt-4 items-center">
            <Button 
              type="button" 
              variant="secondary" 
              width={120}
              onClick={onClose}
            >
             Cancel
            </Button>
          
            <Button 
              type="submit" 
              width={120}
              onClick={handleNextClick}
            >
             Next
            </Button>
          </div>
        </div>
      </Modal>
      <DeactivateAndReplaceUserModal 
        userData={userDataToUse}
        isOpen={isDeactivateAndReplaceModalOpen} 
        onClose={() => setDeactivateAndReplaceModalOpen(false)}
        onBack={handleBackToParent}
        showTriggerButton={false}
      />
      <DeactivateUserModal 
        userData={userDataToUse}
        isOpen={isDeactivateUserModalOpen} 
        onClose={() => setDeactivateUserModalOpen(false)}
        onBack={handleBackToParent}
        showTriggerButton={false}
      />
      <EditUserDetailsModal 
        isOpen={showEditUserDetailsModal} 
        onClose={() => setShowEditUserDetailsModal(false)}
        onBack={handleBackToParent}
        showTriggerButton={false}
      />
    </>
  );
};

export default EditUserAccessModal;