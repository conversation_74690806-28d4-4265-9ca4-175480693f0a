import { getNowInPST } from '../../util/dateUtils';
export const PERIOD_STATUS_KEYS = [
  'certified',
  'locked',
  'notLocked',
  'notCertifiedButLocked',
] as const;

/**
 * Generate tagStatuses mapping for fiscal periods based on lockout/certification dates and current date.
 * @param {Array} calendarDetails - Array of period objects from displayDateData
 * @returns {Object} tagStatuses mapping
 */
export function generatePeriodStatuses(
  calendarDetails: any[],
  dispatch: Function
): Record<string, Record<string, string[]>> {
 
  const tagStatuses: Record<string, Record<string, string[]>> = PERIOD_STATUS_KEYS.reduce(
    (acc, key) => {
      acc[key] = {};
      return acc;
    },
    {} as Record<string, Record<string, string[]>>
  );
  if (!Array.isArray(calendarDetails)) return tagStatuses;

  // Get current time in PST (America/Los_Angeles)
  const now = getNowInPST();

  // Group weeks by period number
  const periodGroups: Record<string, any[]> = {};
  calendarDetails.forEach((week) => {
    const periodNum = String(week.fiscalPeriodNumber);
    if (!periodGroups[periodNum]) periodGroups[periodNum] = [];
    periodGroups[periodNum].push(week);
  });

  // For each period, determine status and assign week numbers
  Object.entries(periodGroups).forEach(([periodNum, weeks]) => {
    if (!weeks.length) return;
    const period = weeks[0]; // All weeks in a period share the same period-level dates
    const periodEnd = new Date(period.fiscalPeriodEndDate);
    const lockout = period.fiscalPeriodLockoutDate
      ? new Date(period.fiscalPeriodLockoutDate)
      : null;
    const cert = period.fiscalPeriodCertificationDate
      ? new Date(period.fiscalPeriodCertificationDate)
      : null;
    // Collect all week numbers for this period
    const weekNumbers = weeks.map((w) => String(w.fiscalWeekNumber));

    if (isCertified(periodEnd, cert, now)) {
      tagStatuses.certified[periodNum] = weekNumbers;
    }
    // Only add to locked if not certified
    if (isLocked(periodEnd, lockout, now) && !isCertified(periodEnd, cert, now)) {
      tagStatuses.locked[periodNum] = weekNumbers;
    }
    if (isNotLocked(periodEnd, lockout, now)) {
      tagStatuses.notLocked[periodNum] = weekNumbers;
    }
    if (isNotCertifiedButLocked(periodEnd, lockout, cert, now)) {
      tagStatuses.notCertifiedButLocked[periodNum] = weekNumbers;
    }
  });

 
  return tagStatuses;
}

function isCertified(periodEnd: Date, cert: Date | null, now: Date): boolean {
  return !!(cert && periodEnd <= cert && now >= cert);
}

function isLocked(periodEnd: Date, lockout: Date | null, now: Date): boolean {
  return !!(lockout && periodEnd <= lockout && now >= lockout);
}

function isNotLocked(
  periodEnd: Date,
  lockout: Date | null,
  now: Date
): boolean {
  return !lockout || periodEnd > lockout || now < lockout;
}

function isNotCertifiedButLocked(
  periodEnd: Date,
  lockout: Date | null,
  cert: Date | null,
  now: Date
): boolean {
  return !!(
    lockout &&
    periodEnd <= lockout &&
    now >= lockout &&
    (!cert || !(periodEnd <= cert && now >= cert))
  );
}
