import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import '@testing-library/jest-dom/extend-expect';
import WeekSelection from '../ForecastEdit/weekSelection';

describe('WeekSelection Component', () => {
    const mockOnWeekSelect = jest.fn();
    const mockStore = configureStore([]);
    let store;

    const renderComponent = (props = {}) => {
        const defaultProps = {
            isSingleWeekSelect: false,
            errorWeeks: [],
            touchedWeeks: [],
            weeksInQuarter: 12,
            selectedWeeks: [],
            weekData: { 1: {}, 2: {}, 3: {}, 4: {}, 5: {}, 6: {}, 7: {}, 8: {}, 9: {}, 10: {}, 11: {}, 12: {} }, 
            onWeekSelect: mockOnWeekSelect,
        };
        return render(
            <Provider store={store}>
                <WeekSelection {...defaultProps} {...props} />
            </Provider>
        );
    };

    beforeEach(() => {
        mockOnWeekSelect.mockClear();
        store = mockStore({});
    });

    test('renders week numbers correctly', () => {
        const { getByText } = renderComponent();
        for (let i = 1; i <= 12; i++) {
            expect(getByText(i.toString())).toBeInTheDocument();
        }
    });

    test('calls onWeekSelect with correct weeks when multiple weeks are selected', () => {
        const { getByText } = renderComponent();
        const week3 = getByText('3');
        const week4 = getByText('4');

        fireEvent.click(week3);

        expect(mockOnWeekSelect).toHaveBeenCalledWith([3]);
    });

    test('renders custom styles for error and valid weeks', () => {
        const { getByText } = renderComponent({
            errorWeeks: ['2'],
            touchedWeeks: ['2', '3'],
            weekData: { 3: {} },
        });
    
        const week2 = getByText('2');
        const week3 = getByText('3');
    
        expect(week2).toHaveClass('week-error');
        expect(week3).toHaveClass('week-valid');
    });

    test('deselects a week correctly', () => {
        const { getByText } = renderComponent({
            selectedWeeks: [10],
        });

        const week10 = getByText('10');
        fireEvent.click(week10);
        expect(mockOnWeekSelect).toHaveBeenCalledWith([]); 
    });

    test('handles single week selection mode correctly', () => {
        const { getByText } = renderComponent({
            isSingleWeekSelect: true,
            selectedWeeks: [6],
        });
    
        const week6 = getByText('6');
        const week7 = getByText('7');
        fireEvent.click(week7);
        expect(mockOnWeekSelect).toHaveBeenCalledWith([7]); 
    });

    test('does not select any week initially', () => {
        const { getByText } = renderComponent();
    
        for (let i = 1; i <= 12; i++) {
            const week = getByText(i.toString());
            expect(week).not.toHaveClass('bg-blue-500'); 
        }
    });

});