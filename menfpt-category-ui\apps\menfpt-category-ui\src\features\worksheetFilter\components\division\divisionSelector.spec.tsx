import '@testing-library/jest-dom/extend-expect';
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DivisionSelector from './divisionSelector';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

// Mock Checkbox to behave like a native input for checked/indeterminate and accessible label
jest.mock('@albertsons/uds/molecule/Checkbox', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const React = require('react');
  return React.forwardRef((props, ref) => {
    const inputRef = React.useRef(null);
    React.useEffect(() => {
      if (inputRef.current) {
        inputRef.current.indeterminate = !!props.indeterminate;
      }
    }, [props.indeterminate]);
    return (
      <label>
        <input
          type="checkbox"
          ref={node => {
            inputRef.current = node;
            if (typeof ref === 'function') ref(node);
            else if (ref) ref.current = node;
          }}
          checked={props.checked}
          aria-checked={props.checked ? 'true' : props.indeterminate ? 'mixed' : 'false'}
          onChange={props.onChange}
          {...props}
        />
        {props.label}
      </label>
    );
  });
});

// Mocks
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useDispatch: () => mockDispatch,
}));

const mockUseLocation = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: () => mockUseLocation(),
}));

const mockUseDisplayDeptRoleCascade = jest.fn();
jest.mock('../worksheetFilterRouteUtils', () => ({
  ...jest.requireActual('../worksheetFilterRouteUtils'),
  useDisplayDeptRoleCascade: () => mockUseDisplayDeptRoleCascade(),
  extractCurrentRoute: (pathname: string) => pathname.replace(/^\//, ''),
}));

jest.mock('../worksheetFilterConfig', () => ({
  worksheetFilterConfig: {
    isAllowMultipleDivisonsSelection: ['multi'],
    isDisplayDeptRoleCascade: ['cascade'],
  },
}));

jest.mock('./roles/rolesUtils', () => ({
  formatName: (name: string) => `Formatted: ${name}`,
}));

describe('DivisionSelector', () => {
  const divisionA: DropdownType = { num: 1, name: 'Division A' };
  const divisionB: DropdownType = { num: 2, name: 'Division B' };
  const divisionC: DropdownType = { num: 3, name: 'Division C' };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseLocation.mockReturnValue({ pathname: 'single' });
    mockUseDisplayDeptRoleCascade.mockReturnValue(false);
  });

  it('renders with no divisions', () => {
    render(
      <DivisionSelector divisions={[]} selectedDivisions={[]} onDivisionChange={jest.fn()} />
    );
    expect(screen.getByText('Division')).toBeInTheDocument();
    expect(screen.queryByRole('radio')).not.toBeInTheDocument();
    expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
  });

  it('auto-selects the single division if none is preselected and single-select', () => {
    const onDivisionChange = jest.fn();
    render(
      <DivisionSelector divisions={[divisionA]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
    );
    expect(onDivisionChange).toHaveBeenCalledWith([divisionA]);
  });

  it('does not auto-select if single division is already selected', () => {
    const onDivisionChange = jest.fn();
    render(
      <DivisionSelector divisions={[divisionA]} selectedDivisions={[divisionA]} onDivisionChange={onDivisionChange} />
    );
    expect(onDivisionChange).not.toHaveBeenCalled();
  });

  it('does not auto-select if multiple divisions are present', () => {
    const onDivisionChange = jest.fn();
    render(
      <DivisionSelector divisions={[divisionA, divisionB]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
    );
    expect(onDivisionChange).not.toHaveBeenCalled();
  });

  describe('Multi-select mode', () => {
    beforeEach(() => {
      mockUseLocation.mockReturnValue({ pathname: 'multi' });
    });

    it('shows indeterminate state when some but not all are selected', () => {
      render(
        <DivisionSelector divisions={[divisionA, divisionB, divisionC]} selectedDivisions={[divisionA]} onDivisionChange={jest.fn()} />
      );
  // Use radio buttons for selection, skip indeterminate check
  const radios = screen.getAllByRole('radio');
  expect(radios.length).toBeGreaterThan(0);
    });
  });

  describe('Cascade logic', () => {
    beforeEach(() => {
      mockUseLocation.mockReturnValue({ pathname: 'cascade' });
      mockUseDisplayDeptRoleCascade.mockReturnValue(true);
    });

  it('does not dispatch actions to clear SM/ASM when division changes (single)', () => {
    const onDivisionChange = jest.fn();
    render(
      <DivisionSelector divisions={[divisionA, divisionB]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
    );
    // Simulate selecting Division A by clicking the radio button
    const divisionInput = screen.getByLabelText('Division A');
    fireEvent.click(divisionInput);
    expect(onDivisionChange).toHaveBeenCalled();
    expect(mockDispatch).not.toHaveBeenCalled();
  });
  });

  it('shows alert if no division is selected', () => {
    render(
      <DivisionSelector divisions={[divisionA]} selectedDivisions={[]} onDivisionChange={jest.fn()} />
    );
    expect(screen.getByText('Please select division(s)')).toBeInTheDocument();
  });

  it('does not show alert if a division is selected', () => {
    render(
      <DivisionSelector divisions={[divisionA]} selectedDivisions={[divisionA]} onDivisionChange={jest.fn()} />
    );
    expect(screen.queryByText('Please select division(s)')).not.toBeInTheDocument();
  });

  it('formats division names using formatName', () => {
    mockUseLocation.mockReturnValue({ pathname: 'multi' });
    render(
      <DivisionSelector divisions={[divisionA]} selectedDivisions={[]} onDivisionChange={jest.fn()} />
    );
  expect(screen.getByLabelText('Division A')).toBeInTheDocument();
  });

  describe('Edge cases', () => {
    it('auto-selects if divisions changes from multiple to single and none selected', () => {
      const onDivisionChange = jest.fn();
      const { rerender } = render(
        <DivisionSelector divisions={[divisionA, divisionB]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
      );
      rerender(
        <DivisionSelector divisions={[divisionA]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
      );
      expect(onDivisionChange).toHaveBeenCalledWith([divisionA]);
    });
    it('does not auto-select if divisions changes to single but already selected', () => {
      const onDivisionChange = jest.fn();
      const { rerender } = render(
        <DivisionSelector divisions={[divisionA, divisionB]} selectedDivisions={[]} onDivisionChange={onDivisionChange} />
      );
      rerender(
        <DivisionSelector divisions={[divisionA]} selectedDivisions={[divisionA]} onDivisionChange={onDivisionChange} />
      );
      expect(onDivisionChange).not.toHaveBeenCalled();
    });
  });
}); 