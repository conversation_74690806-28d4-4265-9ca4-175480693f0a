import React, { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import Drawer from '@albertsons/uds/molecule/Drawer';
import Button from '@albertsons/uds/molecule/Button';
import Radio from '@albertsons/uds/molecule/Radio';
import TextField from '@albertsons/uds/molecule/TextField';
import TextArea from '@albertsons/uds/molecule/TextArea';
import AutoComplete from '@albertsons/uds/molecule/AutoComplete';
import Alert from '@albertsons/uds/molecule/Alert';
import Link from '@albertsons/uds/molecule/Link';
import { Divider } from '@mui/material';
import { Info, TriangleAlert } from 'lucide-react';
import { useFormik, useFormikContext } from 'formik';
import './bottomdropdowm.scss';
import './editForecast.scss'
import WeekSelection from './weekSelection';
import { EditForecastAdjustmentProps, FormValues } from './types';
import { forecastAdjustmentReasons } from './constants';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { FormFieldNames, Week, ADJ_SUB_ROW, Adjustment, SaveAdjustmentResponse } from '../../interfaces/edit-forecast-adjustments';
import SkeletonLoader from '@albertsons/uds/molecule/SkeletonLoader';
import { calculateSalesPublicPcnt, safeScrollIntoView, scrollToFirstError } from './forecastCalculations';
import { useEditForecastBffBody } from './forecastCalculations'
import { useSaveAdjustmentEditsMutation } from '../../server/Api/menfptCategoryAPI';
import { useDispatch } from 'react-redux';
import clsx from 'clsx';
import HistoryDrawer from '../../features/historyDrawer';
import { createAdjustmentApiBody, getFiscalWeekNumber, saveAdjustment } from './editForecastHelper';
import Spinner from '@albertsons/uds/molecule/Spinner';
import { Tooltip } from '@albertsons/uds/molecule/Tooltip';
import { useExtractBannerId } from '../../util/filterUtils';

const DEVIATION_WARNING_MSG = '+/-10% deviation to prior merchant forecast';
// salesPublicPcnt: totalValue[salesPublic] / (totalBaseLineValue[salesPublic])
// bookgrossProfit calculation: (totalBaseLineValue[grossProfit] / totalBaseLineValue[salesPublic]+1) * totalValue[salesPublic]
// markdown : ( (totalBaseLineValue[marksDown] / totalBaseLineValue[salesPublic]) * totalValue[salesPublic] ) - totalBaseLineValue[marksDown]

// grossProfit pcnt is: grossProfit / (salesPublic + baseLine[salesPublic])

const DECIMAL_POINTS = 0;
const DECIMAL_POINTS_PCT = 2;

const TEXT_AREA_MAX_CHAR = 300;

const numberFormatter = new Intl.NumberFormat('en-US', {
  style: 'decimal',
  useGrouping: true,
  minimumFractionDigits: DECIMAL_POINTS,
  maximumFractionDigits: DECIMAL_POINTS
});

const pctFormatter = {
  format: (value: number) => {
    // Truncate to 2 decimal places without rounding
    const truncated = Math.floor(value * 100) / 100;
    return truncated.toFixed(2).replace(/\.?0+$/, '');
  }
};


const formToActualFields = {
  grossProfit: 'line5BookGrossProfitNbr',
  salesPublicPct: 'line1PublicToSalesNbr',
  salesPublic: 'line1PublicToSalesNbr',
  marksDown: 'line5MarkDownsNbr',
  totalShrink: 'line5ShrinkNbr',
  suppliesPackaging: 'line6SuppliesPackagingNbr',
  allowances: 'line7RetailsAllowancesNbr',
  selling: 'line7RetailsSellingAllowancesNbr',
  nonSelling: 'line7RetailsNonSellingAllowancesNbr'
}

const selectors: Record<string, string> = {
  selectedWeeks: 'div.text-left.font-bold.leading-6.block.break-words',
  metrics: '.edit-notifications .text-left.font-bold',
  adjustmentReason: '.adjustment-error', // You may need to add this attribute
  comment: 'span.block.truncate[title=""]'
};

type Allowance = {
  selling: number | null;
  nonSelling: number | null;
  totalAllowances: number | null;
}

const fieldOrder: string[] = ['selectedWeeks', 'metrics', 'adjustmentReason', 'comment'];

const ForecastEdit: React.FC<EditForecastAdjustmentProps> = ({
  isOpen,
  setOpen,
  selectedEditWeek,
}) => {

  const [activeWeekSubmit, setActiveWeekSubmit] = useState(false)
  const [warnings, setWarnings] = useState<any>({})
  const [isSaveApiLoading, setIsSaveApiLoading] = useState<boolean>(false);

  const [weeksCalculated, setWeeksCalculated] = useState<Week[]>([]);

  const [saveAdjustmentResponseState, setSaveAdjustmentResponseState] = useState<any>();

  const { data: adjustmentWorksheetDataSlice } = useSelectorWrap('adjustmentWorkSheetFilter_rn') || {}; //{data:slice_info};
  const adjustmentWorksheetData = new Map(Object.entries(adjustmentWorksheetDataSlice)); // deserialize to Map object

  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: userInfo } = useSelectorWrap('userInfo_rn');

  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [historyPosition, setHistoryPosition] = useState('left');

const [editStates, setEditStates] = useState({});

const handleInputFocus = (name, value) => {
  setEditStates((prev) => ({
    ...prev,
    [name]: {
      ...prev[name],
      previousValue: value,
      editing: true,
    },
  }));
};

const handleInputBlur = (name) => {
  setEditStates((prev) => ({
    ...prev,
    [name]: {
      ...prev[name],
      editing: false,
    },
  }));
};

const handleRestoreValue = (name, setter) => {
  setEditStates((prev) => ({
    ...prev,
    [name]: {
      ...prev[name],
      editing: true,
    },
  }));
  setter({
    target: {
      value: editStates[name]?.previousValue ?? '',
      id: name,
    },
  });
};


  // Replace historyModalOpen usage with local state:
  const openHistoryDrawer = (open: boolean, position: string) => {
    setIsHistoryOpen(open);
    setHistoryPosition(position);
  };

  const { setEditForecastBffBody } = useEditForecastBffBody();


  const adjustmentErrorRef = useRef<HTMLDivElement | null>(null);

  const [saveAdjustmentEdits] = useSaveAdjustmentEditsMutation();

  const [totalBaselineValue, setTotalBaselineValue] = useState<FormFieldNames>({
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  });

  const [totalLastYearActual, setTotalLastYearActual] = useState<FormFieldNames>({
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  })

  const [totalValue, setTotalValue] = useState<FormFieldNames>({
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  })

  const dispatch = useDispatch();

  const bannerId = useExtractBannerId();

  const formFields = [
    {
      label: (<span className="text-[#5A697B] flex items-center">Line 8 <Info className="text-[#5A697B] ml-3" size={16} /></span>),
      name: '',
    },
    {
      label: 'DR 08 Real GP before other Rev-Sales',
      name: 'realGrossProfitNbr',
      inputType: 'readonly',
      showWarning: true
    },
    {
      label: (<span className="text-[#5A697B] flex items-center">Line 1 <Info className="text-[#5A697B] ml-3" size={16} /></span>),
      name: '',
    },
    {
      label: 'Sales to Public',
      name: 'salesPublic',
      inputType: 'dollar',
      showWarning: true,
    },
    {
      label: (<span className="text-[#5A697B] flex items-center">Line 5 sub-lines <Info className="text-[#5A697B] ml-3" size={16} /></span>),
      name: '',
    },
    {
      label: 'Book gross profit',
      name: 'grossProfit',
      inputType: 'text',
      showWarning: true
    },
    {
      label: 'Markdowns',
      name: 'marksDown',
      inputType: 'text',
      showWarning: true
    },
    {
      label: 'Total Shrink',
      name: 'totalShrink',
      inputType: 'text',
      showWarning: true
    },
    {
      label: (<span className="text-[#5A697B] flex items-center">Line 6 <Info className="text-[#5A697B] ml-3" size={16} /></span>),
      name: '',
    },
    {
      label: 'Supplies Packaging',
      name: 'suppliesPackaging',
      inputType: 'noPct',
      showWarning: true
    },
    {
      label: (<span className="text-[#5A697B] flex items-center">Line 7 <Info className="text-[#5A697B] ml-3" size={16} /></span>),
      name: '',
    },
    {
      label: 'Allowance',
      name: 'allowances',
      inputType: 'noPct',
      showWarning: true
    },
    {
      label: 'Selling',
      name: 'selling',
      inputType: 'noPct',
      subField: true
    },
    {
      label: 'Non Selling',
      name: 'nonSelling',
      inputType: 'noPct',
      subField: true
    }
  ]
  const ArrowSymbol = (color: string) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.99996 9.33073L2.66663 5.9974M2.66663 5.9974L5.99996 2.66406M2.66663 5.9974H9.66663C10.1481 5.9974 10.6249 6.09224 11.0698 6.2765C11.5147 6.46077 11.9189 6.73086 12.2594 7.07134C12.5998 7.41182 12.8699 7.81603 13.0542 8.26089C13.2385 8.70575 13.3333 9.18255 13.3333 9.66406C13.3333 10.1456 13.2385 10.6224 13.0542 11.0672C12.8699 11.5121 12.5998 11.9163 12.2594 12.2568C11.9189 12.5973 11.5147 12.8674 11.0698 13.0516C10.6249 13.2359 10.1481 13.3307 9.66663 13.3307H7.33329"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

  const transformForecastData = (dataMap, subRow: string, projectionRow: string) => {
    const forecastData = {}

    /** Iterate through each week in the map */
    let weekIndex = 1; //start numbering from 1

    dataMap && dataMap.forEach((weekData, weekKey) => {

      if (/^Weeks\d+$/.test(weekKey)) {
        /** Find the forecast entry */
        const forecastEntry = weekData.find(entry => entry.subRow === subRow) || weekData.find(entry => entry.subRow === projectionRow);

        if (forecastEntry) {
          forecastData[String(forecastEntry?.fiscalWeekNbr)?.slice(-2)] = {
            salesPublic: forecastEntry.line1PublicToSalesNbr,
            grossProfit: forecastEntry.line5BookGrossProfitNbr,
            grossProfitPct: forecastEntry.line5BookGrossProfitPct,
            marksDown: forecastEntry.line5MarkDownsNbr,
            marksDownPct: forecastEntry.line5MarkDownsPct,
            totalShrink: forecastEntry.line5ShrinkNbr,
            totalShrinkPct: forecastEntry.line5ShrinkPct,
            suppliesPackaging: forecastEntry.line6SuppliesPackagingNbr,
            allowances: forecastEntry.line7RetailsAllowancesNbr,
            selling: forecastEntry.line7RetailsSellingAllowancesNbr,
            nonSelling: forecastEntry.line7RetailsNonSellingAllowancesNbr
          }
        }
        weekIndex++; //Increment numeric week values
      }
    })
    return forecastData

  }


  const validate = (values, isSubmitting) => {
    const errors: any = {}; /** To restrict the form from submitting */
    const warnings: any = {}; /** Displays warning message for all metric fields, saving adjustment is allowed with warnings */

    const forecastData = transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.MERCH_FORECAST, ADJ_SUB_ROW.PROJECTION);

    // console.log(forecastData)

    /**** Fields to be validated for metrics warning *****/
    const fieldsToCheck = [
      'salesPublic',
      'grossProfit',
      'marksDown',
      'totalShrink',
      'suppliesPackaging',
      'allowances',
      'selling',
      'nonSelling',
      'realGrossProfitNbr'
    ];

    /** Select at least one week for both 'multiple week editing' or 'week wise editing' */
    if (!values.selectedWeeks || values.selectedWeeks.length === 0) {
      errors.selectedWeeks = 'Please select at least one week'
    }

    /******************* Validate for respective weeks *********************************/

    if (values.selectedRadio === 'singleweek') {
      const weeksToValidate = isSubmitting ? Object.keys({ ...values.weekData, [values.selectedWeeks[0]]: values.weekData[values.selectedWeeks[0]] || {} })
        : Object.keys(values.weekData)
      // const weeksToValidate = Object.keys({...values.weekData, [values.selectedWeeks[0]]: values.weekData[values.selectedWeeks[0]] || {}})

      weeksToValidate.forEach((week) => {
        const weekData = values.weekData?.[week] || {};
        const weekErrors: any = {};
        const weekWarnings: any = {};

        if(weekData?.touched?.size > 0) {
          /**** Validating for metrics, any of the above fields should have valid data *****/
        const anyFieldFilled = fieldsToCheck.some((field) => {
          const val = weekData[field];
          return val !== undefined && val !== '' && val !== null;
        })

        if (!anyFieldFilled) {
          weekErrors.metrics = 'Please adjust at least one of the metrics below'
        }

        /**** Additional check for AdjustmentReason and Comments Fileds  *****/
        if (!weekData.adjustmentReason || !Object.values(weekData.adjustmentReason).length) {
          weekErrors.adjustmentReason = `Please select an adjustment reason`
        }

        if (!weekData.comment || weekData.comment === '') {
          weekErrors.comment = `Please add comment`
        } else if (isCommentValid(weekData.comment, TEXT_AREA_MAX_CHAR)) {
          weekErrors.comment = 'Comment Exceeds Max Length'
        }


        const totalBaseline = {
          'salesPublic': 0,
          'grossProfit': 0,
          'marksDown': 0,
          'totalShrink': 0,
          'suppliesPackaging': 0,
          'allowances': 0,
          'selling': 0,
          'nonSelling': 0
        }
        const updatedTotalValue = {
          'salesPublic': 0,
          'grossProfit': 0,
          'marksDown': 0,
          'totalShrink': 0,
          'suppliesPackaging': 0,
          'allowances': 0,
          'selling': 0,
          'nonSelling': 0

        }

        const baselineValues = forecastData?.[week] || {};
        fieldsToCheck.forEach((field) => {

          let totalValue = {}
          if (field === 'salesPublic') {
            totalValue = { ...baselineValues }
            totalValue[field] += Number(("" +weekData[field])?.replace(/,/g, ''))
          } else if(field === 'realGrossProfitNbr') {
            const enteredValue = getLine8(weekData);
            const baseline = getLine8(baselineValues);
            if (baseline !== undefined && enteredValue !== undefined) {
              totalValue[field] = enteredValue;
              baselineValues[field] = baseline;
            }
          } else {
            const baseline = forecastData?.[week]?.[field];
            const enteredValue = Number(("" + weekData[field])?.replace(/,/g, ''))

            if (baseline !== undefined && weekData[field] !== '') {
              totalValue[field] = enteredValue;
            }
          }

          const totalBaseline = baselineValues[field]
          const totalEnteredValue = totalValue[field]

          /**The below check is to handle the negative number scenarios
           * For negative numbers product with 0.9 becomes more negative
           * So the upper bound and the lower bound flips
           */
          const lowerBound = (totalBaseline >= 0) ? totalBaseline * 0.9 : totalBaseline * 1.1;
          const upperBound = (totalBaseline >= 0) ? totalBaseline * 1.1 : totalBaseline * 0.9;
          if (totalEnteredValue < lowerBound || totalEnteredValue > upperBound) {
            weekWarnings[field] = DEVIATION_WARNING_MSG
          }
        })

        if (Object.keys(weekErrors).length > 0) {
          if (!errors.weekData) errors.weekData = {}
          errors.weekData[week] = weekErrors
        }

        if (Object.keys(weekWarnings).length > 0) {
          if (!warnings.weekData) warnings.weekData = {}
          warnings.weekData[week] = weekWarnings
        }

        }
      })
      if(isSubmitting) {
        const _values =  values.weekData || {};
        let anyWeekEdited = false;
        Object.keys(_values).forEach((week) => {
          if(_values[week]?.touched?.size > 0) {
            anyWeekEdited = true;
          }
        });
        if(!anyWeekEdited &&  Object.keys(errors).length >0) {
          errors.weekData = {}
          errors.weekData[values.selectedWeeks[0]] = "Please adjust at least one of the metrics below";
        }
      }
    }
    /******************* Validate for all selected weeks - one time validation *********************************/
    else if (values.selectedRadio === 'groupweeks') {

      /**** Validating for metrics, any of the above fields should have valid data *****/
      const anyFieldFilled = fieldsToCheck.some((field) => {
        const val = values[field];
        return val !== undefined && val !== '' && val !== null;
      })

      if (!anyFieldFilled) {
        errors.metrics = 'Please adjust at least one of the metrics below'
      }

      /**** Additional check for AdjustmentReason and Comments Fileds  *****/
      if (!Object.values(values.adjustmentReason).length) {
        errors.adjustmentReason = 'Please select an adjustment reason'
      }

      if (!values.comment) {
        errors.comment = 'Please add comment'
      } else if (isCommentValid(values.comment, TEXT_AREA_MAX_CHAR)) {
        errors.comment = 'Comment Exceeds Max Length';
      }



      fieldsToCheck.forEach((field) => {

        let totalValue = {}
        if (field === 'salesPublic') {
          totalValue = { ...totalBaselineValue }
          formik.values.selectedWeeks.forEach(() => {
            totalValue[field] += Number(values[field]?.replace(/,/g, ''))
          })
        } else {
          values.selectedWeeks.forEach((week) => {
            const baseline = forecastData?.[week]?.[field];
            const enteredValue = Number(values[field]?.replace(/,/g, ''))

            if (baseline !== undefined && values[field] !== '') {
              totalValue[field] = enteredValue;

            }
          })
        }

        const totalBaseline = totalBaselineValue[field]
        const totalEnteredValue = totalValue[field]

        /**The below check is to handle the negative number scenarios
         * For negative numbers product with 0.9 becomes more negative
         * So the upper bound and the lower bound flips
         */
        const lowerBound = (totalBaseline >= 0) ? totalBaseline * 0.9 : totalBaseline * 1.1;
        const upperBound = (totalBaseline >= 0) ? totalBaseline * 1.1 : totalBaseline * 0.9;
        if (totalEnteredValue < lowerBound || totalEnteredValue > upperBound) {
          warnings[field] = DEVIATION_WARNING_MSG
        }

      })
    }
    setWarnings(warnings)
    return errors
  }

  const initialValues: FormValues = {
    salesPublic: '',
    salesPublicPct: '',
    grossProfit: '',
    marksDown: '',
    totalShrink: '',
    suppliesPackaging: '',
    allowances: '',
    selectedWeeks: [],
    adjustmentReason: {},
    comment: '',
    selectedRadio: 'singleweek',
    metrics: '',
    weekData: {}, /**For week-specific forms */
    errors: {},
    touchedWeeks: {},
    grossProfitPct: '',
    marksDownPct: '',
    totalShrinkPct: '',
    allowancesPct: '',
    selling: '',
    nonSelling: '',
    sellingPct: '',
    nonSellingPct: '',
    touched: new Set<string>(),
  }

  const isWeekUpdated = (_values) => {
    if(_values?.weekData?.[_values.selectedWeeks[0]]?.touched?.size > 0) {
      return true;
    }
  }

  const formik = useFormik<FormValues>({
    initialValues,
    validate: (values) => validate(values, false),
    onSubmit: (values) => {
      setIsSaveApiLoading(true);
      const apiBody: Adjustment = createAdjustmentApiBody(appliedFilters, userInfo, weeksCalculated, bannerId);
      setEditForecastBffBody(apiBody);
      saveAdjustmentValue(apiBody);
    },
  });



  useEffect(() => {
    if (isOpen) {
      // helps with auto scroll once you fix an error
      const selectedWeek = formik.values.selectedWeeks[0]
      const error = formik.values.selectedRadio === 'singleweek' ? formik.errors.weekData?.[selectedWeek] || formik.errors : formik.errors
      if (Object.keys(error || {}).length > 0) {

        scrollToFirstError(error, selectors, fieldOrder);
      }
    }
  }, [formik.errors, formik.errors.weekData, formik.errors, formik?.isSubmitting, formik.values.selectedWeeks]);

useEffect(() => {
  if (isOpen && selectedEditWeek) {
    const weekNbr = Number(String(selectedEditWeek).slice(-2));
    formik.setFieldValue('selectedWeeks', [weekNbr]);
  }
}, [isOpen, selectedEditWeek]);

  const recalculateSelectedWeekPayload = (salesPublic: any = undefined, _touched: any = undefined): Week[] => {
    if (formik.values.selectedRadio === 'groupweeks') {

      const values = formik.values
      const weeks: Week[] = values.selectedWeeks.map((week): Week => {
        // debugger;
        const formattedWeekKey = `Weeks${week.toString().padStart(2, '0')}`; // Format week as "Weeks01", "Weeks02", etc.
        const previousFcstData = adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST) ||
          adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.PROJECTION);
        const previousLYData = adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.LAST_YEAR_ACTUAL)
        const touched = _touched || values?.touched;
        const I33 = Number(salesPublic || formik.values.salesPublic);
        const J2 = Number(previousLYData.line1PublicToSalesNbr)
        const I2 = Number(previousFcstData.line1PublicToSalesNbr);
        const I34 = I33 + I2;
        const K2 = Number(previousFcstData.line5BookGrossProfitNbr || 0);
        const M2 = Number(previousFcstData.line5MarkDownsNbr || 0);
        const O2 = Number(previousFcstData.line5ShrinkNbr || 0);
        const K33DeltaBgpNbr = ((K2 / I2) * (I2 + I33)) - K2;
        const M33DeltaMrkNbr = ((M2 / I2) * I34) - M2;
        const O33DeltaShkNbr = ((O2 / I2) * I34) - O2;
        const K34bgpNbr = touched?.has('grossProfit') ? Number(values.grossProfit?.replace(/,/g, '')) : K33DeltaBgpNbr + K2;
        const M34MrkNbr = touched?.has('marksDown') ? Number(values.marksDown?.replace(/,/g, '')) : M33DeltaMrkNbr + M2;
        const O34ShkNbr = touched?.has('totalShrink') ? Number(values.totalShrink?.replace(/,/g, '')) : O33DeltaShkNbr + O2;
        // calc perc
        const J34Line1Pcnt = (I34 - J2) / J2;
        const L34BgpPcnt = K34bgpNbr / I34;
        const N34MrkPcnt = M34MrkNbr / I34;
        const P34ShkPcnt = O34ShkNbr / I34;
        const allowancesValue: Allowance = getAllowances(values.selling, values.nonSelling, previousFcstData);
        const suppliesPackaging = getSuppliesPackaging(values.suppliesPackaging);
        const weekField: Week =
        {
          fiscalWeekNbr: Number(previousFcstData['fiscalWeekNbr']),
          editedColumns: Array.from(values.touched).join("|"),
          previousAggregatedData: {
            line1PublicToSalesNbr: previousFcstData?.line1PublicToSalesNbr || 0,
            line1PublicToSalesPct: previousFcstData?.line1PublicToSalesPct || 0,
            line5BookGrossProfitNbr: previousFcstData?.line5BookGrossProfitNbr || 0,
            line5BookGrossProfitPct: previousFcstData?.line5BookGrossProfitPct || 0,
            line5MarkDownsNbr: previousFcstData?.line5MarkDownsNbr || 0,
            line5MarkDownsPct: previousFcstData?.line5MarkDownsPct || 0,
            line5ShrinkNbr: previousFcstData?.line5ShrinkNbr || 0,
            line5ShrinkPct: previousFcstData?.line5ShrinkPct || 0,
            line6SuppliesPackagingNbr: previousFcstData?.line6SuppliesPackagingNbr || 0,
            line7RetailsAllowancesNbr: previousFcstData?.line7RetailsAllowancesNbr || 0,
            line7RetailsSellingAllowancesNbr: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
            line7RetailsNonSellingAllowancesNbr: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0
          },
          newAggregatedData: {
            //TODO : need to separate the api creation from field pop
            line1PublicToSalesNbr: I34,
            line1PublicToSalesPct: J34Line1Pcnt,
            line5BookGrossProfitNbr: K34bgpNbr, //Number(previousAdjData?.line5BookGrossProfitNbr || 0) + Number(values.grossProfit || 0),
            line5BookGrossProfitPct: L34BgpPcnt,
            line5MarkDownsNbr: M34MrkNbr,//Number(previousAdjData?.line5MarkDownsNbr || 0) + Number(values.marksDown || 0),
            line5MarkDownsPct: N34MrkPcnt,
            line5ShrinkNbr: O34ShkNbr,//Number(previousAdjData?.line5ShrinkNbr) + Number(values.totalShrink || 0),
            line5ShrinkPct: P34ShkPcnt,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowancesValue.totalAllowances,
            line7RetailsSellingAllowancesNbr: allowancesValue.selling,
            line7RetailsNonSellingAllowancesNbr: allowancesValue.nonSelling
          },
          reason: values.adjustmentReason?.name || '',
          comment: values.comment || '',
        }
        return weekField
      });
      return weeks.sort((a, b) => Number(a.fiscalWeekNbr) - Number(b.fiscalWeekNbr));
    }
    else {
      const values = formik.values
      const weeks: Week[] = values.selectedWeeks.map((week): Week => {
        // debugger;
        const formattedWeekKey = `Weeks${week.toString().padStart(2, '0')}`; // Format week as "Weeks01", "Weeks02", etc.
        const previousFcstData = adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST) ||
          adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.PROJECTION);
        const previousLYData = adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.LAST_YEAR_ACTUAL)
        const touched = _touched || values?.weekData?.[week]?.touched;
        const I33 = Number(salesPublic ? salesPublic : formik?.values?.weekData?.[week]?.salesPublic?.replace(/,/g, '')) || 0;
        const J2 = Number(previousLYData?.line1PublicToSalesNbr || 0);
        const I2 = Number(previousFcstData?.line1PublicToSalesNbr || 0);
        const I34 = I33 + I2;
        const K2 = Number(previousFcstData?.line5BookGrossProfitNbr || 0);
        const M2 = Number(previousFcstData?.line5MarkDownsNbr || 0);
        const O2 = Number(previousFcstData?.line5ShrinkNbr || 0);
        const K33DeltaBgpNbr = I2 ? ((K2 / I2) * I34) - K2 : 0;
        const M33DeltaMrkNbr = I2 ? ((M2 / I2) * I34) - M2 : 0;
        const O33DeltaShkNbr = I2 ? ((O2 / I2) * I34) - O2 : 0;
        const K34bgpNbr = touched?.has('grossProfit') ? Number(formik?.values?.weekData?.[week]?.grossProfit?.replace(/,/g, '')) : K33DeltaBgpNbr + K2;
        const M34MrkNbr = touched?.has('marksDown') ? Number(formik?.values?.weekData?.[week]?.marksDown?.replace(/,/g, '')) : M33DeltaMrkNbr + M2;
        const O34ShkNbr = touched?.has('totalShrink') ? Number(formik?.values?.weekData?.[week]?.totalShrink?.replace(/,/g, '')) : O33DeltaShkNbr + O2;
        // calc perc
        const J34Line1Pcnt = J2 ? (I34 - J2) / J2 : 0;
        const L34BgpPcnt = I34 ? K34bgpNbr / I34 : 0;
        const N34MrkPcnt = I34 ? M34MrkNbr / I34 : 0;
        const P34ShkPcnt = I34 ? O34ShkNbr / I34 : 0;
        const allowancesValue: Allowance = getAllowances(values?.weekData?.[week]?.selling, values?.weekData?.[week]?.nonSelling, previousFcstData);
        const suppliesPackaging = getSuppliesPackaging(values?.weekData?.[week]?.suppliesPackaging);
        const weekField: Week =
        {
          fiscalWeekNbr: getFiscalWeekNumber(previousFcstData, previousLYData),
          editedColumns: Array.from(formik?.values?.weekData?.[week]?.touched || new Set<string>())
            .filter((field) => field !== 'adjustmentReason' && field !== 'comment')
            .join('|'),
          previousAggregatedData: {
            line1PublicToSalesNbr: previousFcstData?.line1PublicToSalesNbr || 0,
            line1PublicToSalesPct: previousFcstData?.line1PublicToSalesPct || 0,
            line5BookGrossProfitNbr: previousFcstData?.line5BookGrossProfitNbr || 0,
            line5BookGrossProfitPct: previousFcstData?.line5BookGrossProfitPct || 0,
            line5MarkDownsNbr: previousFcstData?.line5MarkDownsNbr || 0,
            line5MarkDownsPct: previousFcstData?.line5MarkDownsPct || 0,
            line5ShrinkNbr: previousFcstData?.line5ShrinkNbr || 0,
            line5ShrinkPct: previousFcstData?.line5ShrinkPct || 0,
            line6SuppliesPackagingNbr: previousFcstData?.line6SuppliesPackagingNbr || 0,
            line7RetailsAllowancesNbr: previousFcstData?.line7RetailsAllowancesNbr || 0,
            line7RetailsSellingAllowancesNbr: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
            line7RetailsNonSellingAllowancesNbr: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0
          },
          newAggregatedData: {
            //TODO : need to separate the api creation from field pop
            line1PublicToSalesNbr: I34,
            line1PublicToSalesPct: J34Line1Pcnt,
            line5BookGrossProfitNbr: K34bgpNbr, //Number(previousAdjData?.line5BookGrossProfitNbr || 0) + Number(values.grossProfit || 0),
            line5BookGrossProfitPct: L34BgpPcnt,
            line5MarkDownsNbr: M34MrkNbr,//Number(previousAdjData?.line5MarkDownsNbr || 0) + Number(values.marksDown || 0),
            line5MarkDownsPct: N34MrkPcnt,
            line5ShrinkNbr: O34ShkNbr,//Number(previousAdjData?.line5ShrinkNbr) + Number(values.totalShrink || 0),
            line5ShrinkPct: P34ShkPcnt,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowancesValue.totalAllowances,
            line7RetailsSellingAllowancesNbr: allowancesValue.selling,
            line7RetailsNonSellingAllowancesNbr: allowancesValue.nonSelling
          },
          reason: formik?.values?.weekData?.[week]?.adjustmentReason?.name || '',
          comment: formik?.values?.weekData?.[week]?.comment || '',
        }
        return weekField
      });
      return weeks.sort((a, b) => Number(a.fiscalWeekNbr) - Number(b.fiscalWeekNbr));
    }
  }

  useEffect(() => {
    if (isOpen && isWeekUpdated(formik.values)) {
      const updatedWeeks = recalculateSelectedWeekPayload();
      if (formik.values.selectedRadio === 'singleweek') {
        setWeeksCalculated((prevWeeks) => {
          const updatedWeekMap = new Map(prevWeeks.map((week) => [week.fiscalWeekNbr, week]));
          updatedWeeks.forEach((week) => {
            updatedWeekMap.set(week.fiscalWeekNbr, week);
          });
          return Array.from(updatedWeekMap.values());
        });
      } else {
        setWeeksCalculated([...updatedWeeks]);
      }
    }
  }, [formik.values.selectedWeeks, formik.values]);

  const weeksInQuarter = useMemo<any>(() => {
    return Object.keys(adjustmentWorksheetDataSlice).filter(key => key.includes("Week")).length;
  }, [adjustmentWorksheetDataSlice]);

  /** Calculate Total baseline values on load for selected weeks */
  useEffect(() => {
    if(formik.values.selectedWeeks.length > 0) {
      const newTotalBaseline = {
        'salesPublic': 0,
        'grossProfit': 0,
        'grossProfitPct': 0,
        'marksDown': 0,
        'marksDownPct': 0,
        'totalShrink': 0,
        'totalShrinkPct': 0,
        'suppliesPackaging': 0,
        'allowances': 0,
        'selling': 0,
        'nonSelling': 0,
      }
      const updatedTotalValue = {
        'salesPublic': 0,
        'grossProfit': 0,
        'marksDown': 0,
        'totalShrink': 0,
        'suppliesPackaging': 0,
        'allowances': 0,
        'selling': 0,
        'nonSelling': 0,
      }
      const lastYearActual: FormFieldNames = {
        salesPublic: 0,
        grossProfit: 0,
        marksDown: 0,
        totalShrink: 0,
        suppliesPackaging: 0,
        allowances: 0,
        selling: 0,
        nonSelling: 0
      }
      formik.values.selectedWeeks.forEach((week) => {
        Object.keys(newTotalBaseline).forEach((field) => {
          const baselineValue = transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.MERCH_FORECAST, ADJ_SUB_ROW.PROJECTION)?.[week]?.[field] || 0;
          newTotalBaseline[field] += baselineValue;

          let fieldValue = 0;
          if (formik.values.selectedRadio === 'singleweek') {
            fieldValue = formik?.values?.weekData?.[week]?.[field] || '';
          }

          const totalInputValue = Number(fieldValue) * formik.values.selectedWeeks.length
          updatedTotalValue[field] = totalInputValue + newTotalBaseline[field]

          const baseLineLastYear = transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.LAST_YEAR_ACTUAL, '')?.[week]?.[field] || 0
          lastYearActual[field] += baseLineLastYear
        })

      })

      const touchedFields = formik?.values?.weekData?.[formik.values.selectedWeeks[0]]?.touched;
      const isNewWeek = formik.values.selectedRadio === 'singleweek' && (!touchedFields || touchedFields.size <= 0);
      if (formik.values.selectedWeeks.length <= 0 || isNewWeek || formik.values.selectedRadio === 'groupweeks') {
        formik.setFieldValue('marksDown', '');
        formik.setFieldValue('grossProfit', '');
        formik.setFieldValue('totalShrink', '');
        formik.setFieldValue('salesPublic', '');
        formik.setFieldValue('suppliesPackaging', '');

        formik.setFieldValue('salesPublicPct', '');
        formik.setFieldValue('marksDownPct', '');
        formik.setFieldValue('grossProfitPct', '');
        formik.setFieldValue('totalShrinkPct', '');

        formik.setFieldValue('allowances', '');
        formik.setFieldValue('selling', '');
        formik.setFieldValue('nonSelling', '');
      }
      const weekNumber = formik.values.selectedWeeks[0]
      const baseValues = getBaseValues(weekNumber);
      formik.values.weekData = {
        [weekNumber] : {...baseValues},
        ...formik.values.weekData
      }
      setTotalLastYearActual(lastYearActual)
      setTotalBaselineValue(newTotalBaseline)
      setTotalValue(updatedTotalValue)
    }

  }, [formik.values.selectedWeeks])

  const getBaseValues = (week) => {
    const formattedWeekKey = `Weeks${week.toString().padStart(2, '0')}`; // Format week as "Weeks01", "Weeks02", etc.
    const previousFcstData = adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST) ||
      adjustmentWorksheetDataSlice[formattedWeekKey].find(subRows => subRows.subRow === ADJ_SUB_ROW.PROJECTION);
    const newTotalBaseline: any = {
      'grossProfit': 0,
      'grossProfitPct': 0,
      'marksDown': 0,
      'marksDownPct': 0,
      'totalShrink': 0,
      'totalShrinkPct': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      // 'selling': 0,
      // 'nonSelling': 0,
    }
    if(previousFcstData) {
      newTotalBaseline['grossProfit'] = numberFormatter.format(previousFcstData.line5BookGrossProfitNbr);
      newTotalBaseline['grossProfitPct'] = pctFormatter.format(previousFcstData.line5BookGrossProfitPct);
      newTotalBaseline['marksDown'] = numberFormatter.format(previousFcstData.line5MarkDownsNbr);
      newTotalBaseline['marksDownPct'] = pctFormatter.format(previousFcstData.line5MarkDownsPct);
      newTotalBaseline['totalShrink'] = numberFormatter.format(previousFcstData.line5ShrinkNbr);
      newTotalBaseline['totalShrinkPct'] = pctFormatter.format(previousFcstData.line5ShrinkPct);
      newTotalBaseline['allowances'] = numberFormatter.format(previousFcstData.line7RetailsAllowancesNbr);
      newTotalBaseline['suppliesPackaging'] = numberFormatter.format(previousFcstData.line6SuppliesPackagingNbr);
      if(previousFcstData.line7RetailsSellingAllowancesNbr !== 0)
      newTotalBaseline['selling'] = numberFormatter.format(previousFcstData.line7RetailsSellingAllowancesNbr);
      if(previousFcstData.line7RetailsNonSellingAllowancesNbr !== 0)
      newTotalBaseline['nonSelling'] = numberFormatter.format(previousFcstData.line7RetailsNonSellingAllowancesNbr);
    }
    return newTotalBaseline;
  }

  const handleSubmit = (ev) => {
    const errors = validate(formik.values, true)
    const selectedWeek = formik.values.selectedWeeks[0]
    const error = formik.values.selectedRadio === 'singleweek' ? formik.errors.weekData?.[selectedWeek] || formik.errors : formik.errors
    if (Object.keys(error || {}).length > 0) {
      // this is needed because on singleWeek we are doing short circuiting
      // whereas in group selection we are letting handlesubmit to go thru
      scrollToFirstError(error, selectors, fieldOrder);
    }
    if (formik.values.selectedRadio === 'singleweek') {
      if (Object.keys(errors).length > 0 && Object.keys(errors.weekData || {}).length > 0) {
        formik.setErrors(errors);
        setActiveWeekSubmit(true)
      }
      else {
        formik.handleSubmit()
      }
    }
    else {
      // setIsSaveApiLoading(true);
      formik.handleSubmit()
    }

  }

  const updateTotalValue = (name, value) => {
    const updatedTotalValue = { ...totalValue }
    if (!isNaN(Number(value))) {
      updatedTotalValue[name] = totalBaselineValue[name] + (Number(value) * formik.values.selectedWeeks.length);
    } 
    else {
       updatedTotalValue[name] = totalBaselineValue[name];
    }
    setTotalValue(updatedTotalValue)
    return updatedTotalValue;
  }

  const isCommentValid = (comment: string, maxLength) => {
    return comment.length > maxLength;
  }

  const renderInputs = ({ state, setter, inputType, name, stateWarnings = {}, touched }) => {
    const editState = editStates[name] || {};
    const newOnChange = (e) => {
      setter(e);
    };
    const getPctValue = (fieldName) => {
      if (formik.values.selectedWeeks.length <= 0) return '';
      if (formik.values.selectedRadio === 'groupweeks') return formik.values[`${fieldName}Pct`];
      const specificWeekData = formik.values.weekData?.[formik.values.selectedWeeks[0]];
      if (!specificWeekData) return '';
      return specificWeekData[`${fieldName}Pct`] || '';
    };
    const getSubLabelLine1 = (_value, _name) => {
      const _totalValue = numberFormatter.format(Number(totalValue?.[_name]))
      if(Number(_value)) {
        const _totalBaseLineValue = numberFormatter.format(Number(totalBaselineValue[_name]))
        return `$${_totalBaseLineValue} ➔ $${_totalValue}`;
      }
      return `Current Sales: $${_totalValue}`;
    };
    const getSubLabelLineItems = (_value, _name, _isPct = false) => {
      const formatter = _isPct ? pctFormatter : numberFormatter;
      const _baseLineValue = formatter.format(Number(removeCommas(totalBaselineValue[_name] || "0")));
      const _inputValue = formatter.format(Number(removeCommas(_value)));
      if(_inputValue !== _baseLineValue) {
        return _isPct ? `was: ${pctFormatter.format(Number(_baseLineValue))}%` : `was: $${_baseLineValue}`;
      }
      return ``;
    };

    const getSubLabelforLine8 = () => {
      const _inputValue = numberFormatter.format(
        Number(removeCommas(getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]))))
      const _baseLineValue = numberFormatter.format(Number(removeCommas(getLine8(totalBaselineValue))))
      if(_inputValue !== _baseLineValue) {
        return `was: $${_baseLineValue}`;
      }
      return ``;
    }

    const getSubLabelforLine8Pct = () => {
      const _inputValue = numberFormatter.format(
        Number(removeCommas(getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]))))
      const line1Base = totalBaselineValue['salesPublic'] || 0;
      const _baseLineValue = numberFormatter.format(Number(removeCommas(getLine8(totalBaselineValue))))
      if(_inputValue !== _baseLineValue) {
        return line1Base ? `was: ${pctFormatter.format((Number(removeCommas(_baseLineValue)) / line1Base) * 100)}%` : ``;
      }
      return ``;
    }

    const error = stateWarnings[name] === DEVIATION_WARNING_MSG;
    const isAllowanceField = name === 'allowances';
    // Always show restore button, enable only if value changed from previousValue
    const canRestore = state !== editState.previousValue && editState.editing;
    const arrowColor = canRestore ? '#1B6EBB' : '#B9C0D4';
    const line8Value = getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]);
    const line8Pct = totalValue['salesPublic'] ? (line8Value / totalValue['salesPublic']) * 100 : 0;
    const baseLine8Value = getSubLabelforLine8();
    const baseLine8Pct = getSubLabelforLine8Pct();
    switch (inputType) {
      case 'dollar':
        return (
          <>
            <TextField
              className="w-[150px] gap-[4px] rounded border-[1px]"
              value={state}
              title={state}
              prefix="$"
              subLabel={`${getSubLabelLine1(state, name)}`}
              placeholder="±0"
              onChange={newOnChange}
              error={error}
              id={name}
              size="sm"
              data-testid={`${name}-input`}
            />
            <TextField
              className={`w-[100px] rounded border-[1px]`}
              value={getPctValue(name)}
              placeholder="± 0"
              suffix="%"
              onChange={newOnChange}
              id={`${name}Pct`}
              size="sm"
              data-testid={`${name}Pct-input`}
              name={`Input for ${name}Pct in percentage`}
            />
          </>
        );
      case 'noPct':
        return (
          <>
            {isAllowanceField ? (
              <div data-testid={`${name}-input`} className="w-fit h-fit inline-flex flex-col items-center">
                <div className="font-semibold pl-3 self-start min-w-[135px] min-h-[32px] content-center">{`$${state || 0}`}</div>
              </div>
            ) : (
              <div className="flex flex-col items-start w-fit">
                <TextField
                  className="w-[273px] gap-[4px] rounded border-[1px]"
                  value={state}
                  prefix="$"
                  onChange={newOnChange}
                  subLabel={(name !== 'selling' && name !== 'nonSelling') ? `${getSubLabelLineItems(state, name)}` : ``}
                  id={name}
                  size="sm"
                  data-testid={`${name}-input`}
                  onFocus={() => handleInputFocus(name, state)}
                  onBlur={() => handleInputBlur(name)}
                />
                {(name === 'selling' || name === 'nonSelling') && (
                  <div className="flex justify-between w-[273px] mt-[4px] text-sm text-[#5a697b] items-center">
                    {(Number(removeCommas(state)) !== Number(removeCommas(totalBaselineValue[name])) && state !== '') ? (
                      <div>
                        {`${getSubLabelLineItems(state, name)}`}
                      </div>
                    ) : (
                      <div />
                    )}
                    <button
                      type="button"
                      className="ml-auto flex items-center gap-1 text-[#1b6ebb] underline disabled:text-gray-400"
                      onMouseDown={e => e.preventDefault()}
                      onClick={() => handleRestoreValue(name, setter)}
                      disabled={!canRestore}
                    >
                      {ArrowSymbol(arrowColor)}
                      Restore value
                    </button>
                  </div>
                )}
              </div>
            )}
          </>
        );
      case 'text':
        return (
          <>
            <TextField
              className="w-[150px] gap-[4px] rounded border-[1px]"
              value={state}
              prefix="$"
              onChange={newOnChange}
              error={error}
              subLabel={`${getSubLabelLineItems(state, name)}`}
              id={name}
              size="sm"
              data-testid={`${name}-input`}

            />
            <TextField
              className="w-[100px] rounded border-[1px]"
              value={(getPctValue(name))}
              suffix="%"
              onChange={newOnChange}
              subLabel={`${getSubLabelLineItems(getPctValue(name), `${name}Pct`, true)}`}
              id={`${name}Pct`}
              size="sm"
              data-testid={`${name}Pct-input`}
              aria-describedby={`${name}Pct-description`}
            />
          </>
        );
      case 'readonly':
        return (
          <div data-testid={`${name}-input`} className="inline-flex flex-row items-center gap-6">
            <div className="gap-2 min-w-[130px] self-end items-end min-h-[32px] flex flex-col">
              <span className='font-normal text-base leading-5 tracking-normal align-middle'> {`$${numberFormatter.format(line8Value) || 0}`}</span>
              <span className='text-gray-500 font-normal text-sm leading-4 tracking-normal align-middle'> {baseLine8Value}</span>

            </div>
            <div className="gap-2 min-w-[100px] justify-center self-start items-start flex flex-col">
              <span className='font-normal text-base leading-5 tracking-normal align-middle'> {`${pctFormatter.format(line8Pct) || 0}%`}</span>
              <span className='text-gray-500 font-normal text-sm leading-4 tracking-normal align-middle'> {baseLine8Pct}</span>
            </div>
          </div>
        );
      default:
        return null;
    }
  }

  useEffect(() => {
    if (!isOpen) {
      clearFormFields()
      setIsSaveApiLoading(false);
    }
  }, [isOpen])

  const addSign = (value, name) => {

    let targetvalue = value.replace(/[^0-9.\-+]/g, '');
    if (targetvalue.length > 1) {
      targetvalue = targetvalue.replace(/(?<!^)[+-]/g, '')
    }
    if (name === 'salesPublic') {
      if (targetvalue && !targetvalue.startsWith('+') && !targetvalue.startsWith('-')) {
        targetvalue = `+${targetvalue}`
      }
    }
    return targetvalue;
  }

  const removeCommas = (value) => {
      return (value && typeof value === 'string') ? value.replace(/,/g, '') : value;
  }

  const getLine8 = (newValues: any) => {
    const line5a = newValues?.grossProfit || '0';
    const line5b = newValues?.marksDown || '0';
    const line5c = newValues?.totalShrink || '0';
    const line6 = newValues?.suppliesPackaging || '0';
    const line7 = newValues?.allowances || '0';
    const line5 = Number(removeCommas(line5a)) + Number(removeCommas(line5b)) + Number(removeCommas(line5c));
    const line8 = line5 + Number(removeCommas(line6)) + Number(removeCommas(line7));
    return line8;
  }

  const isValidTargetValue = (value) => {
    const parsed = Number(value);
    return !Number.isNaN(parsed) && parsed !== 0;
  }
  /** This method is to ensure all the form fields in the single week selection is validated properly by appropriately adding and deleting the entries from weekdata
   * Once user types values - capture the data weekwise
   * Once the user deletes the value of the field, delete the entry from weekdata to determine the week is in error state / valid state
   * Validation should happen only when there is any change in the particular week, which is been captured in weekdata, so its important to keep the weekdata updated
   */
  const singleWeekHandleChange = (targetName, e, week) => {

    const targetId: string = e.target.id;
    let targetvalue = e.target.value;
    let isPct = false;
    let actualTargetName = targetName;
    if (targetId.includes('Pct')) {
      isPct = true;
      actualTargetName = `${targetName}Pct`
    } else {
      targetvalue = targetvalue && addSign(targetvalue, targetName)
    }

    formik.setValues((prevValues) => {
      const newValues = { ...prevValues }
      /***Ensure weekdata exists */
      if (!newValues.weekData) {
        newValues.weekData = {}
      }

      if (!targetvalue) {
        /** Remove the specified field */
        if (newValues.weekData[week]) {
          delete newValues.weekData[week][targetName];
          delete newValues.weekData[week][`${targetName}Pct`];

          /** if  weekData[week] is now an empty object, remove it*/
          if (Object.keys(newValues.weekData[week]).length === 0) {
            delete newValues.weekData[week]
          }
        }

        /** If weekdata itself is empty after deleting, remove it completely */
        if (Object.keys(newValues.weekData).length === 0) {
          // delete newValues.weekData;
          newValues.weekData = {};

        }
      } else {
        /** Set the specified field */
        const touched = newValues.weekData?.[week]?.touched || new Set<string>();
        touched.add(targetName);
        newValues.weekData = {
          ...newValues.weekData,
          [week]: {
            ...newValues.weekData?.[week],
            [actualTargetName]: targetvalue,
            touched
          }
        }
      }

      if (targetName === 'salesPublic') {
        newValues.weekData[week].touched = resetTouched(newValues.weekData[week].touched);
        if (isValidTargetValue(targetvalue)) {
          if (isPct) {
            const adjustedSP = totalBaselineValue['salesPublic'];
            targetvalue = adjustedSP * (Number(e.target.value) / 100);
            newValues.weekData[week].salesPublic = addSign(numberFormatter.format(targetvalue), targetName).replace(/,/g, '');
          }
          const calculatedWeek = recalculateSelectedWeekPayload(targetvalue, newValues.weekData[week].touched).find((w) => Number(String(w.fiscalWeekNbr).slice(-2)) === week);
          const prevSalesPublic = calculatedWeek?.previousAggregatedData?.line1PublicToSalesNbr || 0;
          const bgpVal = calculatedWeek?.newAggregatedData.line5BookGrossProfitNbr
          const shrVal = calculatedWeek?.newAggregatedData.line5ShrinkNbr;
          const mrkVal = calculatedWeek?.newAggregatedData.line5MarkDownsNbr;
          const spPct = prevSalesPublic ? calculateSalesPublicPcnt(targetvalue, prevSalesPublic) : 0;
          const bgpPct = calculatedWeek?.newAggregatedData.line5BookGrossProfitPct || 0;
          const mrkPct = calculatedWeek?.newAggregatedData.line5MarkDownsPct || 0;
          const shrPct = calculatedWeek?.newAggregatedData.line5ShrinkPct || 0;

          newValues.weekData[week].grossProfit = bgpVal ? numberFormatter.format(bgpVal) : '';
          newValues.weekData[week].totalShrink = shrVal ? numberFormatter.format(shrVal) : '';
          newValues.weekData[week].marksDown = mrkVal ? numberFormatter.format(mrkVal) : '';

          newValues.weekData[week].grossProfitPct = pctFormatter.format(bgpPct * 100);
          newValues.weekData[week].totalShrinkPct = pctFormatter.format(shrPct * 100)
          newValues.weekData[week].marksDownPct = pctFormatter.format(mrkPct * 100)

          if (isPct === false) {
            newValues.weekData[week].salesPublicPct = pctFormatter.format(spPct * 100) || '';
          }

        } else if(targetvalue !== '+' && targetvalue !== '-') {
          const baseValues = getBaseValues(week);
          newValues.weekData[week].salesPublic = baseValues.salesPublic || '';
          newValues.weekData[week].salesPublicPct = baseValues.salesPublicPct || '';
          newValues.weekData[week].grossProfit = baseValues.grossProfit || '';
          newValues.weekData[week].grossProfitPct = baseValues.grossProfitPct || '';
          newValues.weekData[week].marksDown = baseValues.marksDown || '';
          newValues.weekData[week].marksDownPct = baseValues.marksDownPct || '';
          newValues.weekData[week].totalShrink = baseValues.totalShrink || '';
          newValues.weekData[week].totalShrinkPct = baseValues.totalShrinkPct || '';
        }
        updateTotalValue(targetName, targetvalue);
      }
      else if (newValues.weekData[week] && (targetName === 'selling' || targetName === 'nonSelling')) {
        if(!newValues.weekData[week].selling && !newValues.weekData[week].nonSelling ) {
          const baseValues = getBaseValues(week);
          newValues.weekData[week].allowances = baseValues.allowances || '';
        } else {
          const sellingValue = Number(removeCommas(newValues.weekData[week].selling) || 0);
          const nonSellingValue = Number(removeCommas(newValues.weekData[week].nonSelling) || 0);
          const allowancesValue = getAllowancesValue(sellingValue, nonSellingValue);
          newValues.weekData[week].allowances = numberFormatter.format(allowancesValue);
        }
      }
      else if (newValues.weekData[week] && (targetName === 'grossProfit' || targetName === 'marksDown' || targetName === 'totalShrink')) {
        if (isValidTargetValue(targetvalue)) {
          const adjustedSalesPublic = totalValue['salesPublic'];
          if (isPct) {
            targetvalue = (adjustedSalesPublic * Number(e.target.value) / 100);
            newValues.weekData[week][targetName] = numberFormatter.format(targetvalue);
          } else {
            const pctChange = adjustedSalesPublic ? (Number(targetvalue)) / adjustedSalesPublic : 0;
            newValues.weekData[week][`${targetName}Pct`] = pctFormatter.format(pctChange * 100);
          }
        }
      }
      return newValues;
    })

  }

  const resetTouched = (touched: Set<string>) => {
    touched.delete('grossProfit');
    touched.delete('marksDown');
    touched.delete('totalShrink');
    return touched;
  }

  const getAllowancesValue = (sellingValue, nonSellingValue) => {
    return isNaN(sellingValue) ? 0 : sellingValue + (isNaN(nonSellingValue) ? 0 : nonSellingValue);
  }

  const getAllowances = (selling, nonSelling, previousFcstData) => {
    let allowances: Allowance = {
      selling: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
      nonSelling: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0,
      totalAllowances: previousFcstData?.line7RetailsAllowancesNbr || 0
    };
    if (selling || nonSelling) {
      allowances.selling = Number(removeCommas(selling)) || 0;
      allowances.nonSelling = Number(removeCommas(nonSelling)) || 0;
      allowances.totalAllowances = getAllowancesValue(allowances.selling, allowances.nonSelling);
    }
    return allowances;
  }

  const getSuppliesPackaging = (value: string | undefined) => {
    if (value) {
      return Number(removeCommas(value));
    }
    return null;
  }

  const multiWeekHandleChange = (targetName, e) => {
    formik.setFieldValue('touched', {
      ...formik.values.touched,
      [targetName]: true
    });

    const targetId: string = e.target.id;
    let targetvalue = e.target.value;
    let isPct = false;
    let actualTargetName = targetName;
    if (targetId.includes('Pct')) {
      isPct = true;
      actualTargetName = `${targetName}Pct`
    } else {
      targetvalue = targetvalue && addSign(targetvalue, targetName)
    }

    formik.setValues((prevValues) => {
      const newValues = { ...prevValues };
      if (!targetvalue) {
        newValues[targetName] = "";
        newValues[`${targetName}Pct`] = "";
      } else {
        newValues.touched.add(targetName);
      }
      newValues[actualTargetName] = targetvalue;
      updateTotalValue(targetName, targetvalue);
      if (targetName === 'salesPublic') {
        newValues.touched = resetTouched(newValues.touched);
        if (isValidTargetValue(targetvalue)) {
          if (isPct) {
            const adjustedSP = totalBaselineValue['salesPublic'];
            targetvalue = adjustedSP * (Number(e.target.value) / 100);
            newValues.salesPublic = addSign(numberFormatter.format(targetvalue), targetName).replace(/,/g, '');
          }
          const calculatedWeeks = recalculateSelectedWeekPayload(targetvalue, newValues.touched);
          const totals = {
            bgpVal: 0,
            shrVal: 0,
            mrkVal: 0,
            spVal: 0,
            prvSpVal: 0
          };
          calculatedWeeks.forEach(week => {
            const bgpVal = week?.newAggregatedData.line5BookGrossProfitNbr
            const shrVal = week?.newAggregatedData.line5ShrinkNbr;
            const mrkVal = week?.newAggregatedData.line5MarkDownsNbr;
            const spVal = week?.newAggregatedData.line1PublicToSalesNbr;
            const prvSpVal = week?.previousAggregatedData.line1PublicToSalesNbr;
            totals.bgpVal = totals.bgpVal += bgpVal;
            totals.shrVal = totals.shrVal += shrVal;
            totals.mrkVal = totals.mrkVal += mrkVal;
            totals.spVal = totals.spVal += spVal;
            totals.prvSpVal = totals.prvSpVal += prvSpVal;
          });
          const spPct = calculateSalesPublicPcnt(Number(targetvalue), totals.prvSpVal) || 0;
          const bgpPct = totals.bgpVal / totals.spVal;
          const mrkPct = totals.mrkVal / totals.spVal;
          const shrPct = totals.shrVal / totals.spVal;

          newValues.grossProfit = numberFormatter.format(totals.bgpVal);
          newValues.marksDown = numberFormatter.format(totals.mrkVal);
          newValues.totalShrink = numberFormatter.format(totals.shrVal);

          if (isPct === false) {
            newValues.salesPublicPct = numberFormatter.format(spPct * 100);
          }
          newValues.grossProfitPct = numberFormatter.format(bgpPct * 100);
          newValues.marksDownPct = numberFormatter.format(mrkPct * 100);
          newValues.totalShrinkPct = numberFormatter.format(shrPct * 100);
        } else {
          newValues.grossProfit = String('');
          newValues.totalShrink = String('');
          newValues.marksDown = String('');
          newValues.grossProfitPct = String('');
          newValues.totalShrinkPct = String('');
          newValues.marksDownPct = String('');
        }
        updateTotalValue(targetName, targetvalue);
      }
      else if (targetName === 'selling' || targetName === 'nonSelling') {
        const sellingValue = Number(newValues.selling || 0);
        const nonSellingValue = Number(newValues.nonSelling || 0);
        const allowancesValue = getAllowancesValue(sellingValue, nonSellingValue);
        newValues.allowances = numberFormatter.format(allowancesValue);
      }
      else if (targetName === 'grossProfit' || targetName === 'marksDown' || targetName === 'totalShrink') {
        if (isValidTargetValue(targetvalue)) {
          const calculatedWeeks = recalculateSelectedWeekPayload(formik.values.salesPublic);
          if (isPct) {
            let totals = 0;
            calculatedWeeks.forEach(week => {
              totals = totals + (week.newAggregatedData.line1PublicToSalesNbr * Number(e.target.value) / 100);
            });
            newValues[targetName] = numberFormatter.format(totals);
          } else {
            const adjustedSalesPublic = totalValue['salesPublic'];
            const pctChange = (Number(targetvalue)) / adjustedSalesPublic;
            newValues[`${targetName}Pct`] = numberFormatter.format(pctChange * 100);
          }
        }
      }
      return newValues;
    });
  }
  /** This is a temporary method for the error banner since the icon is not proper on using the uds syntax
   * Below are the code used in uds directly
   */
  const renderErrorAlert = (error) => {
    return (
      <div role="alert" className="flex items-start rounded-lg p-4 border min-h-fit cursor-default bg-red-50 text-compliant-red border-red-300 w-[682px] edit-notifications" style={{ zIndex: 30, top: '76px' }}>

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#9D2210" stroke="#FFF6F5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="min-w-[24px]">
          <polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>

        <div className="w-[calc(100%-80px)] mx-4">
          <div className="text-left font-bold leading-6 block break-words">{error}</div>
        </div>

      </div>)
  }

  const getWarningComponent = (name, stateWarnings) => {
    const error = stateWarnings?.[name] === DEVIATION_WARNING_MSG;
    const subLabel = error ? stateWarnings[name] : '';
    if (!error) return null;
    return (
      <div>
        <Tooltip
          zIndex={2}
          anchor='top'
          variant='dark'
          label={`${subLabel}`}>
            <TriangleAlert color="#F0A92C" />
        </Tooltip>
      </div>
    )
  }

  /** Resetting the form on Radio change / On Submitting / On closing the Edit forecast modal */
  const clearFormFields = (radioSelection = 'singleweek') => {
    setTotalBaselineValue({
      'salesPublic': 0,
      'grossProfit': 0,
      'marksDown': 0,
      'totalShrink': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      'selling': 0,
      'nonSelling': 0
    })
    setTotalValue({
      'salesPublic': 0,
      'grossProfit': 0,
      'marksDown': 0,
      'totalShrink': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      'selling': 0,
      'nonSelling': 0
    })
    setWarnings({})
    formik.resetForm()
    formik.setFieldValue('salesPublicPct', '');
    formik.setFieldValue('marksDownPct', '');
    formik.setFieldValue('grossProfitPct', '');
    formik.setFieldValue('totalShrinkPct', '');
    /** Making the radio selection back to Group weeks */
    formik.setFieldValue('selectedRadio', radioSelection);
    setWeeksCalculated([]);
  }

  const saveAdjustmentValue = async (postBody: Adjustment | undefined) => {
    const isSaveAdjustmentSuccess = await saveAdjustment(postBody, dispatch, saveAdjustmentEdits);
    if (isSaveAdjustmentSuccess) {
      setIsSaveApiLoading(false);
      setOpen(false);
    } else {
      setIsSaveApiLoading(false);
    }
  }

  const EditAdjustmentButtons = (saveLoader: boolean) => (
    <div className="w-[421px] fixed bottom-0  bg-white col-span-2 flex justify-between gap-2  py-3  z-50 ">
        <Button data-testid="save-adjustment-button" type="button" onClick={handleSubmit} className='save-button-width' disabled={saveLoader}>
        {saveLoader ? <Spinner id='save-spinner' variant='solid' size='xs' /> : "Save adjustment"}
      </Button>
        <Link onClick={() => openHistoryDrawer(true,"left")} className="link-decoration" > Audit History </Link>
       <HistoryDrawer
        isOpen={isHistoryOpen}
        setOpen={setIsHistoryOpen}
        position={historyPosition}
      />
     </div>
  );
  return (
    <Drawer
      anchor="right"
      isOpen={isOpen}
      setOpen={setOpen}
      hideBackdrop={false}
      header={
        <div className="text-lg font-semibold overflow-hidden">
          Edit Adjustment
        </div>
      }
    >
      <form className="edit-forecast">
        <div className='grid grid-cols-2 gap-x-6 gap-y-2 '>

          <div className='col-span-2'>
            <Radio.Group
              onChange={(val) => clearFormFields(val)}
              error={formik.errors.selectedRadio}
              value={formik.values.selectedRadio}
            >
              <Radio label='Enter for each week separately' value='singleweek' />
              <Radio disabled label='Enter same values for selected weeks' value='groupweeks' />
            </Radio.Group>
          </div>

          <Divider orientation="horizontal" />

          <div className='col-span-2'>

            {/* Below line has to be replaced with the commented out uds code, once the uds component works on its own syntax */}

            {((formik.values.selectedRadio === 'groupweeks' && formik.submitCount > 0) || (formik.values.selectedRadio === 'singleweek')) && formik.errors.selectedWeeks && renderErrorAlert(formik.errors.selectedWeeks)}



            <span className='text-sm font-bold whitespace-nowrap select-none truncate'>
              Applied Weeks
            </span>

            <WeekSelection
              selectedWeeks={formik.values.selectedWeeks}
              touchedWeeks={(Object.keys(formik.values.touchedWeeks || {}))}
              errorWeeks={Object.keys(formik.errors.weekData || {})}
              weekData={formik.values.weekData}
              onWeekSelect={(weeks) => {
                const prevWeek = formik.values.selectedWeeks?.[0];
                const nextWeek = weeks?.[0];
                setActiveWeekSubmit(false)
                if (formik.values.selectedRadio === 'singleweek' && prevWeek !== null && prevWeek !== nextWeek) {
                  formik.validateForm();
                  const weekData = formik.values?.weekData?.[prevWeek];
                  if (weekData && weekData?.touched?.size > 0) {
                    formik.setFieldValue('touchedWeeks', {
                      ...formik.values.touchedWeeks,
                      [prevWeek]: true
                    })
                  }
                }
                if (JSON.stringify(formik.values.selectedWeeks) !== JSON.stringify(weeks)) {
                  formik.setFieldValue('selectedWeeks', weeks);
                }
              }}
              isSingleWeekSelect={formik.values.selectedRadio === 'singleweek'}
              weeksInQuarter={weeksInQuarter}
              firstWeekNbr={Number(Object.keys(adjustmentWorksheetDataSlice)?.filter(key => key.includes("Week"))[0]?.replace('Weeks', ''))}
            />

          </div>

          {/********************* Group Weeks Form ****************************/}
          {formik.values.selectedRadio === 'groupweeks' && <>
            <div className='col-span-2'>

              <Alert isOpen={true} sticky={false} variant='informational' className="edit-notifications" size="medium">
                <div className="notification-banner-text">Your adjustment will be proportionally applied to each SMIC</div>
              </Alert>


              {/* Below line has to be replaced with the commented out uds code, once the uds component works on its own syntax */}

              {formik.submitCount > 0 && formik.errors.metrics && renderErrorAlert(formik.errors.metrics)}

              {/* {formik.submitCount>0 && formik.errors.metrics && <Alert isOpen={true} sticky={false} variant='error' className="edit-notifications" size="medium">
                  <div> {formik.errors.metrics}</div>
                </Alert>} */}

              <div className="grid gap-3 mt-2 mb-6 text-black text-md">
                {formFields.map(({ label, name, inputType, subField }, index) => {
                  return (
                    <React.Fragment key={index}>
                      <div
                        className={`grid grid-cols-3 items-center metrics ${subField ? 'ml-9 -mt-2' : ''
                          }`}
                      >
                        <div className="flex gap-2">
                          <div className="w-fit h-fit inline-flex flex-col">
                            <div className={`flex items-center font-nunito text-xs font-semibold leading-4 tracking-normal ${subField ? 'w-[100px]' : 'w-[135px]'} h-[32px] cursor-pointer`}>
                              {label}
                            </div>
                          </div>
                          {renderInputs({
                            state: formik.values[name],
                            setter: (e) => {
                              multiWeekHandleChange(name, e);
                            },
                            inputType,
                            name,
                            stateWarnings: warnings,
                            touched: formik.values.touched,
                          })}
                        </div>
                      </div>
                      {label === 'salesPublic' && <Divider orientation="horizontal" />}
                      {label === 'Total Shrink' && <Divider orientation="horizontal" />}
                      {label === 'Supplies Packaging' && <Divider orientation="horizontal" />}
                      {label === 'Non Selling' && <Divider orientation="horizontal" />}
                    </React.Fragment>
                  );
                })}
              </div>
            </div>


            <div className='col-span-2 relative'>
              <span className='text-sm font-bold whitespace-nowrap select-none truncate'>
                Adjustment reason<span className="text-red-500">*</span>
              </span>
              <AutoComplete
                items={forecastAdjustmentReasons}
                itemKey="id"
                itemText="name"
                placeholder="Make a selection"
                width={410}
                size="md"
                menuHeight={250}
                error={formik.submitCount > 0 && formik.errors.adjustmentReason ? true : false}
                onChange={(val) => {
                  formik.setFieldValue('adjustmentReason', val);
                }}
                zIndex={100}
              />
              {formik.submitCount > 0 && formik.errors.adjustmentReason && <div ref={adjustmentErrorRef} className="edit-notifications-tex adjustment-error"> {`Please enter an adjustment reason`}</div>}
            </div>

            <div className='col-span-2 pb-[70px]'>
              <span className='text-sm font-bold whitespace-nowrap select-none truncate'>
                Comment<span className="text-red-500">*</span>
              </span>
              <TextArea
                name="comment"
                isRequired
                resize="resize-y"
                error={formik.submitCount > 0 && formik.errors.comment}
                onChange={(e) => {
                  formik.setFieldValue('comment', (e.target as HTMLInputElement).value);
                }}
                maxCharacters={TEXT_AREA_MAX_CHAR}
                value={formik.values.comment}
                className={`w-full ${formik.submitCount > 0 && formik.errors.comment ? 'border-red-600' : ''}`}
                data-testid="Group comment"
                aria-label={'Group comment'}
                aria-describedby={'Group-weeks-comment-description'}
              />
            </div>
            {EditAdjustmentButtons(isSaveApiLoading)}
          </>
          }


          {/********************* Single Week Form ****************************/}
          {formik.values.selectedRadio === 'singleweek' &&
            formik.values.selectedWeeks.map((week) => (
              <div key={week} className='col-span-2'>
                <div className='col-span-2'>

                  <Alert isOpen={true} sticky={false} variant='informational' className="edit-notifications" size="medium">
                    <div className="notification-banner-text">Your adjustment will be proportionally applied to each SMIC</div>
                  </Alert>


                  {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.metrics && renderErrorAlert((formik.errors.weekData as Record<number, any>)?.[week]?.metrics)}

                  {/* {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.metrics && <Alert isOpen={true} sticky={false} variant='error' className="edit-notifications" size="medium">
                        <div> {(formik.errors.weekData as Record<number, any>)?.[week]?.metrics}</div>
                      </Alert>} */}

                  <div className="grid gap-3 mt-2 mb-6 text-black text-md">
                    {formFields.map(({ label, name, inputType, subField, showWarning }, index) => {
                      return (
                        <React.Fragment key={index}>
                          <div
                            className={`grid grid-cols-3 items-center metrics ${subField ? 'ml-9 -mt-2' : ''
                              }`}
                          >
                            <div className="flex gap-2">
                              <div className={`${subField ? 'w-[100px]' : 'w-[140px]'} flex-none justify-between h-fit inline-flex flex-row items-center`}>
                                <div className={`flex items-center font-nunito text-xs font-semibold leading-4 tracking-normal h-[32px] cursor-pointer`}>
                                  {label}
                                </div>
                                {showWarning && getWarningComponent(name, warnings.weekData?.[week])}
                              </div>
                              {renderInputs({
                                state: formik.values.weekData?.[week]?.[name] || '',
                                setter: (e) => { singleWeekHandleChange(name, e, week) },
                                inputType,
                                name,
                                stateWarnings: warnings.weekData?.[week],
                                touched: Object.keys(formik.values.touchedWeeks || {}).includes(String(week))
                              })}
                            </div>
                          </div>
                          {name === 'realGrossProfitNbr' && <Divider orientation="horizontal" />}
                          {label === 'Sales to Public' && <Divider orientation="horizontal" />}
                          {label === 'Total Shrink' && <Divider orientation="horizontal" />}
                          {label === 'Non Selling' && <Divider orientation="horizontal" />}
                          {label === 'Supplies Packaging' && <Divider orientation="horizontal" />}
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>


                <div className='col-span-2 relative'>
                  <span className='text-sm font-bold whitespace-nowrap select-none truncate'>
                    Adjustment reason<span className="text-red-500">*</span>
                  </span>
                  <AutoComplete
                    items={forecastAdjustmentReasons}
                    itemKey="id"
                    size="md"
                    itemText="name"
                    placeholder="Make a selection"
                    width={410}
                    menuHeight={250}
                    error={(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.adjustmentReason ? true : false}
                    onChange={(val) => { formik.setFieldValue(`weekData.${week}.adjustmentReason`, val) }}
                    value={formik.values.weekData?.[week]?.adjustmentReason}
                    zIndex={100}
                  />
                  {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.adjustmentReason && <div ref={adjustmentErrorRef} className="edit-notifications-text adjustment-error"> {`Please enter an adjustment reason`}</div>}
                </div>

                <div className='col-span-2 pb-[70px]'>
                  <span className='text-sm font-bold whitespace-nowrap select-none truncate'>
                    Comment<span className="text-red-500">*</span>
                  </span>
                  <TextArea
                    name={`comment-${week}`}
                    isRequired
                    resize="resize-y"
                    error={(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.comment}
                    onChange={e => {
                      formik.setFieldValue(`weekData.${week}.comment`, (e.target as HTMLInputElement).value)
                    }}
                    maxCharacters={TEXT_AREA_MAX_CHAR}
                    value={formik.values.weekData?.[week]?.comment}
                    className={`w-full h-[298] ${(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) && (formik.errors.weekData as Record<number, any>)?.[week]?.comment ? 'border-red-600' : ''}`}
                    data-testid={`Comment-${week}`}
                    aria-label={`Comment-${week}`}
                    aria-describedby={`Comment-${week}-description`}
                  />
                </div>
                {EditAdjustmentButtons(isSaveApiLoading)}
              </div>
            ))
          }
        </div>
        <HistoryDrawer
          isOpen={isHistoryOpen}
          setOpen={setIsHistoryOpen}
          position={historyPosition}
        />
      </form>
    </Drawer>
  );
};

export default ForecastEdit;
