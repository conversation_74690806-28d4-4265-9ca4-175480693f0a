import React, { useState } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import Button from '@albertsons/uds/molecule/Button';

interface ConfirmationModalProps {
  title: string;
  message: string;
  confirmText: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ title, message, confirmText, isOpen, setIsOpen }) => {

  return (
    <>
      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)} iconSmall={false} width={800} height={316} data-testid="confirmation-modal">
        <div className='text-center select-none font-bold text-[28px] mt-16'>
          {title}
        </div>
        <div className='text-center select-none text-[20px] mt-4 '>
          {message}
        </div>
        <div className='flex items-center justify-center w-full my-8'>
          <Button width={200} size='lg' className='mr-2 whitespace-nowrap' variant='secondary' onClick={() => setIsOpen(false)} data-testid="confirmation-cancel-button">
           Cancel
          </Button>
          <Button width={200} size='lg' className='ml-2 whitespace-nowrap' data-testid="confirmation-confirm-button">
            {confirmText}
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default ConfirmationModal;