import React, { useState, useEffect } from 'react';
import Select, { Option } from '@albertsons/uds/molecule/Select';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { setSaveWeekSelection } from './releaseWeek.slice';
import { useDispatch } from 'react-redux';
import './release-week-select.css';
import { utcToZonedTime } from 'date-fns-tz';

export interface SelectWeekProps {
  weekChange?: (item: { name: string; num: number; value:string; weekNumber: number }) => void;
  selectedTab: string;
}


const PERFORMANCE_SUMMARY = 'Performance Summary';
export const SelectWeek: React.FC<SelectWeekProps> = ({ weekChange,selectedTab }) => {
  const [selection, setSelection] = useState<{ name: string; num: number; value:string; weekNumber:number } | null>(null);
  const [error, setError] = useState(false);
  const dispatch = useDispatch();
  const { data: calendarWeek } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};
  const [options, setOptions] = useState<any[]>([]);
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: displayDate } = useSelectorWrap('displayDate_rn');

  // Get current and selected quarter numbers
  const currentQuarterNbr = displayDate?.fiscalQuarterNumber;
  const selectedQuarterNbr = appliedFilters?.timeframe?.fiscalQuarterNumber || appliedFilters?.timeframe?.num;

  type CalendarWeek = {
    fiscalWeekEndDate: string;
    fiscalWeekStartDate: string;
    fiscalWeekNumber: number;
  };

  useEffect(() => {
    setData();
  }, [calendarWeek]);

    const filterWeeks = (weeks, now) => weeks && weeks.filter((week, idx) => {
    const [mm, dd, yyyy] = week.fiscalWeekEndDate.split('/');
    let endDate = utcToZonedTime(`${yyyy}-${mm}-${dd}`, 'America/Los_Angeles');
    // Add 3 days to endDate
    endDate.setDate(endDate.getDate() + 3);
    return endDate <= now;
  });

  function setOptionsData(options, now) {
    // Determine if selected quarter is current quarter
    if(selectedQuarterNbr) {
      const isCurrentQuarter =
      currentQuarterNbr === selectedQuarterNbr;
      let newOptions = [...options];
      let selectedOption = newOptions[0] || null;
      if (!isCurrentQuarter) {
        newOptions.splice(0, 1);
        selectedOption = newOptions[0];
      }
      setOptions(newOptions);
      setSelection(selectedOption || null); // 'Latest data' is always first
      dispatch(setSaveWeekSelection({
        from: 'quarterchange',
        ...selectedOption || {}
      }));
    }
  }

function setData() {
  if (calendarWeek && Array.isArray(calendarWeek)) {
    const weeks: CalendarWeek[] = [...calendarWeek].reverse();
    const now = utcToZonedTime(new Date(), 'America/Los_Angeles'); // Convert to Pacific Time
    now.setMilliseconds(0);
    const filteredWeeks = filterWeeks(weeks, now);
    const options = [
       {
        name: 'Latest data',
        num: 0,
        weekNumber: 0,
        value: '',
      },
      ...filteredWeeks.map((week, idx) => ({
        name: `Fiscal Wk ${week.fiscalWeekNumber
          .toString()
          .slice(-2)} (${formatMMDD(week.fiscalWeekStartDate)} - ${formatMMDD(
          week.fiscalWeekEndDate
        )}) version`,
        num: idx + 1,
        weekNumber: week.fiscalWeekNumber,
        value: week.fiscalWeekEndDate,
      }))
    ];
    setOptionsData(options, now);
  } else {
    setOptions([]);
  }
}

    function formatMMDD(dateStr: string) {
      if (!dateStr) return '';
      const [mm, dd] = dateStr.split('/');
      return `${mm}/${dd}`;
    }

  const onChange = (item: { name: string; num: number; value:string; weekNumber:number }) => {
    setSelection(item);
    if (weekChange) weekChange(item);
     dispatch(setSaveWeekSelection({
      ...item,
      from: 'dropdown'
     }));
  };

  return (
     <>

      {(selectedTab === PERFORMANCE_SUMMARY || selectedTab === "Performance Variance") ? (

    <Select
      placeholder='Latest data'
      itemText='name'
      onChange={onChange}
      menuHeight={options.length === 1 ? 90: 244}
      menuWidth={299}
      zIndex={0}
      size='sm'
      error={error}
      value={selection ?? undefined}
    >
      {options.map((item, idx) => (
        <Option key={idx} item={item} />
      ))}
    </Select>
      ):null}
    </>
  );
};