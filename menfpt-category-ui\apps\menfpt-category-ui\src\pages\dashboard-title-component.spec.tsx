import '@testing-library/jest-dom';
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import TitleComponent from './dashboard-title-component';

const mockStore = configureStore([]);

jest.mock('../components/HelpIcon', () => () => <span role="img">Mock HelpIcon</span>);

if (typeof window !== 'undefined') {
  if (!window.crypto) {
    window.crypto = {} as Crypto;
  }
  window.crypto.getRandomValues = function (array) {
    if (!array || typeof array.length !== 'number') {
      throw new Error('Expected an array with a length property');
    }
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  };
}

describe('TitleComponent', () => {
  let store;
  beforeEach(() => {
    store = mockStore({
      menfptCategory_rn: { data: { message: 'Test Title' } },
      featureFlags_rn: { data: { dashboardFilter: true } },
    });
  });

  beforeAll(() => {
    if (!window.crypto) {
      window.crypto = {} as Crypto;
    }
    window.crypto.getRandomValues = (arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    };
  });

  it('renders the title from menfptCategoryTitle', () => {
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders HelpIcon', () => {
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('renders Select components when FeatureFlags is true', () => {
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    expect(screen.getByText('Quarter')).toBeInTheDocument();
    expect(screen.getByText('Divisions')).toBeInTheDocument();
    expect(screen.getByText('Departments')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();
  });

  it('does not render Select components when FeatureFlags is false', () => {
    store = mockStore({
      menfptCategory_rn: { data: { message: 'Test Title' } },
      featureFlags_rn: { data: { dashboardFilter: false } },
    });
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    expect(screen.queryByText('Quarter')).not.toBeInTheDocument();
    expect(screen.queryByText('Divisions')).not.toBeInTheDocument();
    expect(screen.queryByText('Departments')).not.toBeInTheDocument();
    expect(screen.queryByText('Categories')).not.toBeInTheDocument();
  });

  it('handles Select onChange events', () => {
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    // Simulate select changes if needed, e.g. fireEvent.change(...)
    // This is a placeholder for edge/interaction cases
  });

  it('handles empty menfptCategoryTitle', () => {
    store = mockStore({
      menfptCategory_rn: { data: {} },
      featureFlags_rn: { data: { dashboardFilter: true } },
    });
    render(
      <Provider store={store}>
        <TitleComponent />
      </Provider>
    );
    // Check that the heading exists and is empty
    const heading = screen.getByRole('heading', { level: 2 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent('Allocatr Insights');
  });
}); 