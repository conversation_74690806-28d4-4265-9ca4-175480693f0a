{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "noImplicitAny": false, "paths": {"@ui/features": ["libs/features/src/index.ts"], "@ui/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}