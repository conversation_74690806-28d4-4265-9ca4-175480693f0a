import Link from '@albertsons/uds/molecule/Link';
import React from 'react';
import { useTimeframeDisplay } from './worksheetFilterRouteUtils';
import { WorksheetFilterDisplayProps } from './worksheetFilterTypes';
import "../WorksheetHeaderControls.css";
import { toTitleCase, truncate } from '@ui/utils';
import { deserializeSmData } from './utils/serializationUtils';

const WorksheetFilterDisplay: React.FC<WorksheetFilterDisplayProps> = ({
  appliedFilters,
  openFilterModal,
  division
}) => {
  const shouldDisplayTimeFrame = useTimeframeDisplay();

  const renderDivisionText = () => {
    if (appliedFilters.division.length === division.length) return 'All your divisions';
    return appliedFilters.division
      .map((div) => truncate(`${toTitleCase(div.name)}`))
      .join(', ');
  };

  const renderDepartmentText = () => {
    if (!appliedFilters.department) {
      return appliedFilters.desk?.name
        ? toTitleCase(appliedFilters.desk.name)
        : '';
    }
    if (Array.isArray(appliedFilters.department)) {
      return appliedFilters.department.length > 1
        ? `, ${appliedFilters.department.length} departments`
        : appliedFilters.department.length > 0
          ? `, ${truncate(toTitleCase(appliedFilters.department[0].name))}`
          : '';
    }
    return `, ${truncate(toTitleCase(appliedFilters.department.name))}`;
  };

  const renderRoleText = () => {
    // get first item of map appliedFilters.selectedSm
    const selectedSm = deserializeSmData(appliedFilters.selectedSm ?? {});
    
    if (!appliedFilters.selectedSm || appliedFilters.selectedSm.size === 0) {
      return '';
    }
    const singleSm = Array.from(selectedSm?.keys())?.[0] || '';
    const selectedAsm = Array.from((selectedSm?.get(singleSm) ?? new Set()).keys())[0] || '';

    if (selectedAsm) {
      return `/.../ ${truncate(toTitleCase(selectedAsm))}`;
    }
    if (singleSm) {
      return ` / ${truncate(toTitleCase(singleSm))}`;
    }
    return (!singleSm && !selectedAsm) ? ' / ASM (optional)' : '';
  };

  const renderFilterResults = () => {

    return (
      <div className="text-gray-600">
        <span className="ResultFilter pr-1">
          Showing results for:
        </span>
        <span className='ResultFilterResult'>
        {shouldDisplayTimeFrame && appliedFilters.timeframe && (
          <span className="mr-1">
            {appliedFilters.timeframe.name},
          </span>
        )}
        <span>
          {appliedFilters.desk?.name
            ? toTitleCase(appliedFilters.desk.name)
            : <>{renderDivisionText()}{renderDepartmentText()}{renderRoleText()}</>}
        </span>
        </span>
      </div>
    );
  };

  return (
    <div className="flex justify-between items-center space-x-4">
      <div className="flex items-center mt-2 space-x-4 width=[499px]">
        <div className="flex items-center space-x-2">
          {appliedFilters.division.length > 0 && renderFilterResults()}
          <Link onClick={openFilterModal} className="ChangeButton cursor-pointer">
            Change
          </Link>
        </div>
      </div>
    </div>
  );
};

export default WorksheetFilterDisplay;