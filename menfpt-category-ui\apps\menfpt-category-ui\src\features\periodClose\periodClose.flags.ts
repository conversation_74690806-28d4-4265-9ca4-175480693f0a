import { setLastQrtrData } from '../../components/quarterDetails.slice';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { getNowInPST } from '../../util/dateUtils';
import { generatePeriodStatuses } from './generatePeriodStatuses';
import { setPrevQuarterTabData } from './periodClose.slice';
import { setPeriodStatuses } from './periodStatuses.slice';

// Helper: Sort the lastQrtrData by fiscalWeekNumber, highest fiscalWeekNumber first
function sortByWeekNumberDesc(data: any[]): any[] {
  return [...data].sort((a, b) => Number(b.fiscalWeekNumber) - Number(a.fiscalWeekNumber));
}

// Helper: Extract relevant fields from the first element
function extractQuarterFields(quarter: any) {
  return {
    fiscalPeriodLockoutDate: quarter.fiscalPeriodLockoutDate,
    fiscalPeriodCertificationDate: quarter.fiscalPeriodCertificationDate,
    fiscalQuarterStartDate: quarter.fiscalQuarterStartDate,
    fiscalQuarterEndDate: quarter.fiscalQuarterEndDate,
    fiscalQuarterNumber: quarter.fiscalQuarterNumber,
    fiscalWeekNumber: quarter.fiscalWeekNumber,
  };
}



// Helper: Calculate quarter status
function calculateQuarterStatus(now: Date, lockoutDate: string, certDate: string): 'certified' | 'locked' | 'open' | undefined {
  // If currentDt <= fiscalPeriodLockoutDate, set the quarter status as open
  // If currentDt >= fiscalPeriodCertificationDate, set the quarter status as certified
  // If currentDt >= fiscalPeriodLockoutDate && currentDt < fiscalPeriodCertificationDate, set the quarter status as locked
  const cert = certDate ? new Date(certDate) : null;
  const lockout = lockoutDate ? new Date(lockoutDate) : null;
  if (lockout && now <= lockout) {
    return 'open';
  } else if (cert && now >= cert) {
    return 'certified';
  } else if (lockout && now >= lockout && (!cert || now < cert)) {
    return 'locked';
  }
  return undefined;
}

function getLastQuarterStatus({ lastQrtrData, dispatch }: { lastQrtrData: any[], dispatch: (action: any) => void }) {
  //sort the lastQrtrData by fiscalPeriodNumber, highest  fiscalPeriodNumber in the start
  if (!Array.isArray(lastQrtrData) || lastQrtrData.length === 0) return undefined;
  const sorted = sortByWeekNumberDesc(lastQrtrData);
  //get the first element and read  "fiscalPeriodLockoutDate", "fiscalPeriodCertificationDate", "fiscalQuarterStartDate", "fiscalQuarterEndDate"
  const first = sorted[0];
  const {
    fiscalPeriodLockoutDate,
    fiscalPeriodCertificationDate,
    fiscalQuarterStartDate,
    fiscalQuarterEndDate,
    fiscalQuarterNumber,
    fiscalWeekNumber
  } = extractQuarterFields(first);

  const now = getNowInPST();
  // If currentDt > =  fiscalPeriodCertificationDate, set the quarter status as certified
  // If currentDt > =  fiscalPeriodLockoutDate && currentDt  <  fiscalPeriodCertificationDate, set the quarter status as locked
  // All the date operations Should happen in PST time zone.
  const quarterStatus = calculateQuarterStatus(now, fiscalPeriodLockoutDate, fiscalPeriodCertificationDate);
  // Only allow 'certified', 'locked', or undefined for quarterStatus
 
  // Set isDisplayLastQtrTab to true only if quarterStatusRaw is 'open' or 'locked'
  const isDisplayLastQtrTab = quarterStatus === 'open' || quarterStatus === 'locked';
  // In the end, return this object
  const obj =  {
    isDisplayLastQtrTab,
    quarterLockoutDate: fiscalPeriodLockoutDate,
    quarterCertificationDate: fiscalPeriodCertificationDate,
    quarterStatus,
    fiscalQuarterStartDate,
    fiscalQuarterEndDate,
    lastQtrNbr: fiscalQuarterNumber,
    lastQtrWeekNbr: fiscalWeekNumber,
  };
  if (obj) {
    dispatch(setPrevQuarterTabData(obj));
  }
}



/**
 * Checks if the Adjust Forecast button should be disabled based on periodStatuses.
 * Disable Adjust forecast button when the last period in the selected quarter is closed.
 * @returns {boolean} - True if the button should be disabled.
 */
export function useShouldDisableEditForecastButton(): boolean {
 
  const { data: periodStatuses } = useSelectorWrap('periodStatuses_rn');  
 
  return !(
    Object.keys(periodStatuses?.notLocked || {}).length > 0
  );
}

/**
 * Handles the logic for processing previous quarter calendar API response and generating tag statuses.
 * @param {any} calendarApiResp - The response from the calendar API.
 * @param {any} calendarApiPayload - The payload used for the calendar API call.
 * @param {Function} dispatch - The Redux dispatch function.
 */
export function handlePrevQuarterPeriodClose(
  calendarApiResp: any,
  calendarApiPayload: any,
  dispatch: (action: any) => void
) {
  if (!calendarApiResp || calendarApiPayload?.type !== 'lastQrtr_periodClose') {
    return;
  }
  dispatch(setLastQrtrData(calendarApiResp));
  const tagStatuses = generatePeriodStatuses(calendarApiResp, dispatch);
  dispatch(setPeriodStatuses(tagStatuses));

  // If prev qtr tagstatuses is available, check the object and create flags for different interactions.
  getLastQuarterStatus({lastQrtrData: calendarApiResp, dispatch});
}

/**
 * Custom hook to get the weeks to be disabled for the quarter displayed in the table.
 * Uses RTK state via useSelectorWrap.
 * @returns {string[]} Array of week identifiers to be disabled.
 */
export function useWeeksToBeDisabledForQuarter(): string[] {
  // Disable closed weeks in Adjust Forecast Panel
  const { data: periodStatuses } = useSelectorWrap('periodStatuses_rn');

  let weeksToBeDisabled: string[] = [];

  if (periodStatuses) {
    const certifedWeeks = Object.values(
      periodStatuses.certified || {}
    ).flat() as string[];
    const lockedWeeks = Object.values(
      periodStatuses.locked || {}
    ).flat() as string[];
    const notCertifiedButLockedWeeks = Object.values(
      periodStatuses.notCertifiedButLocked || {}
    ).flat() as string[];
    weeksToBeDisabled = Array.from(
      new Set([...lockedWeeks, ...notCertifiedButLockedWeeks, ...certifedWeeks])
    );
  }

  return weeksToBeDisabled;
}
