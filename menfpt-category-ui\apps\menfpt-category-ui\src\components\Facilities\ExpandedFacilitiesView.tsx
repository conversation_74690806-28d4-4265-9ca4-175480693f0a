import React, { useState, useMemo } from 'react';
import { ArrowLeft } from 'lucide-react';
import FacilitiesTableCard from './FacilitiesTableCard';
import Search from '@albertsons/uds/molecule/Search';

type Props<T> = {
  title: string;
  columns: any[];
  items: T[];
  headerLabel: string;
  tooltip?: React.ReactNode;
  onBack: () => void;
  searchPlaceholder?: string;
};

function ExpandedFacilitiesView<T>({
  title,
  columns,
  items,
  headerLabel,
  tooltip,
  onBack,
  searchPlaceholder = 'Search the list...',
}: Props<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const filteredItems = useMemo(
    () =>
      searchTerm
        ? items.filter((row) =>
            Object.values(row as Record<string, unknown>).some((value) =>
              String(value).toLowerCase().includes(searchTerm.toLowerCase())
            )
          )
        : items,
    [searchTerm, items]
  );

  return (
    <div className="bg-[#F1F4F9] w-full flex flex-col min-h-auto h-auto overflow-visible">
      <div className="px-12 py-4 mt-0">
        <div className="flex flex-col items-start w-fit bg-[#F1F4F9]">
          <div
            className="flex items-center font-bold uppercase cursor-pointer font-nunito text-[12px] text-[#1B6EBB] tracking-[0.04em]"
            onClick={onBack}
          >
            <ArrowLeft size={16} className="mr-1" /> FACILITIES
          </div>
          <div className="flex items-center ml-2 mt-1">
            <span
              className="font-bold font-nunito text-[#22223B] text-[24px]"
            >
              {headerLabel}
            </span>
            {tooltip}
          </div>
        </div>
        <FacilitiesTableCard
          title={`${title} - ${filteredItems.length}`}
          updatedText=""
          columns={columns}
          items={filteredItems}
          itemKey={(item) => (item as any).id}
          footer={null}
          noPagination={false}
          pageSize={searchTerm ? filteredItems.length || 1 : 10}
          extraHeaderContent={
            <div className="-mt-1.5 w-[208px]">
              <Search
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={setSearchTerm}
              />
            </div>
          }
        />
      </div>
    </div>
  );
}

export default ExpandedFacilitiesView;
