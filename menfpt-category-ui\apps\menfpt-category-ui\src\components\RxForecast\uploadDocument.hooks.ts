import { useMemo } from 'react';
import { format } from 'date-fns-tz';
import { getNowInPST } from '../../util/dateUtils';

export const useUploadDayValidation = (envVariables: any) => {
  const isUploadAllowed = useMemo(() => {
    if (!envVariables?.GetEnvVariables?.variables?.PHARMA_UPLOAD_DAYS) {
      return true;
    }

    const pharmaUploadDays = envVariables.GetEnvVariables.variables.PHARMA_UPLOAD_DAYS;
    const allowedDays = pharmaUploadDays
      .split(',')
      .map(day => day.trim().toUpperCase());

    const currentDate = getNowInPST();
    const currentDay = format(currentDate, 'EEEE', { timeZone: 'America/Los_Angeles' }).toUpperCase();
    return allowedDays.includes(currentDay);
    //return true;
  }, [envVariables]);

  const allowedDaysMessage = useMemo(() => {
    if (!envVariables?.GetEnvVariables?.variables?.PHARMA_UPLOAD_DAYS) {
      return '';
    }
    
    return envVariables.GetEnvVariables.variables.PHARMA_UPLOAD_DAYS
      .split(',')
      .map(day => day.trim())
      .join(' and ');
  }, [envVariables]);

  return { isUploadAllowed, allowedDaysMessage };
}; 