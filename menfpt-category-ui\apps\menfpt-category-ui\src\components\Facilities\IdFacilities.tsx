import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import './Facilities.css'; 
import InfoTooltip from '../InfoTooltip';
import { Info, Pen, ArrowLeft } from 'lucide-react'; 
import NewFacilitiesCard from './NewFacilities'; 
import mockData from './MockData.json';
import FacilitiesTableCard from './FacilitiesTableCard';
import Facilities from './ClosingFacilities';
import DynamicCardBottom from './DynamicCardBottom';
import { useNavigate, useLocation } from 'react-router-dom';
import Search from '@albertsons/uds/molecule/Search';
import React, { useState, useMemo } from 'react';
import helpIcon from '../../assets/help-icon.svg';

type Facility = {
  id: number;
  facility: string;
  assetType: string;
  siteName: string;
  posStartDate: string;
  QuarterReportingBegins?: string;
};

const items: Facility[] = mockData.IdFacilities;

const IdFacilities = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isExpanded = location.pathname === '/idfacilities';
  const [searchTerm, setSearchTerm] = useState('');

  // Define columns inside the component so isExpanded is always current
  const columns: Column<Facility>[] = [
    {
      id: 'facility',
      label: (
        <div className="facility-header"> 
          <span>Facility</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Number and Name of facility'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'facility',
      hideFromMenu: true,
    },
    {
      id: 'assetType',
      label: (
        <div className="facility-header">
          <span>Asset Type</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Type of facility'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'assetType',
      hideFromMenu: true,
    },
    {
      id: 'siteName',
      label: (
        <div className="facility-header">
          <span>Site Name and A...</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Facility name and address'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'siteName',
      hideFromMenu: true,
    },
    {
      id: 'posStartDate',
      label: (
        <div className="facility-header">
          <span>POS Start date</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Date Point Of Sale is operational'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'posStartDate',
      hideFromMenu: true,
    },
  ];

  const filteredItems = useMemo(() => {
    return searchTerm
      ? items.filter((row) =>
          Object.values(row).some((value) =>
            String(value).toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : items;
  }, [searchTerm, items]);

  if (isExpanded) {
    return (
      <div className="bg-[#F1F4F9] w-full flex flex-col min-h-auto h-auto overflow-visible">
        <div className="px-12 py-4 mt-0">
          <div className="flex flex-col items-start w-fit bg-[#F1F4F9]">
            <div
              className="flex items-center font-bold uppercase cursor-pointer font-nunito text-xs text-[#1B6EBB] tracking-[0.04em]"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft size={16} className="mr-1" /> FACILITIES
            </div>
            <div className="flex items-center ml-2 mt-1">
              <span
                className="font-bold font-nunito text-[#22223B] text-2xl"
              >
                ID Facility
              </span>
              {isExpanded && (
                <span className="uds-tooltip-right ml-3">
                  <InfoTooltip
                    label={'Facilities opened in the prior Quarter that we have had less than 12 months'}
                    icon={
                      <img
                        src={helpIcon}
                        alt="Help"
                        className="inline-flex items-center justify-center w-8 h-8 rounded-full text-[#1B6EBB] font-bold text-base font-inherit leading-none"
                      />
                    }
                    anchor="right"
                    variant="dark"
                    className="uds-tooltip-right"
                  />
                </span>
              )}
            </div>
          </div>
          <FacilitiesTableCard
            title={`ID Facility - ${filteredItems.length}`}
            updatedText=""
            columns={columns}
            items={filteredItems}
            itemKey="id"
            footer={null}
            noPagination={false}
            pageSize={searchTerm ? filteredItems.length || 1 : 10}
            extraHeaderContent={
              <div className="-mt-1.5 w-[208px]">
                <Search
                  placeholder="Search the list..."
                  value={searchTerm}
                  onChange={setSearchTerm}
                />
              </div>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <FacilitiesTableCard
      title={`ID Facility - ${items.length}`}
      updatedText="Updated 2 days ago"
      columns={columns}
      items={items.slice(0, 3)}
      itemKey="id"
      footer={
        items.length > 3 ? (
          <DynamicCardBottom
            facilityType="idfacility"
            count={items.length}
            showViewMore={true}
            viewMoreCount={items.length - 3}
            onViewMore={() => navigate('/idfacilities')}
          />
        ) : null
      }
    />
  );
};

export default IdFacilities;
