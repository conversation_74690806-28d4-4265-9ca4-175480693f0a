import React from 'react';

interface ClearSelectionProps {
  onClick: () => void;
  show: boolean;
}

const ClearSelection: React.FC<ClearSelectionProps> = ({ onClick, show }) => {
  if (!show) return null;
  return (
    <button
      type="button"
      className="ml-2 text-blue-600 hover:underline text-xs font-medium focus:outline-none"
      onClick={onClick}
      data-testid="clear-selection-link"
    >
      Clear
    </button>
  );
};

export default ClearSelection;
