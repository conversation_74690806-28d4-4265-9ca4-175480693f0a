import { DropdownType } from '../../interfaces/worksheetFilter';

/**
 * Forms a desk name string based on filter selections
 * If any variable is null, then the variable and its preceding variables are not added
 */
export const formDeskNameFromFilterSelection = ({
  selectedDivision,
  selectedDepartment,
  selectedSm,
  selectedAsm,
}: {
  selectedDivision: DropdownType | null;
  selectedDepartment: DropdownType | null;
  selectedSm: string | null;
  selectedAsm: string | null;
}): string => {
  const parts: (string | number)[] = [];

  if (selectedDivision?.num) {
    parts.push(selectedDivision.num);

    if (selectedDepartment?.num) {
      parts.push(selectedDepartment.num);

      if (selectedSm) {
        parts.push(selectedSm);

        if (selectedAsm) {
          parts.push(selectedAsm);
        }
      }
    }
  }

  return parts.join('-');
};