import React, { useState, useMemo } from 'react';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import './Facilities.css'; 
import InfoTooltip from '../InfoTooltip';
import { Info } from 'lucide-react'; 
import mockData from './MockData.json';
import FacilitiesTableCard from './FacilitiesTableCard';
import DynamicCardBottom from './DynamicCardBottom';
import { useNavigate, useLocation } from 'react-router-dom';
import helpIcon from '../../assets/help-icon.svg';
import ExpandedFacilitiesView from './ExpandedFacilitiesView';

type Facility = {
  id: number;
  facility: string;
  assetType: string;
  siteName: string;
  posStartDate: string;
  QuarterReportingBegins: string;
};

const items: Facility[] = mockData.KeeperFacilities;

const KeeperFacilities = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isExpanded = location.pathname === '/keeperfacilities';

  const [searchTerm, setSearchTerm] = useState('');

  const columns: Column<Facility>[] = [
    {
      id: 'facility',
      label: (
        <div className="facility-header"> 
          <span>Facility</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin tool-tip-initilizer-top">
              <InfoTooltip
                label={'Number and Name of facility'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'facility',
      hideFromMenu: true,
    },
    {
      id: 'assetType',
      label: (
        <div className="facility-header">
          <span>Asset Type</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Type of facility'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'assetType',
      hideFromMenu: true,
    },
    {
      id: 'siteName',
      label: (
        <div className="facility-header">
          <span>Site Name and A...</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Facility name and address'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'siteName',
      hideFromMenu: true,
    },
    {
      id: 'posStartDate',
      label: (
        <div className="facility-header">
          <span>POS Start date</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Date Point Of Sale is operational'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'posStartDate',
      hideFromMenu: true,
    },
    {
      id: 'QuarterReportingBegins',
      label: (
        <div className="facility-header">
          <span>Quarter Reporting Begins</span>
          {!isExpanded && (
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'The Date at which Qauaterly report begins'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          )}
        </div>
      ),
      value: 'QuarterReportingBegins',
      hideFromMenu: true,
    },
  ];

  const filteredItems = useMemo(() => {
    return searchTerm
      ? items.filter((row) =>
          Object.values(row).some((value) =>
            String(value).toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : items;
  }, [searchTerm, items]);

  if (isExpanded) {
    return (
      <ExpandedFacilitiesView
        title="Keeper Facility"
        columns={columns}
        items={items}
        headerLabel="Keeper Facility"
        tooltip={
          <span className="uds-tooltip-right ml-3">
            <InfoTooltip
              label={'Facilities opened in the prior Quarter that we have had less than 12 months'}
              icon={
                <img
                  src={helpIcon}
                  alt="Help"
                  className="inline-flex items-center justify-center w-8 h-8 rounded-full text-[#1B6EBB] font-bold text-[14px] leading-none"
                  style={{ fontFamily: 'inherit' }}
                />
              }
              anchor="right"
              variant="dark"
              className="uds-tooltip-right"
            />
          </span>
        }
        onBack={() => navigate(-1)}
      />
    );
  }

  return (
    <FacilitiesTableCard
      title={`Keeper Facility - ${items.length}`}
      updatedText="Updated 2 days ago"
      columns={columns}
      items={items.slice(0, 3)}
      itemKey="id"
      footer={
        items.length > 3 ? (
          <DynamicCardBottom
            facilityType="keeperfacility"
            count={items.length}
            showViewMore={true}
            viewMoreCount={items.length - 3}
            onViewMore={() => navigate('/keeperfacilities')}
          />
        ) : null
      }
    />
  );
};

export default KeeperFacilities;
