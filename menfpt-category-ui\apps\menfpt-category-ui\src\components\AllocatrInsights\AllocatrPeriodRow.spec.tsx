import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AllocatrPeriodRow from './AllocatrPeriodRow';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

const mockStore = configureStore([]);
const store = mockStore({
  periodStatuses_rn: { data: {} }, // Add expected key for useSelectorWrap
});

// Mock props for AllocatrPeriodRow
const defaultProps = {
  period: {
    id: 'total-1',
    name: 'Total Period',
    periodNumber: 1,
    line1Projection: 0,
    lastYear: 0,
    actualOrForecast: 0,
    idPercentage: 0,
    vsLY: { value: 0, percentage: 0 },
    vsProjection: { value: 0, percentage: 0 },
    bookGrossProfit: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    markdown: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    shrink: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    line5: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
    line6: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
    line7: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
    line8: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
    hasMerchantForecast: false,
    // Add any other required fields if needed
  },
  alwaysSticky: false,
  stickyTop: 0,
  isLastTotal: false,
  isPeriodActualUsed: false,
};

jest.mock('./ActualUsedIndicator', () => ({ __esModule: true, default: () => <span data-testid="mock-actual-used-indicator" /> }));
jest.mock('../renderRowCloseStatus', () => () => <span data-testid="mock-render-row-close-status" />);

describe('AllocatrPeriodRow', () => {
  it('renders without crashing', () => {
    render(
      <Provider store={store}>
        <AllocatrPeriodRow {...defaultProps} />
      </Provider>
    );
  });

});
