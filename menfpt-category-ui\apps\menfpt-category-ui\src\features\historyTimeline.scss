
.history-header {
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.details-column {
  flex: 1;
  width: 252px;
  max-width: 252px;
  padding-right: 0.5rem;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
}

.value-header-column {
  width: 140px;
  min-width: 140px;
  text-align: left;
  padding-left: 8px;
}

.value-column {
  width: 150px;
  min-width: 150px;
  max-width: 160px;
  text-align: left;
  padding-left: 0px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.history-row {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.history-main-container {
  padding: 0;
  max-width: 100%;
  overflow-x: hidden;
}

[class*="Timeline_timelineItem"] {
  padding-bottom: 16px;
}
.history-ml-0 {
  margin-left: 0;
  padding-right: 10px;
}


