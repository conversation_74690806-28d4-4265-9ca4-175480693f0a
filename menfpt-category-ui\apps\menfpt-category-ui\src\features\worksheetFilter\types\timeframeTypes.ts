import { DropdownType } from '../../../interfaces/worksheetFilter';

export interface TimeframeDropdownType extends DropdownType {
  startDate?: string;
  endDate?: string;
  fiscalYear?: number;
  fiscalQuarterNumber?: number;
}

export interface TimeframeSelectorProps {
  selectedTimeframe?: TimeframeDropdownType;
  onTimeframeChange: (timeframe: TimeframeDropdownType) => void;
  selectedPeriods?: DropdownType[];
  selectedWeeks?: { periodNum: number; weekNum: number }[];
  handlePeriodChange?: (periods: DropdownType[]) => void;
  handleWeeksChange?: (weeks: { periodNum: number; weekNum: number }[]) => void;
} 