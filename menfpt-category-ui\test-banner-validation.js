// Test script to verify banner data validation logic

// Mock functions
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

const formatCurrency = (value) => value === null || value === undefined || value === '' ? '' : `$${value}`;
const getDeptName = (smicData, deptId, fallback) => fallback || `Dept ${deptId}`;
const getDivisionName = (smicData, divisionId, fallback) => {
  const found = smicData.find((item) => String(item.divisionId) === String(divisionId));
  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);
};
const getBannerName = (smicData, divisionId, bannerId, fallback) => {
  const found = smicData.find((item) => 
    String(item.divisionId) === String(divisionId) && 
    String(item.bannerId) === String(bannerId)
  );
  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
};

// Mock mapRow function
const mapRow = (baseRow, data, formatCurrency, type) => ({
  ...baseRow,
  '$ Projection': formatCurrency(data.line1Projection),
  type: type
});

// Simplified addDepartmentRows function
const addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { 
    departmentName: isTotal ? (dept.name || 'Total') : `${dept.id} - ${deptName}` 
  };
  
  // Add quarter row
  if (dept.quarter) {
    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));
  }
};

// Updated addRows function with banner validation
const addRows = (rows, data, smicData, useWeekId = false) => {
  // Handle the new structure with divisions and banners
  if (data.divisions && Array.isArray(data.divisions)) {
    // First, add the Total row data if it exists at the root level
    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {
      const divisionsCount = data.divisions ? data.divisions.length : 0;
      const totalData = {
        id: 'Total',
        name: `Total of ${divisionsCount} Divisions`,
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
      addDepartmentRows(rows, totalData, smicData, useWeekId);
    }
    
    // Then, process divisions and banners
    data.divisions.forEach((division) => {
      const divisionName = getDivisionName(smicData, division.id, division.name);
      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };
      
      // Add division quarter row
      if (division.quarter) {
        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));
      }

      // Process banners within division with validation
      if (division.banners && Array.isArray(division.banners) && division.banners.length > 0) {
        division.banners.forEach((banner) => {
          // Check if banner has valid data before displaying
          const hasValidBannerData = banner.quarter || 
                                   (banner.departments && banner.departments.length > 0) ||
                                   banner.periods || 
                                   banner.weeks;
          
          if (hasValidBannerData) {
            const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);
            const bannerBaseRow = { departmentName: `  ${bannerName}` };

            // Add banner quarter row
            if (banner.quarter) {
              rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));
            }

            // Process departments within banner
            if (banner.departments && Array.isArray(banner.departments)) {
              banner.departments.forEach((dept) => {
                addDepartmentRows(rows, dept, smicData, useWeekId);
              });
            }
          }
        });
      }
    });
  }
};

console.log('Testing Banner Data Validation Logic:');
console.log('====================================');

// Test 1: Banner with valid quarter data
console.log('\nTest 1 - Banner with Valid Quarter Data:');
const testData1 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'ACME STORES',
      quarter: { quarterNumber: 202502, line1Projection: 50000 } // Valid data
    }]
  }]
};

const rows1 = [];
addRows(rows1, testData1, [], false);
console.log('Rows generated:', rows1.length);
console.log('Banner row included:', rows1.some(row => row.departmentName.includes('Acme Stores')));
console.log('Expected: Banner should be included ✅');

// Test 2: Banner with no valid data
console.log('\nTest 2 - Banner with No Valid Data:');
const testData2 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'EMPTY BANNER'
      // No quarter, departments, periods, or weeks data
    }]
  }]
};

const rows2 = [];
addRows(rows2, testData2, [], false);
console.log('Rows generated:', rows2.length);
console.log('Banner row included:', rows2.some(row => row.departmentName.includes('Empty Banner')));
console.log('Expected: Banner should NOT be included ✅');

// Test 3: Banner with departments but no quarter
console.log('\nTest 3 - Banner with Departments but No Quarter:');
const testData3 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'BANNER WITH DEPTS',
      departments: [{ // Valid departments data
        id: '301',
        name: 'Grocery',
        quarter: { quarterNumber: 202502, line1Projection: 25000 }
      }]
    }]
  }]
};

const rows3 = [];
addRows(rows3, testData3, [], false);
console.log('Rows generated:', rows3.length);
console.log('Banner row included:', rows3.some(row => row.departmentName.includes('Banner With Depts')));
console.log('Expected: Banner should be included (has departments) ✅');

// Test 4: Empty banners array
console.log('\nTest 4 - Empty Banners Array:');
const testData4 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [] // Empty array
  }]
};

const rows4 = [];
addRows(rows4, testData4, [], false);
console.log('Rows generated:', rows4.length);
console.log('Any banner rows:', rows4.some(row => row.departmentName.startsWith('  ')));
console.log('Expected: No banner rows should be included ✅');

// Test 5: No banners property
console.log('\nTest 5 - No Banners Property:');
const testData5 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 }
    // No banners property at all
  }]
};

const rows5 = [];
addRows(rows5, testData5, [], false);
console.log('Rows generated:', rows5.length);
console.log('Any banner rows:', rows5.some(row => row.departmentName.startsWith('  ')));
console.log('Expected: No banner rows should be included ✅');

console.log('\n✅ All tests completed!');
console.log('\n📋 Banner Validation Rules:');
console.log('✅ Banner is displayed if it has:');
console.log('   - quarter data, OR');
console.log('   - departments array with items, OR');
console.log('   - periods data, OR');
console.log('   - weeks data');
console.log('❌ Banner is NOT displayed if:');
console.log('   - No valid data properties');
console.log('   - Empty banners array');
console.log('   - No banners property');
console.log('   - All data properties are null/undefined/empty');
