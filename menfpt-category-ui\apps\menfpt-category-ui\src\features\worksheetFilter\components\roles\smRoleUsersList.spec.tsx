import '@testing-library/jest-dom/extend-expect';
import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import SmRoleUsersList from './smRoleUsersList';

describe('SmRoleUsersList', () => {
  const mockStore = configureStore([]);
  const initialState = {
    asmDataForSelectedSm_rn: { data: { selectedSm: 'sm1' } },
    deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: null, cascadeSearchSelectedItemType: null } },
  };
  let store;
  const smData = [
    { sm: 'sm1', asmArr: ['asm1', 'asm2'] },
    { sm: 'sm2', asmArr: ['asm3'] },
  ];

  beforeEach(() => {
    store = mockStore(initialState);
  });

  it('renders SM list and header', () => {
    const { getByText, getByTestId } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={smData} />
      </Provider>
    );
    expect(getByText('SM (optional)')).toBeInTheDocument();
    expect(getByTestId('sm-label-Sm1')).toBeInTheDocument();
    expect(getByTestId('sm-label-Sm2')).toBeInTheDocument();
  });

  it('shows ClearSelection when more than one SM', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={smData} />
      </Provider>
    );
    expect(getByTestId('clear-selection-link')).toBeInTheDocument();
  });

  it('calls handleSmChange and updates selection when SM radio is clicked', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={smData} />
      </Provider>
    );
    const smRadio = getByTestId('sm-label-Sm1');
    fireEvent.click(smRadio);
    // The radio should now be checked
    expect(smRadio).toBeChecked();
  });

  it('handles empty SM list', () => {
    const { queryByText } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={[]} />
      </Provider>
    );
    expect(queryByText('Sm1')).not.toBeInTheDocument();
  });

  it('shows empty state when no smData and no search', () => {
    const { getByText } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={[]} />
      </Provider>
    );
    expect(getByText('Select a department to view')).toBeInTheDocument();
  });

  it('shows empty state with search active and no matching SM', () => {
    // Set cascadeSearchSelectedItemType to 'sm' and cascadeSearchSelectedItemId to a value not in smData
    const cascadeStore = mockStore({
      asmDataForSelectedSm_rn: { data: { selectedSm: 'sm1' } },
      deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: 'notfound', cascadeSearchSelectedItemType: 'sm' } },
    });
    const { getByText } = render(
      <Provider store={cascadeStore}>
        <SmRoleUsersList smData={smData} activeSearchQuery="notfound" />
      </Provider>
    );
    expect(getByText('No matching SM found')).toBeInTheDocument();
  });

  it('filters SM list when search is active and cascadeSearchSelectedItemType is sm', () => {
    // Override initialState for cascade search
    const cascadeStore = mockStore({
      asmDataForSelectedSm_rn: { data: { selectedSm: 'sm1' } },
      deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: 'sm2', cascadeSearchSelectedItemType: 'sm' } },
    });
    const { queryByTestId } = render(
      <Provider store={cascadeStore}>
        <SmRoleUsersList smData={smData} activeSearchQuery="sm2" />
      </Provider>
    );
    expect(queryByTestId('sm-label-Sm2')).toBeInTheDocument();
    expect(queryByTestId('sm-label-Sm1')).not.toBeInTheDocument();
  });

  it('ClearSelection link calls dispatch when clicked', () => {
    const { getByTestId, rerender } = render(
      <Provider store={store}>
        <SmRoleUsersList smData={smData} />
      </Provider>
    );
    // Select SM1
    fireEvent.click(getByTestId('sm-label-Sm1'));
    expect(getByTestId('sm-label-Sm1')).toBeChecked();

    // Click ClearSelection
    fireEvent.click(getByTestId('clear-selection-link'));

    // Rerender to simulate Redux state update (since mockStore doesn't update automatically)
    rerender(
      <Provider store={mockStore({
        asmDataForSelectedSm_rn: { data: { selectedSm: '' } },
        deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: null, cascadeSearchSelectedItemType: null } },
      })}>
        <SmRoleUsersList smData={smData} />
      </Provider>
    );

    expect(getByTestId('sm-label-Sm1')).not.toBeChecked();
    expect(getByTestId('sm-label-Sm2')).not.toBeChecked();
  });
});
