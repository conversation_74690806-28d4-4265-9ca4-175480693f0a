.epbcs-sync-monitor {
  padding: 0;
  :global {
    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      thead tr {background-color: #f3f4f6;}
      th, td {padding: 0.75rem;
        font-size: 0.875rem;
      }
    }
  }
  .table-header-divider {
    width: px;
    height: 100%;
    background-color: #9ca3af; 
    position: absolute;
    right: 0;
    top: 0;
  }
}
.status-pill {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1px;
  font-size: 0.875rem;
  text-align: center;
  &.complete {
    background-color: #d1fae5;
    color: #065f46;
  }
  &.processing {
    background-color: #e0f2fe;
    color: #0369a1;
  }
}
.square-tag {
  border-radius: 3px !important;
}
.table-header {
  background-color: #e1e7eb;
  color: #333; 
  font-weight: bold;
}
.cell-base {
  position: relative;
  background-color: white;
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 0.5rem; 
  overflow: hidden;
}
.divider-base {
  position: absolute;
  background-color: #d1d5db; 
  width: 1px;
  height: 100%;
  bottom: 0;
}
.header-text {
  justify-content: center;
  color: #1f2937; 
  font-size: 0.875rem;
  font-weight: bold;
  font-family: 'Nunito Sans', sans-serif;
  line-height: 1;
}
.cell-text {
  justify-content: center;
  color: #1f2937; 
  font-size: 0.875rem; 
  font-weight: normal;
  font-family: 'Nunito Sans', sans-serif;
  line-height: 1;
}
.container-style {
  width: 560px;
  align-self: stretch;
  border-radius: 0.5rem; 
  outline: 1px solid #cbd5e1; 
  outline-offset: -1px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: visible;
}
.sync-container {
  width: 100%;
  border-radius: 0.5rem; 
  outline: 1px solid #cbd5e1;
  outline-offset: -1px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: visible;
}
.flex-start-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: hidden;
}
.card-style {
  flex: 1;
  padding: 0.75rem; 
  background-color: white; 
  border-radius: 0.5rem; 
  outline: 1px solid #cbd5e1; 
  outline-offset: -1px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem; 
}
.flex-column-container {
  align-self: stretch; 
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 1.25rem; 
  flex-grow: 1;
  min-height: 0;
  overflow-y: auto;     
  overflow-x: hidden;    
  width: 100%;
}
.flex-inline-container {
  align-self: stretch; 
  display: inline-flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0.5rem; 
  width: 100%;
}
.flex-column-stretch {
  flex: 1;
  display: inline-flex;
  flex-direction: column;
  justify-content: stretch;
  align-items: flex-start;
}
.flex-column-center {
  flex: 1;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}