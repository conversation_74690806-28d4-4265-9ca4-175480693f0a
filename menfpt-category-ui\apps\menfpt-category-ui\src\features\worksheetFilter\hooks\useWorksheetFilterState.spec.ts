import { renderHook, act } from '@testing-library/react-hooks';
import { useWorksheetFilterState } from './useWorksheetFilterState';
import * as routeUtils from '../worksheetFilterRouteUtils';
import * as divisionUtils from '../divisionUtils';
import * as departmentUtils from '../departmentUtils';
import * as deskUtils from '../deskUtils';
import * as categoryOperations from '../categoryOperations';
import * as rolesUtils from '../components/roles/rolesUtils';
import { setDepartments } from '../components/department/departments.slice';
import {
  setAsmDataForSelectedSm,
  setSelectedAsm,
} from '../components/roles/rolesFilter.slice';
import { DropdownType } from '../../../interfaces/worksheetFilter';
import { AppliedFilterState } from '../worksheetFilterTypes';

// Mock Redux
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
  useSelector: jest.fn(), // For general useSelector, e.g., deptRoleSuggestionsState_rn
}));

jest.mock('../../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

// Mock utility modules
jest.mock('../worksheetFilterRouteUtils');
jest.mock('../divisionUtils');
jest.mock('../departmentUtils');
jest.mock('../deskUtils');
jest.mock('../categoryOperations');
jest.mock('../components/roles/rolesUtils');

// Typed mocks for utility functions
const {
  useTimeframeDisplay,
  useDisplayDeptRoleCascade,
  useMultipleDepartmentsSelection,
} = routeUtils as jest.Mocked<typeof routeUtils>;
const { extractDivisionsFromFiltersList } = divisionUtils as jest.Mocked<
  typeof divisionUtils
>;
const {
  getDepartmentsForDivisions,
  updateDepartmentsAndDesks: mockUpdateDepartmentsAndDesks,
} = departmentUtils as jest.Mocked<typeof departmentUtils>;
const { getDesksForDivision } = deskUtils as jest.Mocked<typeof deskUtils>;
const { getCategoriesForDepartmentReltdFilters, getCategoriesForDesk } =
  categoryOperations as jest.Mocked<typeof categoryOperations>;
const { getSmDataByDivisionAndDept, getAsmListForSelectedSM } =
  rolesUtils as jest.Mocked<typeof rolesUtils>;
const { useSelectorWrap } = jest.requireMock('../../../rtk/rtk-utilities');
const { useSelector } = jest.requireMock('react-redux');

const baseMockFiltersList = [
  {
    divisionId: 1,
    divisionName: 'Division A',
    deptId: 10,
    deptName: 'Dept 1',
    deskId: 101,
    deskName: 'Desk 1A',
  },
  {
    divisionId: 1,
    divisionName: 'Division A',
    deptId: 20,
    deptName: 'Dept 2',
    deskId: 102,
    deskName: 'Desk 2A',
  },
  {
    divisionId: 2,
    divisionName: 'Division B',
    deptId: 30,
    deptName: 'Dept 3',
    deskId: 203,
    deskName: 'Desk 3B',
  },
];

let mockReduxStore: any;

describe('useWorksheetFilterState', () => {
  let defaultAppliedFilters: AppliedFilterState;

  beforeEach(() => {
    mockDispatch.mockClear();
    mockReduxStore = {
      departments_rn: { data: [] },
      asmDataForSelectedSm_rn: {
        data: { selectedSm: null, asmForSelectedSm: [] },
      },
      selectedAsm_rn: { data: [] },
      activeTabInFilter_rn: { data: 'department' }, // Default tab
      deptRoleSuggestions_rn: { data: undefined },
    };

    useSelectorWrap.mockImplementation((selectorName: string) => {
      return mockReduxStore[selectorName] || { data: undefined };
    });
    useSelector.mockImplementation(() => mockReduxStore.deptRoleSuggestions_rn); // For deptRoleSuggestionsState_rn

    mockDispatch.mockImplementation((action: any) => {
      if (action.type === setDepartments.type) {
        mockReduxStore.departments_rn = { data: action.payload };
      }
      if (action.type === setAsmDataForSelectedSm.type) {
        mockReduxStore.asmDataForSelectedSm_rn = { data: action.payload };
      }
      if (action.type === setSelectedAsm.type) {
        mockReduxStore.selectedAsm_rn = { data: action.payload };
      }
    });

    // Default mock implementations for imported functions/hooks
    useTimeframeDisplay.mockReturnValue(false);
    useDisplayDeptRoleCascade.mockReturnValue(false);
    useMultipleDepartmentsSelection.mockReturnValue(false); // Default to single department selection

    extractDivisionsFromFiltersList.mockImplementation((filtersList: any[]) => {
      if (!filtersList) return [];
      const divisionsMap = new Map();
      filtersList.forEach((item) => {
        if (item.divisionId && item.divisionName) {
          divisionsMap.set(item.divisionId, {
            name: item.divisionName,
            num: Number(item.divisionId),
          });
        }
      });
      return Array.from(divisionsMap.values());
    });

    getDepartmentsForDivisions.mockImplementation(
      (filtersList: any[], selectedDivs: DropdownType[]) => {
        if (!filtersList || !selectedDivs || selectedDivs.length === 0)
          return [];
        const divisionNums = selectedDivs.map((d) => d.num);
        return filtersList
          .filter((f) => divisionNums.includes(Number(f.divisionId)))
          .map((f) => ({ name: f.deptName, num: Number(f.deptId) }))
          .filter(
            (dept, index, self) =>
              index === self.findIndex((d) => d.num === dept.num)
          );
      }
    );

    mockUpdateDepartmentsAndDesks.mockImplementation(
      (
        selectedDivision,
        FiltersList,
        setDepartmentsCb, // This is (departments) => dispatch(setDepartments(departments))
        setDesksCb,
        setSelectedDepartmentCb, // Hook's own state setter
        setSelectedDeskCb, // Hook's own state setter
        getDesksForDivisionFn // Actual getDesksForDivision function
      ) => {
        if (
          selectedDivision &&
          selectedDivision.length > 0 &&
          FiltersList &&
          FiltersList.length > 0
        ) {
          const depts = getDepartmentsForDivisions(
            FiltersList,
            selectedDivision
          );
          setDepartmentsCb(depts); // Dispatches setDepartments, updates mockReduxStore

          const desks = getDesksForDivisionFn(FiltersList, selectedDivision[0]); // Use the passed getDesksForDivision
          setDesksCb(desks); // Updates local state 'desks' in the hook
        } else {
          setDepartmentsCb([]);
          setDesksCb([]);
        }
        // setSelectedDepartmentCb and setSelectedDeskCb are called by handleDivisionChange before this.
        // This mock focuses on populating departments and desks lists.
      }
    );

    getDesksForDivision.mockImplementation(
      (filtersList: any[], division: DropdownType) => {
        if (!filtersList || !division) return [];
        return filtersList
          .filter(
            (f) =>
              Number(f.divisionId) === division.num && f.deskId && f.deskName
          )
          .map((f) => ({ name: f.deskName, num: Number(f.deskId) }))
          .filter(
            (desk, index, self) =>
              index === self.findIndex((d) => d.num === desk.num)
          );
      }
    );

    getCategoriesForDepartmentReltdFilters.mockReturnValue([]);
    getCategoriesForDesk.mockReturnValue([]);
    getSmDataByDivisionAndDept.mockReturnValue([]);
    getAsmListForSelectedSM.mockReturnValue([]);

    defaultAppliedFilters = {
      division: [],
      department: undefined,
      desk: undefined,
      timeframe: undefined,
      selectedSm: undefined,
      selectedAsm: undefined,
    };
  });

  describe('GIVEN the filter modal is open', () => {
    const isOpen = true;
    const mockOnClose = jest.fn();
    const mockOnApply = jest.fn();

    describe('AND multiple department selection is enabled', () => {
      beforeEach(() => {
        useMultipleDepartmentsSelection.mockReturnValue(true);
      });

      describe('WHEN no specific department is initially applied for a selected division', () => {
        it('THEN it should preselect all available departments for that division', () => {
          const appliedFilters: AppliedFilterState = {
            ...defaultAppliedFilters,
            division: [{ name: 'Division A', num: 1 }], // Division A is selected
            department: undefined, // No department applied
          };

          // Expected departments for Division A from baseMockFiltersList
          const expectedDepartmentsForDivisionA = [
            { name: 'Dept 1', num: 10 },
            { name: 'Dept 2', num: 20 },
          ];

          const { result, rerender } = renderHook(
            (props) =>
              useWorksheetFilterState(
                props.isOpen,
                props.onClose,
                props.FiltersList,
                props.appliedFilters,
                props.onApply
              ),
            {
              initialProps: {
                isOpen,
                onClose: mockOnClose,
                FiltersList: baseMockFiltersList,
                appliedFilters,
                onApply: mockOnApply,
              },
            }
          );

          // Initial render: useEffect for appliedFilters runs, then useEffect for selectedDivision, then preselection logic.
          // 1. appliedFilters sets selectedDivision to [{ name: 'Division A', num: 'A' }]
          // 2. useEffect for selectedDivision change calls mockUpdateDepartmentsAndDesks.
          //    - mockUpdateDepartmentsAndDesks calls setDepartmentsCb with depts for Division A.
          //    - dispatch(setDepartments(expectedDepartmentsForDivisionA)) is called.
          //    - mockReduxStore.departments_rn.data becomes expectedDepartmentsForDivisionA.
          // 3. result.current.departments (from useSelectorWrap) is now expectedDepartmentsForDivisionA.
          // 4. useEffect for preselecting departments runs:
          //    - isMultipleDepartmentsSelection is true.
          //    - departments.length > 0 (it's 2).
          //    - selectedDepartment is undefined.
          //    - hasUserInteractedWithDepartments is false.
          //    - setSelectedDepartment(expectedDepartmentsForDivisionA) is called.

          expect(result.current.departments).toEqual(
            expectedDepartmentsForDivisionA
          );
          expect(result.current.selectedDepartment).toEqual(
            expectedDepartmentsForDivisionA
          );
        });
      });

      describe('WHEN handleDeskChange is called', () => {
        it('THEN it should update selectedDesk, reset department/SM/ASM, and update categories', () => {
          const initialAppliedFilters: AppliedFilterState = {
            division: [{ name: 'Division A', num: 1 }],
            department: { name: 'Dept 1', num: 10 }, // Initially a department is selected
            desk: undefined,
            timeframe: undefined,
            selectedSm: 'sm1', // Changed to string
            selectedAsm: ['asm1'], // Changed to string[]
          };

          // Setup initial desks in the hook's state for Division A
          const initialDesksForDivisionA = [
            { name: 'Desk 1A', num: 101 },
            { name: 'Desk 2A', num: 102 },
          ];
          // We can't directly set hook state, but handleDivisionChange (if called) would populate it.
          // For this test, we assume desks are populated if a division was previously selected.
          // The hook's internal `desks` state will be used by `updateCategoriesBasedOnFilterApplied`.

          const { result, rerender } = renderHook(
            (props) =>
              useWorksheetFilterState(
                props.isOpen,
                props.onClose,
                props.FiltersList,
                props.appliedFilters,
                props.onApply
              ),
            {
              initialProps: {
                isOpen,
                onClose: mockOnClose,
                FiltersList: baseMockFiltersList,
                appliedFilters: initialAppliedFilters,
                onApply: mockOnApply,
              },
            }
          );

          // Simulate that desks for Division A are loaded (e.g. by a prior division selection)
          // This is a bit indirect; ideally, we'd control the `desks` state directly if possible,
          // but `setDesks` is internal to the hook. We rely on `updateDepartmentsAndDesks` mock.
          act(() => {
            // This will trigger the useEffect in the hook that calls mockUpdateDepartmentsAndDesks
            // which in turn calls setDesksCb (the hook's internal setDesks)
            result.current.handleDivisionChange(initialAppliedFilters.division);
            // Reset department to what it was, as handleDivisionChange clears it
            result.current.handleDepartmentChange(
              initialAppliedFilters.department as DropdownType
            );
          });

          const newSelectedDesk: DropdownType = { name: 'Desk 1A', num: 101 };
          const expectedCategoriesForDesk1A = [
            { name: 'Category Y', num: 401 },
          ]; // Assuming CY maps to a number
          getCategoriesForDesk.mockReturnValue(expectedCategoriesForDesk1A);

          act(() => {
            result.current.handleDeskChange(newSelectedDesk);
          });

          // Verify state updates
          expect(result.current.selectedDesk).toEqual(newSelectedDesk);
          expect(result.current.selectedDepartment).toBeUndefined(); // Department should be cleared

          // Verify Redux dispatches for clearing SM/ASM
          expect(mockDispatch).toHaveBeenCalledWith(
            setAsmDataForSelectedSm({ selectedSm: '', asmForSelectedSm: [], smDataForSelectedDept: [] })
          );
          expect(mockDispatch).toHaveBeenCalledWith(setSelectedAsm([]));

          // Verify categories update (useEffect on selectedDesk calls updateCategoriesBasedOnFilterApplied)
          expect(getCategoriesForDesk).toHaveBeenCalledWith(
            baseMockFiltersList,
            newSelectedDesk
          );
          expect(result.current.categories).toEqual(
            expectedCategoriesForDesk1A
          );
        });
      });

      describe('WHEN handleTimeframeChange is called', () => {
        it('THEN it should update selectedTimeframe', () => {
          const { result } = renderHook(
            (props) =>
              useWorksheetFilterState(
                props.isOpen,
                props.onClose,
                props.FiltersList,
                props.appliedFilters,
                props.onApply
              ),
            {
              initialProps: {
                isOpen,
                onClose: mockOnClose,
                FiltersList: baseMockFiltersList,
                appliedFilters: defaultAppliedFilters, // Start with default (empty) applied filters
                onApply: mockOnApply,
              },
            }
          );

          expect(result.current.selectedTimeframe).toBeUndefined(); // Initially undefined

          const newTimeframe: DropdownType = { name: 'Q2', num: 1002 }; // Assuming Q2 maps to a number
          act(() => {
            result.current.handleTimeframeChange(newTimeframe);
          });

          expect(result.current.selectedTimeframe).toEqual(newTimeframe);
        });
      });
    });
  });
});
