import Modal from '@albertsons/uds/molecule/Modal';
import React, { useState } from 'react';
import { FilterModalProps } from './worksheetFilterTypes';
// Import the hook directly with the full path
import { useWorksheetFilterState } from '../../features/worksheetFilter/hooks/useWorksheetFilterState';
// Import the components with full paths
import ModalContent from './components/filterModal/ModalContent';
import ModalFooter from './components/filterModal/ModalFooter';
import ModalContainer from './components/filterModal/ModalContainer';
import ModalHeader from './components/filterModal/ModalHeader';
// import './worksheetFilterModal.css';

const WorksheetFilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  FiltersList,
  appliedFilters,
  onApply,
}) => {
  const filterState = useWorksheetFilterState(
    isOpen,
    onClose,
    FiltersList,
    appliedFilters,
    onApply
  );

  const getModalContent = () => (<ModalContent
    shouldDisplayTimeFrame={filterState.shouldDisplayTimeFrame}
    selectedTimeframe={filterState.selectedTimeframe}
    selectedPeriods={filterState.selectedPeriods}
    selectedWeeks={filterState.selectedWeeks}
    handleTimeframeChange={filterState.handleTimeframeChange}
    handlePeriodChange={filterState.handlePeriodChange}
    handleWeeksChange={filterState.handleWeekChange}
    divisions={filterState.divisions}
    selectedDivision={filterState.selectedDivision}
    desks={filterState.desks}
    selectedDepartment={filterState.selectedDepartment}
    selectedDesk={filterState.selectedDesk}
    categories={filterState.categories}
    isDisplayDeptRoleCascade={filterState.isDisplayDeptRoleCascade}
    activeTabInFilter={filterState.activeTabInFilter}
    handleDivisionChange={filterState.handleDivisionChange}
    handleDepartmentChange={filterState.handleDepartmentChange}
    handleDeskChange={filterState.handleDeskChange}
  />);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="w-[90vw] max-h-[80vh] min-h-0"
     >
      {({ close }) => (
        <ModalContainer className="worksheet-filter-modal-container py-2 px-4">
          <ModalHeader
            title="Advanced Filter"
            subheading={filterState.shouldDisplayTimeFrame
              ? "Below are the filter options to view the Dashboard data"
              : "Below are the filter options to view the Adjustment worksheet data"}
            onClose={close}
          />
          <div className="worksheet-filter-modal-content">
            {getModalContent()}
          </div>
            <ModalFooter
              shouldDisplayTimeFrame={filterState.shouldDisplayTimeFrame}
              selectedTimeframe={filterState.selectedTimeframe}
              selectedPeriods={filterState.selectedPeriods}
              selectedWeeks={filterState.selectedWeeks}
              selectedDivision={filterState.selectedDivision}
              selectedDesk={filterState.selectedDesk}
              selectedDepartment={filterState.selectedDepartment}
              resetFilters={filterState.resetFilters}
              applyFilters={filterState.applyFilters}
              onCancel={filterState.onCancel}
              close={close}
            />
        </ModalContainer>
      )}
    </Modal>
  );
};

export default WorksheetFilterModal;
