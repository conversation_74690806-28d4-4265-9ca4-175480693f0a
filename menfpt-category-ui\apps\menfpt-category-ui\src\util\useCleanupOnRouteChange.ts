import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { setPeriodStatuses } from '../features/periodClose/periodStatuses.slice';
import { clearPrevQuarterTabData } from '../features/periodClose/periodClose.slice';
import {
  clearDataForQrtrDisplayedInTable,
  clearLastQrtrData,
} from '../components/quarterDetails.slice';


export function useCleanupOnRouteChange() {
  const dispatch = useDispatch();
  const location = useLocation();

  const cleanupPeriodCloseSlices = () => {
    dispatch(setPeriodStatuses({}));
    dispatch(clearPrevQuarterTabData());
    dispatch(clearDataForQrtrDisplayedInTable());
    dispatch(clearLastQrtrData());
  };

  useEffect(() => {
    cleanupPeriodCloseSlices();
  }, [location.pathname]);
}
