import React, { ReactNode } from 'react';

// Styles as variables
export const modalContainerStyles = "bg-white rounded-lg p-[20px] font-nunito font-semibold text-base leading-5 tracking-normal";

interface ModalContainerProps {
  children: ReactNode;
  className?: string;
}

const ModalContainer: React.FC<ModalContainerProps> = ({ children, className }) => {
  return (
    <div className={`${modalContainerStyles} ${className}`}>
      {children}
    </div>
  );
};

export default ModalContainer;
