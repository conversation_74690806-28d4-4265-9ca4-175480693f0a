import { useSelectorWrap } from '../rtk/rtk-utilities';
import { worksheetFilterConfig } from '../features/worksheetFilter/worksheetFilterConfig';

interface DivisionBannerPair {
  divisionId: string;
  bannerId: string;
}

/**
 * Hook to extract unique division banner IDs from applied filters
 * @returns Array of unique division banner IDs or division-banner pairs based on filterPg
 */
export const useExtractBannerId = (): any => {
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');

  if (!appliedFilters?.division || appliedFilters?.division.length === 0) {
    return [];
  }

  // Check if we're on the dashboard page
  if (appliedFilters.filterPg === worksheetFilterConfig.lsKeyDashboardPg) {
    // Return division-banner pairs for dashboard
    const divisionBannerPairs: DivisionBannerPair[] = [];
    
    appliedFilters.division.forEach((division: any) => {
      if (division.banners && division.banners.length > 0) {
        division.banners.forEach((banner: any) => {
          if (banner.num !== undefined && banner.num !== null) {
            divisionBannerPairs.push({
              divisionId: String(division.num),
              bannerId: String(banner.num)
            });
          }
        });
      }
      // If a division is selected without any banners, include bannerId as "0"
      else {
        if (division?.num !== undefined && division?.num !== null) {
          divisionBannerPairs.push({
            divisionId: String(division.num),
            bannerId: '00',
          });
        }
      }
    });
    
    return divisionBannerPairs;
  }
  
  // Default behavior for other pages - return unique banner IDs
  const bannerIds = appliedFilters.division
    .map((division: any) => {
      return division.banners && division.banners?.map((banner: any) => banner.num) || [];
    })
    .flat()
    .filter((id: any) => id !== undefined && id !== null)
    .map((id: any) => String(id));
  
  // Return unique banner IDs as string array
  const uniqueBannerIds: string[] = Array.from(new Set(bannerIds));
  return uniqueBannerIds;
};