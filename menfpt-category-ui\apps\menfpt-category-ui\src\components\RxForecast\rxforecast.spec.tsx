import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Rxforecast from './rxforecast';

// Mock child components
jest.mock('./rxforecast-title-component', () => () => (
  <div data-testid="rx-forecast-title-component">
    RxForecast Title Component
  </div>
));

describe('Rxforecast', () => {
  it('renders the component correctly', () => {
    render(<Rxforecast />);

    expect(screen.getByTestId('rx-forecast-title-component')).toBeInTheDocument();
  });

  it('renders title component', () => {
    render(<Rxforecast />);

    expect(screen.getByText('RxForecast Title Component')).toBeInTheDocument();
  });

  it('renders without any props', () => {
    expect(() => render(<Rxforecast />)).not.toThrow();
  });

  it('has correct component hierarchy', () => {
    const { container } = render(<Rxforecast />);

    // Should have React Fragment > div > title component
    const fragment = container;
    const outerDiv = fragment.firstChild as HTMLElement;
    expect(outerDiv).toBeInstanceOf(HTMLDivElement);
    
    const titleComponent = screen.getByTestId('rx-forecast-title-component');
    expect(titleComponent).toBeInTheDocument();
  });

  it('renders consistently on multiple renders', () => {
    const { rerender } = render(<Rxforecast />);

    expect(screen.getByTestId('rx-forecast-title-component')).toBeInTheDocument();

    rerender(<Rxforecast />);

    expect(screen.getByTestId('rx-forecast-title-component')).toBeInTheDocument();
  });
});
