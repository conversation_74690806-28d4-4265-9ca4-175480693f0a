import { getRelativeTime, formatToPST } from './timeUtils';

// Mock date-fns-tz to have consistent test results
jest.mock('date-fns-tz', () => ({
  utcToZonedTime: jest.fn((date: Date) => date),
  format: jest.fn((date: Date, pattern: string) => {
    // Simple mock implementation for testing
    const mockDate = new Date(date);
    if (pattern === 'MMM d, yyyy') {
      return mockDate.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    }
    return mockDate.toLocaleDateString();
  })
}));

describe('timeUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.error to prevent error logs in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getRelativeTime', () => {
    const mockCurrentDate = new Date('2023-10-15T10:00:00.000Z');

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(mockCurrentDate);
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return empty string for empty updatedTs', () => {
      expect(getRelativeTime('')).toBe('');
      expect(getRelativeTime(null as any)).toBe('');
      expect(getRelativeTime(undefined as any)).toBe('');
    });

    it('should return "just now" for time differences less than 1 minute', () => {
      const thirtySecondsAgo = new Date(mockCurrentDate.getTime() - 30 * 1000).toISOString();
      expect(getRelativeTime(thirtySecondsAgo)).toBe('just now');
    });

    it('should return correct minutes ago', () => {
      const twoMinutesAgo = new Date(mockCurrentDate.getTime() - 2 * 60 * 1000).toISOString();
      const oneMinuteAgo = new Date(mockCurrentDate.getTime() - 1 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneMinuteAgo)).toBe('1 minute ago');
      expect(getRelativeTime(twoMinutesAgo)).toBe('2 minutes ago');
    });

    it('should return correct hours ago', () => {
      const oneHourAgo = new Date(mockCurrentDate.getTime() - 1 * 60 * 60 * 1000).toISOString();
      const twoHoursAgo = new Date(mockCurrentDate.getTime() - 2 * 60 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneHourAgo)).toBe('1 hour ago');
      expect(getRelativeTime(twoHoursAgo)).toBe('2 hours ago');
    });

    it('should return correct days ago', () => {
      const oneDayAgo = new Date(mockCurrentDate.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString();
      const threeDaysAgo = new Date(mockCurrentDate.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneDayAgo)).toBe('1 day ago');
      expect(getRelativeTime(threeDaysAgo)).toBe('3 days ago');
    });

    it('should return correct weeks ago', () => {
      const oneWeekAgo = new Date(mockCurrentDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const twoWeeksAgo = new Date(mockCurrentDate.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneWeekAgo)).toBe('1 week ago');
      expect(getRelativeTime(twoWeeksAgo)).toBe('2 weeks ago');
    });

    it('should return correct months ago', () => {
      const oneMonthAgo = new Date(mockCurrentDate.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const twoMonthsAgo = new Date(mockCurrentDate.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneMonthAgo)).toBe('1 month ago');
      expect(getRelativeTime(twoMonthsAgo)).toBe('2 months ago');
    });

    it('should return correct years ago', () => {
      const oneYearAgo = new Date(mockCurrentDate.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString();
      const twoYearsAgo = new Date(mockCurrentDate.getTime() - 730 * 24 * 60 * 60 * 1000).toISOString();
      
      expect(getRelativeTime(oneYearAgo)).toBe('1 year ago');
      expect(getRelativeTime(twoYearsAgo)).toBe('2 years ago');
    });

    it('should handle custom current timestamp', () => {
      const customCurrentDate = new Date('2023-10-15T12:00:00.000Z');
      const twoHoursEarlier = new Date('2023-10-15T10:00:00.000Z').toISOString();
      
      expect(getRelativeTime(twoHoursEarlier, customCurrentDate.toISOString())).toBe('2 hours ago');
    });

    it('should handle future dates (negative differences)', () => {
      const futureDate = new Date(mockCurrentDate.getTime() + 2 * 60 * 60 * 1000).toISOString();
      // For future dates, the function should still work mathematically
      // but will show negative time which would be handled in the UI
      const result = getRelativeTime(futureDate);
      expect(typeof result).toBe('string');
    });

    it('should handle invalid date strings gracefully', () => {
      const result = getRelativeTime('invalid-date');
      // The function might return different values based on how it handles invalid dates
      // Check that it either returns empty string or doesn't crash
      expect(typeof result).toBe('string');
    });

    it('should handle edge case with exactly 1 minute difference', () => {
      const exactlyOneMinuteAgo = new Date(mockCurrentDate.getTime() - 60 * 1000).toISOString();
      expect(getRelativeTime(exactlyOneMinuteAgo)).toBe('1 minute ago');
    });

    it('should handle edge case with exactly 1 hour difference', () => {
      const exactlyOneHourAgo = new Date(mockCurrentDate.getTime() - 60 * 60 * 1000).toISOString();
      expect(getRelativeTime(exactlyOneHourAgo)).toBe('1 hour ago');
    });
  });

  describe('formatToPST', () => {
    it('should return empty string for empty timestamp', () => {
      expect(formatToPST('')).toBe('');
      expect(formatToPST(null as any)).toBe('');
      expect(formatToPST(undefined as any)).toBe('');
    });

    it('should format date with default pattern', () => {
      const timestamp = '2023-10-15T10:00:00.000Z';
      const result = formatToPST(timestamp);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    it('should format date with custom pattern', () => {
      const timestamp = '2023-10-15T10:00:00.000Z';
      const customPattern = 'yyyy-MM-dd';
      const result = formatToPST(timestamp, customPattern);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });
    it('should use default format pattern when not provided', () => {
      const timestamp = '2023-10-15T10:00:00.000Z';
      const result = formatToPST(timestamp);
      
      // Should call format with the default pattern
      expect(result).toBeTruthy();
    });

    it('should handle ISO string timestamps', () => {
      const isoTimestamp = new Date().toISOString();
      const result = formatToPST(isoTimestamp);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    it('should handle timestamp with timezone information', () => {
      const timestampWithTz = '2023-10-15T10:00:00.000-07:00';
      const result = formatToPST(timestampWithTz);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    it('should handle numeric timestamps', () => {
      const numericTimestamp = Date.now().toString();
      const result = formatToPST(numericTimestamp);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });
  });

  describe('Integration tests', () => {
    it('should work together for a complete workflow', () => {
      const timestamp = '2023-10-15T10:00:00.000Z';
      
      // Format the date
      const formattedDate = formatToPST(timestamp);
      expect(formattedDate).toBeTruthy();
      
      // Get relative time
      const relativeTime = getRelativeTime(timestamp);
      expect(relativeTime).toBeTruthy();
    });

    it('should handle same timestamp for both functions', () => {
      const timestamp = new Date().toISOString();
      
      const formatted = formatToPST(timestamp);
      const relative = getRelativeTime(timestamp);
      
      expect(formatted).toBeTruthy();
      expect(relative).toBe('just now');
    });
  });
});
