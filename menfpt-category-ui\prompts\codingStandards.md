1. Function Guidelines

   - A function should not have more than 15 lines

   - A function should have a single responsibility



2. File Guidelines

   - A file should not have more than 150 lines

   - A file should have a single responsibility

   - Name files in camelCase with relevant context



3. React Best Practices

   - Don't add more than 3 lines inside useEffect, instead move the code to a function:

     ```javascript

     useEffect(() => {

         validateLoginForm()

     })

     ```

   - If a function is being passed as a reference to a child component, wrap it with useCallback

   - Route paths should always be constants, never hardcoded



4. Forms and Styling

   - Use Formik for all forms

   - Use Tailwind for styling, avoid creating custom classes



5. State Management and API Calls

   - Use RTK Query for making API calls

   - Use RTK Slice for passing data between components

   - Don't use props for passing data between distant components unless it's a parent-child relationship

   - Never directly use fetch method, instead use custom wrapper like fetchService()

   - Never hardcode API URLs

    - Use RTK query instead of mutation for API calls as it has inbuilt caching and avoids duplicate calls.


6. Constants and References

   - Never hardcode routes or apiUrls directly in files

   - Reference constants from dedicated constant files

   - Example: `appRoutes_common.loginRoute`



7. Image Handling

   - Don't use direct Image elements

   - Always use `<RenderImg>` component for image optimization



8. Naming Conventions

   - Use relevant names for:

     - Variables

     - Routes

     - Function names

     - Component names

     - Files:  Use *.mock.* for mocks(EG: worsheetTableData.mock.ts)

     - API URLs



9. Folder structure

    - Use vertical slicing. Keep a feature related files in a single folder eg: All login related files within login folder



10. Nx.dev best practices

    - Place the code inside libs folder, the app folder should act as a consumer

11. Use config file to control the functionality instead of complex if , else conditions. 

Eg:

export const worksheetFilterConfig = {
  isDisplayTimeFrame: [routeConstants.dashboard] //Routes where time frame needs to be displayed.  
};


12. For every feature, there should be a container file eg: filter-container.tsx which contains all the child components.

13. specs should be located parallel to the component
14. The code should be compatible with touch devices like iPad.