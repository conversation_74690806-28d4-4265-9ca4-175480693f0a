import React from 'react';
import { prettyDOM, waitFor } from '@testing-library/react';
import { render, fireEvent, findByText } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider, useDispatch } from 'react-redux';
import { app_store } from '../../rtk/store';
import ForecastEdit from '../ForecastEdit/editForecastAdjustment';
import { EditForecastAdjustmentProps } from '../ForecastEdit/types';
import '@testing-library/jest-dom/extend-expect';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { applied_filter_slice_info, slice_info } from './edit-forecast-adjustment-test-data';
import userEvent from '@testing-library/user-event';
import {screen} from '@testing-library/react';
import { useSaveAdjustmentEditsMutation } from '../../server/Api/menfptCategoryAPI';
import { setSaveAdjustmentApiStatus } from '../ForecastEdit/editForecast.slice';
import { useEditForecastBffBody, scrollToFirstError } from '../ForecastEdit/forecastCalculations';
import {mock_week123_api_body, mock_week1_api_body} from './editForecastAdjustment-mock-test-expects';
import { Adjustment } from '../../interfaces/edit-forecast-adjustments';

// Mock the useSelectorWrap function
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

// jest.mock('../ForecastEdit/forecastCalculations', () => ({
//   ...jest.requireActual('../ForecastEdit/forecastCalculations'),
//   setEditForecastBffBody: jest.fn(),
// }));

jest.mock('../ForecastEdit/forecastCalculations', () => ({
  ...jest.requireActual('../ForecastEdit/forecastCalculations'),
  useEditForecastBffBody: jest.fn(() => ({
    setEditForecastBffBody: jest.fn(),
  })),
  safeScrollIntoView: jest.fn(),
  scrollToFirstError: jest.fn(),
}));


jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

jest.mock('../ForecastEdit/editForecast.slice', () => ({
  setSaveAdjustmentApiStatus: jest.fn(),
}));

jest.mock('../../server/Api/menfptCategoryAPI', () => ({
  ...jest.requireActual('../../server/Api/menfptCategoryAPI'),
  useSaveAdjustmentEditsMutation: jest.fn(() => [jest.fn()]),
}));

// Mock the saveAdjustmentValue function
jest.mock('../ForecastEdit/editForecastAdjustment', () => ({
  ...jest.requireActual('../ForecastEdit/editForecastAdjustment'),
  saveAdjustmentValue: jest.fn(),
}));

const CLASS_CONSTS = {
  UNSELECTED_BUTTON: 'py-2 px-0 text-center rounded cursor-pointer font-nunito-sans font-normal text-xs leading-4 text-gray-800 bg-gray-200',
  SELECTED_BUTTON: 'py-2 px-0 text-center rounded cursor-pointer font-nunito-sans font-normal text-xs leading-4 text-white bg-blue-500',
}

/**
 * Behavior:
 * If you enter a value in salesPublic, then other auto-pop fields (bgp,mrk,shrk) will show
 * comma seperated values (decimal). If you directly enter a value in the auto-pop fields
 * then no comma separation will be shown.
 *
 * If you enter a value in the percent fields, then the value will be shown in decimal format
 */

describe.skip('ForecastEdit Component', () => {

    const defaultProps: EditForecastAdjustmentProps = {
        isOpen: true,
        setOpen: jest.fn(),
        historyModalOpen: jest.fn(),
        setEditMessage: jest.fn()
    };


    // beforeAll(() => {
    //   jest.spyOn(console, 'error').mockImplementation((message) => {
    //     if (message.includes('Could not parse CSS stylesheet')) {
    //       return;
    //     }
    //     console.error('suppress');
    //   });
    // });

    // afterAll(() => {
    //   jest.restoreAllMocks();
    // });

    class ResizeObserver {
      observe() {}
      unobserve() {}
      disconnect() {}
    }

    beforeEach(() => {
      window.ResizeObserver = ResizeObserver as any;
      // Mock implementation of useSelectorWrap
      (useSelectorWrap as jest.Mock).mockImplementation((selectorName) => {
        if (selectorName === 'adjustmentWorkSheetFilter_rn') {
          return {
        data: slice_info,
          };
        } else if (selectorName === 'appliedFilter_rn') {
          return {
        data: applied_filter_slice_info,
          };
        } else if (selectorName === 'userInfo_rn') {
          return {
        data: {userId: 'ANAMA05'},
          };
        } else {
          // Return a default value for other selectors
          return { data: {} };
        }
      });
    });

    it('should render without crashing', () => {


        const { getByText } = render(
            <Router>
              <Provider store={app_store}>
                <ForecastEdit {...defaultProps} />
              </Provider>
            </Router>
          );
        expect(getByText('Edit Adjustment')).toBeInTheDocument();
    });

    it.skip('should allow me to select week 1 from Applied Weeks ', async () => {
      const { getByText, findByText } = render(
        <Router>
          <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
      );

      // Wait for the element with the specified class name and value "1" to appear
      const weekElement = await findByText('1', {});
      expect(weekElement).toBeInTheDocument();
      expect(weekElement).toHaveClass(
        CLASS_CONSTS.UNSELECTED_BUTTON
      );

      // Simulate a click on the element using userEvent
      await userEvent.click(weekElement);

      // Verify that the week element now has the updated class indicating the background is blue
      expect(weekElement).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );
      // Verify that the text total= $44,835,162.000
      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();
    })

    it.skip('should auto scroll to errrors', async() => {
      render(
        <Router>
          <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
      );
      // Mock implementation for safeScrollIntoView
      const mockScrollToFirstError = jest.fn();
      (scrollToFirstError as jest.Mock).mockImplementation(mockScrollToFirstError);

      // Simulate clicking the save adjustment button
      const week1Element = await screen.findByText('1', {});
      await userEvent.click(week1Element);

      //const saveAdjustmentButton = await screen.findByTestId('save-adjustment-button');
      const saveAdjustmentButton = await screen.findByRole('button', { name: 'Save adjustment' });

      expect(saveAdjustmentButton).toBeInTheDocument();
      await userEvent.click(saveAdjustmentButton);

      // Verify that safeScrollIntoView was called
      await waitFor(() => {
        expect(mockScrollToFirstError).toHaveBeenCalled();
        expect(mockScrollToFirstError).toHaveBeenCalledTimes(1);
      });    
    })

    it.skip('should show the correct total after selecting two weeks in group form', async () => {
      const { findByText } = render(
        <Router>
          <Provider store={app_store}>
            <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
      );

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);

      // Select week 2
      const week2Element = await findByText('2', {});
      await userEvent.click(week2Element);

      // Wait for the total to appear and verify its value
      
      const totalElement = await screen.findByText(/\Current Sales/, {});
      expect(totalElement).toBeInTheDocument();
      // Select week 3
      const week3Element = await screen.findByText('3');
      await userEvent.click(week3Element);
      expect(week3Element).toBeInTheDocument();
      expect(week3Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );


      // Wait for the updated total to appear and verify its value
      const updatedTotalElement = await screen.findByText(/\$136,191,202/, {});
      expect(updatedTotalElement).toBeInTheDocument();


    })

    it('should find and select the "Enter for each week separately" radio option', async () => {
      const { findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

    });


    it.skip('should allow week select in separate week mode and week 1 in both group and single should be same', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();




    });

    it.skip('should allow week select in separate week mode and week 1 and week 2 ', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );
      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();
      // Select week 2
      const week2Element = await findByText('2', {});
      await userEvent.click(week2Element);
      expect(week2Element).toBeInTheDocument();
      expect(week2Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );
      // week1 element should be unselected after selecting week2 in single week form
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.UNSELECTED_BUTTON
      );
      const totalElement2 = await screen.findByText(/\$45,752,542/);
      expect(totalElement2).toBeInTheDocument();
    });

    it.skip('should show correct value in grossProfit when you enter select week 1 and enter 1 in salesPublic', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // Find the input element with id "salesPublic"
      const salesPublicInput = await screen.findByTestId('salesPublic-input');
      expect(salesPublicInput).toBeInTheDocument();

      // Enter the value "1" into the input element
      await userEvent.type(salesPublicInput, '1');

      // Verify that the input element has the value "1"
      expect(salesPublicInput).toHaveValue('+1');
      // Verify that the grossProfit element has the updated value
      const grossProfitNumber = await screen.findByTestId('grossProfit-input');
      expect(grossProfitNumber).toBeInTheDocument();
      expect(grossProfitNumber).toHaveValue('24,088,229');

      const grossProfitNumberPct = await screen.findByTestId('grossProfitPct-input');
      expect(grossProfitNumberPct).toBeInTheDocument();
      expect(grossProfitNumberPct).toHaveValue('53.73');

      // screen.debug();

      await userEvent.clear(salesPublicInput);
      await userEvent.type(salesPublicInput, '-1');
      expect(salesPublicInput).toHaveValue('-1');


    }
    );

    it.skip('should show correct value in marksDown when you enter select week 1 and enter 1 in salesPublic', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      
      // Find the input element with id "salesPublic"
      const salesPublicInput = await screen.findByTestId('salesPublic-input');
      expect(salesPublicInput).toBeInTheDocument();

      // Enter the value "1" into the input element
      await userEvent.type(salesPublicInput, '1');

      // Verify that the input element has the value "1"
      expect(salesPublicInput).toHaveValue('+1');


      // Find the input element with id "salesPublic"
      const marksDownInput = await screen.findByTestId('marksDown-input');
      expect(marksDownInput).toBeInTheDocument();
      const actual = '-10,446,424';
      expect(marksDownInput).toHaveValue(actual);

      const marksDownPct = await screen.findByTestId('marksDownPct-input');
      expect(marksDownPct).toBeInTheDocument();
      expect(marksDownPct).toHaveValue('-23.30');


    }
    );

    it.skip('should show correct value in totalShrink when you enter select week 1 and enter 1 in salesPublic', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // Find the input element with id "salesPublic"
      const salesPublicInput = await screen.findByTestId('salesPublic-input');
      expect(salesPublicInput).toBeInTheDocument();

      // Enter the value "1" into the input element
      await userEvent.type(salesPublicInput, '1');

      // Verify that the input element has the value "1"
      expect(salesPublicInput).toHaveValue('+1');


      // Find the input element with id "salesPublic"
      const totalShrinkInput = await screen.findByTestId('totalShrink-input');
      expect(totalShrinkInput).toBeInTheDocument();
      const actual = '-1,037,388';
      expect(totalShrinkInput).toHaveValue(actual);

      const totalShrinkPct = await screen.findByTestId('totalShrinkPct-input');
      expect(totalShrinkPct).toBeInTheDocument();
      expect(totalShrinkPct).toHaveValue('-2.31');
    }
    );

    it.skip('should allow individual enter of values in grossProfit, marksDown, totalShrink when selecting week 1', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      const grossProfitNumber = await screen.findByTestId('grossProfit-input');
      expect(grossProfitNumber).toBeInTheDocument();
      expect(grossProfitNumber).toHaveValue('24,088,228');
      // Enter the value "1" into the grossProfitNumber input element
      await userEvent.clear(grossProfitNumber);
      await userEvent.type(grossProfitNumber, '4483516');
      // screen.debug();
      expect(grossProfitNumber).toHaveValue('4483516');
      // Verify that the grossProfit element has the updated value
      const grossProfitNumberPct = await screen.findByTestId('grossProfitPct-input');
      // expect(grossProfitNumberPct).toBeInTheDocument();
      expect(grossProfitNumberPct).toHaveValue('10.00');

      // // clear both inputs
      await userEvent.clear(grossProfitNumber);
      await userEvent.clear(grossProfitNumberPct);

      // Enter 10 percent in grossProfitNumberPct
      await userEvent.type(grossProfitNumberPct, '10');
      // Verify that the input element has the value "10"
      expect(grossProfitNumberPct).toHaveValue('10');
      // // Verify that the grossProfit element has the updated value
      expect(grossProfitNumber).toHaveValue('4,483,516');
    });

    it.skip('should show all adjustment reasons after clicking on Make a selection dropdown', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // Find the dropdown element for adjustment reasons
      const adjustmentReasonDropdown = await screen.findByPlaceholderText('Make a selection', {});
      expect(adjustmentReasonDropdown).toBeInTheDocument();
      // Simulate clicking on the dropdown to open it
      await userEvent.click(adjustmentReasonDropdown);
      // Verify that the dropdown options are displayed
      const adjustmentReasonOption1 = await screen.findByText('Extreme weather', {});
      expect(adjustmentReasonOption1).toBeInTheDocument();
      const adjustmentReasonOption2 = await screen.findByText('Advt plan changes', {});
      expect(adjustmentReasonOption2).toBeInTheDocument();

      const adjustmentReasonOption3 = await screen.findByText('Strategy changes', {});
      expect(adjustmentReasonOption3).toBeInTheDocument();

      const adjustmentReasonOption4 = await screen.findByText('Competitive changes', {});
      expect(adjustmentReasonOption4).toBeInTheDocument();

      const adjustmentReasonOption5 = await screen.findByText('Commodity price/cost change', {});
      expect(adjustmentReasonOption5).toBeInTheDocument();

      const adjustmentReasonOption6 = await screen.findByText('Holidays', {});
      expect(adjustmentReasonOption6).toBeInTheDocument();

      const adjustmentReasonOption7 = await screen.findByText('Shelf stock availability', {});
      expect(adjustmentReasonOption7).toBeInTheDocument();

      const adjustmentReasonOption8 = await screen.findByText('Promotional activity', {});
      expect(adjustmentReasonOption8).toBeInTheDocument();

      const adjustmentReasonOption9 = await screen.findByText('Unit Decreases', {});
      expect(adjustmentReasonOption9).toBeInTheDocument();

      const adjustmentReasonOption10 = await screen.findByText('Other', {});
      expect(adjustmentReasonOption10).toBeInTheDocument();


    }
    );

    it.skip('should show Extreme Weather after selecting it from the dropdown', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // Find the dropdown element for adjustment reasons
      const adjustmentReasonDropdown = await screen.findByPlaceholderText('Make a selection', {});
      expect(adjustmentReasonDropdown).toBeInTheDocument();

      // Simulate clicking on the dropdown to open it
      await userEvent.click(adjustmentReasonDropdown);
      // Verify that the dropdown options are displayed
      const adjustmentReasonOption1 = await screen.findByText('Extreme weather', {});
      expect(adjustmentReasonOption1).toBeInTheDocument();
      // Simulate selecting the "Extreme weather" option
      await userEvent.click(adjustmentReasonOption1);
      // Verify that the selected option is displayed in the dropdown
      expect(await screen.findByPlaceholderText('Extreme weather')).toBeInTheDocument();
    });

    it.skip('should allow user to select supplies packaging, and allowance  and enter values in the input a', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately');
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1');
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // find the input element with role textbox and name suppliesPackaging-input
      const suppliesPackagingInput = await screen.findByTestId('suppliesPackaging-input');
      expect(suppliesPackagingInput).toBeInTheDocument();
      // Enter the value "10000" into the input element
      await userEvent.clear(suppliesPackagingInput);
      await userEvent.type(suppliesPackagingInput, '10000');
      // Verify that the input element has the value "10000"
      expect(suppliesPackagingInput).toHaveValue('10000');
      // Verify that the suppliesPackagingPct element has the updated value

      // find the input element with role textbox and name Input for allowance in dollars
      const allowanceInput = await screen.findByTestId('allowances-input'   );
      //expect(allowanceInput).toBeInTheDocument();
      // Enter the value "70000" into the input element
      await userEvent.type(allowanceInput, '70000');
      // Verify that the input element has the value "70000"
      //expect(allowanceInput).toHaveValue('+70000');
      // Verify that the allowancePct element has the updated value
      // const allowancePct = await screen.findByTestId('allowancesPct-input'   );
      //expect(allowancePct).toBeInTheDocument();
      //expect(allowancePct).toHaveValue('2.0260');

      // enter negative value for allowance input and confirm text
      // await userEvent.clear(allowanceInput);
      await userEvent.type(allowanceInput, '-70000');
      // Verify that the input element has the value "-70000"
      //expect(allowanceInput).toHaveValue('-70000');

    });

    it.skip('should allow user to type comment in comment-1 (comment section for week1) textarea', async () => {
      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON 
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();

      // Find the textarea element for comments
      const commentTextarea = await screen.findByLabelText('Comment-1', {});
      expect(commentTextarea).toBeInTheDocument();

      // Simulate typing a comment in the textarea
      await userEvent.type(commentTextarea, 'This is a test comment');

      // Verify that the textarea contains the typed comment
      expect(commentTextarea).toHaveValue('This is a test comment');
      // Simulate clearing the textarea
      await userEvent.clear(commentTextarea);
      // Verify that the textarea is empty
      expect(commentTextarea).toHaveValue('');
    });

    it.skip('should enter salesPublic as 1, ensure the grossProfit,marksDown,totalShrink are updated, select Extreme weather from the dropdown, and enter a comment', async () => {

      const mockSaveAdjustmentEdits = jest.fn().mockResolvedValue({
        data: { saveAdjustment: { success: true } },
      });

      // // Mock the hook to return the mock function
      (useSaveAdjustmentEditsMutation as jest.Mock).mockReturnValue([mockSaveAdjustmentEdits]);

      const mockDispatch = jest.fn();
      (useDispatch as jest.Mock).mockReturnValue(mockDispatch);


      const mockSetEditForecastBffBody = jest.fn();
      (useEditForecastBffBody as jest.Mock).mockReturnValue({
        setEditForecastBffBody: mockSetEditForecastBffBody,
      });

      const { findByText,findByLabelText } = render(
      <Router>
        <Provider store={app_store}>
        <ForecastEdit {...defaultProps} />
        </Provider>
      </Router>
      );

      // Find the radio button with the label "Enter for each week separately"
      const radioOption = await findByLabelText('Enter for each week separately', {});
      expect(radioOption).toBeInTheDocument();

      // Simulate selecting the radio button
      await userEvent.click(radioOption);

      // Verify that the radio button is selected
      expect(radioOption).toBeChecked();

      // Select week 1
      const week1Element = await findByText('1', {});
      await userEvent.click(week1Element);
      expect(week1Element).toBeInTheDocument();
      expect(week1Element).toHaveClass(
        CLASS_CONSTS.SELECTED_BUTTON
      );

      const totalElement = await screen.findByText(/\$44,835,162/);
      expect(totalElement).toBeInTheDocument();
      // Find the input element with id "salesPublic"
      const salesPublicInput = await screen.findByTestId('salesPublic-input');
      expect(salesPublicInput).toBeInTheDocument();
      // Enter the value "1" into the input element
      await userEvent.type(salesPublicInput, '1');
      // Verify that the input element has the value "1"
      expect(salesPublicInput).toHaveValue('+1');
      // Verify that the grossProfit element has the updated value
      const grossProfitNumber = await screen.findByTestId('grossProfit-input'   );
      expect(grossProfitNumber).toBeInTheDocument();
      expect(grossProfitNumber).toHaveValue('24,088,229');
      const grossProfitNumberPct = await screen.findByTestId('grossProfitPct-input'   );
      expect(grossProfitNumberPct).toBeInTheDocument();
      expect(grossProfitNumberPct).toHaveValue('53.73');
      // Find the input element with id "salesPublic"
      const marksDownInput = await screen.findByTestId('marksDown-input'   );
      expect(marksDownInput).toBeInTheDocument();
      const actual = '-10,446,424';
      expect(marksDownInput).toHaveValue(actual);
      const marksDownPct = await screen.findByTestId('marksDownPct-input'   );
      expect(marksDownPct).toBeInTheDocument();
      expect(marksDownPct).toHaveValue('-23.30');
      // Find the input element with id "salesPublic"
      const totalShrinkInput = await screen.findByTestId('totalShrink-input'   );
      expect(totalShrinkInput).toBeInTheDocument();
      const actual2 = '-1,037,388';
      expect(totalShrinkInput).toHaveValue(actual2);
      const totalShrinkPct = await screen.findByTestId('totalShrinkPct-input'   );
      expect(totalShrinkPct).toBeInTheDocument();
      expect(totalShrinkPct).toHaveValue('-2.31');
      // Find the dropdown element for adjustment reasons
      const adjustmentReasonDropdown = await screen.findByPlaceholderText('Make a selection');
      expect(adjustmentReasonDropdown).toBeInTheDocument();
      // Simulate clicking on the dropdown to open it
      await userEvent.click(adjustmentReasonDropdown);
      // Verify that the dropdown options are displayed
      const adjustmentReasonOption1 = await screen.findByText('Extreme weather');
      expect(adjustmentReasonOption1).toBeInTheDocument();
      // Simulate selecting the "Extreme weather" option
      await userEvent.click(adjustmentReasonOption1);
      // Verify that the selected option is displayed in the dropdown
      expect(await screen.findByPlaceholderText('Extreme weather')).toBeInTheDocument();
      // Find the textarea element for comments
      const commentTextarea = await screen.findByLabelText('Comment-1');
      expect(commentTextarea).toBeInTheDocument();
      // Simulate typing a comment in the textarea
      await userEvent.type(commentTextarea, 'This is a test comment');
      // Verify that the textarea contains the typed comment
      expect(commentTextarea).toHaveValue('This is a test comment');

      // add value to suppliesPackaging and allowances
      // find the input element with role textbox and name suppliesPackaging-input
      const suppliesPackagingInput = await screen.findByTestId('suppliesPackaging-input'   );
      expect(suppliesPackagingInput).toBeInTheDocument();
      // Enter the value "10000" into the input element
      await userEvent.clear(suppliesPackagingInput);
      await userEvent.type(suppliesPackagingInput, '10000');
      // Verify that the input element has the value "10000"
      expect(suppliesPackagingInput).toHaveValue('10000');
      // Verify that the suppliesPackagingPct element has the updated value
      // find the input element with role textbox and name Input for allowance in dollars
      const sellingAllowanceInput = await screen.findByTestId('selling-input'   );
      //expect(allowanceInput).toBeInTheDocument();
      // Enter the value "70000" into the input element
      await userEvent.type(sellingAllowanceInput, '70000');
      // Verify that the input element has the value "70000"
      expect(sellingAllowanceInput).toHaveValue('70000');
      // Verify that the allowancePct element has the updated value
      // const allowancePct = await screen.findByTestId('allowancesPct-input'   );
      //expect(allowancePct).toBeInTheDocument();
      //expect(allowancePct).toHaveValue('2.0260');

      // find the button with Save adjustment
      const saveAdjustmentButton = await screen.findByRole('button', { name: 'Save adjustment'});
      expect(saveAdjustmentButton).toBeInTheDocument();
      // Simulate clicking on the button to save the adjustment
      await userEvent.click(saveAdjustmentButton);

      await waitFor(() => {
        // Assert that dispatch was called with setSaveAdjustmentApiStatus(true)
        expect(mockDispatch).toHaveBeenCalledWith(setSaveAdjustmentApiStatus(true));

        const expectedApiBody = {
          ...mock_week1_api_body,
        };

        // Verify that setEditForecastBffBody was called with the correct API body
        // expect(mockSetEditForecastBffBody).toHaveBeenCalledWith(expectedApiBody);

      });

      // Verify that the save adjustment for form submission was called
      expect(mockSaveAdjustmentEdits).toHaveBeenCalledTimes(1);


    });





});

// Increase timeout for all tests in this suite
jest.setTimeout(15000);

describe('ForecastEdit Component Group week test suite', () => {
    const defaultProps: EditForecastAdjustmentProps = {
      isOpen: true,
      setOpen: jest.fn(),
      historyModalOpen: jest.fn(),
      setEditMessage: jest.fn()
    };




    class ResizeObserver {
      observe() {}
      unobserve() {}
      disconnect() {}
    }

    beforeEach(() => {
      window.ResizeObserver = ResizeObserver as any;
      // Mock implementation of useSelectorWrap
      (useSelectorWrap as jest.Mock).mockImplementation((selectorName) => {
        if (selectorName === 'adjustmentWorkSheetFilter_rn') {
          return {
        data: slice_info,
          };
        } else if (selectorName === 'appliedFilter_rn') {
          return {
        data: applied_filter_slice_info,
          };
        } else if (selectorName === 'userInfo_rn') {
          return {
        data: {userId: 'ANAMA05'},
          };
        } else {
          // Return a default value for other selectors
          return { data: {} };
        }
      });
    });


    it.skip('should find and select the "Enter same values for selected weeks" radio option', async () => {
      const { findByLabelText } = render(
        <Router>
          <Provider store={app_store}>
          <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
        );

        // Find the radio button with the label "Enter for each week separately"
        const radioOption = await screen.findByLabelText('Enter same values for selected weeks');
        expect(radioOption).toBeInTheDocument();
        expect(radioOption).toBeChecked(); // by default it should be selected

        // Simulate selecting the radio button
        await userEvent.click(radioOption);

        // Verify that the radio button is selected
        expect(radioOption).toBeChecked();

    });

    it.skip('should allow selecting multiple weeks and they all should be selected', async () => {
      const { findByText } = render(
        <Router>
          <Provider store={app_store}>
          <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
      );

      // Select week 1
      const week1Element = await screen.findByText('1');
      await userEvent.click(week1Element);

      // Select week 2
      const week2Element = await screen.findByText('2');
      await userEvent.click(week2Element);

      // Wait for the total to appear and verify its value
      const totalElement = await screen.findByText(/\$90,587,704/);
      
      expect(totalElement).toBeInTheDocument();
      // Select week 3
      const week3Element = await screen.findByText('3');
      await userEvent.click(week3Element);
      expect(week3Element).toBeInTheDocument();
      [week1Element,week2Element,week3Element].forEach((cur_week) =>{
        expect(cur_week).toHaveClass(
          CLASS_CONSTS.SELECTED_BUTTON
        );

      })



    });

    it.skip('should allow selecting multiple weeks and salesPublic', async () => {

      const mockSaveAdjustmentEdits = jest.fn().mockResolvedValue({
        data: { saveAdjustment: { success: true } },
      });

      // // Mock the hook to return the mock function
      (useSaveAdjustmentEditsMutation as jest.Mock).mockReturnValue([mockSaveAdjustmentEdits]);

      const mockDispatch = jest.fn();
      (useDispatch as jest.Mock).mockReturnValue(mockDispatch);


      const mockSetEditForecastBffBody = jest.fn();
      (useEditForecastBffBody as jest.Mock).mockReturnValue({
        setEditForecastBffBody: mockSetEditForecastBffBody,
      });

      const { findByText } = render(
        <Router>
          <Provider store={app_store}>
          <ForecastEdit {...defaultProps} />
          </Provider>
        </Router>
      );

      // Select week 1
      const week1Element = await screen.findByText('1');
      await userEvent.click(week1Element);

      // Select week 2
      const week2Element = await screen.findByText('2');
      await userEvent.click(week2Element);

      // Wait for the total to appear and verify its value
      const totalElement = await screen.findByText(/\$90,587,704/);
      expect(totalElement).toBeInTheDocument();
      // Select week 3
      const week3Element = await screen.findByText('3');
      await userEvent.click(week3Element);
      expect(week3Element).toBeInTheDocument();
      [week1Element,week2Element,week3Element].forEach((cur_week) =>{
        expect(cur_week).toHaveClass(
          CLASS_CONSTS.SELECTED_BUTTON
        );

      })

      // Find the input element with id "salesPublic"
      const salesPublicInput = await screen.findByTestId('salesPublic-input'   );
      expect(salesPublicInput).toBeInTheDocument();

      // Enter the value "1" into the input element
      await userEvent.type(salesPublicInput, '1');

      // Verify that the input element has the value "1"
      expect(salesPublicInput).toHaveValue('+1');

      // Verify that the grossProfit element has the updated value
      const grossProfitNumber = await screen.findByTestId('grossProfit-input'   );
      expect(grossProfitNumber).toBeInTheDocument();
      expect(grossProfitNumber).toHaveValue('73,458,802.6181');

      const grossProfitNumberPct = await screen.findByTestId('grossProfitPct-input'   );
      expect(grossProfitNumberPct).toBeInTheDocument();
      expect(grossProfitNumberPct).toHaveValue('53.9380');

      const shrinkNumber = await screen.findByTestId('totalShrink-input'   );
      expect(shrinkNumber).toBeInTheDocument();
      expect(shrinkNumber).toHaveValue('-2,855,096.0629');
      const shrinkNumberPct = await screen.findByTestId('totalShrinkPct-input'   );
      expect(shrinkNumberPct).toBeInTheDocument();
      expect(shrinkNumberPct).toHaveValue('-2.0964');

      const marksDownNumber = await screen.findByTestId('marksDown-input'   );
      expect(marksDownNumber).toBeInTheDocument();
      expect(marksDownNumber).toHaveValue('-32,866,343.7238');
      const marksDownNumberPct = await screen.findByTestId('marksDownPct-input'   );
      expect(marksDownNumberPct).toBeInTheDocument();
      expect(marksDownNumberPct).toHaveValue('-24.1325');

      // find the input element with role textbox and name suppliesPackaging-input
      const suppliesPackagingInput = await screen.findByTestId('suppliesPackaging-input'   );
      expect(suppliesPackagingInput).toBeInTheDocument();
      // Enter the value "10000" into the input element
      await userEvent.type(suppliesPackagingInput, '10000');
      // Verify that the input element has the value "10000"
      expect(suppliesPackagingInput).toHaveValue('10000');
      // Verify that the suppliesPackagingPct element has the updated value

      // find the input element with role textbox and name Input for allowance in dollars
      const allowanceInput = await screen.findByTestId('allowances-input'   );
      //expect(allowanceInput).toBeInTheDocument();
      // Enter the value "70000" into the input element
      await userEvent.type(allowanceInput, '70000');
      // Verify that the input element has the value "70000"
      //expect(allowanceInput).toHaveValue('+70000');
      // Verify that the allowancePct element has the updated value
      // const allowancePct = await screen.findByTestId('allowancesPct-input'   );
      //expect(allowancePct).toBeInTheDocument();
      //expect(allowancePct).toHaveValue('0.6766');

      // find Make a selection dropdown
      const adjustmentReasonDropdown = await screen.findByPlaceholderText('Make a selection');
      expect(adjustmentReasonDropdown).toBeInTheDocument();
      // Simulate clicking on the dropdown to open it
      await userEvent.click(adjustmentReasonDropdown);
      // Verify that the dropdown options are displayed
      const adjustmentReasonOption1 = await screen.findByText('Extreme weather');
      expect(adjustmentReasonOption1).toBeInTheDocument();
      // Simulate selecting the "Extreme weather" option
      await userEvent.click(adjustmentReasonOption1);
      // Verify that the selected option is displayed in the dropdown
      expect(await screen.findByPlaceholderText('Extreme weather')).toBeInTheDocument();
      // Find the textarea element for comments
      const commentTextarea = await screen.findByTestId('Group comment'   );
      expect(commentTextarea).toBeInTheDocument();
      // Simulate typing a comment in the textarea
      await userEvent.type(commentTextarea, 'This is a test comment for Groups 1,2,3 selected');
      // Verify that the textarea contains the typed comment
      expect(commentTextarea).toHaveValue('This is a test comment for Groups 1,2,3 selected');

      // find the button with Save adjustment
      const saveAdjustmentButton = await screen.findByRole('button', { name: 'Save adjustment' }  );
      expect(saveAdjustmentButton).toBeInTheDocument();
      // Simulate clicking on the button to save the adjustment
      await userEvent.click(saveAdjustmentButton);
      // Verify that the save adjustment for form submission was called
      await waitFor(() => {
        expect(mockSaveAdjustmentEdits).toHaveBeenCalledTimes(1);
        // Assert that dispatch was called with setSaveAdjustmentApiStatus(true)
        expect(mockDispatch).toHaveBeenCalledWith(setSaveAdjustmentApiStatus(true));
        const expectedApiBody:Adjustment = {
          ...mock_week123_api_body,
        }

        // Verify that setEditForecastBffBody was called with the correct API body
        // expect(mockSetEditForecastBffBody).toHaveBeenCalledWith(expectedApiBody);
      });

    });
});