import { TimeframeDropdownType } from '../types/timeframeTypes';

export const generateQuartersForYear = (year: number) => {
  const quarters: Array<{
    fiscalYearNumber: number;
    fiscalQuarterNumber: number;
  }> = [];

  for (let quarter = 1; quarter <= 4; quarter++) {
    quarters.push({
      fiscalYearNumber: year,
      fiscalQuarterNumber: parseInt(`${year}${quarter.toString().padStart(2, '0')}`)
    });
  }
  return quarters;
};

export const filterTimeframes = (timeframes: TimeframeDropdownType[], query: string): TimeframeDropdownType[] => {
  if (!query.trim()) return timeframes;

  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

  return timeframes.filter(tf => {
    const name = tf.name.toLowerCase();
    const quarterNum = tf.num.toString();
    const fiscalYear = tf.name.match(/FY(\d+)/)?.[1] || '';

    return searchTerms.every(term =>
      name.includes(term) ||
      quarterNum.includes(term) ||
      fiscalYear.includes(term)
    );
  });
};

export function getCurrentTimeframe(displayDate: any, quartersList: any[]) {
  if (!displayDate || !displayDate.fiscalYearNumber || !displayDate.fiscalQuarterNumber) return undefined;
  const name = `Q${displayDate.fiscalQuarterNumber} FY${displayDate.fiscalYearNumber}`;
  // Optionally, match with quartersList to get additional info (startDate, endDate, etc.)
  const match = quartersList?.find(
    q => q.fiscalYearNumber === displayDate.fiscalYearNumber &&
         (q.fiscalQuarterNumber === displayDate.fiscalQuarterNumber || q.num === displayDate.fiscalQuarterNumber)
  );
  return match || { name, num: displayDate.fiscalQuarterNumber, fiscalYear: displayDate.fiscalYearNumber };
}

/**
 * Converts a quarter number in the format YYYYQQ (e.g., 202501) to a string like 'Q1 FY2025'.
 * @param quarterNumber - The quarter number (e.g., 202501)
 * @returns {string} The formatted quarter string (e.g., 'Q1 FY2025')
 */
export function quarterNumberToLabel(quarterNumber: number | string | null | undefined): string {
  if (quarterNumber === null || quarterNumber === undefined) return '';
  const qStr = quarterNumber.toString();
  if (qStr.length !== 6) return String(quarterNumber);
  const year = qStr.slice(0, 4);
  const quarter = qStr.slice(4, 6).replace(/^0/, '');
  return `Q${quarter} FY${year}`;
} 