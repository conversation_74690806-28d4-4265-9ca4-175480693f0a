import React from 'react';
import "./forecastTimestampsFooter.scss"
interface ForecastTimestampsFooterProps {
  projectionSourceTs?: string;
  dsForecastSourceTs?: string;
  lastYearActualsUpdatedTs?: string;
  formatToPST: (ts: string) => string;
}

const ClockIcon = (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" className="mr-1">
    <path d="M2.67151 2.66406V5.62503M2.67151 5.62503H5.63248M2.67151 5.62503L4.44809 4.02611C5.33935 3.22999 6.47231 2.7565 7.66502 2.68169C8.85773 2.60689 10.041 2.93509 11.0247 3.61361C12.0085 4.29212 12.7356 5.28157 13.0895 6.42305C13.4433 7.56453 13.4032 8.79179 12.9756 9.90775C12.5481 11.0237 11.7579 11.9636 10.732 12.5765C9.70605 13.1893 8.50391 13.4396 7.31862 13.2872C6.13334 13.1347 5.03369 12.5883 4.19628 11.7357C3.35887 10.8831 2.83229 9.77383 2.70112 8.586M8.00125 5.03284V7.9938L10.37 9.17819" stroke="#5A697B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ForecastTimestampsFooter: React.FC<ForecastTimestampsFooterProps> = ({
  projectionSourceTs,
  dsForecastSourceTs,
  lastYearActualsUpdatedTs,
  formatToPST,
}) => (
  <div className="forecast-timestamp-footer flex flex-wrap items-center content-center gap-4 self-stretch py-0 px-5">
    <div className="flex items-center forecast-timestamp-footer__item">
      {ClockIcon}
      {`Last updated Projection ${projectionSourceTs ? formatToPST(projectionSourceTs) : 'NA'}`}
    </div>
    <span className="forecast-timestamp-footer__item">
      {`DS Forecast ${dsForecastSourceTs ? formatToPST(dsForecastSourceTs) : 'NA'}`}
    </span>
   <span className="forecast-timestamp-footer__item">
      {`Actuals  ${lastYearActualsUpdatedTs ? formatToPST(lastYearActualsUpdatedTs) : 'NA'}`}
    </span>
  </div>
);

export default ForecastTimestampsFooter;
