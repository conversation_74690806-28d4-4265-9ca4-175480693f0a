import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './app';
import { StagewiseToolbar } from '@stagewise/toolbar-react';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <StrictMode>
    <BrowserRouter basename="menfpt">
      <App />
    </BrowserRouter>
  </StrictMode>
);

const stagewiseConfig = {
  plugins: []
};

if (process.env.NODE_ENV === 'development') {
  let toolbarRoot = document.getElementById('stagewise-toolbar-root');
  if (!toolbarRoot) {
    toolbarRoot = document.createElement('div');
    toolbarRoot.id = 'stagewise-toolbar-root';
    document.body.appendChild(toolbarRoot);
  }
  const toolbarReactRoot = ReactDOM.createRoot(toolbarRoot);
  toolbarReactRoot.render(
    <StagewiseToolbar config={stagewiseConfig} />
  );
}
