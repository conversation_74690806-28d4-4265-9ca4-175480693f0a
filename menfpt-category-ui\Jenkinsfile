@Library('jenkins-shared-library') _

pipeline {

  options {
    ansiColor('xterm')
  }

  environment {
    appCode               = 'MENFPT'
    appName               = 'menfpt-category-ui'
    registry_type         = 'azure'
    registry              = albUtils.getRegistry(registry_type)
    registryCredential    = albUtils.getRegistryCredentials(registry_type)
    dockerfile            = 'Dockerfile'
    registryRepo          = "/${appCode.toLowerCase()}/${appName.toLowerCase()}"
    CommitId              = 'False'
    tagDetails            = "${env.BRANCH_NAME}_${env.BUILD_NUMBER}"
    imageURI              = "${registry}${registryRepo}:${tagDetails}"
    VeraAppid             = "${appCode}-${appName}-${tagDetails}"
    VeraAppName           = "menfpt-category-ui"
    AbortOnFail           = false
    // CompliancePolicy     = 'warn'
    // VulnerabilityPolicy  = 'warn'
    logLevel              = 'debug'
    RemoveImageOnPublish  = false
  }

  agent { 
    label 'Jenkins_Agent3'
  }

  tools {
    jdk "jdk11"
  }
  
  stages { 
    stage('Build, Test, Coverage and sonar') {
      steps {
        script {
          echo "${env.imageURI}"
          sh 'npm install'
          sh 'npm run build:app'
        }
      } 
    }

  // stage('SonarQube Quality Gate') {
  //     steps {
  //       script {
  //         albBuild.sonarQualityGate(AbortOnFail)
  //       }
  //     }
  //   }
    
    stage('Veracode nodejs Scan') {
      steps {
        script {
          sh """tar -czvf node_modules.tar.gz apps/ package.json package-lock.json"""
          albBuild.veraCodeScannj(VeraAppid, AbortOnFail, VeraAppName)
        }
      }
    }

    stage('Docker build image') {
      steps {
        script {
          dockerImage = albBuild.buildDockerTaggedImage(registry, registryRepo, dockerfile, tagDetails)
        }
      }
    }

    stage('Twistlock Analysis') {
      steps {
        script {
          albBuild.twistscanDockerImage(imageURI, logLevel)
        }
      }
    }


    stage('Docker push image to ACR') {
      steps {
        script {
          albBuild.publishDockerImage(registry, registryCredential, dockerImage, imageURI, removeAfterPublish = RemoveImageOnPublish)
        }
      }
    }

  }

  post {
    failure {
      script {
        albNotify.emailNotify()
      }
    }
    always {
      script {
        albBuild.twistscanPublish()
        addShortText(text: GIT_BRANCH, borderColor: 'BLUE', color: 'GREEN')
      }  
    }
    cleanup {
      script {
        albBuild.removeDockerImage(imageURI)
      }
    }
  }

}