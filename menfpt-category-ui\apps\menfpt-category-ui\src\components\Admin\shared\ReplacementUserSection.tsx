import React, { useImperativeHandle, forwardRef } from 'react';
import { Formik, Form, FormikProps } from 'formik';
import { DynamicFormFields } from './DynamicFormFields';
import { deactivateAndReplaceSchema } from './validationSchemas';
import { CollapseIcon } from './InfoIcon';
import { replacementUserFormConfig } from './ReplacementUserFormConfig';
interface ReplacementUser {
  id: string;
  userName: string;
  role: string;
  ldap: string;
  manager: string;
  department: string;
  desk: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
}
interface ReplacementUserSectionProps {
  users: ReplacementUser[];
  expandedUserId: string | null;
  onToggleExpansion: (userId: string) => void;
  availableDepartments: Array<{ name: string; id: string }>;
  onSubmit?: (values: any) => void;
}
export interface ReplacementUserSectionRef {
  validateAllForms: () => Promise<boolean>;
  submitAllForms: () => void;
}
const ReplacementUserSection = forwardRef<ReplacementUserSectionRef, ReplacementUserSectionProps>(({
  users,
  expandedUserId,
  onToggleExpansion,
  availableDepartments,
  onSubmit
}, ref) => {
  const formRefs = React.useRef<{ [key: string]: FormikProps<any> }>({});
  useImperativeHandle(ref, () => ({
    validateAllForms: async () => {
      const validationPromises = Object.values(formRefs.current).map(async (formikRef) => {
        const errors = await formikRef.validateForm();
        formikRef.setTouched({
          userName: true,
          department: true,
          desk: true,
          effectiveStartDate: true,
          effectiveEndDate: true
        });
        return Object.keys(errors).length === 0;
      });
      
      const results = await Promise.all(validationPromises);
      return results.every(isValid => isValid);
    },
    submitAllForms: () => {
      Object.values(formRefs.current).forEach(formikRef => {
        formikRef.submitForm();
      });
    }
  }), []);
  return (
    <>
      {users.map((user, index) => (
        <div key={user.id}>
          <div className="mt-4">
            <div 
              className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
              onClick={() => onToggleExpansion(user.id)}
            >
              <div className="flex items-center gap-3">
                <div className={`transform transition-transform ${expandedUserId === user.id ? 'rotate-0' : '-rotate-90'}`}>
                  <CollapseIcon />
                </div>
                <div>
                  {expandedUserId === user.id ? (
                    <>
                      <div className="font-semibold text-lg"> Temporary User {index+1}<span className="text-[#bf2912] ">*</span></div>
                      {/* <div className="text-sm text-gray-600">Replacement User {index + 1}</div> */}
                    </>
                  ) : (
                     <div className="leading-6 font-semibold text-lg text-black">
                     Temporary User {index+1}<span className="text-[#bf2912] ">*</span> <span className='text-[#5a697b] '>{user.userName || 'User Name'} - Effective until {user.effectiveEndDate || 'Not Set'}</span>
              </div>
                  )}
                </div>
              </div>
            </div>
            
            {expandedUserId === user.id && (
              <div className="px-4 pb-4 border-t border-gray-100">
                <Formik
                  initialValues={{
                    role: user.role,
                    userName: user.userName,
                    ldap: user.ldap,
                    manager: user.manager,
                    department: user.department,
                    desk: user.desk,
                    effectiveStartDate: user.effectiveStartDate,
                    effectiveEndDate: user.effectiveEndDate
                  }}
                  validationSchema={deactivateAndReplaceSchema}
                  validateOnChange={false}
                  validateOnBlur={true}
                  onSubmit={(values) => {
                    if (onSubmit) {
                      onSubmit(values);
                    } else {
                      console.log('Form values:', values);
                    }
                  }}
                >
                  {(formikProps) => {
                    formRefs.current[user.id] = formikProps;
                    
                    return (
                      <Form>
                        <DynamicFormFields
                          formConfig={{
                            ...replacementUserFormConfig,
                            department: {
                              ...replacementUserFormConfig.department,
                              options: availableDepartments
                            }
                          }}
                          values={formikProps.values}
                          errors={formikProps.errors}
                          handleChange={formikProps.handleChange}
                          setFieldValue={formikProps.setFieldValue}
                          validateField={formikProps.validateField}
                        />
                      </Form>
                    );
                  }}
                </Formik>
              </div>
            )}
          </div>
          <div className="w-[780px] h-px bg-[#c8daeb]" />
        </div>
      ))}
    </>
  );
});
export default ReplacementUserSection;
