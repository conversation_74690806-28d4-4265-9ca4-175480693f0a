import React from 'react';
import Radio from '@albertsons/uds/molecule/Radio';
import { DropdownType } from '../../../interfaces/worksheetFilter';
import { toTitleCase } from '@ui/utils';

interface DeskSelectionProps {
  desks: DropdownType[];
  selectedDesk?: DropdownType;
  onDeskChange: (desk: DropdownType) => void;
}

export const DeskSelection: React.FC<DeskSelectionProps> = ({
  desks,
  selectedDesk,
  onDeskChange,
}) => {
  return (
    <div className="filtered-list-container h-[340px]">
      {desks.map((desk) => (
        <Radio
          key={desk.num}
          label={toTitleCase(desk.name)}
          checked={selectedDesk?.num === desk.num}
          className="BodyDataSRegular h-[44px] pt-3 hover:bg-[#EBF3FA]"
          onChange={() => onDeskChange(desk)}
        />
      ))}
    </div>
  );
};