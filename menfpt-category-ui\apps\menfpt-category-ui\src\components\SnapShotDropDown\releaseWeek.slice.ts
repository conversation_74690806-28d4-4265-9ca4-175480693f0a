import { createGenericSlice } from '../../rtk/rtk-slice';

export const saveWeekSelectionSlice = createGenericSlice({
  name: 'saveWeekSelection_rn',
  initialState: { status: 'loading', data: undefined },
})({
  setSaveWeekSelection(state, { payload }) {
    console.log("state data availbale here is ", state.data);
    state.data = payload;
  },
});

export const { setSaveWeekSelection } = saveWeekSelectionSlice.actions;