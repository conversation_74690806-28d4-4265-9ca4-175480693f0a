import '@testing-library/jest-dom';
import React from 'react';
import { render, screen } from '@testing-library/react';
import EPBCSSyncMonitor from '../EPBCSSyncMonitor';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import menfptCategoryApi from '../../server/Api/menfptCategoryAPI';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useGetJobRunsFromDatabricksQuery } from '../../server/Api/menfptCategoryAPI';

jest.mock('@albertsons/uds/molecule/Tag', () => (props: any) => (
  <div data-testid={`tag-${props.preset}`}>{props.label}</div>
));
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));
jest.mock('../../server/Api/menfptCategoryAPI', () => ({
 ...jest.requireActual('../../server/Api/menfptCategoryAPI'),
 useGetJobRunsFromDatabricksQuery: jest.fn(),
}));

const mockDisplayDate = { fiscalWeekNumber: 104 };
const mockSyncData = {
  getJobRunsFromDatabricks: {
    nextSync: { sync_day: 'Friday', sync_time: '10:00' },
    lastSync: { sync_day: 'Thursday', sync_time: '09:00' },
    syncSessions: [
      { time: '08:00', status: 'Completed', day: 'Monday', date: '2024-06-10' },
      { time: '09:00', status: 'Processing', day: 'Monday', date: '2024-06-10' },
      { time: '10:00', status: 'Failed', day: 'Monday', date: '2024-06-10' },
      { time: '11:00', status: 'Unknown', day: 'Monday', date: '2024-06-10' },
    ],
    syncHistory: {
      weeks: [
        { lastRun: { date: '2024-06-07', time: '10:00', weekNumber: 23 } },
        { lastRun: { date: '2024-05-31', time: '09:00', weekNumber: 22 } },
      ],
    },
  }
};

const mockStore = configureStore({ 
  reducer: { 
    [menfptCategoryApi.reducerPath]: menfptCategoryApi.reducer 
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(menfptCategoryApi.middleware),
});

const renderWithProvider = (ui: React.ReactElement) =>
  render(<Provider store={mockStore}>{ui}</Provider>);

describe('EPBCSSyncMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useSelectorWrap.mockReturnValue({ data: mockDisplayDate });
    useGetJobRunsFromDatabricksQuery.mockImplementation((args) => {
      return { data: mockSyncData, isLoading: false };
    });

    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });

  });

  it('renders sync history rows', async () => {
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync History'))).toBeInTheDocument();
    expect(screen.getByText((content) => content.includes('Last Sync Time'))).toBeInTheDocument();
    expect(screen.getAllByText((content) => content.trim() === 'Week').length).toBeGreaterThan(0);
  });


  it('renders fallback for missing syncData', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: undefined, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findAllByText((content) => content.trim() === '-')).toHaveLength(2);
  });

  it('renders only summary cards and no sync history or sessions when all data is missing', async () => {
    useSelectorWrap.mockReturnValue({ data: undefined });
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: undefined, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText((content) => content.includes('Week 23'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Week 22'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Completed'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Processing'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Fail'))).toBeNull();
  });

  it('renders correct week number when fiscalWeekNumber is a three-digit number', async () => {
    useSelectorWrap.mockReturnValue({ data: { fiscalWeekNumber: 104 } });
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('renders spinner when loading', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: undefined, isLoading: true });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(document.querySelector('.animate-spin-slow')).toBeInTheDocument();
  });

  it('renders fallback for empty syncHistory', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncHistory.weeks = [];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText((content) => content.includes('Week 23'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Week 22'))).toBeNull();
  });

  it('handles syncSessions with missing fields gracefully', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: undefined, status: undefined, day: undefined, date: undefined },
    ];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync Day'))).toBeInTheDocument();
  });

  it('handles missing fiscalWeekNumber', async () => {
    useSelectorWrap.mockReturnValue({ data: {} });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('renders unknown status with no tag', async () => {
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
  });

  it('renders fallback for empty syncHistory', () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncHistory.weeks = [];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText('Week 23')).toBeNull();
    expect(screen.queryByText('Week 22')).toBeNull();
  });

  it('handles missing fiscalWeekNumber', async () => {
    useSelectorWrap.mockReturnValue({ data: {} });
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('shows spinner when loading', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: undefined, isLoading: true });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(document.querySelector('.animate-spin-slow')).toBeInTheDocument();
  });

  it('renders unknown status with no tag', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
  });

  it('renders sync history rows', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync History'))).toBeInTheDocument();
    expect(screen.getByText((content) => content.includes('Last Sync Time'))).toBeInTheDocument();
    expect(screen.getAllByText((content) => content.trim() === 'Week').length).toBeGreaterThan(0);
  });

  it('renders fallback for missing syncData', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: undefined, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findAllByText((content) => content.trim() === '-')).toHaveLength(2);
  });

  it('renders fallback for missing displayDate', async () => {
    useSelectorWrap.mockReturnValue({ data: undefined });
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('renders fallback for missing nextSync/lastSync', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.nextSync = undefined;
    data.getJobRunsFromDatabricks.lastSync = undefined;
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findAllByText((content) => content.trim() === '-')).toHaveLength(2);
  });

  it('renders fallback for empty syncHistory', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncHistory.weeks = [];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText((content) => content.includes('Week 23'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Week 22'))).toBeNull();
  });

  it('handles syncSessions with missing fields gracefully', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: undefined, status: undefined, day: undefined, date: undefined },
    ];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync Day'))).toBeInTheDocument();
  });
  it('renders correct week number when fiscalWeekNumber is undefined', async () => {
    useSelectorWrap.mockReturnValue({ data: {} });
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('renders no tag for unknown status', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: '11:00', status: 'Unknown', day: 'Monday', date: '2024-06-10' }
    ];
    useGetJobRunsFromDatabricksQuery.mockImplementation((args) => ({
      data,
      isLoading: false,
    }));
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
    });

    it('renders the component without crashing', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    const weekHeaders = await screen.findAllByText('Week');
    expect(weekHeaders.length).toBeGreaterThan(0);
    });

    it('renders "-" for missing Next Sync and Last Sync values', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.nextSync = undefined;
    data.getJobRunsFromDatabricks.lastSync = undefined;
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect((await screen.findAllByText('-')).length).toBeGreaterThanOrEqual(2);
    });

    it('renders no Completed/Processing/Failed tags if syncSessions are empty', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-green')).toBeNull();
    expect(screen.queryByTestId('tag-yellow')).toBeNull();
    expect(screen.queryByTestId('tag-red')).toBeNull();
    });

    it('renders no tag for unknown status', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: '11:00', status: 'Unknown', day: 'Monday', date: '2024-06-10' }
    ];
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
    });

  it('shows the correct current week number', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText('4')).toBeInTheDocument();
  });

  it('renders the Sync Day and Scheduled time headers', async () => {
    useGetJobRunsFromDatabricksQuery.mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText('Sync Day')).toBeInTheDocument();
    expect(await screen.findByText('Scheduled time')).toBeInTheDocument();
  });
});
