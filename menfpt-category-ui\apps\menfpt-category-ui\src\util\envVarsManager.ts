export const setEnvParamVal = (name: string, value: string) => {
    // values.set(name, value );
    if (window && window.sessionStorage) {
      window.sessionStorage.setItem(name, value);
    } else {
      throw new Error('WINDOW OR SESSION STORAGE NOT FOUND');
    }
  };
  
  export const getEnvParamVal = (name: string): string => {
    if (window && window.sessionStorage) {
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      if (isLocalhost) {
        // Return a different value if on localhost
        return `http://localhost:4001/menfpt-category-bff`;
      }
  
      return window.sessionStorage.getItem(name) || `${name}_NOT_FOUND`;
    } else {
      throw new Error('WINDOW OR SESSION STORAGE NOT FOUND');
    }
  };