{"name": "menfpt-category-ui-static-server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/menfpt-category-ui-static-server/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/menfpt-category-ui-static-server", "main": "apps/menfpt-category-ui-static-server/src/main.ts", "tsConfig": "apps/menfpt-category-ui-static-server/tsconfig.app.json", "assets": ["apps/menfpt-category-ui-static-server/src/assets"]}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/menfpt-category-ui-static-server/src/environments/environment.ts", "with": "apps/menfpt-category-ui-static-server/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/js:node", "options": {"buildTarget": "menfpt-category-ui-static-server:build"}, "configurations": {"production": {"buildTarget": "menfpt-category-ui-static-server:build:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/menfpt-category-ui-static-server/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/menfpt-category-ui-static-server"], "options": {"jestConfig": "apps/menfpt-category-ui-static-server/jest.config.ts", "passWithNoTests": true}}}, "tags": []}