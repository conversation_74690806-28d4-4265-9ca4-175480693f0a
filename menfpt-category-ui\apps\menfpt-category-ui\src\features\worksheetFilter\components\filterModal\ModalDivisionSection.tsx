import React from 'react';
import DivisionSelector from '../division/divisionSelector';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

interface ModalDivisionSectionProps {
  divisions: DropdownType[];
  selectedDivisions: DropdownType[];
  onDivisionChange: (divisions: DropdownType[]) => void;
}

const ModalDivisionSection: React.FC<ModalDivisionSectionProps> = ({
  divisions,
  selectedDivisions,
  onDivisionChange,
}) => (
  <div className="min-h-0 flex flex-col flex-1 grow overflow-hidden min-w-0 border-r border-[#c8daeb] px-4">
    <DivisionSelector
      divisions={divisions}
      selectedDivisions={selectedDivisions}
      onDivisionChange={onDivisionChange}
    />
  </div>
);

export default ModalDivisionSection; 