import { getEnvParamVal } from './envVarsManager';

export interface Endpoints {
  view: string;
  download: string;
}

export const API_CONFIG = {
  ENV_PARAM_KEY: 'MENFPT_GRAPHQL_ENDPOINT',
  SERVICE_PATH: '/menfpt-category-bff',
  CONTEXT_PATH: '/menfpt-category-ui',
  HELP_ICON: {
    ENDPOINTS: {
      VIEW: 'view',
      DOWNLOAD: 'download'
    }
  }
};

export const isLocalEnvironment = (): boolean => {
  return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
};

export const getEndpoints = (): Endpoints => {
  let url = getEnvParamVal(API_CONFIG.ENV_PARAM_KEY);
  if(url.includes(API_CONFIG.CONTEXT_PATH)) {
    url = url.replace(API_CONFIG.CONTEXT_PATH, API_CONFIG.SERVICE_PATH);
  }
  return {
    view: `${url}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.VIEW}`,
    download: `${url}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.DOWNLOAD}`
  };
};
