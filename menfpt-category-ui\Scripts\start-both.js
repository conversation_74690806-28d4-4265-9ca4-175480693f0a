const os = require('os');
const path = require('path');
const fs = require('fs');
const { spawn, execSync } = require('child_process');

const isWin = os.platform() === 'win32';
const currentDir = process.cwd();
const parentDir = path.dirname(currentDir);
const uiDir = currentDir;
const bffDir = path.join(parentDir, 'menfpt-category-bff');

// Check if BFF directory exists
if (!fs.existsSync(bffDir)) {
    console.error('\x1b[31mError: BFF directory not found at', bffDir, '\x1b[0m');
    console.log('\x1b[33mMake sure menfpt-category-bff is located parallel to menfpt-category-ui\x1b[0m');
    process.exit(1);
}

console.log('\x1b[32mStarting both servers...\x1b[0m');
console.log('\x1b[36mUI Directory:', uiDir, '\x1b[0m');
console.log('\x1b[36mBFF Directory:', bffDir, '\x1b[0m');

try {
    if (isWin) {
        // Windows: Use cmd to start PowerShell windows
        const uiCommand = `cmd /c start powershell -NoExit -Command "cd '${uiDir}'; npm start"`;
        const bffCommand = `cmd /c start powershell -NoExit -Command "cd '${bffDir}'; npm run codegen; npm run start:local"`;
        
        execSync(uiCommand, { stdio: 'inherit' });
        execSync(bffCommand, { stdio: 'inherit' });
    } else {
        // macOS/Linux: Use AppleScript for Terminal
        spawn('osascript', ['-e', 'tell app "Terminal" to do script "cd \\"' + uiDir + '\\" && npm start"'], { stdio: 'inherit', detached: true });
        spawn('osascript', ['-e', 'tell app "Terminal" to do script "cd \\"' + bffDir + '\\" && npm run codegen && npm run start:local"'], { stdio: 'inherit', detached: true });
    }
    
    console.log('\x1b[33mBoth servers are starting in separate windows...\x1b[0m');
} catch (error) {
    console.error('\x1b[31mError:', error.message, '\x1b[0m');
    process.exit(1);
}
