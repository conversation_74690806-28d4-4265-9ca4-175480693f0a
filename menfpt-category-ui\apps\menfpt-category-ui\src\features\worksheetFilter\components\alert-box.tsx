import React from 'react';

interface AlertBoxProps {
    message: string;
    icon?: React.ReactNode;
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
}

const AlertBox: React.FC<AlertBoxProps> = ({
    message,
    icon,
    backgroundColor = '#FAD97F',
    textColor = '#AB4205',
    borderColor = '#AB4205',
}) => {
    return (
        <div
            data-testid="alert-box"
            className="inline-block items-center truncate text-sm/[12px] ml-3 border p-1 rounded"
            style={{
                backgroundColor: backgroundColor,
                color: textColor,
                borderColor: borderColor,
            }}
        >
            {icon && <span className="mr-1">{icon}</span>}
            {message}
        </div>
    );
};

export default AlertBox;