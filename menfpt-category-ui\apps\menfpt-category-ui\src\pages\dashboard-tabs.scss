.dash2Tabs div 
{
    background-color: #F5F5F5;
}

.dash2Tabs div div.flex.justify-between.w-full.h-full 
{
    background-color: white;
    width: 312px;
}


.selected-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 75%;
  height: 4px;
  background-color: #3997EF;
  border-radius: 2px;
}

.tab-with-divider::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background-color: #d1d5db;
}

.tab-padding {
  padding-left: 2rem;
  padding-right: 2rem;

  @media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}

.tabs-container {
  position: relative;
  width: 100%;
}

.sync-button {
  width: 157px;
  height: 32px;
  right: 2rem;
}

.tabs-border-transparent,
.tabs-border-transparent * {
  border-color: transparent !important;
  box-shadow: none !important;
}

.dashboard-tab > div:first-child {
  height: 48px;
  min-height: 48px;
}

.tooltip-container {
  top: var(--tooltip-top, -9999px);
  left: var(--tooltip-left, -9999px);
  opacity: 0;
  transition: opacity 0.1s ease-in-out;
  
  &.positioned {
    opacity: 1;
  }
}