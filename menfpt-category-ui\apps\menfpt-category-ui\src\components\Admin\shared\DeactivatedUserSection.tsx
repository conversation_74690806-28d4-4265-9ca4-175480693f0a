import React from 'react';
import { CollapseIcon } from './InfoIcon';
import InfoTooltip from '../../InfoTooltip';
import { Info } from 'lucide-react';
interface UserData {
  role?: string;
  ldap?: string;
  userName?: string;
  manager?: string;
  department?: string;
  desk?: string;
  effectiveStartDate?: string;
  effectiveEndDate?: string;
}

interface DeactivatedUserSectionProps {
  userData: UserData;
  isExpanded: boolean;
  onToggle: () => void;
}
const DeactivatedUserSection: React.FC<DeactivatedUserSectionProps> = ({
  userData,
  isExpanded,
  onToggle
}) => {
  return (
    <>
      <div className="mt-4">
        <div 
          className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
          onClick={onToggle}
        >
          <div className="flex items-center gap-3">
            <div className={`transform transition-transform ${isExpanded ? 'rotate-0' : '-rotate-90'}`}>
              <CollapseIcon />
            </div>
            <div>
              <div className="leading-6 font-semibold text-lg text-black">
                Deactivated Username<span className="text-[#bf2912] ">*</span> <span className='text-[#5a697b] '>{userData.userName || 'User Name'} - Effective until {userData.effectiveEndDate || 'Not Set'}</span>
              </div>
            </div>
          </div>
        </div>
        
        {isExpanded && (
          <div className="px-4 pb-4 border-t border-gray-100">
            <div className="flex items-start gap-4 w-full mt-4">
              <div className="flex flex-col flex-shrink-0 items-start gap-2 w-[15.4375rem]">
                <div className="flex items-start gap-1">
                  <div className="label text-black text-sm font-bold">Role</div>
                  <InfoTooltip
                    label={"Assigned Role"}
                    icon={<Info size={16} color="#1B6EBB" />}
                    anchor="top"
                    variant="dark"
                    className="uds-tooltip-top"
                  />
                </div>
                <div className="flex items-center gap-1 input  text-[#5a697b]  leading-5">
                  {userData.role || 'Not Set'}
                </div>
              </div>
              <div className="flex flex-col flex-shrink-0 items-start gap-2 w-[6.4375rem]">
                <div className="flex items-start gap-1">
                  <div className="label text-black  text-sm font-bold">LDAP</div>
                    <InfoTooltip
                    label={"Lightweight Directory Access Protocol"}
                    icon={<Info size={16} color="#1B6EBB" />}
                    anchor="top"
                    variant="dark"
                    className="uds-tooltip-top"
                  />
                </div>
                <div className="flex items-center gap-1 input Sans text-[#5a697b]  leading-5">
                  {userData.ldap || 'Not Set'}
                </div>
              </div>
              <div className="flex flex-col items-start gap-2">
                <div className="flex items-start gap-1">
                  <div className="label text-black  text-sm font-bold">Manager Name</div>
                  <div className="text-[#bf2912]  text-sm font-bold">*</div>
                   <InfoTooltip
                    label={"Reporting Manager Name"}
                    icon={<Info size={16} color="#1B6EBB" />}
                    anchor="top"
                    variant="dark"
                    className="uds-tooltip-top"
                  />
                </div>
                <div className="flex items-center gap-1 input  text-[#5a697b]  leading-5">
                  {userData.manager || 'Not Set'}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="w-[780px] h-px bg-[#c8daeb]" />
    </>
  );
};

export default DeactivatedUserSection;
