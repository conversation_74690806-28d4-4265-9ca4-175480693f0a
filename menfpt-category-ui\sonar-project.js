const sonarqubeScanner = require('sonarqube-scanner');
sonarqubeScanner(
  {
    serverUrl: 'https://sonarqube.albertsons.com',
    options: {
      'sonar.projectKey': 'menfpt-category-ui',
      'sonar.sources': './apps/menfpt-category-ui',
      'sonar.exclusions':
        '**/*.test.*, **/*mock*.js,**/*mock*.ts, **/status-constants.tsx, node_modules/**, coverage/**, public/**, build/**, dist/**, reports/**, src/index.tsx, src-msal-server/**',
      'sonar.tests': './apps/menfpt-category-ui',
      'sonar.test.inclusions':
        '**/*.spec.ts,**/*.spec.tsx,**/*.spec.jsx,./apps/**/*.spec.tsx,./apps/**/*.spec.jsx,./apps/**/*.spec.ts',
      'sonar.javascript.lcov.reportPaths':
        'coverage/apps/menfpt-category-ui/lcov.info',
      'sonar.testExecutionReportPaths': 'coverage/test-report.xml',
      // 'sonar.branch.name': 'master',
    },
  },
  () => {}
);