import { USER_ROLES } from './index';
import { competitorPriceConstants } from './menfpt-category-constants';

describe('Constants Index', () => {
  it('should export USER_ROLES from userRoles', () => {
    expect(USER_ROLES).toBeDefined();
    expect(USER_ROLES.PHARMACY).toBe('pharmacyUser');
  });

  it('should export competitorPriceConstants from menfpt-category-constants', () => {
    expect(competitorPriceConstants).toBeDefined();
    expect(competitorPriceConstants.compPriceRequired).toBe('Required Field');
  });

  it('should have all expected exports', () => {
    // Test that all main exports are available
    expect(USER_ROLES).toBeDefined();
    expect(competitorPriceConstants).toBeDefined();
  });
}); 