import React, { useState } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import Button from '@albertsons/uds/molecule/Button';
import Alert from '@albertsons/uds/molecule/Alert';
import { Formik } from 'formik';
import { DynamicFormFields } from './shared/DynamicFormFields';
import { FormActions } from './shared/FormActions';
import { FormConfig } from './shared/formConfig';
import { deactivateUserFormConfig } from './shared/DeactivateUserFormConfig';
interface UserData {
  role?: string;
  ldap?: string;
  userName?: string;
  manager?: string;
  department?: string;
  desk?: string;
  effectiveStartDate?: string;
}
interface DeactivateUserModalProps {
  userData?: UserData;
  isOpen?: boolean;
  onClose?: () => void;
  onBack?: () => void;
  showTriggerButton?: boolean;
}
const DeactivateUserModal: React.FC<DeactivateUserModalProps> = ({
  userData = {},
  isOpen: externalIsOpen,
  onClose: externalOnClose,
  onBack,
  showTriggerButton = true
}) => {
  const [internalIsOpen, setInternalOpen] = useState<boolean>(false);
  
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const handleClose = () => {
    if (externalOnClose) {
      externalOnClose();
    } else {
      setInternalOpen(false);
    }
  };
  const initialValues = {
    role: userData.role || '',
    ldap: userData.ldap || '',
    userName: userData.userName || '',
    manager: userData.manager || '',
    effectiveStartDate: userData.effectiveStartDate || '',
    effectiveEndDate: ''
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={handleClose} width={800}>
        <div className='select-none font-bold text-[28px] mt-4 ml-6'>
          Deactivate User
        </div>
        <div className="w-[800px] h-px bg-[#c8daeb]" />
        <div className='p-4'>
          <Alert isOpen={true} variant='warning' size={"medium"}>
            <div className="San self-stretch text-[#9b3e08] font-nunito font-bold leading-6 ">
              Please review the details below before deactivating the user as this action cannot be undone.
            </div>
          </Alert>
          
          <Formik
            initialValues={initialValues}
            onSubmit={(values) => {
              console.log('Deactivate user with values:', values);
            }}
          >
            {({ values, errors, handleChange, handleSubmit, setFieldValue, validateField }) => (
              <form onSubmit={handleSubmit}>
                <DynamicFormFields
                  formConfig={deactivateUserFormConfig}
                  values={values}
                  errors={errors}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  validateField={validateField}
                />
                
                <FormActions 
                  onBack={onBack}
                  onCancel={handleClose}
                  onSubmit={handleSubmit}
                  showBack={!!onBack}
                  submitText="Confirm"
                />
              </form>
            )}
          </Formik>
        </div>
      </Modal>
    </>
  );
};

export default DeactivateUserModal;