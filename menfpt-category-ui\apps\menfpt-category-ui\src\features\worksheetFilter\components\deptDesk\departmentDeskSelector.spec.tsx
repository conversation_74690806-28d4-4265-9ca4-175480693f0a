import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { MemoryRouter } from 'react-router-dom';
import DepartmentDeskSelector from './departmentDeskSelector';
import * as worksheetFilterRouteUtils from '../../worksheetFilterRouteUtils';
import { DropdownType } from 'apps/menfpt-category-ui/src/interfaces/worksheetFilter';

const mockStore = configureStore([]);

jest.mock('../department/departmentSelection', () => ({
  DepartmentSelection: (props: any) => (
    <div data-testid="department-selection">
      <button onClick={() => props.onDepartmentChange(props.departments[0])}>
        Dept 1
      </button>
      <input
        data-testid="search-input"
        onChange={(e) => props.onSearchChange?.(e.target.value)}
        value={props.searchQuery || ''}
      />
    </div>
  ),
}));

jest.mock('./departmentDeskTabs', () => ({
  DepartmentDeskTabs: (props: any) => (
    <div data-testid="department-desk-tabs">
      <button 
        data-testid="dept-tab-button"
        onClick={() => props.onDepartmentChange(props.departments[0])}
      >
        Tab Dept 1
      </button>
      <button 
        data-testid="desk-tab-button"
        onClick={() => props.onDeskChange(props.desks[0])}
      >
        Tab Desk 1
      </button>
      <input
        data-testid="search-input"
        onChange={(e) => props.onDepartmentSearch?.(e.target.value)}
        value={props.searchQueryDepartment || ''}
      />
    </div>
  ),
}));

jest.mock('../roles/rolesUtils', () => ({
  getSmDataByDivisionAndDept: jest.fn(() => [
    { sm: 'SM1', asmArr: ['ASM1', 'ASM2'] },
    { sm: 'SM2', asmArr: ['ASM3'] },
  ]),
}));

jest.mock('../roles/rolesFilter.slice', () => ({
  setAsmDataForSelectedSm: jest.fn((payload) => ({
    type: 'setAsmDataForSelectedSm',
    payload,
  })),
}));

jest.mock('../../searchUtils', () => ({
  filterBySearch: jest.fn((items, query) => 
    query ? items.filter((item: any) => item.name.toLowerCase().includes(query.toLowerCase())) : items
  ),
}));

const departments = [
  { name: 'Dept 1', num: 1 },
  { name: 'Dept 2', num: 2 },
  { name: 'Test Department', num: 3 },
];

const desks = [
  { name: 'Desk 1', num: 1 },
  { name: 'Desk 2', num: 2 },
  { name: 'Test Desk', num: 3 },
];

describe('DepartmentDeskSelector', () => {
  let store: any;
  const mockDispatch = jest.fn();

  beforeEach(() => {
    store = mockStore({
      departments_rn: { data: departments },
      deptRoleSuggestions_rn: { 
        data: {
          cascadeSearchSelectedItemId: null,
          cascadeSearchSelectedItemType: null,
        } 
      },
      activeTabInFilter_rn: { data: ['department'] },
    });

    // Mock all route utility hooks
    jest.spyOn(worksheetFilterRouteUtils, 'useDeskDisplay').mockReturnValue(false);
    jest.spyOn(worksheetFilterRouteUtils, 'useMultipleDepartmentsSelection').mockReturnValue(false);
    jest.spyOn(worksheetFilterRouteUtils, 'useDisplayDeptRoleCascade').mockReturnValue(false);

    jest.clearAllMocks();
  });

  it('renders DepartmentSelection when isDisplayDesk is false', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });
  it('handles single department selection', () => {
    const onDepartmentChange = jest.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    fireEvent.click(screen.getByText('Dept 1'));
    expect(onDepartmentChange).toHaveBeenCalledWith(departments[0]);
  });

  it('handles multiple department selection', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useMultipleDepartmentsSelection').mockReturnValue(true);
    const onDepartmentChange = jest.fn();
    
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={[departments[0]]}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    fireEvent.click(screen.getByText('Dept 1'));
    expect(onDepartmentChange).toHaveBeenCalled();
  });

  it('handles array department selection directly', () => {
    const onDepartmentChange = jest.fn();
    const testComponent = render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    // Simulate passing an array directly
    const component = testComponent.container.querySelector('[data-testid="department-selection"]');
    if (component) {
      const event = new Event('departmentChange', { bubbles: true });
      (event as any).detail = [departments[0], departments[1]];
      component.dispatchEvent(event);
    }
  });

  it('displays correct header text based on display options', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByText('Department')).toBeInTheDocument();
  });
  it('handles string selectedDepartment conversion', () => {
    const onDepartmentChange = jest.fn();
    
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={'1' as any}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles array of string selectedDepartment conversion', () => {
    const onDepartmentChange = jest.fn();
    
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={['1', '2'] as any}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('filters departments by search query', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    const searchInput = screen.getByTestId('search-input');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    
    expect(searchInput).toHaveValue('test');
  });

  it('initializes SM data when department is pre-selected and cascade is enabled', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useDisplayDeptRoleCascade').mockReturnValue(true);
    
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={departments[0]}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles removing department from multiple selection', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useMultipleDepartmentsSelection').mockReturnValue(true);
    const onDepartmentChange = jest.fn();
    
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={[departments[0]]}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    
    // Click on already selected department to remove it
    fireEvent.click(screen.getByText('Dept 1'));
    expect(onDepartmentChange).toHaveBeenCalled();
  });

  it('handles missing departments in store', () => {
    store = mockStore({
      departments_rn: { data: null },
      deptRoleSuggestions_rn: { data: {} },
      activeTabInFilter_rn: { data: ['department'] },
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });
});

// Additional coverage tests
describe('DepartmentDeskSelector - Additional Coverage', () => {
  const mockStore = configureStore([]);
  const departments = [
    { name: 'Dept 1', num: 1 },
    { name: 'Dept 2', num: 2 },
    { name: 'Test Department', num: 3 },
  ];
  const desks = [
    { name: 'Desk 1', num: 1 },
    { name: 'Desk 2', num: 2 },
    { name: 'Test Desk', num: 3 },
  ];
  let store;
  beforeEach(() => {
    store = mockStore({
      departments_rn: { data: departments },
      deptRoleSuggestions_rn: { data: {} },
      activeTabInFilter_rn: { data: ['department'] },
    });
  });

  it('renders with empty desks array', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={[]}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('renders with empty departments array', () => {
    store = mockStore({
      departments_rn: { data: [] },
      deptRoleSuggestions_rn: { data: {} },
      activeTabInFilter_rn: { data: ['department'] },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles invalid selectedDepartment (not found)', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={999 as any}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('calls onDeskChange with undefined desk', () => {
    const onDeskChange = jest.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={onDeskChange}
          />
        </MemoryRouter>
      </Provider>
    );
    // Simulate desk change with undefined
    onDeskChange(undefined);
    expect(onDeskChange).toHaveBeenCalledWith(undefined);
  });

  it('calls onDepartmentChange with undefined department', () => {
    const onDepartmentChange = jest.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    // Simulate department change with undefined
    onDepartmentChange(undefined);
    expect(onDepartmentChange).toHaveBeenCalledWith(undefined);
  });
  it('handles selectedDepartment as array of IDs (string)', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useDeskDisplay').mockReturnValue(false);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={['1', '2'] as any}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles selectedDepartment as object without num property', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useDeskDisplay').mockReturnValue(false);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={{ name: 'Dept X' } as any}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles selectedDepartment as undefined', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useDeskDisplay').mockReturnValue(false);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={undefined}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles selectedDesk as undefined', () => {
    jest.spyOn(worksheetFilterRouteUtils, 'useDeskDisplay').mockReturnValue(false);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDesk={undefined}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });
});

describe('DepartmentDeskSelector edge cases and branches', () => {
  let store: any;
  beforeEach(() => {
    store = mockStore({
      departments_rn: { data: departments },
      deptRoleSuggestions_rn: { data: {} },
      activeTabInFilter_rn: { data: ['department'] },
    });
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useDeskDisplay').mockReturnValue(false);
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(false);
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useDisplayDeptRoleCascade').mockReturnValue(false);
    jest.clearAllMocks();
  });

  it('maps selectedDepartment from string to object', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={{ name: 'Dept 1', num: 1 }}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('maps selectedDepartment from array of strings to objects', () => {
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(true);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={['1', '2'] as unknown as DropdownType[]}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });
  it('calls onDepartmentChange with array when multiple selection allowed', () => {
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(true);
    const onDepartmentChange = jest.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            selectedDepartment={undefined}
            onDepartmentChange={onDepartmentChange}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    fireEvent.click(screen.getByText('Dept 1'));
    expect(onDepartmentChange).toHaveBeenCalledWith([departments[0]]);
  });

  it('handles cascade selection for sm and ASM', () => {
    store = mockStore({
      departments_rn: { data: departments },
      deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: '1-sm1', cascadeSearchSelectedItemType: 'sm' } },
      activeTabInFilter_rn: { data: ['department'] },
    });
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useDisplayDeptRoleCascade').mockReturnValue(true);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });

  it('handles cascade selection for ASM', () => {
    store = mockStore({
      departments_rn: { data: departments },
      deptRoleSuggestions_rn: { data: { cascadeSearchSelectedItemId: '1-asm1', cascadeSearchSelectedItemType: 'ASM' } },
      activeTabInFilter_rn: { data: ['department'] },
    });
    jest.spyOn(require('../../worksheetFilterRouteUtils'), 'useDisplayDeptRoleCascade').mockReturnValue(true);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DepartmentDeskSelector
            desks={desks}
            onDepartmentChange={jest.fn()}
            onDeskChange={jest.fn()}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByTestId('department-selection')).toBeInTheDocument();
  });
});