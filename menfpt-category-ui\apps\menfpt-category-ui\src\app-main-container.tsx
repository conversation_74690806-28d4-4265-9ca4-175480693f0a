import Spinner from '@albertsons/uds/molecule/Spinner';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { usePdfHelp } from './hooks/usePdfHelp';
import { useGetDisplayDateQuery, useGetUserInfoQuery } from './server/Api/menfptCategoryAPI';
import { UserInfoQuery } from './server/Query/userInfo';
import {
  setDisplayDate,
  setUserInfo,
} from './server/Reducer/menfpt-category.slice';
import { useCleanupOnRouteChange } from './util/useCleanupOnRouteChange';

interface INFPTContainerProps {
  children: JSX.Element[] | JSX.Element;
}

// Generic function to determine if API calls should be skipped based on route patterns
const shouldSkipApiCalls = (pathname: string): boolean => {
  const routesWithoutApiCalls = [
    'access-denied',
    'error',
    '404',
    'unauthorized',
    'maintenance'
  ];
  
  return routesWithoutApiCalls.some(route => pathname.includes(route));
};

const NFPTContainer: React.FunctionComponent<INFPTContainerProps> = ({
  children,
}) => {
  const location = useLocation();
  const skipApiCalls = shouldSkipApiCalls(location.pathname);

  // Skip API calls for routes that don't need them
  const { data: displayDateData, isLoading: isDisplayDateLoading } = useGetDisplayDateQuery({}, {
    skip: skipApiCalls
  });
  const { data: userInfoData, isLoading: isUserInfoLoading } = useGetUserInfoQuery({ query: UserInfoQuery }, {
    skip: skipApiCalls
  });

  usePdfHelp();
  useCleanupOnRouteChange();
  const dispatch = useDispatch();

  // Effect to set display date in Redux store when data is available
  useEffect(() => {
    if (displayDateData && displayDateData.length > 0) {
      dispatch(setDisplayDate(displayDateData[0])); //With empty object payload, the first elem is the current quarter data
    }
  }, [displayDateData, dispatch]);

  // Effect to set user info in Redux store when data is available
  useEffect(() => {
    if (userInfoData?.userInfo) {
      dispatch(setUserInfo(userInfoData.userInfo));
    }
  }, [userInfoData, dispatch]);

  // For routes that don't need API calls, don't show loading spinner
  if (skipApiCalls) {
    return <div className="app-main-container">{children}</div>;
  }

  const isLoading = isDisplayDateLoading || isUserInfoLoading;
  return isLoading ? <Spinner /> : <div className="app-main-container">{children}</div>;

};

export default NFPTContainer;
