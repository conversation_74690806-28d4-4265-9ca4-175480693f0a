import { envVariablesSlice, setEnvVariables, setEnvVariablesError, clearEnvVariables } from './envVariables.slice';

describe('envVariablesSlice', () => {
  const initialState = {
    status: 'loading' as const,
    data: undefined,
  };

  it('should handle initial state', () => {
    expect(envVariablesSlice.reducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle setEnvVariables', () => {
    const mockData = {
      GetEnvVariables: {
        variables: {
          PHARMA_UPLOAD_DAYS: 'THURSDAY,FRIDAY',
          NODE_ENV: 'development',
          PORT: 4001,
        },
      },
    };

    const actual = envVariablesSlice.reducer(initialState, setEnvVariables(mockData));
    expect(actual.data).toEqual(mockData);
    expect(actual.status).toBe('finished');
  });

  it('should handle setEnvVariablesError', () => {
    const actual = envVariablesSlice.reducer(initialState, setEnvVariablesError());
    expect(actual.status).toBe('error');
    expect(actual.data).toBeUndefined();
  });

  it('should handle clearEnvVariables', () => {
    const stateWithData = {
      status: 'finished' as const,
      data: { GetEnvVariables: { variables: { test: 'value' } } },
    };

    const actual = envVariablesSlice.reducer(stateWithData, clearEnvVariables());
    expect(actual.status).toBe('loading');
    expect(actual.data).toBeUndefined();
  });
}); 