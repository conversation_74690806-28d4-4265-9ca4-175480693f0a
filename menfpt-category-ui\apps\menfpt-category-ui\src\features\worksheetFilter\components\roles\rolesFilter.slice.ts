import { createGenericSlice } from '../../../../rtk/rtk-slice';

/* Store the selected SM & the corresponding ASM data */
export const smDataForSelectedDeptSlice = createGenericSlice({
  name: 'smDataForSelectedDept_rn',
  initialState: { status: 'loading', data: {} },
})({
  setSmDataForSelectedDept(state, { payload }) {
    state.data = payload;
  },
});

export const { setSmDataForSelectedDept } = smDataForSelectedDeptSlice.actions;

/* Store the ASM data specific to  selected SM */
export const selectedSmSlice = createGenericSlice({
  name: 'selectedSm_rn',
  initialState: { status: 'loading', data: {} },
})({
  setSelectedSm(state, { payload }) {
    state.data = payload;
  },
});

export const { setSelectedSm } = selectedSmSlice.actions;
