import React, { Suspense } from 'react';
import Spinner from '@albertsons/uds/molecule/Spinner';
import PowerBiReport from './PowerBiReport';

// https://app.powerbi.com/Redirect?action=OpenReport&appId=551119c6-7dd3-4eb0-9fca-d6e9e1fd1cad&reportObjectId=534a541c-1395-43ed-8ae3-c0caee4ec156&ctid=b7f604a0-00a9-4188-9248-42f3a5aac2e9&reportPage=4d9a73fb9dc845aae914&pbi_source=appShareLink&portalSessionId=d5707900-0940-47f0-ad5a-38eb4e87e9cf

const Report: React.FunctionComponent<any> = () => {
    // const powerbiURL = "https://app.powerbi.com/reportEmbed?reportId=86f0b033-a1ac-467b-92d8-9de562a2998d&autoAuth=true&ctid=b7f604a0-00a9-4188-9248-42f3a5aac2e9";
    // const report_id = '534a541c-1395-43ed-8ae3-c0caee4ec156';
    // const ctid = "b7f604a0-00a9-4188-9248-42f3a5aac2e9"
    const report_map = {
      omni_dashboard: '673064ba-b83c-4b46-bff9-8e22374db97d',
      report_nfpt: '96c1a3c0-d69c-4e71-bd09-2ec35c972e9d',
      shoes_for_crews: '534a541c-1395-43ed-8ae3-c0caee4ec156',
    }
    const report_id = report_map.report_nfpt;
    // const shoes_for_crews = `https://app.powerbi.com/reportEmbed?reportId=${report_id}&autoAuth=true&ctid=${ctid}`;
    return (
      <div className='w-screen h-screen'>
        <Suspense
        fallback={
            <Spinner variant="solid" direction="clockwise" size="md" />
        }
      >
        <PowerBiReport reportId={report_id} />
      </Suspense>

      </div>
    );
}

export default Report;
