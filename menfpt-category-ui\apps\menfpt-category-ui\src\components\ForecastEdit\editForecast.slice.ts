import { createGenericSlice } from '../../rtk/rtk-slice';

/* Use this to know if save Adjustment API status */
export const saveAdjustmentApiStatusSlice = createGenericSlice({
  name: 'saveAdjustmentApiStatus_rn',
  initialState: { status: 'loading', data: {} },
})({
  setSaveAdjustmentApiStatus(state, { payload }) {
    state.data = payload;
  },
});

export const { setSaveAdjustmentApiStatus } = saveAdjustmentApiStatusSlice.actions;