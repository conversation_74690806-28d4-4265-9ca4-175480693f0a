import { FormConfig } from "./formConfig";

export const replacementUserFormConfig: FormConfig = {
  role: {
    label: 'Role',
    placeholder: '',
    tooltip: 'Assigned Role',
    type: 'display' as const,
    inputType: 'text' as const,
    required: true,
    disabled: true,
    anchor:'top',
    infoIcon: true
  },
  userName: {
    label: 'Username',
    placeholder: 'Enter First and Last Name',
    tooltip: 'Associate Name',
    type: 'input' as const,
    inputType: 'text' as const,
    required: true,
    disabled: false,
    anchor:'top',
    infoIcon: true
  },
  ldap: {
    label: 'LDAP',
    placeholder: '',
    tooltip: 'Lightweight Directory Access Protocol',
    type: 'display' as const,
    inputType: 'text' as const,
    required: false,
    disabled: true,
    anchor:'right',
    infoIcon: true
  },
  manager: {
    label: 'Manager Name',
    placeholder: '',
    tooltip: 'Reporting Manager Name',
    type: 'display' as const,
    inputType: 'text' as const,
    required: true,
    disabled: true,
    anchor:'top',
    infoIcon: true
  },
    department: {
    label: 'Department ',
    placeholder: 'Choose a Department',
    tooltip: ' User Assigned Oracle Department',
    type: 'select',
    required: false,
    itemText: 'name',
    anchor:'top',
    options: [],
    infoIcon: true
  },
  desk: {
    label: 'Desk ',
    placeholder: 'Choose a Desk(s)',
    tooltip: 'User Assigned Oracle Desks',
    type: 'select',
    required: true,
    itemText: 'name',
    anchor:'top',
    options: [],
    infoIcon: true
  },
  effectiveStartDate: {
    label: 'Select Effective Start Date',
    placeholder: '07/23/2025',
    tooltip: 'Select when user access should start',
    type: 'dateSelector' as const,
    inputType: 'date' as const,
    anchor:'top',
    required: false,
    disabled: false,
    infoIcon: false
  },
  effectiveEndDate: {
    label: 'Select Effective End Date',
    placeholder: '08/23/2025',
    tooltip: 'Select when user access should end',
    type: 'dateSelector' as const,
    inputType: 'date' as const,
    anchor:'top',
    required: false,
    disabled: false,
    infoIcon: false
  }
};