import { ForecastChangeLog } from "../interfaces/forecast-adjustments";

const mockForecastChangeLog: ForecastChangeLog[] = [
    {
        updatedTimestamp: "2023-10-01T12:00:00Z",
        updatedMetrics: [
            {
                fiscalWeekNbrs: "1",
                keyAttributeName: "Department",
                keyAttributeValue: "301",
                reason: "Adjustment",
                comment: "Adjusted due to new data",
                bannerId: ["123", "456"],
                adjustedFields: [
                    {
                        fieldName: "sales",
                        oldValue: "$5,919,090",
                        newValue: "$4,919,090"
                    },
                    {
                        fieldName: "inventory",
                        oldValue: "500",
                        newValue: "450"
                    }
                ]
            }
        ]
    },
    {
        updatedTimestamp: "2023-10-02T12:00:00Z",
        updatedMetrics: [
            {
                fiscalWeekNbrs: "2",
                keyAttributeName: "Desk",
                keyAttributeValue: "302",
                reason: "Correction",
                comment: "Corrected previous error",
                bannerId: ["789"],
                adjustedFields: [
                    {
                        fieldName: "inventory",
                        oldValue: "500",
                        newValue: "450"
                    },
                    {
                        fieldName: "sales",
                        oldValue: "",
                        newValue: "$4,919,090"
                    }
                ]
            },
            {
                fiscalWeekNbrs: "3",
                keyAttributeName: "Desk",
                keyAttributeValue: "303",
                reason: "Adjustment",
                comment: "Adjusted due to new data",
                bannerId: ["123"],
                adjustedFields: [
                    {
                        fieldName: "sales",
                        oldValue: "$5,919,090",
                        newValue: "$4,919,090"
                    },
                    {
                        fieldName: "inventory",
                        oldValue: "500",
                        newValue: "450"
                    }
                ]
            }
        ]
    }
];

export { mockForecastChangeLog };