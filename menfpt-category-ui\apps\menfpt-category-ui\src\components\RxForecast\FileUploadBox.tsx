import React, { useState, DragEvent, ChangeEvent } from "react";

interface FileUploadBoxProps {
  onFileUpload: (files: File[] | FileList) => void;
  onValidationError?: (message: string) => void;
}

const FileUploadBox: React.FC<FileUploadBoxProps> = ({ onFileUpload, onValidationError }) => {
  const [file, setFile] = useState<File | null>(null);

  const uploadIcon = (
    <svg width={25} height={24} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.5 4H7.69998C7.27563 4 6.86866 4.16857 6.5686 4.46863C6.26855 4.76869 6.09998 5.17565 6.09998 5.6V18.4C6.09998 18.8243 6.26855 19.2313 6.5686 19.5314C6.86866 19.8314 7.27563 20 7.69998 20H17.3C17.7243 20 18.1313 19.8314 18.4313 19.5314C18.7314 19.2313 18.9 18.8243 18.9 18.4V8.4L14.5 4Z" fill="#EBF3FA" />
      <path d="M14.1 4V8.8H18.9" fill="#EBF3FA" />
      <path d="M12.5 12V16.8V12Z" fill="#EBF3FA" />
      <path d="M14.9 14.4L12.5 12L10.1 14.4" fill="#EBF3FA" />
      <path d="M14.1 4V8.8H18.9M12.5 12V16.8M12.5 12L14.9 14.4M12.5 12L10.1 14.4M14.5 4H7.69998C7.27563 4 6.86866 4.16857 6.5686 4.46863C6.26855 4.76869 6.09998 5.17565 6.09998 5.6V18.4C6.09998 18.8243 6.26855 19.2313 6.5686 19.5314C6.86866 19.8314 7.27563 20 7.69998 20H17.3C17.7243 20 18.1313 19.8314 18.4313 19.5314C18.7314 19.2313 18.9 18.8243 18.9 18.4V8.4L14.5 4Z" stroke="#5A697B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (validateFile(droppedFile)) {
      setFile(droppedFile);
      onFileUpload([droppedFile]);
    }
  };

  const handleBrowse = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && validateFile(selectedFile)) {
      setFile(selectedFile);
      onFileUpload([selectedFile]);
    }
  };

  const validateFile = (file: File) => {
    const isXlsx =
      file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    const isLt25mb = file.size / 1024 / 1024 <= 25;

    if (!isXlsx) {
      onValidationError?.("Please upload only .xlsx files.");
      return false;
    }
    if (!isLt25mb) {
      onValidationError?.("The file size has been exceeded, please try again.");
      return false;
    }
    return true;
  };

  return (
    <div
      onDragOver={(e) => e.preventDefault()}
      onDrop={handleDrop}
      className="flex justify-center items-center gap-2.5 self-stretch py-6 px-0 rounded border-2 border-dashed border-[#3997ef] bg-[#ebf3fa]"
    >
      <div className="content flex flex-col items-center gap-1">
        <div className="flex items-center pt-1 pb-0 pl-0 pr-3 w-9 h-7">
          {uploadIcon}
        </div>
        <div className="drag_and_drop_or_browse self-stretch text-center">
          <span className="text-[#5a697b] font-['Nunito_Sans'] text-base font-bold leading-5">Drag and drop </span>
          <span className="text-[#5a697b] font-['Nunito_Sans'] text-base font-normal leading-5">document or </span>
          <label className="text-[#1b6ebb] font-['Nunito_Sans'] text-base font-normal leading-5 cursor-pointer ">
            browse
            <input
              type="file"
              className="hidden"
              accept=".xlsx"
              onChange={handleBrowse}
            />
          </label>
        </div>
        <div className="helper_text self-stretch text-[#5a697b] text-center font-nunito text-sm italic">
          Only .xlsx files are allowed. 25 mb maximum file size
        </div>
      </div>
    </div>
  );
};

export default FileUploadBox;
