// Test script to verify the Total row displays with dynamic division count

// Mock functions
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

const formatCurrency = (value) => value === null || value === undefined || value === '' ? '' : `$${value}`;
const getDeptName = (smicData, deptId, fallback) => fallback || `Dept ${deptId}`;
const getDivisionName = (smicData, divisionId, fallback) => {
  const found = smicData.find((item) => String(item.divisionId) === String(divisionId));
  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);
};
const getBannerName = (smicData, divisionId, bannerId, fallback) => {
  const found = smicData.find((item) => 
    String(item.divisionId) === String(divisionId) && 
    String(item.bannerId) === String(bannerId)
  );
  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
};

// Mock mapRow function
const mapRow = (baseRow, data, formatCurrency, type) => ({
  ...baseRow,
  '$ Projection': formatCurrency(data.line1Projection),
  type: type
});

// Updated addDepartmentRows function
const addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { 
    departmentName: isTotal ? (dept.name || 'Total') : `${dept.id} - ${deptName}` 
  };
  
  // Add quarter row
  if (dept.quarter) {
    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));
  }

  // Add periods
  (dept.periods || []).forEach((period) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Period ${period.periodNumber}` },
      period,
      formatCurrency,
      'Period'
    ));
  });

  // Add weeks
  (dept.weeks || []).forEach((week) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Week ${week.weekNumber}` },
      week,
      formatCurrency,
      'Week'
    ));
  });
};

// Updated addRows function
const addRows = (rows, data, smicData, useWeekId = false) => {
  // Handle the new structure with divisions and banners
  if (data.divisions && Array.isArray(data.divisions)) {
    // First, add the Total row data if it exists at the root level
    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {
      const divisionsCount = data.divisions ? data.divisions.length : 0;
      const totalData = {
        id: 'Total',
        name: `Total of ${divisionsCount} division${divisionsCount !== 1 ? 's' : ''}`,
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
      addDepartmentRows(rows, totalData, smicData, useWeekId);
    }
    
    // Then, process divisions and banners
    data.divisions.forEach((division) => {
      const divisionName = getDivisionName(smicData, division.id, division.name);
      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };
      
      // Add division quarter row
      if (division.quarter) {
        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));
      }

      // Process banners within division
      if (division.banners && Array.isArray(division.banners)) {
        division.banners.forEach((banner) => {
          const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);
          const bannerBaseRow = { departmentName: `  ${bannerName}` };
          
          // Add banner quarter row
          if (banner.quarter) {
            rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));
          }

          // Process departments within banner
          if (banner.departments && Array.isArray(banner.departments)) {
            banner.departments.forEach((dept) => {
              addDepartmentRows(rows, dept, smicData, useWeekId);
            });
          }
        });
      }
    });
  } else {
    // Fallback to old structure for backward compatibility
    addDepartmentRows(rows, data, smicData, useWeekId);
  }
};

console.log('Testing Total Row Display with Dynamic Division Count:');
console.log('====================================================');

// Test 1: Single division
console.log('\nTest 1 - Single Division:');
const testData1 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  periods: [{ periodNumber: 202506, line1Projection: 50000 }],
  weeks: [{ weekNumber: 202525, line1Projection: 25000 }],
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 75000 }
  }]
};

const rows1 = [];
addRows(rows1, testData1, [], false);
console.log('First row:', rows1[0].departmentName);
console.log('Expected: "Total of 1 division" ✅');

// Test 2: Multiple divisions
console.log('\nTest 2 - Multiple Divisions:');
const testData2 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 200000 },
  periods: [{ periodNumber: 202506, line1Projection: 100000 }],
  weeks: [{ weekNumber: 202525, line1Projection: 50000 }],
  divisions: [
    {
      id: '34',
      name: 'Division One',
      quarter: { quarterNumber: 202502, line1Projection: 100000 }
    },
    {
      id: '35',
      name: 'Division Two',
      quarter: { quarterNumber: 202502, line1Projection: 100000 }
    }
  ]
};

const rows2 = [];
addRows(rows2, testData2, [], false);
console.log('First row:', rows2[0].departmentName);
console.log('Expected: "Total of 2 divisions" ✅');

// Test 3: Three divisions
console.log('\nTest 3 - Three Divisions:');
const testData3 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 300000 },
  divisions: [
    { id: '34', name: 'Division One' },
    { id: '35', name: 'Division Two' },
    { id: '36', name: 'Division Three' }
  ]
};

const rows3 = [];
addRows(rows3, testData3, [], false);
console.log('First row:', rows3[0].departmentName);
console.log('Expected: "Total of 3 divisions" ✅');

// Test 4: No divisions (edge case)
console.log('\nTest 4 - No Divisions:');
const testData4 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 50000 },
  divisions: []
};

const rows4 = [];
addRows(rows4, testData4, [], false);
console.log('First row:', rows4[0].departmentName);
console.log('Expected: "Total of 0 divisions" ✅');

console.log('\n✅ All tests completed!');
console.log('\n📋 Complete Excel Structure Example:');
console.log('1. Total of 2 divisions (Quarter)    ← Dynamic count based on divisions array');
console.log('2. Period 202506 (Period)');
console.log('3. Week 202525 (Week)');
console.log('4. 34 - Nor. California (Quarter)');
console.log('5.   Acme Stores (Quarter)');
console.log('6. 35 - Southwest (Quarter)');
console.log('7.   Safeway Stores (Quarter)');

console.log('\n🔄 Dynamic Behavior:');
console.log('- 1 division → "Total of 1 division" (singular)');
console.log('- 2 divisions → "Total of 2 divisions" (plural)');
console.log('- 3 divisions → "Total of 3 divisions" (plural)');
console.log('- 0 divisions → "Total of 0 divisions" (edge case)');
