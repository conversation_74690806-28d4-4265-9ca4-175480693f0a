import Alert from '@albertsons/uds/molecule/Alert';
import Link from '@albertsons/uds/molecule/Link';
import React from 'react';
import { getEnvParamVal } from '../../util/envVarsManager';

const RxForecastAlertMessage: React.FC = () => {
  const infoMessage = "You are only uploading pharmacy department projection and forecast.";
  const templateMessage = "Refer this template for uploading the projection and forecast.";
  
  const fileName = "Sample_Pharmacy_File.xlsx";
  
  const handleViewTemplate = () => {
    // Use environment-aware baseUrl and replace UI with BFF
    const baseUrl = getEnvParamVal('MENFPT_GRAPHQL_ENDPOINT');
    const bffUrl = baseUrl.replace('/menfpt-category-ui', '/menfpt-category-bff');
    
    const downloadUrl = `${bffUrl}/api/pharmacy/download/${fileName}`;
    
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    //window.open(downloadUrl, '_blank');
  };
  
  return (
    <Alert isOpen={true} variant="informational" size="medium">
      <div className=" leading-6">
        {infoMessage}
      </div>
      <div className="  text-[#1b6ebb] font-nunito font-semibold leading-5">
        {templateMessage}{" "}
        <Link
          onClick={handleViewTemplate}
          className="cursor-pointer"
          size="medium"
        >
          View Template
        </Link>

      </div>
    </Alert>
  );
};

export default RxForecastAlertMessage;
