import React, { useState } from 'react';
import Radio from '@albertsons/uds/molecule/Radio';
import Search from '@albertsons/uds/molecule/Search';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import {
  TimeframeSelectorProps,
  TimeframeDropdownType,
} from '../../types/timeframeTypes';
import { filterTimeframes } from '../../utils/quarterUtils';
import "./timeframeSelectorStyles.scss";
import PeriodSelector from '../period/periodSelector';
import { SelectableList, SelectableItem } from '../shared';

const TimeframeSelector: React.FC<TimeframeSelectorProps> = ({
  selectedTimeframe,
  onTimeframeChange,
  selectedPeriods = [],
  selectedWeeks = [],
  handlePeriodChange = () => {},
  handleWeeksChange = () => {},
}) => {
  //const [searchQuery, setSearchQuery] = useState<string>('');

  // Get quarters data from Redux store (populated by useCombinedFiltersAndQuarters hook)
  const quartersState = useSelectorWrap('quartersInYr_rn');
  const { data: quarters } = quartersState || { data: [] };

  const uniqueQuartersMap = new Map<string, TimeframeDropdownType>();

  quarters?.forEach((quarter: any) => {
    // Use the correct 6-digit value directly from the backend
    const quarterNum = quarter.fiscalQuarterNumber;
    const quarterName = `Q${quarter.fiscalQuarterNumber.toString().slice(-1)} FY${quarter.fiscalYearNumber}`;
    uniqueQuartersMap.set(quarterName, {
      name: quarterName,
      num: quarterNum, // Use the correct 6-digit value
      startDate: quarter.fiscalQuarterStartDate,
      endDate: quarter.fiscalQuarterEndDate,
      fiscalYear: quarter.fiscalYearNumber,
      fiscalQuarterNumber: quarter.fiscalQuarterNumber,
    });
  });


  const timeframeOptions: TimeframeDropdownType[] = Array.from(
    uniqueQuartersMap.values()
  ).sort((a, b) => {
    const yearA = a.fiscalYear || 0;
    const yearB = b.fiscalYear || 0;
    if (yearB !== yearA) {
      return yearB - yearA;
    }
    return b.num - a.num;
  });

  const handleTimeframeChange = (item: SelectableItem) => {
    const timeframe = item.data as TimeframeDropdownType;
    // Call the parent component's onChange handler
    onTimeframeChange(timeframe);
  };

  // Convert timeframes to SelectableItem format
  const timeframeItems: SelectableItem[] = timeframeOptions.map(timeframe => ({
    id: `${timeframe.fiscalYear}-${timeframe.num}`,
    label: timeframe.name,
    data: timeframe
  }));

  // Convert selected timeframe to SelectableItem format
  const selectedTimeframeItems: SelectableItem[] = selectedTimeframe 
    ? [{
        id: `${selectedTimeframe.fiscalYear}-${selectedTimeframe.num}`,
        label: selectedTimeframe.name,
        data: selectedTimeframe
      }]
    : [];

  // Custom content renderer for nested PeriodSelector
  const renderTimeframeContent = (item: SelectableItem, isSelected: boolean) => {
    if (!isSelected) return null;
    
    const timeframe = item.data as TimeframeDropdownType;
    return (
      <PeriodSelector
        selectedPeriods={selectedPeriods}
        selectedQuarter={timeframe}
        selectedWeeks={selectedWeeks}
        onPeriodChange={handlePeriodChange}
        onWeeksChange={handleWeeksChange}
      />
    );
  };

  return (
    <div className="bg-white flex flex-col flex-1 min-h-0">
      <div className="flex items-center justify-start text-black text-base font-semibold font-['Nunito_Sans'] leading-normal pb-2">
        Timeframe
      </div>
      
      <SelectableList
        items={timeframeItems}
        selectedItems={selectedTimeframeItems}
        isMultipleSelectionAllowed={false}
        onItemChange={handleTimeframeChange}
        emptyMessage="No timeframes found matching"
        className="flex-1"
        listClassName="pr-[5px] self-stretch flex flex-col justify-start items-start nfpt-scrollbar overflow-y-auto nfpt-scrollbar transition-all duration-200"
        itemClassName="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
        renderItemContent={renderTimeframeContent}
      />
    </div>
  );
};

export default TimeframeSelector;
