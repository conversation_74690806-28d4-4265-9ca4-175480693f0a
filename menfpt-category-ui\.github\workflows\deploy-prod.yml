name: deploy-prod
on:
  workflow_dispatch:
    inputs: 
      Tag:
        type: string
        description: 'Tag'    
        required: true
        
jobs:
  prod-deploy:
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-prod-aks-helm.yml@v2
    with:
      ENVIRONMENT: prod
      ACTIONSFILE: "Actionsfile/prod"
      TAG: ${{ github.event.inputs.Tag }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_PROD_WESTUS_CLUSTER_01 }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
  Deploy-Status-check-workflow:
    needs: prod-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/prod" 
    secrets:      
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_PROD_WESTUS_CLUSTER_01 }} 
  rollback-restart-prod:
    needs: [Deploy-Status-check-workflow]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/prod"
      TAG: "${{ github.event.inputs.Tag }}"    
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_PROD_WESTUS_CLUSTER_01 }}
