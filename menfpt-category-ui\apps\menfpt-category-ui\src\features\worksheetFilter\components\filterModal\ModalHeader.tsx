import React from 'react';
import IconButton from '@albertsons/uds/molecule/IconButton';
import { X } from 'lucide-react';

interface ModalHeaderProps {
    title: string;
    subheading?: string;
    onClose: () => void;
}

const ModalHeader: React.FC<ModalHeaderProps> = ({ title, subheading, onClose }) => (
    <div className="flex items-center justify-between px-2 py-1">
        <div className="flex flex-col">
            <h2 className="font-bold text-base leading-6 tracking-normal align-middle">{title}</h2>
            {subheading && (
                <span className="font-normal text-base leading-6 tracking-normal mt-0.5">{subheading}</span>
            )}
        </div>
        <IconButton variant='tertiary' color='neutral' onClick={onClose} aria-label="Close">
            <X />
        </IconButton>
    </div>
);

export default ModalHeader;
