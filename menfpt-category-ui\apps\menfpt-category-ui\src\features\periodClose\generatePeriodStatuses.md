# generatePeriodStatuses

This utility function generates a mapping of fiscal period statuses for use in period close workflows. It is designed to help identify the certification and lockout status of each fiscal period and its constituent weeks, based on period-level dates and the current date.

## Function

```
export function generatePeriodStatuses(calendarDetails: any[]): Record<string, Record<string, string[]>>
```

## Purpose
- To determine the status of each fiscal period (certified, locked, notLocked, notCertifiedButLocked).
- To return, for each status and period, the list of all fiscal week numbers (`fiscalWeekNumber`) that belong to that period.

## Input
- `calendarDetails`: An array of week objects, each containing at least:
  - `fiscalPeriodNumber`: The period this week belongs to
  - `fiscalWeekNumber`: The week number
  - `fiscalPeriodEndDate`, `fiscalPeriodLockoutDate`, `fiscalPeriodCertificationDate`: Period-level dates (same for all weeks in a period)

## Output
- An object with keys: `certified`, `locked`, `notLocked`, `notCertifiedButLocked`.
- Each key maps to an object whose keys are period numbers, and whose values are arrays of week numbers in that period.

### Example Output
```json
{
  "certified": {
    "202501": ["202501", "202502", "202503", "202504"]
  },
  "locked": {
    "202502": ["202505", "202506", "202507", "202508"]
  },
  "notLocked": {
    "202504": ["202513", "202514", "202515", "202516"]
  },
  "notCertifiedButLocked": {
    "202502": ["202505", "202506", "202507", "202508"]
  }
}
```

## Status Logic
- **certified**: Period is certified (current date is on or after the certification date, and the certification date is on or after the period end date). A period that is certified is not considered locked.
- **locked**: Period is locked but **not** certified (current date is on or after the lockout date, and lockout date is on or after the period end date).
- **notLocked**: Period is not locked (either no lockout date is present, the lockout date is before the period end date, or the current date is before the lockout date).
- **notCertifiedButLocked**: Period is locked but not certified. This will contain the same periods as the `locked` status.

## Usage
Call `generatePeriodStatuses(calendarDetails)` with your array of week objects. Use the returned object to determine which weeks in which periods have which statuses. 