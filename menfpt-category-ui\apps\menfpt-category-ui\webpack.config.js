const withModuleFederation = require('@nrwl/react/module-federation');
const moduleFederationConfig = require('./module-federation.config');


module.exports = withModuleFederation({
  ...moduleFederationConfig,
  /*
   * Remote app configuration
   * Note: This should match the name in module-federation.config.js
   */
  name: 'menfpt-category-ui',
  filename: 'remoteEntry.js',
  exposes: {
    './Module': './src/remote-entry.ts',
  },
  shared: (libraryName, defaultConfig) => {
    if (libraryName === 'react' || libraryName === 'react-dom') {
      return {
        ...defaultConfig,
        singleton: true,
        eager: true,
        requiredVersion: false
      };
    }
  },
  watchOptions: {
    ignored: [
      '**/*.spec.ts',
      '**/*.spec.tsx',
      '**/*.test.ts',
      '**/*.test.tsx',
      '**/*.mock.ts',
      '**/__tests__/**',
      '**/__mocks__/**'
    ]
  }
});
