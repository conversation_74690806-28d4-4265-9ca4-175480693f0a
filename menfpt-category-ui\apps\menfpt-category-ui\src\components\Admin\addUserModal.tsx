import React from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import { AddUserForm } from './addUserForm';

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (values: any) => void;
}

const UserModal: React.FC<UserModalProps> = ({ isOpen, onClose, onSubmit }) => {
  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} width={800}>
        <div className='select-none font-bold text-[28px] mt-4 ml-6'>
          Add User
        </div>
        <div className='select-none ml-6'>
          Please enter all the details below for adding a new user
        </div>
        <div className="w-[800px] h-px bg-[#c8daeb]" />
        <div className='p-4'>
          <AddUserForm 
            onCancel={onClose}
            onSubmit={(values) => {
              if (onSubmit) {
                onSubmit(values);
              }
              onClose();
            }}
          />
        </div>
      </Modal>
    </>
  );
};
export default UserModal;