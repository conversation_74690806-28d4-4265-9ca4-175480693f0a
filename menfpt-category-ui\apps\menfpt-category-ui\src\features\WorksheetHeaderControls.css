.categories-scrollbar::-webkit-scrollbar {
    width: thin;
}

.categories-scrollbar::-webkit-scrollbar-track {
   background: transparent;
}

.categories-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #888 transparent; /* thumb color, track color */
}

.dateHeader
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    letter-spacing: 0px;
    vertical-align: middle;
}



.ChangeButton
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0%;
}


.ResultFilter
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;
}


.ResultFilterResult
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;

}

.AdjustmentHeader
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0px;
}

.help-icon {
    width: 32px; 
    height: 32px; 
    border-radius: 8px; 
    cursor: pointer; 
    display: inline-block; 
    margin-left: 10px; 
    position: relative; 
}