import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HistoryTimeline from '../historyTimeline';
import { ForecastChangeLog } from '../../interfaces/forecast-adjustments';

jest.mock('react-tooltip', () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(() => ({
    data: { fiscalQuarterStartDate: '2025-01-01' },
  })),
}));

const mockData: ForecastChangeLog[] = [
  {
    updatedBy: '<PERSON>',
    updatedTimestamp: '2025-01-15T10:00:00Z',
    updatedMetrics: [
      {
        fiscalWeekNbrs: '202501',
        bannerId: ["123", "456"],
        adjustedFields: [
          {
            fieldName: 'line_1_sales_to_public_nbr',
            oldValue: '100.1234',
            newValue: '200.5678',
          },
          {
            fieldName: 'line_7_retail_allowances_nbr',
            oldValue: '50.1234',
            newValue: '75.5678',
          },
        ],
        reason: 'Adjustment Reason',
        comment: 'Adjustment Comment',
      },
    ],
    editedColumns: 'salespublic|allowances',
  },
];

describe('HistoryTimeline Component', () => {
  it('renders the timeline with data', () => {
    render(<HistoryTimeline data={mockData} />);
    expect(screen.getByText('Details')).toBeTruthy();
    expect(screen.getByText('Before')).toBeTruthy();
    expect(screen.getByText('After')).toBeTruthy();
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('Week 1')).toBeTruthy();
  });

  it('renders "No Records Found" when data is empty', () => {
    render(<HistoryTimeline data={[]} />);
    expect(screen.getByText('No Records Found For The Selected Filters')).toBeTruthy();
  });

  it('renders the tooltip with reason and comment', () => {
    render(<HistoryTimeline data={mockData} />);
    expect(screen.getByText('Reason: Adjustment Reason')).toBeTruthy();
    expect(screen.getByText('Comment: Adjustment Comment')).toBeTruthy();
  });

  it('applies a line-through style for struck fields', () => {
    render(<HistoryTimeline data={mockData} />);
    // Check for line-through on struck fields
    const struckElements = screen.getAllByText('$100.1234');
    expect(struckElements[0].parentElement?.className.includes('line-through')).toBeTruthy();
  });

  it('checks for non-struck fields', () => {
    render(<HistoryTimeline data={mockData} />);
    // Check for non-struck fields in the "After" column
    const afterValues = screen.getAllByText('$200.5678');
    expect(afterValues.length).toBeGreaterThan(0);
    // Make sure they don't have line-through
    expect(afterValues[0].parentElement?.className.includes('line-through')).toBeFalsy();
  });

  it('formats the timestamp to PST', () => {
    render(<HistoryTimeline data={mockData} />);
    // The exact time format might vary based on timezone settings
    const dateElements = screen.getAllByText(/\d{2}\/\d{2}\/\d{2}/);
    expect(dateElements.length).toBeGreaterThan(0);
  });

  it('renders timeline with multiple items', () => {
    const multiData = [
      ...mockData,
      {
        updatedBy: 'Jane Smith',
        updatedTimestamp: '2025-02-01T10:00:00Z',
        updatedMetrics: [
          {
            fiscalWeekNbrs: '202502',
            adjustedFields: [
              {
                fieldName: 'line_5_markdowns_nbr',
                oldValue: '10.0000',
                newValue: '20.0000',
              },
            ],
            reason: 'Markdown Reason',
            comment: 'Markdown Comment',
          },
        ],
        editedColumns: 'marksdown',
      },
    ];
    render(<HistoryTimeline data={multiData} />);
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('Jane Smith')).toBeTruthy();
    expect(screen.getByText('Week 1')).toBeTruthy();
    expect(screen.getByText('Week 1')).toBeTruthy();
  });

  it('handles missing calendarWeek data gracefully', () => {
    jest.mock('../../rtk/rtk-utilities', () => ({
      useSelectorWrap: jest.fn(() => ({})),
    }));
    render(<HistoryTimeline data={mockData} />);
    expect(screen.getByText('-')).toBeTruthy();
  });

  it('handles editedColumns with no strike-through', () => {
    const noStrikeData = [{
      ...mockData[0],
      editedColumns: '',
    }];
    render(<HistoryTimeline data={noStrikeData} />);
    const struckElements = screen.getAllByText('$100.1234');
    expect(struckElements[0].parentElement?.className.includes('line-through')).toBeFalsy();
  });

  it('handles edge date (not matching any week)', () => {
    const edgeData = [{
      ...mockData[0],
      updatedTimestamp: '2024-12-31T23:59:59Z', 
    }];
    render(<HistoryTimeline data={edgeData} />);
    expect(screen.getByText('-')).toBeTruthy();
  });
});