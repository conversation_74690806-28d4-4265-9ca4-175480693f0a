import { createGenericSlice } from '../../rtk/rtk-slice';

// Define the full structure for prev quarter tab data
export interface PrevQuarterTabData {
  isDisplayLastQtrTab: boolean;
  quarterLockoutDate: any;
  quarterCertificationDate: any;
  quarterStatus: 'certified' | 'locked' | 'open' | undefined;
  fiscalQuarterStartDate: any;
  fiscalQuarterEndDate: any;
  lastQtrNbr: any;
}

// Slice for saving previous quarter tab data, used to display the tab in adjustment page
export const prevQuarterTabSlice = createGenericSlice({
  name: 'prevQuarterTab_rn',
  initialState: { status: 'loading', data: {
    isDisplayLastQtrTab: false,
    quarterLockoutDate: null,
    quarterCertificationDate: null,
    quarterStatus: undefined,
    fiscalQuarterStartDate: null,
    fiscalQuarterEndDate: null,
    lastQtrNbr: null,
    lastQtrWeekNbr: null,
  } as PrevQuarterTabData },
})({
  setPrevQuarterTabData(state, { payload }: { payload: PrevQuarterTabData }) {
    state.data = payload;
  },
  clearPrevQuarterTabData(state) {
    state.status = 'loading';
    state.data = {
      isDisplayLastQtrTab: false,
      quarterLockoutDate: null,
      quarterCertificationDate: null,
      quarterStatus: undefined,
      fiscalQuarterStartDate: null,
      fiscalQuarterEndDate: null,
      lastQtrNbr: null,
    };
  },
});

export const { setPrevQuarterTabData, clearPrevQuarterTabData } = prevQuarterTabSlice.actions;
