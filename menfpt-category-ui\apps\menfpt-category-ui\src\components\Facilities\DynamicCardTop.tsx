const DynamicCardTop = () => (  
  <div className="dynamic-card-top flex flex-shrink-0 items-start py-1 pl-4 pr-6 h-14 rounded-tl-lg rounded-tr-lg border-t border-t-[#c8daeb] border-r border-r-[#c8daeb] border-l border-l-[#c8daeb] bg-[#fdfdff] w-full">
    <div className="flex items-center gap-3 py-3 px-0 h-12">     
      <div className="flex items-center gap-3">       
        <div className="flex items-center header Sans text-[#2b303c] font-['nunito'] text-lg font-extrabold leading-6"></div>
      </div>
      <div className="w-3 h-12" />
    </div>
    <div className="flex justify-end items-center gap-2 py-3 px-0 h-12">
      <div className="flex flex-col items-start gap-2.5 h-12">
        <div className="flex flex-shrink-0 items-center gap-2 py-3 px-0 h-12 text Sans text-[#5a697b] text-right font-['nunito'] text-[.8125rem]"></div>
      </div>
    </div>
  </div>
);

export default DynamicCardTop;