import { render, screen, act } from '@testing-library/react';
import { configureStore } from '@reduxjs/toolkit';
import { Provider } from 'react-redux';
import HistoryDrawer from '../historyDrawer';
import '@testing-library/jest-dom';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useGetPublishHistoryMutation } from '../../server/Api/menfptCategoryAPI';

jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('../../server/Api/menfptCategoryAPI', () => ({
  useGetPublishHistoryMutation: jest.fn(() => [jest.fn()]),
}));

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

window.ResizeObserver = ResizeObserver as any;

describe('History Drawer Component Test Suite', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        appliedFilter_rn: (state = { data: null }) => state,
        displayDate_rn: (state = { data: null }) => state,
      },
    });

    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      if (key === 'appliedFilter_rn') {
        return {
          data: {
            division: [{ name: '5 - DENVER', num: 5 }],
            department: { name: '301 - GROCERY', num: 301 },
            category: [
              { name: '105 - CATEGORY NAME', num: 105 },
              { name: '106 - ANOTHER CATEGORY', num: 106 },
            ],
            desk: { name: 'DESK NAME', num: 'ajoh356' },
          },
        };
      }
      if (key === 'displayDate_rn') {
        return {
          data: {
            fiscalWeekNumber: 202505,
            fiscalYearNumber: 2025,
          },
        };
      }
      return { data: null };
    });
  });

  it('should render the history drawer', async () => {
    await act(async () => {
      render(
        <Provider store={store}>
          <HistoryDrawer isOpen={true} setOpen={() => {}} />
        </Provider>
      );
    });

    expect(screen.getByText('Audit History')).toBeTruthy();
  });

  it('should show correct text when no data found', async () => {
    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      if (key === 'appliedFilter_rn') {
        return {
          data: {
            division: [],
            department: null,
            category: [],
            desk: null,
          },
        };
      }
      if (key === 'displayDate_rn') {
        return {
          data: {
            fiscalWeekNumber: null,
            fiscalYearNumber: null,
          },
        };
      }
      return { data: null };
    });

    await act(async () => {
      render(
        <Provider store={store}>
          <HistoryDrawer isOpen={true} setOpen={() => {}} />
        </Provider>
      );
    });

    expect(screen.getByText('No Records Found For The Selected Filters')).toBeTruthy();
  });
});