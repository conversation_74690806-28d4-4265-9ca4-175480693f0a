import { renderHook } from '@testing-library/react-hooks';
import * as rtkUtils from '../../rtk/rtk-utilities';
import * as lastQrtrUtils from './periodCloseLastQrtr.utils';
import * as calendarUtils from '../calendarServiceUtils';
import { usePeriodCloseEffect } from './usePeriodCloseEffect';

describe('usePeriodCloseEffect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns lastQuarterNbr from useLastQuarterNbr', () => {
    jest.spyOn(rtkUtils, 'useSelectorWrap').mockReturnValue({ data: { fiscalQuarterNumber: 2, fiscalQuarterStartDate: '2024-01-01' } });
    jest.spyOn(lastQrtrUtils, 'getLastQuarterNbr').mockReturnValue(1);
    const { result } = renderHook(() => usePeriodCloseEffect({
      calendarApiResp: {},
      calendarApiPayload: {},
      dispatch: jest.fn(),
      qrtrNbrDisplayedInTable: 2,
    }));
    expect(result.current).toBe(1);
  });

  it('calls handleCalendarApiResp if type is quarterDisplayedInTable_periodClose', () => {
    const handleCalendarApiResp = jest.spyOn(calendarUtils, 'handleCalendarApiResp').mockImplementation(jest.fn());
    jest.spyOn(rtkUtils, 'useSelectorWrap').mockReturnValue({ data: { fiscalQuarterNumber: 2, fiscalQuarterStartDate: '2024-01-01' } });
    jest.spyOn(lastQrtrUtils, 'getLastQuarterNbr').mockReturnValue(1);
    renderHook(() => usePeriodCloseEffect({
      calendarApiResp: {},
      calendarApiPayload: { type: 'quarterDisplayedInTable_periodClose' },
      dispatch: jest.fn(),
      qrtrNbrDisplayedInTable: 2,
    }));
    expect(handleCalendarApiResp).toHaveBeenCalled();
  });

  it('does not call handleCalendarApiResp if type is not quarterDisplayedInTable_periodClose', () => {
    const handleCalendarApiResp = jest.spyOn(calendarUtils, 'handleCalendarApiResp').mockImplementation(jest.fn());
    jest.spyOn(rtkUtils, 'useSelectorWrap').mockReturnValue({ data: { fiscalQuarterNumber: 2, fiscalQuarterStartDate: '2024-01-01' } });
    jest.spyOn(lastQrtrUtils, 'getLastQuarterNbr').mockReturnValue(1);
    renderHook(() => usePeriodCloseEffect({
      calendarApiResp: {},
      calendarApiPayload: { type: 'other' },
      dispatch: jest.fn(),
      qrtrNbrDisplayedInTable: 2,
    }));
    expect(handleCalendarApiResp).not.toHaveBeenCalled();
  });
}); 