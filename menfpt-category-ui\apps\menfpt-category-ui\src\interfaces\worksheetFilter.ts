export interface DropdownType {
    name: string;
    num: number;
    deskNameArr?: string[];
    banners?: DropdownType[];
  }

export interface TitleFormData {
    division: DropdownType[];
    department: DropdownType[];
    smics: DropdownType[];
    quarter: DropdownType[];
}

export interface SmicDataDetails {
    divisionId: number;
    divisionName: string;
    bannerId?: number;
    bannerName?: string;
    deptId: string;
    deptName: string;
    smicGroupCd: number;
    smicGroupDesc: string;
    smicCategoryCd: number;
    smicCategoryDesc: string;
    smicCategoryId: number;
    retailSectionCd: string;
    retailSectionName: string;
    deskId: string;
    deskName: string;
}

export interface WorkSheetFilterResponse {
    smicData: SmicDataDetails[];
    userId: string;
    userName: string;
    userEmail: string;
    userRole: string;
    createdTimeStamp: string;
    updatedTimeStamp: string;
    createdBy: string;
    updatedBy: string;
}