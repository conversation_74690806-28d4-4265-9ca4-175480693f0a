import { createGenericSlice } from "apps/menfpt-category-ui/src/rtk/rtk-slice";

export const bannerDataForSelectedDivisionsSlice = createGenericSlice({
  name: 'bannerDataForSelectedDivisions_rn',
  initialState: { status: 'loading', data: {} },
})({
  setBannerDataForSelectedDivisions(state, { payload }) {
    state.data = payload;
  },
});

export const { setBannerDataForSelectedDivisions } = bannerDataForSelectedDivisionsSlice.actions;