import { utcToZonedTime, format } from 'date-fns-tz';

const PST_TIMEZONE = 'America/Los_Angeles';

/**
 * Calculates the relative time difference between two timestamps in PST
 * @param updatedTs - The timestamp when something was updated
 * @param currentTs - The current timestamp (defaults to now)
 * @returns Formatted relative time string (e.g., "Just now", "2 minutes ago", "1 week ago")
 */
export const getRelativeTime = (updatedTs: string, currentTs?: string): string => {
  if (!updatedTs) return '';

  try {
    const updatedDate = new Date(updatedTs);
    const currentDate = currentTs ? new Date(currentTs) : new Date();
    const updatedPST = utcToZonedTime(updatedDate, PST_TIMEZONE);
    const currentPST = utcToZonedTime(currentDate, PST_TIMEZONE);
    const diffMs = currentPST.getTime() - updatedPST.getTime();

    if (diffMs < 60000) { 
      return 'just now';
    }
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);
    if (diffYears > 0) {
      return diffYears === 1 ? '1 year ago' : `${diffYears} years ago`;
    } else if (diffMonths > 0) {
      return diffMonths === 1 ? '1 month ago' : `${diffMonths} months ago`;
    } else if (diffWeeks > 0) {
      return diffWeeks === 1 ? '1 week ago' : `${diffWeeks} weeks ago`;
    } else if (diffDays > 0) {
      return diffDays === 1 ? '1 day ago' : `${diffDays} days ago`;
    } else if (diffHours > 0) {
      return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
    } else {
      return diffMinutes === 1 ? '1 minute ago' : `${diffMinutes} minutes ago`;
    }
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return '';
  }
};

/**
 * Formats a timestamp to PST timezone
 * @param timestamp - The timestamp to format
 * @param formatPattern - The format pattern (defaults to 'MMM d, yyyy')
 * @returns Formatted date string in PST
 */
export const formatToPST = (timestamp: string, formatPattern: string = 'MMM d, yyyy'): string => {
  if (!timestamp) return '';
  
  try {
    const date = new Date(timestamp);
    const pstDate = utcToZonedTime(date, PST_TIMEZONE);
    return format(pstDate, formatPattern, { timeZone: PST_TIMEZONE });
  } catch (error) {
    console.error('Error formatting date to PST:', error);
    return '';
  }
};
