import { DropdownType } from '../../interfaces/worksheetFilter';
import { routeConstants } from '../../util/routeConstants';
import { worksheetFilterConfig } from './worksheetFilterConfig';
import { extractCurrentRoute } from './worksheetFilterRouteUtils';



export const getStorageKeyByRoute = ()  => {
  const currentRoute = extractCurrentRoute(window.location.pathname);
  // Return storage key based on route - defaults to adjustment page key
  return currentRoute === routeConstants.dashboard 
    ? worksheetFilterConfig.lsKeyDashboardPg
    : worksheetFilterConfig.lsKeyAdjustmentPg;
 
};




/**
 * Filters divisions to only include those that exist in the valid divisions list
 * @param divisions The divisions to filter
 * @param filtersList The list of filters containing valid divisions
 * @returns Filtered array of divisions
 */










export const getCategoriesForDepartmentReltdFilters = ({
  FiltersList,
  selectedDivision,
  selectedDepartment,
  selectedSm = null,
  selectedAsm = null,
}: {
  FiltersList: any[];
  selectedDivision: DropdownType | null;
  selectedDepartment: DropdownType | DropdownType[];
  selectedSm: string | null;
  selectedAsm: string | null;
}): DropdownType[] => {
  if (!selectedDepartment || !FiltersList?.length) {
    return [];
  }

  const categoryList: DropdownType[] = [];

  // Function to form desk name string based on filter selections
  const formDeskNameFromFilterSelection = ({
    selectedDivision,
    selectedDepartment,
    selectedSm,
    selectedAsm,
  }: {
    selectedDivision: DropdownType | null;
    selectedDepartment: DropdownType | null;
    selectedSm: string | null;
    selectedAsm: string | null;
  }): string => {
    /*
    Form deskNameStr based on the filter selections
    If any variable is null, then the variable and its preceding variable should not be added to the below variable.

     const deskNameStr = `${selectedDivision?.num}-${selectedDepartment?.num}-${selectedSm}-${selectedAsm}`;
    */
    const parts: (string | number)[] = [];

    if (selectedDivision?.num) {
      parts.push(selectedDivision.num);

      if (selectedDepartment?.num) {
        parts.push(selectedDepartment.num);

        if (selectedSm) {
          parts.push(selectedSm);

          if (selectedAsm) {
            parts.push(selectedAsm);
          }
        }
      }
    }

    return parts.join('-');
  };

  // Handle both single department and array of departments
  const departmentNums = Array.isArray(selectedDepartment)
    ? selectedDepartment.map((dept) => dept.num)
    : [selectedDepartment.num];

  // Generate deskName string for filtering if needed
  const singleDepartment =
    Array.isArray(selectedDepartment) && selectedDepartment.length === 1
      ? selectedDepartment[0]
      : !Array.isArray(selectedDepartment)
      ? selectedDepartment
      : null;

  const deskNameTobeMatched = formDeskNameFromFilterSelection({
    selectedDivision,
    selectedDepartment: singleDepartment,
    selectedSm,
    selectedAsm,
  });

  FiltersList.forEach((item: any) => {
    // First check if we have any matching desk names to apply
    if (deskNameTobeMatched && item?.deskNameArr?.some(dn => dn.includes(deskNameTobeMatched))) {
      const cat = {
        name: `${item?.smicCategoryId} - ${item?.smicCategoryDesc}`,
        num: item?.smicCategoryId,
      };
      if (!categoryList.some((c) => c.num === cat.num)) {
        categoryList.push(cat);
      }
    }
    // Otherwise filter by department IDs (division IDs are already handled in desk name matching)
    else if (departmentNums.includes(Number(item.deptId))) {
      const cat = {
        name: `${item?.smicCategoryId} - ${item?.smicCategoryDesc}`,
        num: item?.smicCategoryId,
      };
      if (!categoryList.some((c) => c.num === cat.num)) {
        categoryList.push(cat);
      }
    }
  });
  return categoryList;
};

export const getCategoriesForDesk = (
  FiltersList: any[],
  selectedDesk: DropdownType
): DropdownType[] => {
  if (!selectedDesk || !FiltersList?.length) {
    return [];
  }

  const categoryList: DropdownType[] = [];
  FiltersList.forEach((item: any) => {
    if (item?.deskNameArr?.includes(selectedDesk?.name)) {
      const cat = {
        name: `${item?.smicCategoryId} - ${item?.smicCategoryDesc}`,
        num: item?.smicCategoryId,
      };
      if (!categoryList.some((c) => c.num === cat.num)) {
        categoryList.push(cat);
      }
    }
  });
  return categoryList;
};

export const getCategoriesForDivisionDeptSMASM = (
  FiltersList: any[],
  divisionId: string,
  deptId: string,
  sm: string,
  asm: string
): DropdownType[] => {
  if (!divisionId || !deptId || !sm || !asm || !FiltersList?.length) {
    return [];
  }

  const deskName = `${divisionId}-${deptId}-${sm}-${asm}`;
  const categoryList: DropdownType[] = [];

  FiltersList.forEach((item: any) => {
    if (item?.deskNameArr?.includes(deskName)) {
      const cat = {
        name: `${item?.smicCategoryId} - ${item?.smicCategoryDesc}`,
        num: item?.smicCategoryId,
      };
      if (!categoryList.some((c) => c.num === cat.num)) {
        categoryList.push(cat);
      }
    }
  });

  return categoryList;
};
