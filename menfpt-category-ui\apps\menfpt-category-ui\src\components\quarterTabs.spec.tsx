import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { QuarterTabs } from './quarterTabs';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { useCurrentQuarterNbr } from '../features/calendarServiceUtils';
import { setActiveQuarterTab } from './activeQuarterTab.slice';
import { useDispatch } from 'react-redux';

jest.mock('../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));
jest.mock('../features/calendarServiceUtils', () => ({
  useCurrentQuarterNbr: jest.fn(),
}));
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));
jest.mock('./activeQuarterTab.slice', () => ({
  setActiveQuarterTab: jest.fn(),
}));
jest.mock('@albertsons/uds/molecule/Tooltip', () => ({
  __esModule: true,
  default: ({ label, children }: any) => (
    <div>
      {children}
      <div>{label}</div>
    </div>
  ),
}));

describe('QuarterTabs', () => {
  const mockDispatch = jest.fn();
  beforeEach(() => {
    jest.clearAllMocks();
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
  });

  it('should not render if data is missing', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: null });
    render(<QuarterTabs onQuarterChange={() => {}} />);
    expect(screen.queryByTestId('quarter-tabs')).toBeNull();
  });

  it('should render tabs with correct labels', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        isDisplayLastQtrTab: true,
        lastQtrNbr: 202404,
        isNotLocked: true,
        quarterLockoutDate: '2025-07-01',
      },
    });
    (useCurrentQuarterNbr as jest.Mock).mockReturnValue(202405);
    render(<QuarterTabs onQuarterChange={() => {}} />);
    expect(screen.getByTestId('quarter-tabs')).toBeInTheDocument();
    expect(screen.getByTestId('quarter-tab-last')).toHaveTextContent('Q4 FY2024');
    expect(screen.getByTestId('quarter-tab-current')).toHaveTextContent('Q5 FY2024');
  });

  it('should call onQuarterChange and dispatch correct action when last quarter tab is clicked', () => {
    const onQuarterChange = jest.fn();
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        isDisplayLastQtrTab: true,
        lastQtrNbr: 202404,
        isNotLocked: true,
        quarterLockoutDate: '2025-07-01',
      },
    });
    (useCurrentQuarterNbr as jest.Mock).mockReturnValue(202405);
    render(<QuarterTabs onQuarterChange={onQuarterChange} />);
    // Click the first tab (last quarter)
    fireEvent.click(screen.getByText('Q4 FY2024'));
    expect(onQuarterChange).toHaveBeenCalledWith(202404);
    expect(mockDispatch).toHaveBeenCalledWith(setActiveQuarterTab('lastQtr'));
  });

  it('should call onQuarterChange and dispatch correct action when current quarter tab is clicked', () => {
    const onQuarterChange = jest.fn();
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        isDisplayLastQtrTab: true,
        lastQtrNbr: 202404,
        isNotLocked: true,
        quarterLockoutDate: '2025-07-01',
      },
    });
    (useCurrentQuarterNbr as jest.Mock).mockReturnValue(202405);
    render(<QuarterTabs onQuarterChange={onQuarterChange} />);
    // Click the second tab (current quarter)
    fireEvent.click(screen.getByText('Q5 FY2024'));
    expect(onQuarterChange).toHaveBeenCalledWith(202405);
    expect(mockDispatch).toHaveBeenCalledWith(setActiveQuarterTab('currentQtr'));
  });

  it('should show correct tooltip message for unlocked and locked states', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        isDisplayLastQtrTab: true,
        lastQtrNbr: 202404,
        isNotLocked: true,
        quarterLockoutDate: '2025-07-01',
      },
    });
    (useCurrentQuarterNbr as jest.Mock).mockReturnValue(202405);
    render(<QuarterTabs onQuarterChange={() => {}} />);
    expect(screen.getByText((content) =>
      content.includes("You can edit the last period")
    )).toBeInTheDocument();

    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        isDisplayLastQtrTab: true,
        lastQtrNbr: 202404,
        isNotLocked: false,
        quarterLockoutDate: '2025-07-01',
      },
    });
    render(<QuarterTabs onQuarterChange={() => {}} />);
    expect(screen.getByText((content) =>
      content.includes("You can only view this period")
    )).toBeInTheDocument();
  });
});
