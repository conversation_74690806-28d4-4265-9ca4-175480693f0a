import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import HelpIcon from '../HelpIcon/HelpIcon';
import * as apiEndpoints from '../../util/apiEndpoints';

import { configure } from '@testing-library/react';
import { app_store } from '../../rtk/store';
import { Provider } from 'react-redux';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
configure({ testIdAttribute: 'data-testid' });

const mockWindowOpen = jest.fn();
Object.defineProperty(window, 'open', {
  writable: true,
  value: mockWindowOpen,
});


jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

const originalConsoleLog = console.log;
const mockConsoleLog = jest.fn();
const originalConsoleError = console.error;
const mockConsoleError = jest.fn();

const originalAlert = window.alert;
const mockAlert = jest.fn();

jest.mock('../../util/apiEndpoints', () => ({
  isLocalEnvironment: jest.fn(),
  getEndpoints: jest.fn(),
}));

describe('HelpIcon Component', () => {
  const mockEndpoints = {
    view: 'http://test-url/helpIcon/view',
    download: 'http://test-url/helpIcon/download',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (apiEndpoints.getEndpoints as jest.Mock).mockReturnValue(mockEndpoints);
    (apiEndpoints.isLocalEnvironment as jest.Mock).mockReturnValue(false);
    console.log = mockConsoleLog;
    console.error = mockConsoleError;
    window.alert = mockAlert;
    (useSelectorWrap as jest.Mock).mockImplementation((selectorName) => {
            if (selectorName === 'helpPdfUrl_rn') {
              return {
            data: 'https://rxsafeway.sharepoint.com/',
              };
            }
             else {
              // Return a default value for other selectors
              return { data: {} };
            }
          });
  });

  afterEach(() => {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    window.alert = originalAlert;
  });

  it('renders the default help icon correctly', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass('help-icon');
  });

  it('renders the dashboard variant correctly', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon variant="dashboard" />
    </Provider>
  );
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass('help-icon-dashboard');
  });

  it('renders the dashboard-title variant correctly', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon variant="dashboard-title" />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass('help-icon-dashboard-title');
  });

  it('applies additional className when provided', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon className="custom-class" />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toHaveClass('help-icon');
    expect(iconElement).toHaveClass('custom-class');
  });

  it('has the correct URL in the href attribute', () => {
    render(
      <Provider store={app_store}>
      <HelpIcon />
    </Provider>
  );
    const linkElement = screen.getByRole('link');
    expect(linkElement).toHaveAttribute('href', 'https://rxsafeway.sharepoint.com/');
  });
  it('opens in a new tab when clicked', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
  );
    const linkElement = screen.getByRole('link');
    expect(linkElement).toHaveAttribute('target', '_blank');
    expect(linkElement).toHaveAttribute('rel', 'noopener noreferrer');
  });

  xit('opens the view URL in a new tab when clicked', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');

    fireEvent.click(iconElement);

    expect(mockWindowOpen).toHaveBeenCalledWith(
      mockEndpoints.view,
      '_blank',
      'noopener,noreferrer'
    );
  });

  xit('logs URLs to console when clicked', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');

    fireEvent.click(iconElement);

    expect(mockConsoleLog).toHaveBeenCalledWith(
      'View URL:',
      mockEndpoints.view
    );
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Download URL:',
      mockEndpoints.download
    );
  });

  xit('calls getEndpoints to get the correct URLs', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');

    fireEvent.click(iconElement);

    expect(apiEndpoints.getEndpoints).toHaveBeenCalled();
  });

  xit('handles errors when getEndpoints fails', () => {
    const testError = new Error('Test error');
    (apiEndpoints.getEndpoints as jest.Mock).mockImplementation(() => {
      throw testError;
    });

    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');

    fireEvent.click(iconElement);

    expect(mockConsoleError).toHaveBeenCalledWith(
      'Error handling the help document:',
      testError
    );
    expect(mockAlert).toHaveBeenCalledWith(
      'Failed to open or download the help document. Please check the console for more details.'
    );
  });

  it('uses correct icon source based on variant', () => {
    const { rerender } = render( <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    let iconElement = screen.getByAltText('Help Icon');

    expect(iconElement).toHaveAttribute('src');

    rerender(<Provider store={app_store}>
      <HelpIcon />
    </Provider>);
    iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toHaveAttribute('src');

    rerender(<Provider store={app_store}>
      <HelpIcon />
    </Provider>);
    iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toHaveAttribute('src');
  });

  it('renders with correct title attribute', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    
    
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toHaveAttribute(
      'title',
      'Click to view and download help document'
    );
  });

  it('renders inside a container div', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const containerElement = screen.getByAltText('Help Icon').closest('div');
    expect(containerElement).toHaveClass('help-icon-container');
  });

  xit('handles local environment correctly', () => {
    (apiEndpoints.isLocalEnvironment as jest.Mock).mockReturnValue(true);
    render(
    <Provider store={app_store}>
      <HelpIcon />
    </Provider>
    );
    const iconElement = screen.getByAltText('Help Icon');

    fireEvent.click(iconElement);

    expect(mockWindowOpen).toHaveBeenCalledWith(
      mockEndpoints.view,
      '_blank',
      'noopener,noreferrer'
    );
  });

  it('handles null or undefined props gracefully', () => {
    render(
    <Provider store={app_store}>
      <HelpIcon className={undefined} variant={null as any} />
    </Provider>  
    );
    const iconElement = screen.getByAltText('Help Icon');
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass('help-icon');
  });
});
