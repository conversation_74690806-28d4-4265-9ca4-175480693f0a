import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import TimeframeSelector from './timeframeSelector';
import { TimeframeDropdownType } from '../types/timeframeTypes';

// Create mock store
const mockStore = configureStore([]);
const store = mockStore({
  quartersInYr_rn: {
    data: [
      {
        fiscalYearNumber: 2025,
        fiscalQuarterNumber: 1,
        fiscalQuarterStartDate: '2024-01-01',
        fiscalQuarterEndDate: '2024-03-31'
      },
      {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 4,
        fiscalQuarterStartDate: '2023-10-01',
        fiscalQuarterEndDate: '2023-12-31'
      },
      {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 3,
        fiscalQuarterStartDate: '2023-07-01',
        fiscalQuarterEndDate: '2023-09-30'
      },
      {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 2,
        fiscalQuarterStartDate: '2023-04-01',
        fiscalQuarterEndDate: '2023-06-30'
      }
    ]
  }
});

describe('TimeframeSelector', () => {
  const mockTimeframe: TimeframeDropdownType = {
    name: 'Q1 FY2025',
    num: 1,
    fiscalYear: 2025
  };
  const mockOnTimeframeChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering behavior', () => {
    it('should display the Timeframe title', () => {
      render(
        <Provider store={store}>
          <TimeframeSelector
            selectedTimeframe={mockTimeframe}
            onTimeframeChange={mockOnTimeframeChange}
          />
        </Provider>
      );
      expect(screen.getByText('Timeframe')).toBeInTheDocument();
    });

    it('should display all available timeframe options', () => {
      render(
        <Provider store={store}>
          <TimeframeSelector
            selectedTimeframe={mockTimeframe}
            onTimeframeChange={mockOnTimeframeChange}
          />
        </Provider>
      );
      expect(screen.getByText('Q1 FY2025')).toBeInTheDocument();
      expect(screen.getByText('Q4 FY2024')).toBeInTheDocument();
      expect(screen.getByText('Q3 FY2024')).toBeInTheDocument();
      expect(screen.getByText('Q2 FY2024')).toBeInTheDocument();
    });
  });

  describe('Search functionality', () => {
    it('should filter timeframes based on search query', () => {
      render(
        <Provider store={store}>
          <TimeframeSelector
            selectedTimeframe={mockTimeframe}
            onTimeframeChange={mockOnTimeframeChange}
          />
        </Provider>
      );
      
      const searchInput = screen.getByRole('textbox');
      fireEvent.change(searchInput, { target: { value: '2025' } });
      
      expect(screen.getByText('Q1 FY2025')).toBeInTheDocument();
      expect(screen.queryByText('Q4 FY2024')).not.toBeInTheDocument();
    });

    it('should display empty state when no timeframes match search', () => {
      render(
        <Provider store={store}>
          <TimeframeSelector
            selectedTimeframe={mockTimeframe}
            onTimeframeChange={mockOnTimeframeChange}
          />
        </Provider>
      );
      
      const searchInput = screen.getByRole('textbox');
      fireEvent.change(searchInput, { target: { value: 'invalid' } });
      
      expect(screen.getByText(/No timeframes found matching/)).toBeInTheDocument();
    });
  });

  describe('Timeframe selection', () => {
    it('should call onTimeframeChange when a timeframe is selected', () => {
      render(
        <Provider store={store}>
          <TimeframeSelector
            selectedTimeframe={mockTimeframe}
            onTimeframeChange={mockOnTimeframeChange}
          />
        </Provider>
      );
      
      const radioButton = screen.getByLabelText('Q4 FY2024');
      fireEvent.click(radioButton);
      
      expect(mockOnTimeframeChange).toHaveBeenCalledWith({
        name: 'Q4 FY2024',
        num: 4,
        fiscalYear: 2024,
        startDate: '2023-10-01',
        endDate: '2023-12-31',
        fiscalQuarterNumber: 4
      });
    });
  });

 
});
