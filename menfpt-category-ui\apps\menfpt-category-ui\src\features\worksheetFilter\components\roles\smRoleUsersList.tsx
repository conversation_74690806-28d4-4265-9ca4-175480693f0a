// EXAMPLE: How to refactor smRoleUsersList.tsx to use the new reusable components

import React from 'react';
import { useDispatch } from 'react-redux';
import { setSelectedSm, setSmDataForSelectedDept} from './rolesFilter.slice';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { formatName } from './rolesUtils';
import ClearSelection from './clearSelection';
import { worksheetFilterConfig } from '../../worksheetFilterConfig';
import { extractCurrentRoute } from '../../worksheetFilterRouteUtils';
import { createEmptySerializedSmData, deserializeSmData, serializeSmData } from '../../utils/serializationUtils';
import { SmDataType } from '../../types/smTypes';
import AsmRoleUsersList from './asmRoleUsersList';
import { SelectableList, SelectableItem } from '../shared';

interface SmRoleUsersListProps {
  activeSearchQuery?: string | null;
  smData?: SmDataType;
}

const SmRoleUsersList = ({ activeSearchQuery, smData: smDataProp }: SmRoleUsersListProps) => {
  const dispatch = useDispatch();
  const { data: serializedSmDataForSelectedDept } = useSelectorWrap('smDataForSelectedDept_rn') || {};
  const smDataForSelectedDept = deserializeSmData(serializedSmDataForSelectedDept);
  const { data: serializedSmData } = useSelectorWrap('selectedSm_rn') || {};
  const selectedSm = deserializeSmData(serializedSmData);
  const smData = smDataProp !== undefined ? smDataProp : smDataForSelectedDept;
  const currentRoute = extractCurrentRoute(location.pathname);
  const isMultipleSelectionsAllowed = worksheetFilterConfig.isAllowMultipleDivisonsSelection.includes(currentRoute);

  // Convert Map data to SelectableItem format
  const smItems: SelectableItem[] = Array.from(smData.keys()).map(sm => ({
    id: sm,
    label: formatName(sm),
    data: sm
  }));

  // Convert selected Map to SelectableItem format
  const selectedSmItems: SelectableItem[] = Array.from(selectedSm.keys()).map(sm => ({
    id: sm,
    label: formatName(sm),
    data: sm
  }));

  const handleSmChange = React.useCallback((item: SelectableItem, checked?: boolean) => {
    const sm = item.data as string;
    if (isMultipleSelectionsAllowed) {
      const newSelectedSmMap = new Map(selectedSm);
      checked ? newSelectedSmMap.set(sm, new Set()) : newSelectedSmMap.delete(sm);
      dispatch(setSelectedSm(serializeSmData(newSelectedSmMap)));
    } else {
      const newMap: SmDataType = new Map();
      newMap.set(sm, new Set());
      dispatch(setSelectedSm(serializeSmData(newMap)));
    }
  }, [selectedSm, dispatch, isMultipleSelectionsAllowed]);

  const handleSelectAll = () => {
    if (selectedSm.size === smData.size) {
      dispatch(setSelectedSm(createEmptySerializedSmData()));
    } else {
      dispatch(setSelectedSm(serializeSmData(smData)));
    }
  };

  const handleAsmChange = (checked: boolean, asm: string, sm: string) => {
    const newSelectedSmMap = new Map(selectedSm);
    if (isMultipleSelectionsAllowed) {
      const existingAsmList = newSelectedSmMap.get(sm) || new Set();
      checked ? existingAsmList.add(asm) : existingAsmList.delete(asm);
      newSelectedSmMap.set(sm, existingAsmList);
    } else {
      // Clear all previously selected SMs and only select the current one
      newSelectedSmMap.clear();
      const newSelectedAsm = new Set([asm]);
      newSelectedSmMap.set(sm, newSelectedAsm);
    }
    dispatch(setSelectedSm(serializeSmData(newSelectedSmMap)));
  };

  const renderItemContent = (item: SelectableItem, isSelected: boolean) => {
    return (
        <AsmRoleUsersList 
          selectedSm={item.data}
          handleAsmChange={(checked: boolean, asm: string) => handleAsmChange(checked, asm, item.data)}
        />
    );
  };

  const getIndeterminateState = (item: SelectableItem) => {
    const sm = item.data as string;
    const selectedAsms = selectedSm.get(sm) || new Set();
    const allAsms = smData.get(sm) || new Set();
    return selectedAsms.size > 0 && selectedAsms.size < allAsms.size;
  };

  const header = (
    <div className="flex items-center justify-between text-black text-sm font-semibold font-['Nunito_Sans'] leading-normal">
      <span className='BodyParagraphMSemiBold'>SM & ASM (optional)</span>
      <ClearSelection
        show={!!selectedSm && selectedSm.size > 0}
        onClick={() => {
          dispatch(setSmDataForSelectedDept(serializeSmData(smData)));
          dispatch(setSelectedSm(createEmptySerializedSmData()));
        }}
      />
    </div>
  );

  return (
    <div className="min-h-0 flex flex-col flex-1 grow min-w-0 border-[#c8daeb] px-4"
      >
      <div className="bg-white flex flex-col flex-1 min-h-0">
        {header}
        <SelectableList
          items={smItems}
          selectedItems={selectedSmItems}
          isMultipleSelectionAllowed={isMultipleSelectionsAllowed}
          onItemChange={handleSmChange}
          onSelectAll={handleSelectAll}
          showSelectAll={isMultipleSelectionsAllowed}
          emptyMessage={'Select a department to view'}
          renderItemContent={renderItemContent}
          listClassName="pr-[5px] self-stretch relative flex-1 flex flex-col justify-start items-start overflow-y-auto nfpt-scrollbar transition-all duration-200"
          itemClassName="self-stretch min-h-10 p-2.5 bg-white rounded-lg inline-flex justify-start items-center gap-6 overflow-hidden"
          enableExpandCollapse={true}
          getIndeterminateState={getIndeterminateState}
        />
      </div>
    </div>
  );
};

export default SmRoleUsersList;
