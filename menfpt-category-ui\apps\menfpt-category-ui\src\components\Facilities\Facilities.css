.facilities-table  table thead {
    position: static;
    top: 0;
}
.facilities-table-custom::-webkit-scrollbar {
  display: none !important;
}
.facilities-table-custom {
  width: 100%;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
    -ms-overflow-style: none;  
  scrollbar-width: none;

}
.facilities-table-custom .uds-table {
  margin: 0 !important;
  padding: 0 !important;
  background-color: white !important;
  border-radius: 0 !important;
}
.facilities-table-custom .uds-table__columns,
.facilities-table-custom .uds-table__columns-button,
.facilities-table-custom .uds-table__columns-menu,
.facilities-table-custom [id^='headlessui-popover-button'],
.facilities-table-custom [data-testid='root-popover'] {
  display: none !important;
}
.facilities-table-custom .uds-table_tr {
  border-bottom: 1px solid #ececec !important;
}
.facilities-table-custom .uds-table_td,
.facilities-table-custom .uds-table_th {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}
.facilities-table-custom .uds-table_tr:last-child .uds-table_td {
  border-bottom: 0 !important;
}

.facility-header {
  margin-left: 0.75rem; 
  font-weight: bold;    
  font-size: 0.875rem;  
  line-height: 1rem;    
  color: var(--uds-dark-text, #22223b); 
  user-select: none;   
  white-space: nowrap;  
  text-overflow: ellipsis; 
  margin-right: 0.75rem; 
  display: flex;        
  align-items: center;  
}

.keeper-table-style {
  width: 1262px;
  height: 605px;
  top: 156px;
  left: 84px;
  border-radius: 8px;
  border-width: 1px;
  padding-top: 8px;
  padding-bottom: 8px;
  gap: 8px;
  opacity: 1;
  border-style: solid;
  border-color: #ececec;
  position: relative; 
}

.facilities-header {
  width: 1302px;
  height: 80px;
  position: relative;
  top: 64px;
  left: 64px;
  opacity: 1;
  padding: 12px 20px;
  gap: 10px;
  box-sizing: border-box;
}

.select-no-border {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: transparent !important;
  padding: 0 !important;
  min-height: 0 !important;
  height: auto !important;
}

.select-no-border .uds-select__input,
.select-no-border .uds-select__button {
  padding-right: 0 !important;
}

.select-no-border .uds-select__caret,
.select-no-border .uds-select__icon {
  position: static !important;  
  margin-left: 4px !important;    
  right: unset !important;
  display: inline !important;     
  vertical-align: middle !important;
}

.select-no-border .uds-select__input,
.select-no-border .uds-select__button {
  padding-right: 1px !important;  
}

.select-no-border .uds-select__caret,
.select-no-border .uds-select__icon {
  right: 0px !important;          
  margin-left: 0 !important;    
}

.select-no-border .uds-select__button {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 0px !important; 
}