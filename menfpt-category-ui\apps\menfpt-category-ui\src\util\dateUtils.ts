import { utcToZonedTime } from 'date-fns-tz';
/**
 * Adds a specified number of days to a date string in MM/DD/YYYY format
 * @param dateStr Date string in format MM/DD/YYYY (e.g., "03/15/2025")
 * @param days Number of days to add (default: 2)
 * @returns New date string in the same format (MM/DD/YYYY)
 */
export function addDaysToDate(dateStr: string, days): string {
    // Parse the date from MM/DD/YYYY format
    const [month, day, year] = dateStr.split('/').map(Number);

    // Create a date object (month is 0-indexed in JavaScript Date)
    const date = new Date(year, month - 1, day);

    // Add the specified number of days
    date.setDate(date.getDate() + days);

    // Format the result back to MM/DD/YYYY
    const newMonth = String(date.getMonth() + 1).padStart(2, '0');
    const newDay = String(date.getDate()).padStart(2, '0');
    const newYear = date.getFullYear();

    return `${newMonth}/${newDay}/${newYear}`;
}
// Helper: Get the current date in PST timezone
export function getNowInPST(): Date {
    // All the date operations Should happen in PST time zone.
    return utcToZonedTime(new Date(), 'America/Los_Angeles');
}