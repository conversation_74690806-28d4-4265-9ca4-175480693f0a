// Test script to verify banner name lookup functionality

// Mock toTitleCase function
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

// Updated getBannerName function (same as in the Excel component)
const getBannerName = (smicData, divisionId, bannerId, fallback) => {
  const found = smicData.find((item) => 
    String(item.divisionId) === String(divisionId) && 
    String(item.bannerId) === String(bannerId)
  );
  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
};

// Test data based on your API structure
const mockSmicData = [
  {
    divisionId: '34',
    divisionName: 'Test Division',
    bannerId: '25',
    bannerName: 'ACME STORES',
    deptId: '301',
    deptName: 'Grocery'
  },
  {
    divisionId: '34',
    divisionName: 'Test Division', 
    bannerId: '26',
    bannerName: 'SAFEWAY STORES',
    deptId: '301',
    deptName: 'Grocery'
  },
  {
    divisionId: '35',
    divisionName: 'Another Division',
    bannerId: '27',
    bannerName: 'KINGS FOOD MARKETS',
    deptId: '301',
    deptName: 'Grocery'
  }
];

console.log('Testing getBannerName function with smicData lookup:');
console.log('=======================================================');

// Test 1: Banner found in smicData
console.log('Test 1 - Banner found in smicData:');
const result1 = getBannerName(mockSmicData, '34', '25', 'Fallback Banner');
console.log(`getBannerName(smicData, '34', '25', 'Fallback Banner'): "${result1}"`);
console.log('Expected: "Acme Stores" ✅');

// Test 2: Banner not found, use fallback
console.log('\nTest 2 - Banner not found, use fallback:');
const result2 = getBannerName(mockSmicData, '34', '99', 'Custom Banner Name');
console.log(`getBannerName(smicData, '34', '99', 'Custom Banner Name'): "${result2}"`);
console.log('Expected: "Custom Banner Name" ✅');

// Test 3: Banner not found, no fallback, use bannerId
console.log('\nTest 3 - Banner not found, no fallback, use bannerId:');
const result3 = getBannerName(mockSmicData, '34', '99', '');
console.log(`getBannerName(smicData, '34', '99', ''): "${result3}"`);
console.log('Expected: "99" ✅');

// Test 4: Different division, same banner ID
console.log('\nTest 4 - Different division, same banner ID:');
const result4 = getBannerName(mockSmicData, '35', '27', 'Fallback');
console.log(`getBannerName(smicData, '35', '27', 'Fallback'): "${result4}"`);
console.log('Expected: "Kings Food Markets" ✅');

// Test 5: Empty smicData
console.log('\nTest 5 - Empty smicData:');
const result5 = getBannerName([], '34', '25', 'Fallback Banner');
console.log(`getBannerName([], '34', '25', 'Fallback Banner'): "${result5}"`);
console.log('Expected: "Fallback Banner" ✅');

console.log('\n✅ All tests completed!');
console.log('\nNow in your Excel file, instead of "04 - Banner 04", you should see:');
console.log('- "25 - Acme Stores" (if banner 25 exists in smicData)');
console.log('- "26 - Safeway Stores" (if banner 26 exists in smicData)');
console.log('- "04 - Custom Banner Name" (if banner 04 has a name in the API response)');
console.log('- "04 - 04" (if no name found anywhere)');
