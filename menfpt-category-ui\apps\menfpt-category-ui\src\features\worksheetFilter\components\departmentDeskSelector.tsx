import React, { useState } from 'react';
import { DropdownType } from '../../../interfaces/worksheetFilter';
import {
  useDeskDisplay,
  useDisplayDeptRoleCascade,
  useMultipleDepartmentsSelection,
} from '../worksheetFilterRouteUtils';
import {
  filterBySearch,
} from '../searchUtils';
import './deptDesk/departmentDeskSelectorStyles.scss';
import { DepartmentSelection } from './department/departmentSelection';
import { getSmDataByDivisionAndDept } from './roles/rolesUtils';
import { useSelectorWrap } from '../../../rtk/rtk-utilities';
import { useDispatch } from 'react-redux';
import { clearCascadeSearchSelectedItem } from './suggestions/deptRoleSuggestions.slice';
import { setDepartmentDeskSearchQuery } from './deptDesk/departmentDeskSearchQuery.slice';
import AlertBox from './alert-box';
import { SmDataType } from '../types/smTypes';


interface DepartmentDeskSelectorProps {
  desks: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  selectedDesk?: DropdownType;
  divisionId?: string;
  onDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  onDeskChange: (desk: DropdownType) => void;
}

const DepartmentDeskSelector: React.FC<DepartmentDeskSelectorProps> = ({
  desks,
  selectedDepartment,
  selectedDesk,
  divisionId,
  onDepartmentChange,
  onDeskChange,
}) => {
  const departmentsState = useSelectorWrap('departments_rn');
  const departments = departmentsState.data || [];
  const deptRoleSuggestionsState = useSelectorWrap("deptRoleSuggestions_rn");
  const [searchQueryDepartment, setSearchQueryDepartment] =
    useState<string>('');
  const [searchQueryDesk, setSearchQueryDesk] = useState<string>('');
  const [smDataForSelectedDept, setSmDataForSelectedDept] = useState<SmDataType>(new Map());

  // Use the utility hooks to determine if desk display and multiple departments selection are allowed
  const isDisplayDesk = useDeskDisplay();
  const isMultipleDeptsSelectionAllowed = useMultipleDepartmentsSelection();
  const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();
  const dispatch = useDispatch();

  // Initialize SM data if a department is already selected
  React.useEffect(() => {
    if (isDisplayDeptRoleCascade && selectedDepartment && !Array.isArray(selectedDepartment)) {
      const smForSelectedDept = getSmDataByDivisionAndDept({
        selectedDeptId: selectedDepartment.num,
        deptListForSelectedDivision: departments,
      }) as SmDataType;
      setSmDataForSelectedDept(smForSelectedDept);
    }
  }, [isDisplayDeptRoleCascade, selectedDepartment, departments]);

  // Combine cascade effect: update both selectedDepartment and SM list in sync
  // React.useEffect(() => {
  //   const { cascadeSearchSelectedItemId, cascadeSearchSelectedItemType } = deptRoleSuggestionsState?.data || {};
  //   if (
  //     cascadeSearchSelectedItemId &&
  //     ['department', 'sm', 'ASM'].includes(cascadeSearchSelectedItemType)
  //   ) {
  //     let deptId = null;
  //     if (cascadeSearchSelectedItemType === 'department') {
  //       deptId = cascadeSearchSelectedItemId;
  //     } else {
  //       deptId = cascadeSearchSelectedItemId.split('-')[0];
  //     }
  //     if (deptId) {
  //       const foundDept = departments.find((d: any) => String(d.num) === String(deptId));
  //       if (foundDept) {
  //         // Only update if not already selected
  //         if (
  //           !selectedDepartment ||
  //           (Array.isArray(selectedDepartment)
  //             ? !selectedDepartment.some((d: any) => String(d.num) === String(deptId))
  //             : String(selectedDepartment.num) !== String(deptId))
  //         ) {
  //           onDepartmentChange(foundDept);
  //         }
  //         // Always update SM data for the new department
  //         const smForSelectedDept = getSmDataByDivisionAndDept({
  //           selectedDeptId: deptId,
  //           deptListForSelectedDivision: departments,
  //         }) as SmDataType;
  //         setSmDataForSelectedDept(smForSelectedDept);
  //         // Update Redux state for SM/ASM list based on suggestion type
  //         let selectedSm = '';
  //         let asmForSelectedSm: string[] = [];
  //         if (cascadeSearchSelectedItemType === 'sm') {
  //           selectedSm = cascadeSearchSelectedItemId.split('-').slice(1).join('-');
  //           asmForSelectedSm = smForSelectedDept.find(smObj => smObj.sm === selectedSm)?.asmArr || [];
  //         } else if (cascadeSearchSelectedItemType === 'ASM') {
  //           const parts = cascadeSearchSelectedItemId.split('-');
  //           selectedSm = parts.slice(1, parts.length - 1).join('-');
  //           asmForSelectedSm = smForSelectedDept.find(smObj => smObj.sm === selectedSm)?.asmArr || [];
  //         }
  //         dispatch(setAsmDataForSelectedSm({
  //           selectedSm,
  //           asmForSelectedSm,
  //           smDataForSelectedDept: smForSelectedDept,
  //         }));
  //       }
  //     }
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [
  //   deptRoleSuggestionsState?.data?.cascadeSearchSelectedItemId,
  //   deptRoleSuggestionsState?.data?.cascadeSearchSelectedItemType,
  //   departments,
  // ]);

  const filteredDepartments = filterBySearch(
    departments,
    searchQueryDepartment
  );
  const filteredDesks = filterBySearch(desks, searchQueryDesk);

  const handleDepartmentSearch = (query: string) => {
    setSearchQueryDepartment(query);
  };

  const handleDeskSearch = (query: string) => {
    setSearchQueryDesk(query);
  };

  const getSelectedDepartments = () => {
    return Array.isArray(selectedDepartment)
      ? selectedDepartment
      : selectedDepartment
        ? [selectedDepartment]
        : [];
  };

  const handleMultipleDepartmentSelection = (
    department: DropdownType,
    selectedDepts: DropdownType[]
  ) => {
    const isDeptSelected = selectedDepts.some(
      (dept) => dept.num === department.num
    );
    if (isDeptSelected) {
      return selectedDepts.filter((dept) => dept.num !== department.num);
    }
    return [...selectedDepts, department];
  };

  // Map selectedDepartment from ID to department object if needed
  let selectedDepartmentToUse = selectedDepartment;
  if (typeof selectedDepartment === 'string') {
    selectedDepartmentToUse = departments.find((d: any) => String(d.num) === String(selectedDepartment));
  } else if (selectedDepartment && !Array.isArray(selectedDepartment) && typeof selectedDepartment.num === 'undefined') {
    selectedDepartmentToUse = departments.find((d: any) => String(d.num) === String(selectedDepartment));
  } else if (selectedDepartment && !Array.isArray(selectedDepartment) && typeof selectedDepartment.num !== 'undefined') {
    // Already an object, do nothing
  } else if (Array.isArray(selectedDepartment)) {
    // If it's an array of IDs, map each to object
    selectedDepartmentToUse = selectedDepartment.map((id: any) => {
      if (typeof id === 'string') {
        return departments.find((d: any) => String(d.num) === String(id));
      }
      return id;
    }).filter(Boolean);
  }

  const handleDepartmentChange = (department: DropdownType | DropdownType[]) => {
    // Clear cascade search query when user manually updates department
    dispatch(clearCascadeSearchSelectedItem());
    dispatch(setDepartmentDeskSearchQuery(''));
    // If we received an array, it's already processed by the DepartmentSelection component
    if (Array.isArray(department)) {
      onDepartmentChange(department);
      return;
    }

    // Otherwise, handle single department selection
    if (isMultipleDeptsSelectionAllowed) {
      const selectedDepts = getSelectedDepartments();
      const updatedDepts = handleMultipleDepartmentSelection(
        department,
        selectedDepts
      );
      onDepartmentChange(updatedDepts);
    } else {
      onDepartmentChange(department);

      const smForSelectedDept = getSmDataByDivisionAndDept({
        selectedDeptId: department.num,
        deptListForSelectedDivision: departments,
      }) as SmDataType;
      setSmDataForSelectedDept(smForSelectedDept);
    }
  };

  const handleDeskChange = (desk: DropdownType) => {
    onDeskChange(desk);
  };

  const showDeptAlert = () => {
    if(departments.length > 0) {
      return (
        !selectedDepartment ||
        (Array.isArray(selectedDepartment) && selectedDepartment.length < 1)
      );
    }
    return false;
  }

  const headerText = "Department";
  return (
  <div className="bg-white flex flex-col flex-1 min-h-0">
      <span className="BodyParagraphMSemiBold flex items-center">
        {headerText}
        {showDeptAlert() && (
          <AlertBox message={`Please select ${headerText}(s)`} />
        )}
      </span>
      <DepartmentSelection
        departments={filteredDepartments} // Pass filtered departments
        selectedDepartment={selectedDepartmentToUse}
        isMultipleSelectionAllowed={isMultipleDeptsSelectionAllowed}
        onDepartmentChange={handleDepartmentChange}
        onSmDataChange={setSmDataForSelectedDept}
        showSearch={true}
        searchQuery={searchQueryDepartment}
        onSearchChange={handleDepartmentSearch}
        showHeader={false}
        />
    </div>
  );
};

export default DepartmentDeskSelector;