import { getLastFriday, getLastFridayInQuarter, getNextMondayAfterQuarter, getFridayOfSelectedWeek } from './getLastFriday';
import { format, utcToZonedTime } from 'date-fns-tz';

const DAY_END_TS = 'T23:59:59.000';
const LATEST_DATA = 'Latest data';

interface PerformanceVarianceConfig {
  selectedWeek: any;
  currentQuarterNbr: number | undefined;
  appliedFiscalQtrNbr: number | undefined;
  appliedFilters: any;
  weekNumbersInQuarter: number[];
  currentWeekIndexInQuarter: number;
  totalWeeksInQuarter: number;
  getAllPreviousweeks: (selectedWeek: any) => number[];
  timeZone: string;
}

interface SnapshotTimestamps {
  snapshotTimestampFriday: string;
  snapshotTimestamp: string;
  weekNumbersPayload: number[];
}

export const getPerformanceVarianceSnapshotTimestamps = (config: PerformanceVarianceConfig): SnapshotTimestamps => {
  const {
    selectedWeek,
    currentQuarterNbr,
    appliedFiscalQtrNbr,
    appliedFilters,
    weekNumbersInQuarter,
    currentWeekIndexInQuarter,
    totalWeeksInQuarter,
    getAllPreviousweeks,
    timeZone
  } = config;

  let snapshotTimestampFriday = '';
  let snapshotTimestamp = '';
  let weekNumbersPayload: number[] = [];

  // Use selected week for "Last Friday" calculation if available and not "Latest data"
  if (selectedWeek && selectedWeek.name !== LATEST_DATA && selectedWeek.value) {
    const fridayOfSelectedWeek = getFridayOfSelectedWeek(selectedWeek.value);
    const datePart = fridayOfSelectedWeek.split('T')[0];
    snapshotTimestampFriday = `${datePart}${DAY_END_TS}`;
    snapshotTimestamp = new Date().toISOString();
    
    // Get weeks up to the selected week using the existing getAllPreviousweeks function
    weekNumbersPayload = getAllPreviousweeks(selectedWeek)?.slice(0, -1);
  } else {
    // Fallback to original logic if no week is selected or "Latest data" is selected
    if (currentQuarterNbr !== undefined && currentQuarterNbr === appliedFiscalQtrNbr) {
      const lastFriday = getLastFriday();
      const datePart = lastFriday.split('T')[0];
      snapshotTimestampFriday = `${datePart}${DAY_END_TS}`;
      snapshotTimestamp = new Date().toISOString();
      if (currentWeekIndexInQuarter > 2) {
        weekNumbersPayload = weekNumbersInQuarter.slice(0, currentWeekIndexInQuarter - 2);
      }
    } else if (appliedFiscalQtrNbr !== undefined) {
      snapshotTimestampFriday = `${getLastFridayInQuarter(appliedFilters.timeframe.endDate)}${DAY_END_TS}`;
      snapshotTimestamp = `${getNextMondayAfterQuarter(appliedFilters.timeframe.endDate)}${DAY_END_TS}`;
      weekNumbersPayload = weekNumbersInQuarter.slice(0, totalWeeksInQuarter - 1);
    }
  }

  return {
    snapshotTimestampFriday,
    snapshotTimestamp,
    weekNumbersPayload
  };
};

export const formatSnapshotTimestamps = (snapshotTimestamps: SnapshotTimestamps, timeZone: string) => {
  const { snapshotTimestampFriday, snapshotTimestamp } = snapshotTimestamps;
  
  const snapshotFridayPT = format(
    utcToZonedTime(new Date(snapshotTimestampFriday), timeZone),
    'yyyy-MM-dd HH:mm:ss zzz', 
    { timeZone }
  );
  
  const snapshotPT = format(
    utcToZonedTime(new Date(snapshotTimestamp), timeZone),
    'yyyy-MM-dd HH:mm:ss zzz', 
    { timeZone }
  );

  return {
    snapshotFridayPT,
    snapshotPT
  };
};