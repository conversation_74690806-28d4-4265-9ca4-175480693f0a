import React, { useState } from 'react';
import SelectableListItem, { SelectableItem } from './SelectableListItem';
import SelectAllCheckbox from './SelectAllCheckbox';
import { is } from 'date-fns/locale';

interface SelectableListProps<T = any> {
  items: SelectableItem[];
  selectedItems: SelectableItem[];
  isMultipleSelectionAllowed: boolean;
  onItemChange: (item: SelectableItem, checked?: boolean) => void;
  onSelectAll?: () => void;
  showSelectAll?: boolean;
  selectAllLabel?: string;
  emptyMessage?: string;
  className?: string;
  listClassName?: string;
  itemClassName?: string;
  disabled?: boolean;
  renderItemContent?: (item: SelectableItem, isSelected: boolean) => React.ReactNode;
  enableExpandCollapse?: boolean;
  getIndeterminateState?: (item: SelectableItem) => boolean; // New prop for indeterminate state
}

const SelectableList: React.FC<SelectableListProps> = ({
  items,
  selectedItems,
  isMultipleSelectionAllowed,
  onItemChange,
  onSelectAll,
  showSelectAll = false,
  selectAllLabel,
  emptyMessage = 'No items available',
  className = '',
  listClassName = '',
  itemClassName = '',
  disabled = false,
  renderItemContent,
  enableExpandCollapse = false,
  getIndeterminateState,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string | number>>(new Set());

  const isItemSelected = (item: SelectableItem) => {
    return selectedItems.some(selected => selected.id === item.id);
  };

  const isItemExpanded = (item: SelectableItem) => {
    return expandedItems.has(item.id);
  };

  const toggleExpanded = (item: SelectableItem) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(item.id)) {
        newSet.delete(item.id);
      } else {
        newSet.add(item.id);
      }
      return newSet;
    });
  };

  const getTestId = (item: SelectableItem) => {
    return `selectable-item-${item.id}`;
  };

  if (items.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500 flex-grow">
        {emptyMessage}
      </div>
    );
  }

  return (
    <>
      {showSelectAll && isMultipleSelectionAllowed && onSelectAll && (
        <SelectAllCheckbox
          label={selectAllLabel}
          selectedCount={selectedItems.length}
          totalCount={items.length}
          onSelectAll={onSelectAll}
          disabled={disabled}
        />
      )}
      <div data-scroll-bar="true" data-slot="false" className={listClassName}>
        {items.map((item) => {
          const isSelected = isItemSelected(item);
          const isExpanded = isItemExpanded(item);
          const showChevron = enableExpandCollapse;
          const expandableContent = renderItemContent && renderItemContent(item, isSelected);
          const hasExpandableContent = !!expandableContent;
          const isIndeterminate = getIndeterminateState ? getIndeterminateState(item) : false;
          
          const handleItemChange = (item: SelectableItem, checked?: boolean) => {
            // Call the original onChange
            onItemChange(item, checked);
            
            // If expand/collapse is enabled, expand when selected, collapse when unselected
            if (enableExpandCollapse) {
              if (isMultipleSelectionAllowed) {
                // For checkboxes, expand when checked, collapse when unchecked
                if (checked) {
                  setExpandedItems(prev => new Set([...prev, item.id]));
                } else {
                  setExpandedItems(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(item.id);
                    return newSet;
                  });
                }
              } else {
                // For radio buttons, expand the selected item and collapse others
                setExpandedItems(new Set([item.id]));
              }
            }
          };
          
          return (
            <SelectableListItem
              key={item.id}
              item={item}
              isSelected={isSelected}
              isIndeterminate={isIndeterminate}
              isMultipleSelectionAllowed={isMultipleSelectionAllowed}
              onChange={handleItemChange}
              disabled={disabled}
              itemClassName={itemClassName}
              testId={getTestId(item)}
              showChevron={showChevron}
              isExpanded={isExpanded}
              onToggleExpand={() => toggleExpanded(item)}
              hasExpandableContent={hasExpandableContent}
            >
              {(!enableExpandCollapse || isExpanded) && expandableContent}
            </SelectableListItem>
          );
        })}
      </div>
    </>
  );
};

export default SelectableList;
