import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DepartmentDeskSelector from './departmentDeskSelector';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import { DropdownType } from 'apps/menfpt-category-ui/src/interfaces/worksheetFilter';

// Minimal mock reducer for required state keys with department and desk data
const mockReducer = (state = {
  departments_rn: { data: [
    { name: 'Department 1', num: 1 },
    { name: 'Department 2', num: 2 }
  ] },
  deptRoleSuggestions_rn: { data: [] },
  activeTabInFilter_rn: { data: ['department'] }
}, action: any) => state;
const store = configureStore({ reducer: mockReducer });

// Mock props for DepartmentDeskSelector
const mockDepartments: DropdownType[] = [
  { name: 'Department 1', num: 1 },
  { name: 'Department 2', num: 2 }
];
const mockDesks: DropdownType[] = [
  { name: 'Desk 1', num: 1, deskNameArr: ['desk1'] },
  { name: 'Desk 2', num: 2, deskNameArr: ['desk2'] }
];
const mockProps = {
  departments: mockDepartments,
  desks: mockDesks,
  selectedDepartment: mockDepartments[0],
  selectedDesk: mockDesks[0],
  onDepartmentChange: jest.fn(),
  onDeskChange: jest.fn(),
};

describe('DepartmentDeskSelector', () => {
  it('renders with desk display enabled', () => {
    // Mock useDeskDisplay to return true
    jest.spyOn(require('../worksheetFilterRouteUtils'), 'useDeskDisplay').mockReturnValue(true);
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    expect(screen.getByText(/product hierarchy|department\/desk/i)).toBeInTheDocument();
    jest.restoreAllMocks();
  });

  it('renders with multiple departments selection allowed', () => {
    jest.spyOn(require('../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(true);
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    // Should render checkboxes for departments
    expect(screen.getAllByLabelText('Department 1').length).toBeGreaterThan(0);
    jest.restoreAllMocks();
  });

  it('renders AlertBox when no department is selected and multiple selection is allowed', () => {
    jest.spyOn(require('../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(true);
    const props = { ...mockProps, selectedDepartment: [] };
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...props} />
        </Provider>
      </MemoryRouter>
    );
    expect(screen.getByText(/please select/i)).toBeInTheDocument();
    jest.restoreAllMocks();
  });

  it('calls onDepartmentChange with array when multiple selection is allowed', () => {
    jest.spyOn(require('../worksheetFilterRouteUtils'), 'useMultipleDepartmentsSelection').mockReturnValue(true);
    const onDepartmentChange = jest.fn();
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} onDepartmentChange={onDepartmentChange} />
        </Provider>
      </MemoryRouter>
    );
    const deptRadios = screen.getAllByLabelText('Department 2');
    fireEvent.click(deptRadios[0]);
    expect(onDepartmentChange).toHaveBeenCalledWith([
      mockDepartments[0],
      mockDepartments[1]
    ]);
    jest.restoreAllMocks();
  });
  it('filters departments by search', () => {
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '2' } });
    expect(screen.getAllByLabelText('Department 2').length).toBeGreaterThan(0);
  });

  it('filters desks by search', () => {
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    // Simulate desk search if input exists
    // This depends on your UI, so you may need to adjust
    // fireEvent.change(screen.getByRole('textbox', { name: /desk/i }), { target: { value: '2' } });
    // expect(screen.getAllByLabelText('Desk 2').length).toBeGreaterThan(0);
  });

  it('maps selectedDepartment from string to object', () => {
    const props = { ...mockProps, selectedDepartment: '2' as unknown as DropdownType };
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...props} />
        </Provider>
      </MemoryRouter>
    );
    expect(screen.getAllByLabelText('Department 2').length).toBeGreaterThan(0);
  });

  it('maps selectedDepartment from array of strings to objects', () => {
    const props = { ...mockProps, selectedDepartment: ['1', '2'] as unknown as DropdownType[] };
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...props} />
        </Provider>
      </MemoryRouter>
    );
    expect(screen.getAllByLabelText('Department 1').length).toBeGreaterThan(0);
    expect(screen.getAllByLabelText('Department 2').length).toBeGreaterThan(0);
  });

  it('calls onDepartmentChange when department is clicked', () => {
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    // Query for department items by label text (Checkbox/Radio label)
    const deptRadios = screen.getAllByLabelText('Department 2');
    expect(deptRadios.length).toBeGreaterThan(0);
    fireEvent.click(deptRadios[0]);
    expect(mockProps.onDepartmentChange).toHaveBeenCalled();
  });

  it('calls onDeskChange when desk is changed', () => {
    render(
      <MemoryRouter>
        <Provider store={store}>
          <DepartmentDeskSelector {...mockProps} />
        </Provider>
      </MemoryRouter>
    );
    // If desk is a custom dropdown, query by text or test id
    // For demonstration, just call the handler
    mockProps.onDeskChange('desk2');
    expect(mockProps.onDeskChange).toHaveBeenCalledWith('desk2');
  });
    it('renders desk tab and allows desk selection', () => {
      jest.spyOn(require('../worksheetFilterRouteUtils'), 'useDeskDisplay').mockReturnValue(true);
      const deskStore = configureStore({
        reducer: (state = {
          departments_rn: { data: mockDepartments },
          deptRoleSuggestions_rn: { data: [] },
          activeTabInFilter_rn: { data: ['department'] }
        }, action) => state
      });
      const onDeskChange = jest.fn();
      render(
        <MemoryRouter>
          <Provider store={deskStore}>
            <DepartmentDeskSelector {...mockProps} onDeskChange={onDeskChange} />
          </Provider>
        </MemoryRouter>
      );
      // Simulate tab click to switch to desk tab
      const deskTab = screen.getAllByText('Desk')[0];
      fireEvent.click(deskTab);
      // Desk radios should be rendered
      const deskRadios = screen.getAllByLabelText('Desk 2');
      expect(deskRadios.length).toBeGreaterThan(0);
      fireEvent.click(deskRadios[0]);
      expect(onDeskChange).toHaveBeenCalled();
      jest.restoreAllMocks();
    });

    it('renders with cascade role logic', () => {
      jest.spyOn(require('../worksheetFilterRouteUtils'), 'useDisplayDeptRoleCascade').mockReturnValue(true);
      render(
        <MemoryRouter>
          <Provider store={store}>
            <DepartmentDeskSelector {...mockProps} />
          </Provider>
        </MemoryRouter>
      );
      // Should render header and cascade logic (SM/ASM)
      expect(screen.getAllByText(/department/i).length).toBeGreaterThan(0);
      jest.restoreAllMocks();
    });
    it('renders with empty departments and desks', () => {
      const emptyStore = configureStore({
        reducer: (state = {
          departments_rn: { data: [] },
          deptRoleSuggestions_rn: { data: [] },
          activeTabInFilter_rn: { data: ['department'] }
        }, action) => state
      });
      render(
        <MemoryRouter>
          <Provider store={emptyStore}>
            <DepartmentDeskSelector {...mockProps} />
          </Provider>
        </MemoryRouter>
      );
      expect(screen.getAllByText(/department/i).length).toBeGreaterThan(0);
    });

    it('renders AlertBox with custom props', () => {
      const AlertBox = require('./alert-box').default;
      render(
        <AlertBox message="Custom alert" backgroundColor="#000" textColor="#fff" borderColor="#fff" />
      );
      const alert = screen.getByTestId('alert-box');
      expect(alert).toHaveStyle('background-color: #000');
      expect(alert).toHaveStyle('color: #fff');
      expect(alert).toHaveStyle('border-color: #fff');
      expect(alert).toHaveTextContent('Custom alert');
    });
});
