import { createGenericSlice } from "../rtk/rtk-slice";


//Stores which quarter tab is active. Used for period close.
export const activeQuarterTabSlice = createGenericSlice({
  name: 'activeQuarterTab_rn',
  initialState: { status: 'loading', data: {} },
})({
  setActiveQuarterTab(state, { payload }) {

    state.data = payload;
  },
});


export const { setActiveQuarterTab } = activeQuarterTabSlice.actions;




