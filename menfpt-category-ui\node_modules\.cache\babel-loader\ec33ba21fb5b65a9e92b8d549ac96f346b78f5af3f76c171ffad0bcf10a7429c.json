{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\pages\\\\dashboard-tabs.tsx\";\nimport React, { useState, useRef } from 'react';\n// import { createPortal } from 'react-dom';\nimport Report from './report';\nimport \"./dashboard-tabs.scss\";\nimport Drawer from '@albertsons/uds/molecule/Drawer';\nimport Button from '@albertsons/uds/molecule/Button';\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\n// Update the import path and casing to match the actual file location\nimport { SelectWeek } from './../components/SnapShotDropDown/release-week-select';\nimport { CircleAlert } from 'lucide-react';\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';\nimport Icon from '@albertsons/uds/molecule/Link';\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg';\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\nimport { getNowInPST } from '../util/dateUtils';\nimport { format } from 'date-fns-tz';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar TabsLabels = /*#__PURE__*/function (TabsLabels) {\n  TabsLabels[\"LEADING_INDICATORS\"] = \"Leading Indicators\";\n  TabsLabels[\"PERFORMANCE_SUMMARY\"] = \"Performance Summary\";\n  TabsLabels[\"FORECAST_VARIANCE\"] = \"Performance Variance\";\n  return TabsLabels;\n}(TabsLabels || {}); // const tabClassNames = {\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\n// };\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\nconst DashboardTabs = () => {\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [selectedWeek, setSelectedWeek] = useState(null);\n  const [performanceSummaryData, setPerformanceSummaryData] = useState([]);\n  const [forecastVarianceData, setForecastVarianceData] = useState([]);\n  const [dashboardLoading, setDashboardLoading] = useState(true);\n  const {\n    data: worksheetFilters = {}\n  } = useSelectorWrap('workSheetFilterList_rn');\n  const [showMessage, setShowMessage] = useState(false);\n  const [tooltipPosition, setTooltipPosition] = useState(null);\n  const alertIconRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const smicData = worksheetFilters.smicData || [];\n\n  // Safely access displayDate with a fallback\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\n  const displayDate = (displayDateSelector == null ? void 0 : displayDateSelector.data) || {};\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n\n  // const handleTabClick = (tabName: Tabs) => {\n  //   setSelectedTab(tabName);\n  // };\n\n  const handleSyncMonitorClick = () => {\n    setIsDrawerOpen(true);\n  };\n  const handleWeekChange = item => {\n    setSelectedWeek(item);\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\n  };\n  const handlePerformanceSummaryData = data => {\n    setPerformanceSummaryData(data);\n    setDashboardLoading(false);\n  };\n  const handleForecastVarianceData = data => {\n    setForecastVarianceData(data);\n    setDashboardLoading(false);\n  };\n  const renderTabContent = tab => {\n    switch (tab) {\n      case TabsLabels.LEADING_INDICATORS:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Report, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.PERFORMANCE_SUMMARY:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.PERFORMANCE_SUMMARY,\n            onDataLoaded: handlePerformanceSummaryData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.FORECAST_VARIANCE:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.FORECAST_VARIANCE,\n            onDataLoaded: handleForecastVarianceData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const visibleTabs = [TabsLabels.LEADING_INDICATORS, TabsLabels.PERFORMANCE_SUMMARY, TabsLabels.FORECAST_VARIANCE];\n  const classes = 'flex justify-center items-center h-48 text';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-2 py-2 overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\",\n          style: {\n            margin: '5px 10px',\n            padding: '5px',\n            width: '600px',\n            borderColor: 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            initialTab: visibleTabs.indexOf(selectedTab),\n            variant: \"light\",\n            onChange: idx => setSelectedTab(visibleTabs[idx]),\n            className: \"w-full border-transparent dashboard-tab\",\n            children: visibleTabs.map((tab, idx) => /*#__PURE__*/_jsxDEV(Tab, {\n              className: classes,\n              children: /*#__PURE__*/_jsxDEV(Tab.Header, {\n                children: tab === TabsLabels.FORECAST_VARIANCE ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  tabIndex: 2,\n                  onBlur: () => setShowMessage(false),\n                  style: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    gap: '4px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tool-tip-initilizer-top\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      zIndex: 9999,\n                      anchor: \"top\",\n                      variant: \"dark\",\n                      className: 'uds-tooltip-top',\n                      label: ' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.',\n                      children: /*#__PURE__*/_jsxDEV(CircleAlert, {\n                        size: 16,\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        color: \" #1B6EBB\",\n                        onClick: e => {\n                          e.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 138,\n                        columnNumber: 19\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 25\n                  }, this), tab]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 23\n                }, this) : tab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          before: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-4 h-4 flex items-center text-[#1B6EBB]\",\n            children: [\" \", /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 75\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this),\n          className: \"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\",\n          onClick: async () => {\n            console.log('Download button clicked');\n            console.log('Selected tab:', selectedTab);\n            try {\n              // Force refresh data before download\n              let dataToDownload = [];\n              if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\n                console.log('Preparing Performance Summary download...');\n\n                // Use a promise to wait for the data\n                dataToDownload = await new Promise(resolve => {\n                  // Get fresh data\n                  const allocatrComponent = document.querySelector('[data-tab=\"' + TabsLabels.PERFORMANCE_SUMMARY + '\"]');\n\n                  // If we already have data, use it\n                  if (performanceSummaryData && performanceSummaryData.length > 0) {\n                    console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\n                    resolve(performanceSummaryData);\n                  } else {\n                    console.log('No performance data found in state, please try refreshing the page');\n                    resolve([]);\n                  }\n                });\n\n                // If we got data, download it\n                if (dataToDownload && dataToDownload.length > 0) {\n                  console.log('Data ready for download:', dataToDownload.length, 'rows');\n                  handleDownloadExcel(dataToDownload, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  console.error('No data available to download');\n                  alert('No data available to download. Please try refreshing the page.');\n                }\n              } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\n                console.log('Preparing Forecast Variance download...');\n\n                // Use a promise to wait for the data\n                dataToDownload = await new Promise(resolve => {\n                  // If we already have data, use it\n                  if (forecastVarianceData && forecastVarianceData.length > 0) {\n                    console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\n                    resolve(forecastVarianceData);\n                  } else {\n                    console.log('No variance data found in state, please try refreshing the page');\n                    resolve([]);\n                  }\n                });\n\n                // If we got data, download it\n                if (dataToDownload && dataToDownload.length > 0) {\n                  console.log('Data ready for download:', dataToDownload.length, 'rows');\n                  handleDownloadExcel(dataToDownload, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  console.error('No data available to download');\n                  alert('No data available to download. Please try refreshing the page.');\n                }\n              }\n            } catch (error) {\n              console.error('Download error:', error);\n              alert('Error preparing download: ' + (error.message || 'Unknown error'));\n            }\n          },\n          children: \"Download as Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-6\",\n          children: /*#__PURE__*/_jsxDEV(SelectWeek, {\n            weekChange: handleWeekChange,\n            selectedTab: selectedTab\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"sync-button\",\n            size: \"xs\",\n            variant: \"secondary\",\n            onClick: handleSyncMonitorClick,\n            children: \"EPBCS Sync Monitor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: renderTabContent(selectedTab)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      isOpen: isDrawerOpen,\n      setOpen: setIsDrawerOpen,\n      hideBackdrop: false,\n      width: \"608px\",\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"EPBCS Sync Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(EPBCSSyncMonitor, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\nexport default DashboardTabs;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Report", "Drawer", "<PERSON><PERSON>", "useSelectorWrap", "Tabs", "Tab", "EPBCSSyncMonitor", "AllocatrInsights", "SelectWeek", "Circle<PERSON>lert", "<PERSON><PERSON><PERSON>", "Icon", "ReactComponent", "Download", "handleDownloadExcel", "getNowInPST", "format", "jsxDEV", "_jsxDEV", "TabsLabels", "downloadedDate", "DashboardTabs", "selectedTab", "setSelectedTab", "PERFORMANCE_SUMMARY", "isDrawerOpen", "setIsDrawerOpen", "selectedWeek", "setSelectedWeek", "performanceSummaryData", "setPerformanceSummaryData", "forecastVarianceData", "setForecastVarianceData", "dashboardLoading", "setDashboardLoading", "data", "worksheetFilters", "showMessage", "setShowMessage", "tooltipPosition", "setTooltipPosition", "alertIconRef", "tooltipRef", "smicData", "displayDateSelector", "displayDate", "appliedFilters", "handleSyncMonitorClick", "handleWeekChange", "item", "handlePerformanceSummaryData", "handleForecastVarianceData", "renderTabContent", "tab", "LEADING_INDICATORS", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataLoaded", "FORECAST_VARIANCE", "visibleTabs", "classes", "className", "style", "margin", "padding", "width", "borderColor", "initialTab", "indexOf", "variant", "onChange", "idx", "map", "Header", "tabIndex", "onBlur", "display", "alignItems", "gap", "position", "zIndex", "anchor", "label", "size", "cursor", "color", "onClick", "e", "stopPropagation", "before", "console", "log", "dataToDownload", "Promise", "resolve", "allocatrComponent", "document", "querySelector", "length", "error", "alert", "message", "weekChange", "isOpen", "<PERSON><PERSON><PERSON>", "hideBackdrop", "header"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\n// import { createPortal } from 'react-dom';\r\nimport Report from './report';\r\nimport LaggingIndicatorPage from './lagging-indicator-page';\r\nimport \"./dashboard-tabs.scss\";\r\nimport Drawer from '@albertsons/uds/molecule/Drawer';\r\nimport Button from '@albertsons/uds/molecule/Button';\r\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\r\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\r\nimport Tag from '@albertsons/uds/molecule/Tag';\r\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\r\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\r\n// Update the import path and casing to match the actual file location\r\nimport {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';\r\nimport { CircleAlert } from 'lucide-react';\r\nimport { useCurrentQuarterNbr } from '../features/calendarServiceUtils';\r\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';\r\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; \r\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\r\nimport { getNowInPST } from '../util/dateUtils';\r\nimport { format } from 'date-fns-tz';\r\n\r\nenum TabsLabels {\r\n  LEADING_INDICATORS = 'Leading Indicators',\r\n  PERFORMANCE_SUMMARY = 'Performance Summary',\r\n  FORECAST_VARIANCE = 'Performance Variance'\r\n}\r\n\r\n// const tabClassNames = {\r\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\r\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\r\n// };\r\n\r\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\r\nconst DashboardTabs = () => {\r\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);\r\n  const [performanceSummaryData, setPerformanceSummaryData] = useState<any[]>([]);\r\n  const [forecastVarianceData, setForecastVarianceData] = useState<any[]>([]);\r\n  const [dashboardLoading, setDashboardLoading] = useState(true);\r\n  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');\r\n   const [showMessage, setShowMessage] = useState(false);\r\n  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);\r\n  const alertIconRef = useRef<HTMLDivElement>(null);\r\n  const tooltipRef = useRef<HTMLDivElement>(null);\r\n  const smicData = worksheetFilters.smicData || [];\r\n\r\n  // Safely access displayDate with a fallback\r\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\r\n  const displayDate = displayDateSelector?.data || {};\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n\r\n  // const handleTabClick = (tabName: Tabs) => {\r\n  //   setSelectedTab(tabName);\r\n  // };\r\n\r\n  const handleSyncMonitorClick = () => {\r\n    setIsDrawerOpen(true);\r\n  };\r\n\r\n  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {\r\n    setSelectedWeek(item);\r\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\r\n  };\r\n\r\n  const handlePerformanceSummaryData = (data: any[]) => {\r\n    setPerformanceSummaryData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n\r\n  const handleForecastVarianceData = (data: any[]) => {\r\n    setForecastVarianceData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n  const renderTabContent = (tab: TabsLabels) => {\r\n    switch (tab) {\r\n      case TabsLabels.LEADING_INDICATORS:\r\n        return <div><Report /></div>;\r\n      case TabsLabels.PERFORMANCE_SUMMARY:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>\r\n      case TabsLabels.FORECAST_VARIANCE:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const visibleTabs = [\r\n    TabsLabels.LEADING_INDICATORS,\r\n    TabsLabels.PERFORMANCE_SUMMARY,\r\n    TabsLabels.FORECAST_VARIANCE\r\n  ];\r\n  const classes = 'flex justify-center items-center h-48 text';\r\n\r\n  return (\r\n    <div>\r\n\r\n      <div className=\"flex items-center justify-between px-2 py-2 overflow-x-auto\">\r\n        <div className=\"tabs-container\">\r\n\r\n          <div\r\n            className=\"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\"\r\n            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}\r\n          >\r\n\r\n            <Tabs\r\n              initialTab={visibleTabs.indexOf(selectedTab)}\r\n              variant='light'\r\n              onChange={idx => setSelectedTab(visibleTabs[idx])}\r\n              className='w-full border-transparent dashboard-tab'\r\n            >\r\n              {visibleTabs.map((tab, idx) => (\r\n                <Tab className={classes} key={tab}>\r\n                  <Tab.Header>\r\n\r\n                    {tab === TabsLabels.FORECAST_VARIANCE ? (\r\n                      <span\r\n                        tabIndex={2}\r\n                        onBlur={() => setShowMessage(false)}\r\n                        style={{\r\n                          display: 'inline-flex',\r\n                          alignItems: 'center',\r\n                          gap: '4px',\r\n                          position: 'relative'\r\n                        }}>\r\n\r\n                        <div \r\n                          className=\"relative inline-block\"\r\n                        >\r\n                          <span className='tool-tip-initilizer-top'></span>\r\n                        <Tooltip\r\n                  zIndex={9999}\r\n                  anchor='top'\r\n                  variant='dark'\r\n                  className={'uds-tooltip-top'}\r\n                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>\r\n                  <CircleAlert\r\n                    size={16}\r\n                    style={{ cursor: 'pointer' }}\r\n                    color=\" #1B6EBB\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                        </div>\r\n                        \r\n                        {tab}\r\n                      </span>\r\n                    ) : (\r\n                      tab\r\n                    )}\r\n                  </Tab.Header>\r\n              </Tab>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </div>\r\n        <div className=\"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\">\r\n          <Icon\r\n            before={\r\n              <span className=\"w-4 h-4 flex items-center text-[#1B6EBB]\"> <Download/> </span>\r\n            }\r\n            className=\"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\"\r\n            onClick={async () => {\r\n              console.log('Download button clicked');\r\n              console.log('Selected tab:', selectedTab);\r\n              \r\n              try {\r\n                // Force refresh data before download\r\n                let dataToDownload = [];\r\n                \r\n                if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\r\n                  console.log('Preparing Performance Summary download...');\r\n                  \r\n                  // Use a promise to wait for the data\r\n                  dataToDownload = await new Promise<any[]>((resolve) => {\r\n                    // Get fresh data\r\n                    const allocatrComponent = document.querySelector('[data-tab=\"' + TabsLabels.PERFORMANCE_SUMMARY + '\"]');\r\n                    \r\n                    // If we already have data, use it\r\n                    if (performanceSummaryData && performanceSummaryData.length > 0) {\r\n                      console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\r\n                      resolve(performanceSummaryData);\r\n                    } else {\r\n                      console.log('No performance data found in state, please try refreshing the page');\r\n                      resolve([]);\r\n                    }\r\n                  });\r\n                  \r\n                  // If we got data, download it\r\n                  if (dataToDownload && dataToDownload.length > 0) {\r\n                    console.log('Data ready for download:', dataToDownload.length, 'rows');\r\n                    handleDownloadExcel(\r\n                      dataToDownload,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    console.error('No data available to download');\r\n                    alert('No data available to download. Please try refreshing the page.');\r\n                  }\r\n                } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\r\n                  console.log('Preparing Forecast Variance download...');\r\n                  \r\n                  // Use a promise to wait for the data\r\n                  dataToDownload = await new Promise<any[]>((resolve) => {\r\n                    // If we already have data, use it\r\n                    if (forecastVarianceData && forecastVarianceData.length > 0) {\r\n                      console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\r\n                      resolve(forecastVarianceData);\r\n                    } else {\r\n                      console.log('No variance data found in state, please try refreshing the page');\r\n                      resolve([]);\r\n                    }\r\n                  });\r\n                  \r\n                  // If we got data, download it\r\n                  if (dataToDownload && dataToDownload.length > 0) {\r\n                    console.log('Data ready for download:', dataToDownload.length, 'rows');\r\n                    handleDownloadExcel(\r\n                      dataToDownload,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    console.error('No data available to download');\r\n                    alert('No data available to download. Please try refreshing the page.');\r\n                  }\r\n                }\r\n              } catch (error: any) {\r\n                console.error('Download error:', error);\r\n                alert('Error preparing download: ' + (error.message || 'Unknown error'));\r\n              }\r\n            }}\r\n          >Download as Excel\r\n          </Icon>\r\n        </div>\r\n      <div className='flex items-center gap-4'>\r\n           <div className='mr-6'>\r\n        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>\r\n      </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              className=\"sync-button\"\r\n              size=\"xs\"\r\n              variant=\"secondary\"\r\n              onClick={handleSyncMonitorClick}\r\n            >\r\n              EPBCS Sync Monitor\r\n            </Button>\r\n          </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        {renderTabContent(selectedTab)}\r\n      </div>\r\n\r\n      <Drawer\r\n        anchor=\"right\"\r\n        isOpen={isDrawerOpen}\r\n        setOpen={setIsDrawerOpen}\r\n        hideBackdrop={false}\r\n        width=\"608px\"\r\n        header={<div>EPBCS Sync Monitor</div>}\r\n      >\r\n        <EPBCSSyncMonitor />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,iCAAiC;AACpD,OAAOC,MAAM,MAAM,iCAAiC;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAOC,IAAI,IAAIC,GAAG,QAAQ,+BAA+B;AAEzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E;AACA,SAAUC,UAAU,QAAQ,sDAAsD;AAClF,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAOC,OAAO,MAAM,kCAAkC;AAAC,OAAOC,IAAI,MAAM,+BAA+B;AACvG,SAASC,cAAc,IAAIC,QAAQ,QAAQ,uCAAuC;AAClF,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,IAEhCC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA,EAAVA,UAAU,SAMf;AACA;AACA;AACA;AAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACD,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;AAC1D,MAAMM,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAACqB,UAAU,CAACK,mBAAmB,CAAC;EAC9E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAyE,IAAI,CAAC;EAC9H,MAAM,CAAC+B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhC,QAAQ,CAAQ,EAAE,CAAC;EAC/E,MAAM,CAACiC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlC,QAAQ,CAAQ,EAAE,CAAC;EAC3E,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM;IAAEqC,IAAI,EAAEC,gBAAgB,GAAG,CAAC;EAAE,CAAC,GAAGjC,eAAe,CAAC,wBAAwB,CAAC;EAChF,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAM2C,YAAY,GAAG1C,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAM2C,UAAU,GAAG3C,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM4C,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,IAAI,EAAE;;EAEhD;EACA,MAAMC,mBAAmB,GAAGzC,eAAe,CAAC,gBAAgB,CAAC;EAC7D,MAAM0C,WAAW,GAAG,CAAAD,mBAAmB,oBAAnBA,mBAAmB,CAAET,IAAI,KAAI,CAAC,CAAC;EACnD,MAAM;IAAEA,IAAI,EAAEW;EAAe,CAAC,GAAG3C,eAAe,CAAC,kBAAkB,CAAC;;EAEpE;EACA;EACA;;EAEA,MAAM4C,sBAAsB,GAAGA,CAAA,KAAM;IACnCrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMsB,gBAAgB,GAAIC,IAAqE,IAAK;IAClGrB,eAAe,CAACqB,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMC,4BAA4B,GAAIf,IAAW,IAAK;IACpDL,yBAAyB,CAACK,IAAI,CAAC;IAC/BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMiB,0BAA0B,GAAIhB,IAAW,IAAK;IAClDH,uBAAuB,CAACG,IAAI,CAAC;IAC7BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMkB,gBAAgB,GAAIC,GAAe,IAAK;IAC5C,QAAQA,GAAG;MACT,KAAKlC,UAAU,CAACmC,kBAAkB;QAChC,oBAAOpC,OAAA;UAAAqC,QAAA,eAAKrC,OAAA,CAAClB,MAAM;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9B,KAAKxC,UAAU,CAACK,mBAAmB;QACjC,oBAAON,OAAA;UAAAqC,QAAA,eAAKrC,OAAA,CAACX,gBAAgB;YAACe,WAAW,EAAEH,UAAU,CAACK,mBAAoB;YAACoC,YAAY,EAAEV;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChI,KAAKxC,UAAU,CAAC0C,iBAAiB;QAC/B,oBAAO3C,OAAA;UAAAqC,QAAA,eAAKrC,OAAA,CAACX,gBAAgB;YAACe,WAAW,EAAEH,UAAU,CAAC0C,iBAAkB;YAACD,YAAY,EAAET;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5H;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMG,WAAW,GAAG,CAClB3C,UAAU,CAACmC,kBAAkB,EAC7BnC,UAAU,CAACK,mBAAmB,EAC9BL,UAAU,CAAC0C,iBAAiB,CAC7B;EACD,MAAME,OAAO,GAAG,4CAA4C;EAE5D,oBACE7C,OAAA;IAAAqC,QAAA,gBAEErC,OAAA;MAAK8C,SAAS,EAAC,6DAA6D;MAAAT,QAAA,gBAC1ErC,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAE7BrC,OAAA;UACE8C,SAAS,EAAC,8IAA8I;UACxJC,KAAK,EAAE;YAAEC,MAAM,EAAE,UAAU;YAAEC,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAC,OAAO;YAAEC,WAAW,EAAE;UAAc,CAAE;UAAAd,QAAA,eAGzFrC,OAAA,CAACd,IAAI;YACHkE,UAAU,EAAER,WAAW,CAACS,OAAO,CAACjD,WAAW,CAAE;YAC7CkD,OAAO,EAAC,OAAO;YACfC,QAAQ,EAAEC,GAAG,IAAInD,cAAc,CAACuC,WAAW,CAACY,GAAG,CAAC,CAAE;YAClDV,SAAS,EAAC,yCAAyC;YAAAT,QAAA,EAElDO,WAAW,CAACa,GAAG,CAAC,CAACtB,GAAG,EAAEqB,GAAG,kBACxBxD,OAAA,CAACb,GAAG;cAAC2D,SAAS,EAAED,OAAQ;cAAAR,QAAA,eACtBrC,OAAA,CAACb,GAAG,CAACuE,MAAM;gBAAArB,QAAA,EAERF,GAAG,KAAKlC,UAAU,CAAC0C,iBAAiB,gBACnC3C,OAAA;kBACE2D,QAAQ,EAAE,CAAE;kBACZC,MAAM,EAAEA,CAAA,KAAMxC,cAAc,CAAC,KAAK,CAAE;kBACpC2B,KAAK,EAAE;oBACLc,OAAO,EAAE,aAAa;oBACtBC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,KAAK;oBACVC,QAAQ,EAAE;kBACZ,CAAE;kBAAA3B,QAAA,gBAEFrC,OAAA;oBACE8C,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,gBAEjCrC,OAAA;sBAAM8C,SAAS,EAAC;oBAAyB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDzC,OAAA,CAACR,OAAO;sBACdyE,MAAM,EAAE,IAAK;sBACbC,MAAM,EAAC,KAAK;sBACZZ,OAAO,EAAC,MAAM;sBACdR,SAAS,EAAE,iBAAkB;sBAC7BqB,KAAK,EAAE,0HAA2H;sBAAA9B,QAAA,eAClIrC,OAAA,CAACT,WAAW;wBACV6E,IAAI,EAAE,EAAG;wBACTrB,KAAK,EAAE;0BAAEsB,MAAM,EAAE;wBAAU,CAAE;wBAC7BC,KAAK,EAAC,UAAU;wBAChBC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACrB;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,EAELN,GAAG;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GAEPN;cACD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC,GAxCeN,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyC9B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJzC,OAAA;QAAK8C,SAAS,EAAC,oEAAoE;QAAAT,QAAA,eACjFrC,OAAA,CAACP,IAAI;UACHiF,MAAM,eACJ1E,OAAA;YAAM8C,SAAS,EAAC,0CAA0C;YAAAT,QAAA,GAAC,GAAC,eAAArC,OAAA,CAACL,QAAQ;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC/E;UACDK,SAAS,EAAC,8FAA8F;UACxGyB,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnBI,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACtCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAExE,WAAW,CAAC;YAEzC,IAAI;cACF;cACA,IAAIyE,cAAc,GAAG,EAAE;cAEvB,IAAIzE,WAAW,KAAKH,UAAU,CAACK,mBAAmB,EAAE;gBAClDqE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;gBAExD;gBACAC,cAAc,GAAG,MAAM,IAAIC,OAAO,CAASC,OAAO,IAAK;kBACrD;kBACA,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,aAAa,GAAGjF,UAAU,CAACK,mBAAmB,GAAG,IAAI,CAAC;;kBAEvG;kBACA,IAAIK,sBAAsB,IAAIA,sBAAsB,CAACwE,MAAM,GAAG,CAAC,EAAE;oBAC/DR,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEjE,sBAAsB,CAACwE,MAAM,EAAE,MAAM,CAAC;oBACtFJ,OAAO,CAACpE,sBAAsB,CAAC;kBACjC,CAAC,MAAM;oBACLgE,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;oBACjFG,OAAO,CAAC,EAAE,CAAC;kBACb;gBACF,CAAC,CAAC;;gBAEF;gBACA,IAAIF,cAAc,IAAIA,cAAc,CAACM,MAAM,GAAG,CAAC,EAAE;kBAC/CR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,cAAc,CAACM,MAAM,EAAE,MAAM,CAAC;kBACtEvF,mBAAmB,CACjBiF,cAAc,EACdpD,QAAQ,EACRG,cAAc,EACd,wDAAwD1B,cAAc,OACxE,CAAC;gBACH,CAAC,MAAM;kBACLyE,OAAO,CAACS,KAAK,CAAC,+BAA+B,CAAC;kBAC9CC,KAAK,CAAC,gEAAgE,CAAC;gBACzE;cACF,CAAC,MAAM,IAAIjF,WAAW,KAAKH,UAAU,CAAC0C,iBAAiB,EAAE;gBACvDgC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;gBAEtD;gBACAC,cAAc,GAAG,MAAM,IAAIC,OAAO,CAASC,OAAO,IAAK;kBACrD;kBACA,IAAIlE,oBAAoB,IAAIA,oBAAoB,CAACsE,MAAM,GAAG,CAAC,EAAE;oBAC3DR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE/D,oBAAoB,CAACsE,MAAM,EAAE,MAAM,CAAC;oBACjFJ,OAAO,CAAClE,oBAAoB,CAAC;kBAC/B,CAAC,MAAM;oBACL8D,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;oBAC9EG,OAAO,CAAC,EAAE,CAAC;kBACb;gBACF,CAAC,CAAC;;gBAEF;gBACA,IAAIF,cAAc,IAAIA,cAAc,CAACM,MAAM,GAAG,CAAC,EAAE;kBAC/CR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,cAAc,CAACM,MAAM,EAAE,MAAM,CAAC;kBACtEvF,mBAAmB,CACjBiF,cAAc,EACdpD,QAAQ,EACRG,cAAc,EACd,qDAAqD1B,cAAc,OACrE,CAAC;gBACH,CAAC,MAAM;kBACLyE,OAAO,CAACS,KAAK,CAAC,+BAA+B,CAAC;kBAC9CC,KAAK,CAAC,gEAAgE,CAAC;gBACzE;cACF;YACF,CAAC,CAAC,OAAOD,KAAU,EAAE;cACnBT,OAAO,CAACS,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;cACvCC,KAAK,CAAC,4BAA4B,IAAID,KAAK,CAACE,OAAO,IAAI,eAAe,CAAC,CAAC;YAC1E;UACF,CAAE;UAAAjD,QAAA,EACH;QACD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRzC,OAAA;QAAK8C,SAAS,EAAC,yBAAyB;QAAAT,QAAA,gBACnCrC,OAAA;UAAK8C,SAAS,EAAC,MAAM;UAAAT,QAAA,eACxBrC,OAAA,CAACV,UAAU;YAACiG,UAAU,EAAEzD,gBAAiB;YAAC1B,WAAW,EAAEA;UAAY;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACFzC,OAAA;UAAK8C,SAAS,EAAC,yBAAyB;UAAAT,QAAA,eACtCrC,OAAA,CAAChB,MAAM;YACL8D,SAAS,EAAC,aAAa;YACvBsB,IAAI,EAAC,IAAI;YACTd,OAAO,EAAC,WAAW;YACnBiB,OAAO,EAAE1C,sBAAuB;YAAAQ,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENzC,OAAA;MAAK8C,SAAS,EAAC,iBAAiB;MAAAT,QAAA,EAC7BH,gBAAgB,CAAC9B,WAAW;IAAC;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAENzC,OAAA,CAACjB,MAAM;MACLmF,MAAM,EAAC,OAAO;MACdsB,MAAM,EAAEjF,YAAa;MACrBkF,OAAO,EAAEjF,eAAgB;MACzBkF,YAAY,EAAE,KAAM;MACpBxC,KAAK,EAAC,OAAO;MACbyC,MAAM,eAAE3F,OAAA;QAAAqC,QAAA,EAAK;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eAEtCrC,OAAA,CAACZ,gBAAgB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAED,eAAetC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}