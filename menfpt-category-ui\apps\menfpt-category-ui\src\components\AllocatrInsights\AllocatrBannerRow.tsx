import React, { forwardRef } from 'react';
import { BannerData } from '../../interfaces/allocatr-insights';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { renderAllRows } from './utils/tableCell';

interface AllocatrBannerRowProps {
  banner: BannerData;
  expanded: boolean;
  onToggle: (id: string) => void;
  isQuarterActualUsed?: boolean;
}

const AllocatrBannerRow = forwardRef<HTMLTableRowElement, AllocatrBannerRowProps>(({ banner, expanded, onToggle, isQuarterActualUsed }, ref) => (
  <tr ref={ref} key={`banner-${banner.id}`} className="banner-row bg-[#fff]">
    <td>
        <button className="cursor-pointer" onClick={() => onToggle(banner.id)}>
            {expanded ? <ChevronUp size={14}/> : <ChevronDown size={14} />}
        </button>
    </td>
    <td className="banner-cell">
      <div className="flex items-center justify-start h-full">
        <div className="max-w-[180px] min-w-0 flex items-center">
          <span className="banner-name-cell font-bold block w-full min-w-0 break-words" title={banner.name}>
            {banner.name}
          </span>
        </div>
      </div>
    </td>
    {banner.quarter
      ? renderAllRows(banner.quarter, isQuarterActualUsed)
      : Array.from({ length: 29 }).map((_, i) => <td key={i}></td>)}
  </tr>
));

export default AllocatrBannerRow;