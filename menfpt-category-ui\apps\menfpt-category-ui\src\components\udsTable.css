.hide {
    background-color: #F9F9FB;;
}

:root {
    --worksheet-filter-height: 100px; /* Adjust this value based on your worksheetFilter height */
  }

table thead
{
    position: sticky;
    top: var(--worksheet-filter-height); 
    z-index: 1;
}

table td {
    color: #2B303C;
}
.worksheet-table thead {
    position: relative;
}


/* Tooltip styles */
.tooltip-text {
    cursor: pointer;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
}

.uds-modal-center-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%; 
  min-height: 308px; 
}