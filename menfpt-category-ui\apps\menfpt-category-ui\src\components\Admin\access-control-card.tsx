import React, { useMemo, useState } from "react";
import AccessControlTable from "../../components/Admin/access-control-table";
import Card from "@albertsons/uds/molecule/Card";
import AccessControlCardHeader from "./access-control-card-header";
import Drawer from "@albertsons/uds/molecule/Drawer";
import AccessControlFilters, { Filters } from "../Admin/access-control-drawer";
import UserModal from "./addUserModal";
import EditUserAccessModal from "./editUserAccess";
import tableData from "../Admin/access-control-mock-data.json";

const AccessControlCard = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<Filters>({});
  const [isOpen, setOpen] = useState(false);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [selectedUserData, setSelectedUserData] = useState<any>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const roleOptions = useMemo(
    () => Array.from(new Set(tableData.map((r) => r.role).filter(Boolean))),
    []
  );

  const statusOptions = useMemo(
    () =>
      Array.from(
        new Set(tableData.map((r) => r.accessStatus).filter(Boolean))
      ),
    []
  );

  const departmentOptions = useMemo(
    () =>
      Array.from(
        new Set(tableData.map((r) => r.oracleDepartment).filter(Boolean))
      ),
    []
  );
  const filteredData = useMemo(() => {
    return tableData.filter((row) => {
      const matchSearch = searchTerm
        ? Object.values(row).some((value) =>
            String(value).toLowerCase().includes(searchTerm.toLowerCase())
          )
        : true;

      const matchFilters =
        (!filters.userName ||
          String(row.userName)
            .toLowerCase()
            .includes(filters.userName.toLowerCase())) &&
        (!filters.role || row.role === filters.role) &&
        (!filters.accessStatus ||
          row.accessStatus === filters.accessStatus) &&
        (!filters.oracleDepartment ||
          row.oracleDepartment === filters.oracleDepartment);

      return matchSearch && matchFilters;
    });
  }, [searchTerm, filters]);

  const handleEditAccess = (userData: any) => {
    setSelectedUserData(userData);
    setShowEditUserModal(true);
  };

  return (
    <div>
      <Card className="min-w-[600px] m-5">
        <Card.Header>
          <AccessControlCardHeader
            title="Access List"
            count={filteredData.length}
            location="Seattle"
            onSearch={setSearchTerm}
            onFilter={() => setOpen(true)}
            onReset={() => {
              setSearchTerm("");
              setFilters({});
            }}
            onDownload={() => console.log("Download clicked")}
            onAddUser={() => setShowAddUserModal(true)}
            showSuccessAlert={showSuccessAlert}
          />
        </Card.Header>
        <Card.Content noPadding>
          <AccessControlTable 
            isAdmin={true} 
            items={filteredData} 
            onEditAccess={handleEditAccess}
          />
        </Card.Content>
      </Card>

      {/* Add User Modal */}
      <UserModal 
        isOpen={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        onSubmit={(values) => {
          console.log("User submitted:", values);
          setShowSuccessAlert(true)
        }}
      />
      <EditUserAccessModal 
        isOpen={showEditUserModal}
        onClose={() => setShowEditUserModal(false)}
        userData={selectedUserData}
      />

      {/* Drawer */}
      <Drawer
        anchor="right"
        isOpen={isOpen}
        setOpen={setOpen}
        hideBackdrop={false}
        header={<div className="text-lg font-semibold">Filters</div>}
      >
        <AccessControlFilters
          initialValues={filters}
          roleOptions={roleOptions}
          statusOptions={statusOptions}
          departmentOptions={departmentOptions}
          onApply={(values) => {
            setFilters(values);
            setOpen(false);
          }}
          onReset={() => {
            setFilters({});
            setOpen(false);
          }}
        />
      </Drawer>
    </div>
  );
};

export default AccessControlCard;
