import { DropdownType } from '../../interfaces/worksheetFilter';

export const getDepartmentsForDivisions = (
  FiltersList: any[],
  selectedDivisions: DropdownType[]
): DropdownType[] => {
  if (selectedDivisions.length === 0 || !FiltersList?.length) {
    return [];
  }

  const departmentList: DropdownType[] = [];
  const departmentMap = new Map<string, Set<string>>();

  // First pass: collect all desk names for each department
  FiltersList.forEach((item: any) => {
    if (selectedDivisions.some((div) => div.num === item.divisionId)) {
      const deptId = item?.deptId;
      if (!departmentMap.has(deptId)) {
        departmentMap.set(deptId, new Set());
      }
      if (item?.deskName) {
        departmentMap.get(deptId)?.add(item.deskName);
      }
    }
  });

  // Second pass: create department objects with deskNameArr
  FiltersList.forEach((item: any) => {
    if (selectedDivisions.some((div) => div.num === item.divisionId)) {
      const dept = {
        name: `${item?.deptId} - ${item?.deptName}`,
        num: item?.deptId,
        deskNameArr: Array.from(departmentMap.get(item?.deptId) || []),
      };
      if (!departmentList.some((d) => d.num === dept.num)) {
        departmentList.push(dept);
      }
    }
  });
  return departmentList;
};

/**
 * Updates departments and desks based on selected divisions and filters list.
 * Modularized from useWorksheetFilterState for reuse.
 */
export const updateDepartmentsAndDesks = (
  selectedDivision: DropdownType[],
  FiltersList: any[],
  setDepartments: (departments: DropdownType[]) => void,
  setDesks: (desks: DropdownType[]) => void,
  setSelectedDepartment: (department: DropdownType | DropdownType[] | undefined) => void,
  setSelectedDesk: (desk: DropdownType | undefined) => void,
  getDesksForDivision: (FiltersList: any[], selectedDivision: DropdownType) => DropdownType[]
) => {
  if (selectedDivision.length > 0 && FiltersList?.length > 0) {
    const departmentList = getDepartmentsForDivisions(
      FiltersList,
      selectedDivision
    );
    setDepartments(departmentList);

    // If only one division is selected, get desks for that division
    if (selectedDivision.length === 1) {
      const deskList = getDesksForDivision(FiltersList, selectedDivision[0]);
      setDesks(deskList);
    } else {
      setDesks([]);
      setSelectedDesk(undefined);
    }
  } else {
    setDepartments([]);
    setSelectedDepartment(undefined);
    setDesks([]);
    setSelectedDesk(undefined);
  }
};
