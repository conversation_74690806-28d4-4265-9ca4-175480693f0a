import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setAppliedFilter } from '../../server/Reducer/menfpt-category.slice';
import { 
  FilterState, 
  AppliedFilterState, 
  WorksheetFilterContextType 
} from './worksheetFilterTypes';
import { useLocation } from 'react-router-dom';
import { worksheetFilterConfig } from './worksheetFilterConfig';
import { serializeSmData } from './utils/serializationUtils';

const defaultFilterState: FilterState = {
  division: [],
  department: undefined,
  desk: undefined,
  category: [],
};

const defaultAppliedFilters: AppliedFilterState = {
  division: [],
  department: undefined,
  desk: undefined,
  timeframe: undefined,
};

const WorksheetFilterContext = createContext<WorksheetFilterContextType | undefined>(undefined);

export const WorksheetFilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [filterState, setFilterState] = useState<FilterState>(defaultFilterState);
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilterState>(defaultAppliedFilters);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const dispatch = useDispatch();
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    const isTimeFrameAllowed = worksheetFilterConfig.isDisplayTimeFrame.includes(currentPath);
    
    if (!isTimeFrameAllowed && appliedFilters.timeframe) {
      setAppliedFilters(prev => ({
        ...prev,
        timeframe: undefined
      }));
    }
  }, [location.pathname]);

  const openFilterModal = () => {
    // Set the modal state based on applied filters
    setFilterState({
      ...filterState,
      division: appliedFilters.division,
      department: appliedFilters.department,
      desk: appliedFilters.desk,
    });
    setIsFilterModalOpen(true);
  };

  const closeFilterModal = () => {
    setIsFilterModalOpen(false);
  };

  const applyFilters = (newFilters: AppliedFilterState) => {
    dispatch(setAppliedFilter({
      division: newFilters.division,
      department: newFilters.department,
      desk: newFilters.desk,
      category: filterState.category,
      timeframe: newFilters.timeframe,
      periods: newFilters.periods,
      selectedSm: serializeSmData(newFilters.selectedSm ?? new Map()),
      selectedWeeks: newFilters.selectedWeeks,
      filterPg: newFilters.filterPg,
    }));

    setAppliedFilters(newFilters);
    closeFilterModal();
  };

  return (
    <WorksheetFilterContext.Provider
      value={{
        filterState,
        appliedFilters,
        setFilterState,
        setAppliedFilters: applyFilters,
        openFilterModal,
        closeFilterModal,
        isFilterModalOpen,
      }}
    >
      {children}
    </WorksheetFilterContext.Provider>
  );
};

export const useWorksheetFilter = (): WorksheetFilterContextType => {
  const context = useContext(WorksheetFilterContext);
  if (context === undefined) {
    throw new Error('useWorksheetFilter must be used within a WorksheetFilterProvider');
  }
  return context;
};
