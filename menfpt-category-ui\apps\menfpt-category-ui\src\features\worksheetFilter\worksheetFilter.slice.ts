import { createGenericSlice } from 'apps/menfpt-category-ui/src/rtk/rtk-slice';

/* Store the selected tab in the filter */
export const activeTabInFilterSlice = createGenericSlice({
  name: 'activeTabInFilter_rn',
  initialState: { status: 'loading', data: ['department'] },
})({
  setActiveTabInFilter(state, { payload }) {
    state.data = payload;
  },
});

export const { setActiveTabInFilter } = activeTabInFilterSlice.actions;
