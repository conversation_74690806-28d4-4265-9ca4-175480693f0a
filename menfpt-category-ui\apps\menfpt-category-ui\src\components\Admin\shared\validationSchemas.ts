import * as Yup from 'yup';

// Validation schema for add user form
export const AddUserSchema = Yup.object().shape({
  role: Yup.string().required('Role is required'),
  userName: Yup.string().required('User Name is required'),
  manager: Yup.string().required('Manager is required'),
  desk: Yup.string().required('Desk is required'),
});

export const deactivateAndReplaceSchema = Yup.object().shape({
  userName: Yup.string().required('User Name is required'),
  desk: Yup.string().required('Desk is required'),
});
