{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\pages\\\\dashboard-tabs.tsx\";\nimport React, { useState, useRef } from 'react';\n// import { createPortal } from 'react-dom';\nimport Report from './report';\nimport \"./dashboard-tabs.scss\";\nimport Drawer from '@albertsons/uds/molecule/Drawer';\nimport Button from '@albertsons/uds/molecule/Button';\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\n// Update the import path and casing to match the actual file location\nimport { SelectWeek } from './../components/SnapShotDropDown/release-week-select';\nimport { CircleAlert } from 'lucide-react';\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';\nimport Icon from '@albertsons/uds/molecule/Link';\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg';\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\nimport { getNowInPST } from '../util/dateUtils';\nimport { format } from 'date-fns-tz';\n\n// Extend the Window interface to include __ALLOCATR_DATA__\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar TabsLabels = /*#__PURE__*/function (TabsLabels) {\n  TabsLabels[\"LEADING_INDICATORS\"] = \"Leading Indicators\";\n  TabsLabels[\"PERFORMANCE_SUMMARY\"] = \"Performance Summary\";\n  TabsLabels[\"FORECAST_VARIANCE\"] = \"Performance Variance\";\n  return TabsLabels;\n}(TabsLabels || {}); // const tabClassNames = {\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\n// };\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\n\n// Add this function above your DashboardTabs component\n\n// Explicitly type the function and result array\nconst transformDataForExcel = data => {\n  // Handle hierarchical data from BFF response (which is an object with a 'divisions' property)\n  if (data && data.divisions && Array.isArray(data.divisions)) {\n    console.log('Data is in hierarchical format, using as-is');\n    // The data is already in the correct structure.\n    // We just need to ensure periods and weeks are correctly placed within each department.\n    data.divisions.forEach(division => {\n      division.banners.forEach(banner => {\n        banner.departments.forEach(department => {\n          // The BFF response provides periods and weeks at the department level.\n          // We can directly use them.\n          // If weeks were outside periods, we would group them here.\n        });\n      });\n    });\n    return data.divisions;\n  }\n\n  // The existing logic for converting flat data to hierarchical structure\n  const flatData = Array.isArray(data) ? data : [];\n  if (flatData.length === 0) {\n    console.log('No valid data to transform');\n    return [];\n  }\n  console.log('Converting flat data to hierarchical structure');\n\n  // Create a hierarchical structure\n  const divisions = {};\n  const result = [];\n\n  // First pass: create divisions and collect departments with their data\n  const departmentsByDivision = {};\n  data.forEach(item => {\n    // First column usually contains hierarchy information\n    const hierarchyField = Object.keys(item)[0]; // Get the first column name\n    const divisionInfo = item[hierarchyField] || '';\n\n    // Check if it's a division (format: \"34 - Mid-Atlantic\")\n    if (/^\\d+ - /.test(divisionInfo)) {\n      const [divId, ...divNameParts] = divisionInfo.split(' - ');\n      const divName = divNameParts.join(' - ');\n      if (!divisions[divId]) {\n        // Create division\n        const division = {\n          id: divId,\n          name: divName,\n          quarter: {\n            line1Projection: item[\"$ Projection\"],\n            lastYear: item[\"$ Last Year\"],\n            actualOrForecast: item[\"$Actual/Merch.Forecast\"] || item[\"$ Actual/Merch. Forecast\"],\n            idPercentage: item[\"Keeper% (Includes ID)\"],\n            vsLY: {\n              value: item[\"$ vs LY\"]\n            },\n            vsProjection: {\n              value: item[\"$ vs Projection\"]\n            }\n          },\n          banners: []\n        };\n\n        // Add default banner (will contain departments not assigned to specific banners)\n        division.banners.push({\n          id: \"00\",\n          name: \"Default\",\n          quarter: {},\n          departments: []\n        });\n        divisions[divId] = division;\n        result.push(division);\n        departmentsByDivision[divId] = [];\n      }\n    }\n    // Check if it's a department (format: \"3070000 - Tobacco\")\n    else if (/^\\d{7} - /.test(divisionInfo)) {\n      // Store the department info to be processed in second pass\n      const [deptId, ...deptNameParts] = divisionInfo.split(' - ');\n      const deptName = deptNameParts.join(' - ');\n\n      // Find the associated division (use the most recently created division)\n      const lastDivId = Object.keys(divisions).pop();\n      if (lastDivId) {\n        departmentsByDivision[lastDivId] = departmentsByDivision[lastDivId] || [];\n        departmentsByDivision[lastDivId].push({\n          id: deptId,\n          name: deptName,\n          data: item\n        });\n      }\n    }\n    // Other rows (periods, weeks) will be handled separately\n  });\n\n  // Second pass: Assign departments to their divisions\n  Object.keys(divisions).forEach(divId => {\n    const division = divisions[divId];\n    const departments = departmentsByDivision[divId] || [];\n\n    // For simplicity, assign all departments to the default banner\n    // In a real implementation, you'd determine the banner based on your data structure\n    departments.forEach(dept => {\n      division.banners[0].departments.push({\n        id: dept.id,\n        name: dept.name,\n        quarter: {\n          line1Projection: dept.data[\"$ Projection\"],\n          lastYear: dept.data[\"$ Last Year\"],\n          actualOrForecast: dept.data[\"$Actual/Merch.Forecast\"] || dept.data[\"$ Actual/Merch. Forecast\"],\n          idPercentage: dept.data[\"Keeper% (Includes ID)\"],\n          vsLY: {\n            value: dept.data[\"$ vs LY\"]\n          },\n          vsProjection: {\n            value: dept.data[\"$ vs Projection\"]\n          }\n        },\n        periods: [],\n        // These will be populated if period/week data is available in flat structure\n        weeks: [] // These will be populated if period/week data is available in flat structure\n      });\n    });\n\n    // If you have banner information, you would create additional banners here\n  });\n  console.log('Transformed data:', result);\n  return result;\n};\nconst DashboardTabs = () => {\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [selectedWeek, setSelectedWeek] = useState(null);\n  const [performanceSummaryData, setPerformanceSummaryData] = useState([]);\n  const [forecastVarianceData, setForecastVarianceData] = useState([]);\n  const [dashboardLoading, setDashboardLoading] = useState(true);\n  const {\n    data: worksheetFilters = {}\n  } = useSelectorWrap('workSheetFilterList_rn');\n  const [showMessage, setShowMessage] = useState(false);\n  const [tooltipPosition, setTooltipPosition] = useState(null);\n  const alertIconRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const smicData = worksheetFilters.smicData || [];\n\n  // Safely access displayDate with a fallback\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\n  const displayDate = (displayDateSelector == null ? void 0 : displayDateSelector.data) || {};\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n\n  // const handleTabClick = (tabName: Tabs) => {\n  //   setSelectedTab(tabName);\n  // };\n\n  const handleSyncMonitorClick = () => {\n    setIsDrawerOpen(true);\n  };\n  const handleWeekChange = item => {\n    setSelectedWeek(item);\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\n  };\n  const handlePerformanceSummaryData = data => {\n    setPerformanceSummaryData(data);\n    setDashboardLoading(false);\n  };\n  const handleForecastVarianceData = data => {\n    setForecastVarianceData(data);\n    setDashboardLoading(false);\n  };\n  const renderTabContent = tab => {\n    switch (tab) {\n      case TabsLabels.LEADING_INDICATORS:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Report, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.PERFORMANCE_SUMMARY:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.PERFORMANCE_SUMMARY,\n            onDataLoaded: handlePerformanceSummaryData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.FORECAST_VARIANCE:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.FORECAST_VARIANCE,\n            onDataLoaded: handleForecastVarianceData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const visibleTabs = [TabsLabels.LEADING_INDICATORS, TabsLabels.PERFORMANCE_SUMMARY, TabsLabels.FORECAST_VARIANCE];\n  const classes = 'flex justify-center items-center h-48 text';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-2 py-2 overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\",\n          style: {\n            margin: '5px 10px',\n            padding: '5px',\n            width: '600px',\n            borderColor: 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            initialTab: visibleTabs.indexOf(selectedTab),\n            variant: \"light\",\n            onChange: idx => setSelectedTab(visibleTabs[idx]),\n            className: \"w-full border-transparent dashboard-tab\",\n            children: visibleTabs.map((tab, idx) => /*#__PURE__*/_jsxDEV(Tab, {\n              className: classes,\n              children: /*#__PURE__*/_jsxDEV(Tab.Header, {\n                children: tab === TabsLabels.FORECAST_VARIANCE ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  tabIndex: 2,\n                  onBlur: () => setShowMessage(false),\n                  style: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    gap: '4px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tool-tip-initilizer-top\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      zIndex: 9999,\n                      anchor: \"top\",\n                      variant: \"dark\",\n                      className: 'uds-tooltip-top',\n                      label: ' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.',\n                      children: /*#__PURE__*/_jsxDEV(CircleAlert, {\n                        size: 16,\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        color: \" #1B6EBB\",\n                        onClick: e => {\n                          e.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 19\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 25\n                  }, this), tab]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this) : tab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          before: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-4 h-4 flex items-center text-[#1B6EBB]\",\n            children: [\" \", /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 75\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this),\n          className: \"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\",\n          onClick: async () => {\n            console.log('Download button clicked');\n            console.log('Selected tab:', selectedTab);\n            try {\n              if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\n                console.log('Preparing Performance Summary download...');\n\n                // Try to extract data directly from the DOM\n                // First check if we already have data in state\n                if (performanceSummaryData && performanceSummaryData.length > 0) {\n                  console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\n                  const transformedData = transformDataForExcel(performanceSummaryData);\n                  console.log('Transformed data structure:', transformedData);\n                  handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  // Try to extract data from the table directly\n                  console.log('Attempting to extract data from UI...');\n                  try {\n                    // Find table in the DOM\n                    const tableEl = document.querySelector('.allocatr-insights-table');\n                    if (tableEl) {\n                      console.log('Found table element, extracting data...');\n\n                      // Extracting headers\n                      const headers = Array.from(tableEl.querySelectorAll('thead th')).map(th => th.textContent);\n\n                      // Extracting rows\n                      const rows = Array.from(tableEl.querySelectorAll('tbody tr')).map(tr => Array.from(tr.querySelectorAll('td')).map(td => td.textContent));\n                      console.log(`Extracted ${rows.length} rows from table`);\n                      if (rows.length > 0) {\n                        // Convert to array of objects\n                        const extractedData = rows.map(row => {\n                          const rowData = {};\n                          headers.forEach((header, index) => {\n                            if (header) rowData[header] = row[index];\n                          });\n                          return rowData;\n                        });\n                        console.log('Successfully extracted data from table');\n                        const transformedData = transformDataForExcel(extractedData);\n                        console.log('Transformed data structure:', transformedData);\n                        handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                        return;\n                      }\n                    }\n\n                    // Try global variable as fallback\n                    const dashboardData = window.__ALLOCATR_DATA__ || [];\n                    if (dashboardData && dashboardData.length > 0) {\n                      console.log('Found dashboard data in global variable:', dashboardData.length, 'items');\n                      const transformedData = transformDataForExcel(dashboardData);\n                      console.log('Transformed data structure:', transformedData);\n                      handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                      return;\n                    }\n\n                    // Force refresh data from AllocatrInsights component\n                    // The DOM element does not have a 'refresh' method; remove this block or implement refresh logic differently.\n                    // If you need to trigger a refresh, consider using state or props to re-fetch data in AllocatrInsights.\n                    // For now, just log that refresh is not available.\n                    console.log('No refresh method available on DOM element. Please refresh data via state or props.');\n                    console.error('Could not find data to download');\n                    alert('No data available to download. The data is visible but not accessible for download. Please try refreshing the page.');\n                  } catch (extractError) {\n                    console.error('Error extracting data:', extractError);\n                    alert('Error extracting data for download. Please try again later.');\n                  }\n                }\n              } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\n                // Similar approach for forecast variance\n                if (forecastVarianceData && forecastVarianceData.length > 0) {\n                  console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\n                  handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  console.error('No variance data available to download');\n                  alert('No data available to download. Please try refreshing the page.');\n                }\n              }\n            } catch (error) {\n              console.error('Download error:', error);\n              alert('Error preparing download: ' + (error.message || 'Unknown error'));\n            }\n          },\n          children: \"Download as Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-6\",\n          children: /*#__PURE__*/_jsxDEV(SelectWeek, {\n            weekChange: handleWeekChange,\n            selectedTab: selectedTab\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"sync-button\",\n            size: \"xs\",\n            variant: \"secondary\",\n            onClick: handleSyncMonitorClick,\n            children: \"EPBCS Sync Monitor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: renderTabContent(selectedTab)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      isOpen: isDrawerOpen,\n      setOpen: setIsDrawerOpen,\n      hideBackdrop: false,\n      width: \"608px\",\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"EPBCS Sync Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(EPBCSSyncMonitor, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\nexport default DashboardTabs;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Report", "Drawer", "<PERSON><PERSON>", "useSelectorWrap", "Tabs", "Tab", "EPBCSSyncMonitor", "AllocatrInsights", "SelectWeek", "Circle<PERSON>lert", "<PERSON><PERSON><PERSON>", "Icon", "ReactComponent", "Download", "handleDownloadExcel", "getNowInPST", "format", "jsxDEV", "_jsxDEV", "TabsLabels", "downloadedDate", "transformDataForExcel", "data", "divisions", "Array", "isArray", "console", "log", "for<PERSON>ach", "division", "banners", "banner", "departments", "department", "flatData", "length", "result", "departmentsByDivision", "item", "hierarchyField", "Object", "keys", "divisionInfo", "test", "divId", "divNameParts", "split", "divName", "join", "id", "name", "quarter", "line1Projection", "lastYear", "actualOrForecast", "idPercentage", "vsLY", "value", "vsProjection", "push", "deptId", "deptNameParts", "deptName", "lastDivId", "pop", "dept", "periods", "weeks", "DashboardTabs", "selectedTab", "setSelectedTab", "PERFORMANCE_SUMMARY", "isDrawerOpen", "setIsDrawerOpen", "selectedWeek", "setSelectedWeek", "performanceSummaryData", "setPerformanceSummaryData", "forecastVarianceData", "setForecastVarianceData", "dashboardLoading", "setDashboardLoading", "worksheetFilters", "showMessage", "setShowMessage", "tooltipPosition", "setTooltipPosition", "alertIconRef", "tooltipRef", "smicData", "displayDateSelector", "displayDate", "appliedFilters", "handleSyncMonitorClick", "handleWeekChange", "handlePerformanceSummaryData", "handleForecastVarianceData", "renderTabContent", "tab", "LEADING_INDICATORS", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataLoaded", "FORECAST_VARIANCE", "visibleTabs", "classes", "className", "style", "margin", "padding", "width", "borderColor", "initialTab", "indexOf", "variant", "onChange", "idx", "map", "Header", "tabIndex", "onBlur", "display", "alignItems", "gap", "position", "zIndex", "anchor", "label", "size", "cursor", "color", "onClick", "e", "stopPropagation", "before", "transformedData", "tableEl", "document", "querySelector", "headers", "from", "querySelectorAll", "th", "textContent", "rows", "tr", "td", "extractedData", "row", "rowData", "header", "index", "dashboardData", "window", "__ALLOCATR_DATA__", "error", "alert", "extractError", "message", "weekChange", "isOpen", "<PERSON><PERSON><PERSON>", "hideBackdrop"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\n// import { createPortal } from 'react-dom';\r\nimport Report from './report';\r\nimport LaggingIndicatorPage from './lagging-indicator-page';\r\nimport \"./dashboard-tabs.scss\";\r\nimport Drawer from '@albertsons/uds/molecule/Drawer';\r\nimport Button from '@albertsons/uds/molecule/Button';\r\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\r\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\r\nimport Tag from '@albertsons/uds/molecule/Tag';\r\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\r\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\r\n// Update the import path and casing to match the actual file location\r\nimport {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';\r\nimport { CircleAlert } from 'lucide-react';\r\nimport { useCurrentQuarterNbr } from '../features/calendarServiceUtils';\r\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';\r\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; \r\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\r\nimport { getNowInPST } from '../util/dateUtils';\r\nimport { format } from 'date-fns-tz';\r\n\r\n// Extend the Window interface to include __ALLOCATR_DATA__\r\ndeclare global {\r\n  interface Window {\r\n    __ALLOCATR_DATA__?: any[];\r\n  }\r\n}\r\n\r\nenum TabsLabels {\r\n  LEADING_INDICATORS = 'Leading Indicators',\r\n  PERFORMANCE_SUMMARY = 'Performance Summary',\r\n  FORECAST_VARIANCE = 'Performance Variance'\r\n}\r\n\r\n// const tabClassNames = {\r\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\r\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\r\n// };\r\n\r\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\r\n\r\n// Add this function above your DashboardTabs component\r\n\r\ntype ExcelDepartment = {\r\n  id: string;\r\n  name: string;\r\n  quarter: {\r\n    line1Projection: any;\r\n    lastYear: any;\r\n    actualOrForecast: any;\r\n    idPercentage: any;\r\n    vsLY: { value: any };\r\n    vsProjection: { value: any };\r\n  };\r\n  periods: any[];\r\n  weeks: any[];\r\n};\r\n\r\ntype ExcelBanner = {\r\n  id: string;\r\n  name: string;\r\n  quarter: object;\r\n  departments: ExcelDepartment[];\r\n};\r\n\r\ntype ExcelDivision = {\r\n  id: string;\r\n  name: string;\r\n  quarter: {\r\n    line1Projection: any;\r\n    lastYear: any;\r\n    actualOrForecast: any;\r\n    idPercentage: any;\r\n    vsLY: { value: any };\r\n    vsProjection: { value: any };\r\n  };\r\n  banners: ExcelBanner[];\r\n};\r\n\r\n// Explicitly type the function and result array\r\nconst transformDataForExcel = (data: any): ExcelDivision[] => {\r\n  // Handle hierarchical data from BFF response (which is an object with a 'divisions' property)\r\n  if (data && data.divisions && Array.isArray(data.divisions)) {\r\n    console.log('Data is in hierarchical format, using as-is');\r\n    // The data is already in the correct structure.\r\n    // We just need to ensure periods and weeks are correctly placed within each department.\r\n    data.divisions.forEach((division: ExcelDivision) => {\r\n      division.banners.forEach((banner: ExcelBanner) => {\r\n        banner.departments.forEach((department: ExcelDepartment) => {\r\n          // The BFF response provides periods and weeks at the department level.\r\n          // We can directly use them.\r\n          // If weeks were outside periods, we would group them here.\r\n        });\r\n      });\r\n    });\r\n    return data.divisions;\r\n  }\r\n\r\n  // The existing logic for converting flat data to hierarchical structure\r\n  const flatData = Array.isArray(data) ? data : [];\r\n  if (flatData.length === 0) {\r\n    console.log('No valid data to transform');\r\n    return [];\r\n  }\r\n  \r\n  console.log('Converting flat data to hierarchical structure');\r\n  \r\n  // Create a hierarchical structure\r\n  const divisions: { [key: string]: ExcelDivision } = {};\r\n  const result: ExcelDivision[] = [];\r\n  \r\n  // First pass: create divisions and collect departments with their data\r\n  const departmentsByDivision: { [divId: string]: any[] } = {};\r\n  \r\n  data.forEach(item => {\r\n    // First column usually contains hierarchy information\r\n    const hierarchyField = Object.keys(item)[0]; // Get the first column name\r\n    const divisionInfo = item[hierarchyField] || '';\r\n    \r\n    // Check if it's a division (format: \"34 - Mid-Atlantic\")\r\n    if (/^\\d+ - /.test(divisionInfo)) {\r\n      const [divId, ...divNameParts] = divisionInfo.split(' - ');\r\n      const divName = divNameParts.join(' - ');\r\n      \r\n      if (!divisions[divId]) {\r\n        // Create division\r\n        const division: ExcelDivision = {\r\n          id: divId,\r\n          name: divName,\r\n          quarter: {\r\n            line1Projection: item[\"$ Projection\"],\r\n            lastYear: item[\"$ Last Year\"],\r\n            actualOrForecast: item[\"$Actual/Merch.Forecast\"] || item[\"$ Actual/Merch. Forecast\"],\r\n            idPercentage: item[\"Keeper% (Includes ID)\"],\r\n            vsLY: { value: item[\"$ vs LY\"] },\r\n            vsProjection: { value: item[\"$ vs Projection\"] }\r\n          },\r\n          banners: []\r\n        };\r\n        \r\n        // Add default banner (will contain departments not assigned to specific banners)\r\n        division.banners.push({\r\n          id: \"00\",\r\n          name: \"Default\",\r\n          quarter: {},\r\n          departments: []\r\n        });\r\n        \r\n        divisions[divId] = division;\r\n        result.push(division);\r\n        departmentsByDivision[divId] = [];\r\n      }\r\n    }\r\n    // Check if it's a department (format: \"3070000 - Tobacco\")\r\n    else if (/^\\d{7} - /.test(divisionInfo)) {\r\n      // Store the department info to be processed in second pass\r\n      const [deptId, ...deptNameParts] = divisionInfo.split(' - ');\r\n      const deptName = deptNameParts.join(' - ');\r\n      \r\n      // Find the associated division (use the most recently created division)\r\n      const lastDivId = Object.keys(divisions).pop();\r\n      if (lastDivId) {\r\n        departmentsByDivision[lastDivId] = departmentsByDivision[lastDivId] || [];\r\n        departmentsByDivision[lastDivId].push({\r\n          id: deptId,\r\n          name: deptName,\r\n          data: item\r\n        });\r\n      }\r\n    }\r\n    // Other rows (periods, weeks) will be handled separately\r\n  });\r\n  \r\n  // Second pass: Assign departments to their divisions\r\n  Object.keys(divisions).forEach(divId => {\r\n    const division = divisions[divId];\r\n    const departments = departmentsByDivision[divId] || [];\r\n    \r\n    // For simplicity, assign all departments to the default banner\r\n    // In a real implementation, you'd determine the banner based on your data structure\r\n    departments.forEach(dept => {\r\n      division.banners[0].departments.push({\r\n        id: dept.id,\r\n        name: dept.name,\r\n        quarter: {\r\n          line1Projection: dept.data[\"$ Projection\"],\r\n          lastYear: dept.data[\"$ Last Year\"],\r\n          actualOrForecast: dept.data[\"$Actual/Merch.Forecast\"] || dept.data[\"$ Actual/Merch. Forecast\"],\r\n          idPercentage: dept.data[\"Keeper% (Includes ID)\"],\r\n          vsLY: { value: dept.data[\"$ vs LY\"] },\r\n          vsProjection: { value: dept.data[\"$ vs Projection\"] }\r\n        },\r\n        periods: [], // These will be populated if period/week data is available in flat structure\r\n        weeks: []    // These will be populated if period/week data is available in flat structure\r\n      });\r\n    });\r\n    \r\n    // If you have banner information, you would create additional banners here\r\n  });\r\n  \r\n  console.log('Transformed data:', result);\r\n  return result;\r\n};\r\n\r\nconst DashboardTabs = () => {\r\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);\r\n  const [performanceSummaryData, setPerformanceSummaryData] = useState<any[]>([]);\r\n  const [forecastVarianceData, setForecastVarianceData] = useState<any[]>([]);\r\n  const [dashboardLoading, setDashboardLoading] = useState(true);\r\n  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');\r\n   const [showMessage, setShowMessage] = useState(false);\r\n  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);\r\n  const alertIconRef = useRef<HTMLDivElement>(null);\r\n  const tooltipRef = useRef<HTMLDivElement>(null);\r\n  const smicData = worksheetFilters.smicData || [];\r\n\r\n  // Safely access displayDate with a fallback\r\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\r\n  const displayDate = displayDateSelector?.data || {};\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n\r\n  // const handleTabClick = (tabName: Tabs) => {\r\n  //   setSelectedTab(tabName);\r\n  // };\r\n\r\n  const handleSyncMonitorClick = () => {\r\n    setIsDrawerOpen(true);\r\n  };\r\n\r\n  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {\r\n    setSelectedWeek(item);\r\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\r\n  };\r\n\r\n  const handlePerformanceSummaryData = (data: any[]) => {\r\n    setPerformanceSummaryData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n\r\n  const handleForecastVarianceData = (data: any[]) => {\r\n    setForecastVarianceData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n  const renderTabContent = (tab: TabsLabels) => {\r\n    switch (tab) {\r\n      case TabsLabels.LEADING_INDICATORS:\r\n        return <div><Report /></div>;\r\n      case TabsLabels.PERFORMANCE_SUMMARY:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>\r\n      case TabsLabels.FORECAST_VARIANCE:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const visibleTabs = [\r\n    TabsLabels.LEADING_INDICATORS,\r\n    TabsLabels.PERFORMANCE_SUMMARY,\r\n    TabsLabels.FORECAST_VARIANCE\r\n  ];\r\n  const classes = 'flex justify-center items-center h-48 text';\r\n\r\n  return (\r\n    <div>\r\n\r\n      <div className=\"flex items-center justify-between px-2 py-2 overflow-x-auto\">\r\n        <div className=\"tabs-container\">\r\n\r\n          <div\r\n            className=\"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\"\r\n            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}\r\n          >\r\n\r\n            <Tabs\r\n              initialTab={visibleTabs.indexOf(selectedTab)}\r\n              variant='light'\r\n              onChange={idx => setSelectedTab(visibleTabs[idx])}\r\n              className='w-full border-transparent dashboard-tab'\r\n            >\r\n              {visibleTabs.map((tab, idx) => (\r\n                <Tab className={classes} key={tab}>\r\n                  <Tab.Header>\r\n\r\n                    {tab === TabsLabels.FORECAST_VARIANCE ? (\r\n                      <span\r\n                        tabIndex={2}\r\n                        onBlur={() => setShowMessage(false)}\r\n                        style={{\r\n                          display: 'inline-flex',\r\n                          alignItems: 'center',\r\n                          gap: '4px',\r\n                          position: 'relative'\r\n                        }}>\r\n\r\n                        <div \r\n                          className=\"relative inline-block\"\r\n                        >\r\n                          <span className='tool-tip-initilizer-top'></span>\r\n                        <Tooltip\r\n                  zIndex={9999}\r\n                  anchor='top'\r\n                  variant='dark'\r\n                  className={'uds-tooltip-top'}\r\n                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>\r\n                  <CircleAlert\r\n                    size={16}\r\n                    style={{ cursor: 'pointer' }}\r\n                    color=\" #1B6EBB\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                        </div>\r\n                        \r\n                        {tab}\r\n                      </span>\r\n                    ) : (\r\n                      tab\r\n                    )}\r\n                  </Tab.Header>\r\n              </Tab>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </div>\r\n        <div className=\"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\">\r\n          <Icon\r\n            before={\r\n              <span className=\"w-4 h-4 flex items-center text-[#1B6EBB]\"> <Download/> </span>\r\n            }\r\n            className=\"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\"\r\n            onClick={async () => {\r\n              console.log('Download button clicked');\r\n              console.log('Selected tab:', selectedTab);\r\n              \r\n              try {\r\n                if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\r\n                  console.log('Preparing Performance Summary download...');\r\n                  \r\n                  // Try to extract data directly from the DOM\r\n                  // First check if we already have data in state\r\n                  if (performanceSummaryData && performanceSummaryData.length > 0) {\r\n                    console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\r\n                    const transformedData = transformDataForExcel(performanceSummaryData);\r\n                    console.log('Transformed data structure:', transformedData);\r\n                    handleDownloadExcel(\r\n                      transformedData,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    // Try to extract data from the table directly\r\n                    console.log('Attempting to extract data from UI...');\r\n                    \r\n                    try {\r\n                      // Find table in the DOM\r\n                      const tableEl = document.querySelector('.allocatr-insights-table');\r\n                      \r\n                      if (tableEl) {\r\n                        console.log('Found table element, extracting data...');\r\n                        \r\n                        // Extracting headers\r\n                        const headers = Array.from(tableEl.querySelectorAll('thead th')).map(th => th.textContent);\r\n                        \r\n                        // Extracting rows\r\n                        const rows = Array.from(tableEl.querySelectorAll('tbody tr')).map(tr => \r\n                          Array.from(tr.querySelectorAll('td')).map(td => td.textContent)\r\n                        );\r\n                        \r\n                        console.log(`Extracted ${rows.length} rows from table`);\r\n                        \r\n                        if (rows.length > 0) {\r\n                          // Convert to array of objects\r\n                          const extractedData = rows.map(row => {\r\n                            const rowData = {};\r\n                            headers.forEach((header, index) => {\r\n                              if (header) rowData[header] = row[index];\r\n                            });\r\n                            return rowData;\r\n                          });\r\n                          \r\n                          console.log('Successfully extracted data from table');\r\n                          const transformedData = transformDataForExcel(extractedData);\r\n                          console.log('Transformed data structure:', transformedData);\r\n                          handleDownloadExcel(\r\n                            transformedData,\r\n                            smicData,\r\n                            appliedFilters,\r\n                            `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                          );\r\n                          return;\r\n                        }\r\n                      }\r\n                      \r\n                      // Try global variable as fallback\r\n                      const dashboardData = window.__ALLOCATR_DATA__ || [];\r\n                      if (dashboardData && dashboardData.length > 0) {\r\n                        console.log('Found dashboard data in global variable:', dashboardData.length, 'items');\r\n                        const transformedData = transformDataForExcel(dashboardData);\r\n                        console.log('Transformed data structure:', transformedData);\r\n                        handleDownloadExcel(\r\n                          transformedData,\r\n                          smicData,\r\n                          appliedFilters,\r\n                          `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                        );\r\n                        return;\r\n                      }\r\n                      \r\n                      // Force refresh data from AllocatrInsights component\r\n                      // The DOM element does not have a 'refresh' method; remove this block or implement refresh logic differently.\r\n                      // If you need to trigger a refresh, consider using state or props to re-fetch data in AllocatrInsights.\r\n                      // For now, just log that refresh is not available.\r\n                      console.log('No refresh method available on DOM element. Please refresh data via state or props.');\r\n                      \r\n                      console.error('Could not find data to download');\r\n                      alert('No data available to download. The data is visible but not accessible for download. Please try refreshing the page.');\r\n                    } catch (extractError) {\r\n                      console.error('Error extracting data:', extractError);\r\n                      alert('Error extracting data for download. Please try again later.');\r\n                    }\r\n                  }\r\n                } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\r\n                  // Similar approach for forecast variance\r\n                  if (forecastVarianceData && forecastVarianceData.length > 0) {\r\n                    console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\r\n                    handleDownloadExcel(\r\n                      forecastVarianceData,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    console.error('No variance data available to download');\r\n                    alert('No data available to download. Please try refreshing the page.');\r\n                  }\r\n                }\r\n              } catch (error: any) {\r\n                console.error('Download error:', error);\r\n                alert('Error preparing download: ' + (error.message || 'Unknown error'));\r\n              }\r\n            }}\r\n          >Download as Excel\r\n          </Icon>\r\n        </div>\r\n      <div className='flex items-center gap-4'>\r\n           <div className='mr-6'>\r\n        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>\r\n      </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              className=\"sync-button\"\r\n              size=\"xs\"\r\n              variant=\"secondary\"\r\n              onClick={handleSyncMonitorClick}\r\n            >\r\n              EPBCS Sync Monitor\r\n            </Button>\r\n          </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        {renderTabContent(selectedTab)}\r\n      </div>\r\n\r\n      <Drawer\r\n        anchor=\"right\"\r\n        isOpen={isDrawerOpen}\r\n        setOpen={setIsDrawerOpen}\r\n        hideBackdrop={false}\r\n        width=\"608px\"\r\n        header={<div>EPBCS Sync Monitor</div>}\r\n      >\r\n        <EPBCSSyncMonitor />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,iCAAiC;AACpD,OAAOC,MAAM,MAAM,iCAAiC;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAOC,IAAI,IAAIC,GAAG,QAAQ,+BAA+B;AAEzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E;AACA,SAAUC,UAAU,QAAQ,sDAAsD;AAClF,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAOC,OAAO,MAAM,kCAAkC;AAAC,OAAOC,IAAI,MAAM,+BAA+B;AACvG,SAASC,cAAc,IAAIC,QAAQ,QAAQ,uCAAuC;AAClF,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,IAOKC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA,EAAVA,UAAU,SAMf;AACA;AACA;AACA;AAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACD,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;;AAE1D;;AAsCA;AACA,MAAMM,qBAAqB,GAAIC,IAAS,IAAsB;EAC5D;EACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,SAAS,CAAC,EAAE;IAC3DG,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D;IACA;IACAL,IAAI,CAACC,SAAS,CAACK,OAAO,CAAEC,QAAuB,IAAK;MAClDA,QAAQ,CAACC,OAAO,CAACF,OAAO,CAAEG,MAAmB,IAAK;QAChDA,MAAM,CAACC,WAAW,CAACJ,OAAO,CAAEK,UAA2B,IAAK;UAC1D;UACA;UACA;QAAA,CACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOX,IAAI,CAACC,SAAS;EACvB;;EAEA;EACA,MAAMW,QAAQ,GAAGV,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;EAChD,IAAIY,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IACzBT,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,OAAO,EAAE;EACX;EAEAD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;EAE7D;EACA,MAAMJ,SAA2C,GAAG,CAAC,CAAC;EACtD,MAAMa,MAAuB,GAAG,EAAE;;EAElC;EACA,MAAMC,qBAAiD,GAAG,CAAC,CAAC;EAE5Df,IAAI,CAACM,OAAO,CAACU,IAAI,IAAI;IACnB;IACA,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAMI,YAAY,GAAGJ,IAAI,CAACC,cAAc,CAAC,IAAI,EAAE;;IAE/C;IACA,IAAI,SAAS,CAACI,IAAI,CAACD,YAAY,CAAC,EAAE;MAChC,MAAM,CAACE,KAAK,EAAE,GAAGC,YAAY,CAAC,GAAGH,YAAY,CAACI,KAAK,CAAC,KAAK,CAAC;MAC1D,MAAMC,OAAO,GAAGF,YAAY,CAACG,IAAI,CAAC,KAAK,CAAC;MAExC,IAAI,CAACzB,SAAS,CAACqB,KAAK,CAAC,EAAE;QACrB;QACA,MAAMf,QAAuB,GAAG;UAC9BoB,EAAE,EAAEL,KAAK;UACTM,IAAI,EAAEH,OAAO;UACbI,OAAO,EAAE;YACPC,eAAe,EAAEd,IAAI,CAAC,cAAc,CAAC;YACrCe,QAAQ,EAAEf,IAAI,CAAC,aAAa,CAAC;YAC7BgB,gBAAgB,EAAEhB,IAAI,CAAC,wBAAwB,CAAC,IAAIA,IAAI,CAAC,0BAA0B,CAAC;YACpFiB,YAAY,EAAEjB,IAAI,CAAC,uBAAuB,CAAC;YAC3CkB,IAAI,EAAE;cAAEC,KAAK,EAAEnB,IAAI,CAAC,SAAS;YAAE,CAAC;YAChCoB,YAAY,EAAE;cAAED,KAAK,EAAEnB,IAAI,CAAC,iBAAiB;YAAE;UACjD,CAAC;UACDR,OAAO,EAAE;QACX,CAAC;;QAED;QACAD,QAAQ,CAACC,OAAO,CAAC6B,IAAI,CAAC;UACpBV,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,CAAC,CAAC;UACXnB,WAAW,EAAE;QACf,CAAC,CAAC;QAEFT,SAAS,CAACqB,KAAK,CAAC,GAAGf,QAAQ;QAC3BO,MAAM,CAACuB,IAAI,CAAC9B,QAAQ,CAAC;QACrBQ,qBAAqB,CAACO,KAAK,CAAC,GAAG,EAAE;MACnC;IACF;IACA;IAAA,KACK,IAAI,WAAW,CAACD,IAAI,CAACD,YAAY,CAAC,EAAE;MACvC;MACA,MAAM,CAACkB,MAAM,EAAE,GAAGC,aAAa,CAAC,GAAGnB,YAAY,CAACI,KAAK,CAAC,KAAK,CAAC;MAC5D,MAAMgB,QAAQ,GAAGD,aAAa,CAACb,IAAI,CAAC,KAAK,CAAC;;MAE1C;MACA,MAAMe,SAAS,GAAGvB,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACyC,GAAG,CAAC,CAAC;MAC9C,IAAID,SAAS,EAAE;QACb1B,qBAAqB,CAAC0B,SAAS,CAAC,GAAG1B,qBAAqB,CAAC0B,SAAS,CAAC,IAAI,EAAE;QACzE1B,qBAAqB,CAAC0B,SAAS,CAAC,CAACJ,IAAI,CAAC;UACpCV,EAAE,EAAEW,MAAM;UACVV,IAAI,EAAEY,QAAQ;UACdxC,IAAI,EAAEgB;QACR,CAAC,CAAC;MACJ;IACF;IACA;EACF,CAAC,CAAC;;EAEF;EACAE,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACK,OAAO,CAACgB,KAAK,IAAI;IACtC,MAAMf,QAAQ,GAAGN,SAAS,CAACqB,KAAK,CAAC;IACjC,MAAMZ,WAAW,GAAGK,qBAAqB,CAACO,KAAK,CAAC,IAAI,EAAE;;IAEtD;IACA;IACAZ,WAAW,CAACJ,OAAO,CAACqC,IAAI,IAAI;MAC1BpC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC2B,IAAI,CAAC;QACnCV,EAAE,EAAEgB,IAAI,CAAChB,EAAE;QACXC,IAAI,EAAEe,IAAI,CAACf,IAAI;QACfC,OAAO,EAAE;UACPC,eAAe,EAAEa,IAAI,CAAC3C,IAAI,CAAC,cAAc,CAAC;UAC1C+B,QAAQ,EAAEY,IAAI,CAAC3C,IAAI,CAAC,aAAa,CAAC;UAClCgC,gBAAgB,EAAEW,IAAI,CAAC3C,IAAI,CAAC,wBAAwB,CAAC,IAAI2C,IAAI,CAAC3C,IAAI,CAAC,0BAA0B,CAAC;UAC9FiC,YAAY,EAAEU,IAAI,CAAC3C,IAAI,CAAC,uBAAuB,CAAC;UAChDkC,IAAI,EAAE;YAAEC,KAAK,EAAEQ,IAAI,CAAC3C,IAAI,CAAC,SAAS;UAAE,CAAC;UACrCoC,YAAY,EAAE;YAAED,KAAK,EAAEQ,IAAI,CAAC3C,IAAI,CAAC,iBAAiB;UAAE;QACtD,CAAC;QACD4C,OAAO,EAAE,EAAE;QAAE;QACbC,KAAK,EAAE,EAAE,CAAI;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;EACF,CAAC,CAAC;EAEFzC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAES,MAAM,CAAC;EACxC,OAAOA,MAAM;AACf,CAAC;AAED,MAAMgC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAACqB,UAAU,CAACoD,mBAAmB,CAAC;EAC9E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAyE,IAAI,CAAC;EAC9H,MAAM,CAAC8E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/E,QAAQ,CAAQ,EAAE,CAAC;EAC/E,MAAM,CAACgF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjF,QAAQ,CAAQ,EAAE,CAAC;EAC3E,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM;IAAEwB,IAAI,EAAE4D,gBAAgB,GAAG,CAAC;EAAE,CAAC,GAAG/E,eAAe,CAAC,wBAAwB,CAAC;EAChF,MAAM,CAACgF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAMyF,YAAY,GAAGxF,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMyF,UAAU,GAAGzF,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM0F,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,IAAI,EAAE;;EAEhD;EACA,MAAMC,mBAAmB,GAAGvF,eAAe,CAAC,gBAAgB,CAAC;EAC7D,MAAMwF,WAAW,GAAG,CAAAD,mBAAmB,oBAAnBA,mBAAmB,CAAEpE,IAAI,KAAI,CAAC,CAAC;EACnD,MAAM;IAAEA,IAAI,EAAEsE;EAAe,CAAC,GAAGzF,eAAe,CAAC,kBAAkB,CAAC;;EAEpE;EACA;EACA;;EAEA,MAAM0F,sBAAsB,GAAGA,CAAA,KAAM;IACnCpB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqB,gBAAgB,GAAIxD,IAAqE,IAAK;IAClGqC,eAAe,CAACrC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMyD,4BAA4B,GAAIzE,IAAW,IAAK;IACpDuD,yBAAyB,CAACvD,IAAI,CAAC;IAC/B2D,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMe,0BAA0B,GAAI1E,IAAW,IAAK;IAClDyD,uBAAuB,CAACzD,IAAI,CAAC;IAC7B2D,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMgB,gBAAgB,GAAIC,GAAe,IAAK;IAC5C,QAAQA,GAAG;MACT,KAAK/E,UAAU,CAACgF,kBAAkB;QAChC,oBAAOjF,OAAA;UAAAkF,QAAA,eAAKlF,OAAA,CAAClB,MAAM;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9B,KAAKrF,UAAU,CAACoD,mBAAmB;QACjC,oBAAOrD,OAAA;UAAAkF,QAAA,eAAKlF,OAAA,CAACX,gBAAgB;YAAC8D,WAAW,EAAElD,UAAU,CAACoD,mBAAoB;YAACkC,YAAY,EAAEV;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChI,KAAKrF,UAAU,CAACuF,iBAAiB;QAC/B,oBAAOxF,OAAA;UAAAkF,QAAA,eAAKlF,OAAA,CAACX,gBAAgB;YAAC8D,WAAW,EAAElD,UAAU,CAACuF,iBAAkB;YAACD,YAAY,EAAET;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5H;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMG,WAAW,GAAG,CAClBxF,UAAU,CAACgF,kBAAkB,EAC7BhF,UAAU,CAACoD,mBAAmB,EAC9BpD,UAAU,CAACuF,iBAAiB,CAC7B;EACD,MAAME,OAAO,GAAG,4CAA4C;EAE5D,oBACE1F,OAAA;IAAAkF,QAAA,gBAEElF,OAAA;MAAK2F,SAAS,EAAC,6DAA6D;MAAAT,QAAA,gBAC1ElF,OAAA;QAAK2F,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAE7BlF,OAAA;UACE2F,SAAS,EAAC,8IAA8I;UACxJC,KAAK,EAAE;YAAEC,MAAM,EAAE,UAAU;YAAEC,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAC,OAAO;YAAEC,WAAW,EAAE;UAAc,CAAE;UAAAd,QAAA,eAGzFlF,OAAA,CAACd,IAAI;YACH+G,UAAU,EAAER,WAAW,CAACS,OAAO,CAAC/C,WAAW,CAAE;YAC7CgD,OAAO,EAAC,OAAO;YACfC,QAAQ,EAAEC,GAAG,IAAIjD,cAAc,CAACqC,WAAW,CAACY,GAAG,CAAC,CAAE;YAClDV,SAAS,EAAC,yCAAyC;YAAAT,QAAA,EAElDO,WAAW,CAACa,GAAG,CAAC,CAACtB,GAAG,EAAEqB,GAAG,kBACxBrG,OAAA,CAACb,GAAG;cAACwG,SAAS,EAAED,OAAQ;cAAAR,QAAA,eACtBlF,OAAA,CAACb,GAAG,CAACoH,MAAM;gBAAArB,QAAA,EAERF,GAAG,KAAK/E,UAAU,CAACuF,iBAAiB,gBACnCxF,OAAA;kBACEwG,QAAQ,EAAE,CAAE;kBACZC,MAAM,EAAEA,CAAA,KAAMvC,cAAc,CAAC,KAAK,CAAE;kBACpC0B,KAAK,EAAE;oBACLc,OAAO,EAAE,aAAa;oBACtBC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,KAAK;oBACVC,QAAQ,EAAE;kBACZ,CAAE;kBAAA3B,QAAA,gBAEFlF,OAAA;oBACE2F,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,gBAEjClF,OAAA;sBAAM2F,SAAS,EAAC;oBAAyB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDtF,OAAA,CAACR,OAAO;sBACdsH,MAAM,EAAE,IAAK;sBACbC,MAAM,EAAC,KAAK;sBACZZ,OAAO,EAAC,MAAM;sBACdR,SAAS,EAAE,iBAAkB;sBAC7BqB,KAAK,EAAE,0HAA2H;sBAAA9B,QAAA,eAClIlF,OAAA,CAACT,WAAW;wBACV0H,IAAI,EAAE,EAAG;wBACTrB,KAAK,EAAE;0BAAEsB,MAAM,EAAE;wBAAU,CAAE;wBAC7BC,KAAK,EAAC,UAAU;wBAChBC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACrB;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,EAELN,GAAG;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GAEPN;cACD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC,GAxCeN,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyC9B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJtF,OAAA;QAAK2F,SAAS,EAAC,oEAAoE;QAAAT,QAAA,eACjFlF,OAAA,CAACP,IAAI;UACH8H,MAAM,eACJvH,OAAA;YAAM2F,SAAS,EAAC,0CAA0C;YAAAT,QAAA,GAAC,GAAC,eAAAlF,OAAA,CAACL,QAAQ;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC/E;UACDK,SAAS,EAAC,8FAA8F;UACxGyB,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnB5G,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACtCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE0C,WAAW,CAAC;YAEzC,IAAI;cACF,IAAIA,WAAW,KAAKlD,UAAU,CAACoD,mBAAmB,EAAE;gBAClD7C,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;gBAExD;gBACA;gBACA,IAAIiD,sBAAsB,IAAIA,sBAAsB,CAACzC,MAAM,GAAG,CAAC,EAAE;kBAC/DT,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiD,sBAAsB,CAACzC,MAAM,EAAE,MAAM,CAAC;kBACtF,MAAMuG,eAAe,GAAGrH,qBAAqB,CAACuD,sBAAsB,CAAC;kBACrElD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+G,eAAe,CAAC;kBAC3D5H,mBAAmB,CACjB4H,eAAe,EACfjD,QAAQ,EACRG,cAAc,EACd,wDAAwDxE,cAAc,OACxE,CAAC;gBACH,CAAC,MAAM;kBACL;kBACAM,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBAEpD,IAAI;oBACF;oBACA,MAAMgH,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;oBAElE,IAAIF,OAAO,EAAE;sBACXjH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;sBAEtD;sBACA,MAAMmH,OAAO,GAAGtH,KAAK,CAACuH,IAAI,CAACJ,OAAO,CAACK,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAACxB,GAAG,CAACyB,EAAE,IAAIA,EAAE,CAACC,WAAW,CAAC;;sBAE1F;sBACA,MAAMC,IAAI,GAAG3H,KAAK,CAACuH,IAAI,CAACJ,OAAO,CAACK,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAACxB,GAAG,CAAC4B,EAAE,IAClE5H,KAAK,CAACuH,IAAI,CAACK,EAAE,CAACJ,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAACxB,GAAG,CAAC6B,EAAE,IAAIA,EAAE,CAACH,WAAW,CAChE,CAAC;sBAEDxH,OAAO,CAACC,GAAG,CAAC,aAAawH,IAAI,CAAChH,MAAM,kBAAkB,CAAC;sBAEvD,IAAIgH,IAAI,CAAChH,MAAM,GAAG,CAAC,EAAE;wBACnB;wBACA,MAAMmH,aAAa,GAAGH,IAAI,CAAC3B,GAAG,CAAC+B,GAAG,IAAI;0BACpC,MAAMC,OAAO,GAAG,CAAC,CAAC;0BAClBV,OAAO,CAAClH,OAAO,CAAC,CAAC6H,MAAM,EAAEC,KAAK,KAAK;4BACjC,IAAID,MAAM,EAAED,OAAO,CAACC,MAAM,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC;0BAC1C,CAAC,CAAC;0BACF,OAAOF,OAAO;wBAChB,CAAC,CAAC;wBAEF9H,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;wBACrD,MAAM+G,eAAe,GAAGrH,qBAAqB,CAACiI,aAAa,CAAC;wBAC5D5H,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+G,eAAe,CAAC;wBAC3D5H,mBAAmB,CACjB4H,eAAe,EACfjD,QAAQ,EACRG,cAAc,EACd,wDAAwDxE,cAAc,OACxE,CAAC;wBACD;sBACF;oBACF;;oBAEA;oBACA,MAAMuI,aAAa,GAAGC,MAAM,CAACC,iBAAiB,IAAI,EAAE;oBACpD,IAAIF,aAAa,IAAIA,aAAa,CAACxH,MAAM,GAAG,CAAC,EAAE;sBAC7CT,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgI,aAAa,CAACxH,MAAM,EAAE,OAAO,CAAC;sBACtF,MAAMuG,eAAe,GAAGrH,qBAAqB,CAACsI,aAAa,CAAC;sBAC5DjI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+G,eAAe,CAAC;sBAC3D5H,mBAAmB,CACjB4H,eAAe,EACfjD,QAAQ,EACRG,cAAc,EACd,wDAAwDxE,cAAc,OACxE,CAAC;sBACD;oBACF;;oBAEA;oBACA;oBACA;oBACA;oBACAM,OAAO,CAACC,GAAG,CAAC,qFAAqF,CAAC;oBAElGD,OAAO,CAACoI,KAAK,CAAC,iCAAiC,CAAC;oBAChDC,KAAK,CAAC,qHAAqH,CAAC;kBAC9H,CAAC,CAAC,OAAOC,YAAY,EAAE;oBACrBtI,OAAO,CAACoI,KAAK,CAAC,wBAAwB,EAAEE,YAAY,CAAC;oBACrDD,KAAK,CAAC,6DAA6D,CAAC;kBACtE;gBACF;cACF,CAAC,MAAM,IAAI1F,WAAW,KAAKlD,UAAU,CAACuF,iBAAiB,EAAE;gBACvD;gBACA,IAAI5B,oBAAoB,IAAIA,oBAAoB,CAAC3C,MAAM,GAAG,CAAC,EAAE;kBAC3DT,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmD,oBAAoB,CAAC3C,MAAM,EAAE,MAAM,CAAC;kBACjFrB,mBAAmB,CACjBgE,oBAAoB,EACpBW,QAAQ,EACRG,cAAc,EACd,qDAAqDxE,cAAc,OACrE,CAAC;gBACH,CAAC,MAAM;kBACLM,OAAO,CAACoI,KAAK,CAAC,wCAAwC,CAAC;kBACvDC,KAAK,CAAC,gEAAgE,CAAC;gBACzE;cACF;YACF,CAAC,CAAC,OAAOD,KAAU,EAAE;cACnBpI,OAAO,CAACoI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;cACvCC,KAAK,CAAC,4BAA4B,IAAID,KAAK,CAACG,OAAO,IAAI,eAAe,CAAC,CAAC;YAC1E;UACF,CAAE;UAAA7D,QAAA,EACH;QACD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRtF,OAAA;QAAK2F,SAAS,EAAC,yBAAyB;QAAAT,QAAA,gBACnClF,OAAA;UAAK2F,SAAS,EAAC,MAAM;UAAAT,QAAA,eACxBlF,OAAA,CAACV,UAAU;YAAC0J,UAAU,EAAEpE,gBAAiB;YAACzB,WAAW,EAAEA;UAAY;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACFtF,OAAA;UAAK2F,SAAS,EAAC,yBAAyB;UAAAT,QAAA,eACtClF,OAAA,CAAChB,MAAM;YACL2G,SAAS,EAAC,aAAa;YACvBsB,IAAI,EAAC,IAAI;YACTd,OAAO,EAAC,WAAW;YACnBiB,OAAO,EAAEzC,sBAAuB;YAAAO,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENtF,OAAA;MAAK2F,SAAS,EAAC,iBAAiB;MAAAT,QAAA,EAC7BH,gBAAgB,CAAC5B,WAAW;IAAC;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAENtF,OAAA,CAACjB,MAAM;MACLgI,MAAM,EAAC,OAAO;MACdkC,MAAM,EAAE3F,YAAa;MACrB4F,OAAO,EAAE3F,eAAgB;MACzB4F,YAAY,EAAE,KAAM;MACpBpD,KAAK,EAAC,OAAO;MACbwC,MAAM,eAAEvI,OAAA;QAAAkF,QAAA,EAAK;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eAEtClF,OAAA,CAACZ,gBAAgB;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAED,eAAepC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}