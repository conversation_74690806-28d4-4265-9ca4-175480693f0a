import Alert from '@albertsons/uds/molecule/Alert';
import { on } from 'events';
import React from 'react';

interface AlertMessageProps {
  message: string;
  type: 'success' | 'error';
  open: boolean;
  onClose?: () => void;
}

const AlertMessage: React.FC<AlertMessageProps> = ({ message, type, open, onClose }) => {
  return (
    <div className="flex items-center z-10">
        <Alert
            timeout={2}
            isOpen={open}
            sticky={true}
            variant={type}
            autoClose={true}
            dismissible={true}
            onClose={onClose}
        >
            {message}
        </Alert>
    </div>
  );
};

export default AlertMessage;
