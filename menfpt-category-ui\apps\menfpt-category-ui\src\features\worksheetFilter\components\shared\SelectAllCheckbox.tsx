import React from 'react';
import Checkbox from '@albertsons/uds/molecule/Checkbox';

interface SelectAllCheckboxProps {
  label?: string;
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  disabled?: boolean;
  className?: string;
}

const SelectAllCheckbox: React.FC<SelectAllCheckboxProps> = ({
  label,
  selectedCount,
  totalCount,
  onSelectAll,
  disabled = false,
  className = ''
}) => {
  const getLabel = () => {
    if (label) return label;
    if (totalCount === 0) return "Select";
    return `${selectedCount} Selected`;
  };

  const isAllSelected = totalCount > 0 && selectedCount === totalCount;
  const isIndeterminate = totalCount > 0 && selectedCount > 0 && selectedCount < totalCount;

  return (
    <div className="self-stretch pb-1 flex flex-col justify-start items-start gap-2">
      <div className={`p-2.5 inline-flex justify-start items-center gap-2 w-full border-b border-[#c8daeb] flex-shrink-0 ${className}`}>
        <Checkbox
          label={getLabel()}
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          disabled={disabled || totalCount === 0}
          onChange={onSelectAll}
          className="checkbox-label"
        />
      </div>
    </div>
  );
};

export default SelectAllCheckbox;
