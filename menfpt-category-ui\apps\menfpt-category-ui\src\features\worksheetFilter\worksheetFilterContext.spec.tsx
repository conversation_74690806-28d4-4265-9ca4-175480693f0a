import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { WorksheetFilterProvider, useWorksheetFilter } from './worksheetFilterContext';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { setAppliedFilter } from '../../server/Reducer/menfpt-category.slice';
import { MemoryRouter, useLocation } from 'react-router-dom';

// Mock the Redux store
const mockStore = configureStore({
  reducer: {
    menfptCategory: (state = {}, action: any) => {
      if (action.type === setAppliedFilter.type) {
        return { ...state, appliedFilter: action.payload };
      }
      return state;
    },
  },
});

// Test component that uses the context
const TestComponent = () => {
  const {
    filterState,
    appliedFilters,
    setFilterState,
    setAppliedFilters,
    openFilterModal,
    closeFilterModal,
    isFilterModalOpen,
  } = useWorksheetFilter();

  return (
    <div>
      <div data-testid="filter-modal-state">{isFilterModalOpen.toString()}</div>
      <div data-testid="filter-state">{JSON.stringify(filterState)}</div>
      <div data-testid="applied-filters">{JSON.stringify(appliedFilters)}</div>
      <button onClick={openFilterModal}>Open Modal</button>
      <button onClick={closeFilterModal}>Close Modal</button>
      <button
        onClick={() =>
          setAppliedFilters({
            division: ['test-division'],
            department: 'test-department',
            desk: 'test-desk',
          })
        }
      >
        Apply Filters
      </button>
    </div>
  );
};

// Wrapper component to provide Redux store and Router
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={mockStore}>
    <MemoryRouter>
      <WorksheetFilterProvider>{children}</WorksheetFilterProvider>
    </MemoryRouter>
  </Provider>
);

describe('WorksheetFilterContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide default filter state', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const filterStateElement = screen.getByTestId('filter-state');
    const appliedFiltersElement = screen.getByTestId('applied-filters');

    expect(JSON.parse(filterStateElement.textContent || '')).toEqual({
      division: [],
      department: undefined,
      desk: undefined,
      category: [],
    });

    expect(JSON.parse(appliedFiltersElement.textContent || '')).toEqual({
      division: [],
      department: undefined,
      desk: undefined,
    });
  });

  it('should open and close filter modal', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const modalStateElement = screen.getByTestId('filter-modal-state');
    expect(modalStateElement.textContent).toBe('false');

    fireEvent.click(screen.getByText('Open Modal'));
    expect(modalStateElement.textContent).toBe('true');

    fireEvent.click(screen.getByText('Close Modal'));
    expect(modalStateElement.textContent).toBe('false');
  });

  it('should apply filters correctly', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Apply Filters'));

    const appliedFiltersElement = screen.getByTestId('applied-filters');
    expect(JSON.parse(appliedFiltersElement.textContent || '')).toEqual({
      division: ['test-division'],
      department: 'test-department',
      desk: 'test-desk',
    });
  });

  it('should throw error when used outside provider', () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useWorksheetFilter must be used within a WorksheetFilterProvider');

    consoleError.mockRestore();
  });

  it('should update filter state when modal is opened', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // First apply some filters
    fireEvent.click(screen.getByText('Apply Filters'));

    // Then open the modal
    fireEvent.click(screen.getByText('Open Modal'));

    const filterStateElement = screen.getByTestId('filter-state');
    expect(JSON.parse(filterStateElement.textContent || '')).toEqual({
      division: ['test-division'],
      department: 'test-department',
      desk: 'test-desk',
      category: [],
    });
  });
}); 