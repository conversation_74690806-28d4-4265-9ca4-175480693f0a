export const allocatrDashboardTableQuery = `query GetAllocatrDashboardTableData($allocatrDashboardReq: AllocatrDashboardReq) {
  getAllocatrDashboardTableData(allocatrDashboardReq: $allocatrDashboardReq) {
    allocatrDashboardTableData {
      id
      name
      quarter {
        id
        quarterNumber
        line1Projection
        lastYear
        actualOrForecast
        idPercentage
        vsLY { value percentage }
        vsProjection { value percentage }
        bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
        line6 { projection actualOrForecast vsProjection }
        line7 { projection actualOrForecast vsProjection }
        line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
      }
      periods {
        id
        periodNumber
        line1Projection
        lastYear
        actualOrForecast
        idPercentage
        vsLY { value percentage }
        vsProjection { value percentage }
        bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
        line6 { projection actualOrForecast vsProjection }
        line7 { projection actualOrForecast vsProjection }
        line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
      }
      weeks {
        id
        weekNumber
        periodNumber
        line1Projection
        lastYear
        actualOrForecast
        idPercentage
        vsLY { value percentage }
        vsProjection { value percentage }
        bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
        line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
        line6 { projection actualOrForecast vsProjection }
        line7 { projection actualOrForecast vsProjection }
        line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
        isActualUsed
      }
      divisions {
        id
        name
        quarter {
          id
          quarterNumber
          line1Projection
          lastYear
          actualOrForecast
          idPercentage
          vsLY { value percentage }
          vsProjection { value percentage }
          bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
          markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
          shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
          line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
          line6 { projection actualOrForecast vsProjection }
          line7 { projection actualOrForecast vsProjection }
          line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
        }
        banners {
          id
          name
          quarter {
            id
            quarterNumber
            line1Projection
            lastYear
            actualOrForecast
            idPercentage
            vsLY { value percentage }
            vsProjection { value percentage }
            bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
            markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
            shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
            line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
            line6 { projection actualOrForecast vsProjection }
            line7 { projection actualOrForecast vsProjection }
            line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
          }
          departments {
            id
            name
            quarter {
              id
              quarterNumber
              line1Projection
              lastYear
              actualOrForecast
              idPercentage
              vsLY { value percentage }
              vsProjection { value percentage }
              bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
              line6 { projection actualOrForecast vsProjection }
              line7 { projection actualOrForecast vsProjection }
              line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
            }
            periods {
              id
              periodNumber
              line1Projection
              lastYear
              actualOrForecast
              idPercentage
              vsLY { value percentage }
              vsProjection { value percentage }
              bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
              line6 { projection actualOrForecast vsProjection }
              line7 { projection actualOrForecast vsProjection }
              line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
            }
            weeks {
              id
              weekNumber
              periodNumber
              line1Projection
              lastYear
              actualOrForecast
              idPercentage
              vsLY { value percentage }
              vsProjection { value percentage }
              bookGrossProfit { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              markdown { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              shrink { projectionValue projectionPct actualOrForecast percentActualOrForecast vsProjection }
              line5 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
              line6 { projection actualOrForecast vsProjection }
              line7 { projection actualOrForecast vsProjection }
              line8 { actualOrForecast percentActualOrForecast projectionValue projectionPct vsProjection percentVsProjection }
              isActualUsed
            
            }
          }
        }
      }
    }
  }
}`
