import React from 'react';
import { render } from '@testing-library/react';
import AllocatrInsightsTable from './AllocatrInsightsTable';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import * as rtkUtils from '../../rtk/rtk-utilities';

const mockStore = configureStore([]);
const store = mockStore({});

jest.spyOn(rtkUtils, 'useSelectorWrap').mockImplementation((key) => {
  if (key === 'appliedFilter_rn') return { data: {} };
  if (key === 'workSheetFilterList_rn') return { data: [] };
  if (key === 'dataForQrtrDisplayedInTable_rn') return { data: [] };
  if (key === 'displayDate_rn') return { data: {} };
  return { data: {} };
});

const baseQuarter = {
  id: 'Q1',
  line1Projection: 0,
  lastYear: 0,
  actualOrForecast: 0,
  idPercentage: 0,
  vsLY: { value: 0, percentage: 0 },
  vsProjection: { value: 0, percentage: 0 },
  quarterNumber: 1,
  bookGrossProfit: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
  markdown: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
  shrink: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
  line5: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
  line6: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
  line7: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
  line8: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
  hasMerchantForecast: false,
};

const baseDepartment = {
  id: 'D1',
  name: 'Dept 1',
  quarter: baseQuarter,
};

const totalDepartment = {
  id: 'Total',
  name: 'All Departments',
  quarter: baseQuarter,
};

describe('AllocatrInsightsTable', () => {
  it('renders table headers', () => {
    const insightsData = {
      id: 'root',
      name: 'Root',
      divisions: [],
      quarter: baseQuarter,
      periods: [],
      weeks: [],
    };
    const { getByText } = render(
      <Provider store={store}>
        <AllocatrInsightsTable insightsData={insightsData} selectedTab="Performance Summary" />
      </Provider>
    );
    expect(getByText('Quarter')).toBeInTheDocument();
    expect(getByText('Line 1 (Sales to Public)')).toBeInTheDocument();
  });

  it('renders department row with id and name', () => {
    const insightsData = {
      id: 'root',
      name: 'Root',
      divisions: [
        {
          id: 'Div1',
          name: 'Division 1',
          banners: [
            {
              id: 'Banner1',
              name: 'Banner 1',
              departments: [baseDepartment],
              quarter: baseQuarter,
            },
          ],
          quarter: baseQuarter,
        },
      ],
      quarter: baseQuarter,
      periods: [],
      weeks: [],
    };
    const { container, getByText, queryByText } = render(
      <Provider store={store}>
        <AllocatrInsightsTable insightsData={insightsData} selectedTab="Performance Summary" />
      </Provider>
    );
    // Try to find the department name by text or by cell selector
    const found = queryByText(/Dept 1/) || container.querySelector('.department-name-cell, .division-name-cell');
    expect(found).toBeTruthy();
  });

  it('handles empty departments', () => {
    const insightsData = {
      id: 'root',
      name: 'Root',
      divisions: [],
      quarter: baseQuarter,
      periods: [],
      weeks: [],
    };
    const { container } = render(
      <Provider store={store}>
        <AllocatrInsightsTable insightsData={insightsData} selectedTab="Performance Summary" />
      </Provider>
    );
    expect(container.querySelector('table')).toBeInTheDocument();
  });

  it('handles departments with id Total', () => {
    const insightsData = {
      id: 'Total',
      name: 'All Departments',
      quarter: baseQuarter,
      divisions: [],
      periods: [],
      weeks: [],
    };
    const { container, getByText, queryByText } = render(
      <Provider store={store}>
        <AllocatrInsightsTable insightsData={insightsData} selectedTab="Performance Summary" />
      </Provider>
    );
    // Try to find the Total row by text or by cell selector
    const foundElements = container.querySelector('.division-name-cell, .total-row') || queryByText(/Total/i);
    // If foundElements is a NodeList or array, check length; otherwise, check truthy
    if (Array.isArray(foundElements)) {
      expect(foundElements.length).toBeGreaterThan(0);
    } else {
      expect(foundElements).toBeTruthy();
    }
  });

  it('renders with a different selectedTab (should still render table)', () => {
    const insightsData = {
      id: 'root',
      name: 'Root',
      divisions: [
        {
          id: 'Div1',
          name: 'Division 1',
          banners: [
            {
              id: 'Banner1',
              name: 'Banner 1',
              departments: [baseDepartment],
              quarter: baseQuarter,
            },
          ],
          quarter: baseQuarter,
        },
      ],
      quarter: baseQuarter,
      periods: [],
      weeks: [],
    };
    const { container } = render(
      <Provider store={store}>
        <AllocatrInsightsTable insightsData={insightsData} selectedTab="Other Tab" />
      </Provider>
    );
    expect(container.querySelector('table')).toBeInTheDocument();
  });
});
