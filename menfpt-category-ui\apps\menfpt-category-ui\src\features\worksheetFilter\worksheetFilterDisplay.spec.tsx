import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import WorksheetFilterDisplay from './worksheetFilterDisplay';
import { DropdownType } from '../../interfaces/worksheetFilter';

// Mock Link from UDS
jest.mock('@albertsons/uds/molecule/Link', () => (props: any) => (
  <a {...props}>{props.children}</a>
));

// Mock useTimeframeDisplay
jest.mock('./worksheetFilterRouteUtils', () => ({
  useTimeframeDisplay: jest.fn(),
}));

const { useTimeframeDisplay } = require('./worksheetFilterRouteUtils');

describe('WorksheetFilterDisplay', () => {
  const divisionList: DropdownType[] = [
    { name: 'Div1', num: 1 },
    { name: 'Div2', num: 2 },
  ];
  const department = { name: 'Dept1', num: 1 };
  const desk = { name: 'Desk1', num: 1 };
  const timeframe = { name: 'Q1 2024', num: 1 };

  const baseProps = {
    appliedFilters: {
      division: [divisionList[0]],
      department: undefined,
      desk: undefined,
      category: [],
      timeframe: undefined,
      selectedSm: undefined,
      selectedAsm: undefined,
    },
    openFilterModal: jest.fn(),
    division: divisionList,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useTimeframeDisplay.mockReturnValue(false);
  });

  it('renders with only division selected', () => {
    render(<WorksheetFilterDisplay {...baseProps} />);
    expect(screen.getByText('Showing results for:')).toBeInTheDocument();
    expect(screen.getByText(/Div1/, { exact: false })).toBeInTheDocument();
    expect(screen.getByText('Change')).toBeInTheDocument();
  });

  it('renders with all divisions selected', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, division: divisionList },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/All your divisions/, { exact: false })).toBeInTheDocument();
  });

  it('renders with department as object', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, department },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/Dept1/, { exact: false })).toBeInTheDocument();
  });

  it('renders with department as array (single)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, department: [department] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/Dept1/, { exact: false })).toBeInTheDocument();
  });

  it('renders with department as array (multiple)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, department: [department, { name: 'Dept2', num: 2 }] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/2 departments/, { exact: false })).toBeInTheDocument();
  });

  it('renders with desk only', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, desk },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/Desk1/, { exact: false })).toBeInTheDocument();
  });

  it('renders with selectedSm only', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, selectedSm: 'john smith' },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/john smith/i)).toBeInTheDocument();
  });

  it('renders with selectedAsm only', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, selectedAsm: ['asm name'] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/asm name/i)).toBeInTheDocument();
  });

  it('renders with both selectedSm and selectedAsm', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, selectedSm: 'sm', selectedAsm: ['asm'] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    // ASM takes precedence in renderRoleText
    expect(screen.getByText(/asm/i)).toBeInTheDocument();
  });

  it('renders ASM (optional) when neither selectedSm nor selectedAsm', () => {
    render(<WorksheetFilterDisplay {...baseProps} />);
    expect(screen.getByText(/ASM \(optional\)/i)).toBeInTheDocument();
  });

  it('renders with timeframe when useTimeframeDisplay is true', () => {
    useTimeframeDisplay.mockReturnValue(true);
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, timeframe },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText('Q1 2024,')).toBeInTheDocument();
  });

  it('calls openFilterModal when Change is clicked', () => {
    render(<WorksheetFilterDisplay {...baseProps} />);
    fireEvent.click(screen.getByText('Change'));
    expect(baseProps.openFilterModal).toHaveBeenCalled();
  });

  it('renders nothing if no division is selected', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, division: [] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    // Should not show 'Showing results for:'
    expect(screen.queryByText('Showing results for:')).not.toBeInTheDocument();
  });

  it('renders with department as empty array (should show nothing extra)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, department: [] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    // Should not show any department text
    expect(screen.queryByText(/departments/i)).not.toBeInTheDocument();
  });

  it('renders with department undefined and desk undefined (should show nothing extra)', () => {
    render(<WorksheetFilterDisplay {...baseProps} />);
    // Should not show any department or desk text
    expect(screen.queryByText(/Dept1/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Desk1/)).not.toBeInTheDocument();
  });

  it('renders with department undefined and desk defined (should show desk)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, department: undefined, desk },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/Desk1/, { exact: false })).toBeInTheDocument();
  });

  it('renders with selectedSm set and selectedAsm as empty array (should show SM)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, selectedSm: 'Jane Doe', selectedAsm: [] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    expect(screen.getByText(/Jane Doe/i)).toBeInTheDocument();
  });

  it('renders with selectedAsm as empty array (should not show ASM)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, selectedAsm: [] },
    };
    render(<WorksheetFilterDisplay {...props} />);
    // Should NOT show ASM (optional) since selectedAsm is an empty array
    expect(screen.queryByText(/ASM \(optional\)/i)).not.toBeInTheDocument();
  });

  it('renders special case: division + department, no SM/ASM (should not show role)', () => {
    const props = {
      ...baseProps,
      appliedFilters: { ...baseProps.appliedFilters, division: [divisionList[0]], department },
    };
    render(<WorksheetFilterDisplay {...props} />);
    // Should show only division and department, not ASM/SM
    expect(screen.getByText('Showing results for:')).toBeInTheDocument();
    expect(screen.getByText(/Div1/, { exact: false })).toBeInTheDocument();
    expect(screen.getByText(/Dept1/, { exact: false })).toBeInTheDocument();
    // Should not show ASM (optional) or SM/ASM
    expect(screen.queryByText(/ASM \(optional\)/i)).not.toBeInTheDocument();
  });
}); 