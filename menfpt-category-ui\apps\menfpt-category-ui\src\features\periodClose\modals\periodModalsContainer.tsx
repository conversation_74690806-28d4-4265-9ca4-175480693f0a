import React, { useState, useEffect } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';

import { appConstants } from '@ui/utils';
import { periodCloseConfig } from 'libs/features/src/lib/periodClose/periodClose.config';

import { useSelectorWrap } from '../../../rtk/rtk-utilities';
import { useCurrentQuarterNbr } from '../../calendarServiceUtils';
import { PeriodCloseModal } from './PeriodCloseModal';
import { PeriodLockedModal } from './PeriodLockedModal';
import { quarterNumberToLabel } from '../../worksheetFilter/utils/quarterUtils';



function getInitialModalState({currentQuarterNbr, prevQuarterTabData}: {currentQuarterNbr: number, prevQuarterTabData: any}) {
  const { periodClose_LsKey, closeAlertKey, lockedAlertKey } =
  appConstants.lsKeys.periodClose;
 
 
  // Read the whole object from localStorage
  let periodCloseObj: any = {};
  try {
    const raw = localStorage.getItem(periodClose_LsKey);
    if (raw) periodCloseObj = JSON.parse(raw);
  } catch (e) {
    periodCloseObj = {};
  }
  const quarterObj = periodCloseObj?.[currentQuarterNbr] || {};

  if (
    prevQuarterTabData?.quarterStatus === 'locked' &&
    quarterObj[lockedAlertKey] !== true
  ) {
    return {
      modalToBeDisplayed: 'periodLockedModal',
      quarterNbr: currentQuarterNbr,
      alertKey: lockedAlertKey,
    };
  } else if (
    prevQuarterTabData?.quarterStatus === 'open' &&
    quarterObj[closeAlertKey] !== true
  ) {
    return {
      modalToBeDisplayed: 'periodCloseModal',
      quarterNbr: currentQuarterNbr,
      alertKey: closeAlertKey,
    };
  }
  return { modalToBeDisplayed: null, quarterNbr: null, alertKey: null };
}

export function usePeriodModalsContainer({ worksheetDataLoaded }: { worksheetDataLoaded: boolean }) {
  const { data: lastQrtrPeriodStatuses } = useSelectorWrap(
    'periodStatuses_rn'
  );
  const { data: prevQuarterTabData } = useSelectorWrap('prevQuarterTab_rn');
  const lastQtrNbr = prevQuarterTabData?.lastQtrNbr;
  const quarterLabel = quarterNumberToLabel(lastQtrNbr);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalToBeDisplayed, setModalToBeDisplayed] = useState<string | null>(null);
  const [quarterNbr, setQuarterNbr] = useState<number | null>(null);
  const [alertKey, setAlertKey] = useState<string | null>(null);
  const currentQuarterNbr = useCurrentQuarterNbr();
  const { periodClose_LsKey, closeAlertKey, lockedAlertKey } =
  appConstants.lsKeys.periodClose;
  
  useEffect(() => {
    if (worksheetDataLoaded && typeof currentQuarterNbr === 'number') {
      const { modalToBeDisplayed, quarterNbr, alertKey } = getInitialModalState({currentQuarterNbr, prevQuarterTabData});
      if (modalToBeDisplayed) {
        setModalToBeDisplayed(modalToBeDisplayed);
        setQuarterNbr(quarterNbr);
        setAlertKey(alertKey);
        setIsModalOpen(true);
      }
    }
  }, [lastQrtrPeriodStatuses, currentQuarterNbr, worksheetDataLoaded]);

  const handleModalClose = () => {
    setIsModalOpen(false);
  };
  const handleModalOpen = () => {
    if (quarterNbr && alertKey) {
      
      let periodCloseObj: any = {};
      try {
        const raw = localStorage.getItem(periodClose_LsKey);
        if (raw) periodCloseObj = JSON.parse(raw);
      } catch (e) {
        periodCloseObj = {};
      }
      if (!periodCloseObj[quarterNbr]) periodCloseObj[quarterNbr] = {};
      periodCloseObj[quarterNbr][alertKey] = true;
      localStorage.setItem(periodClose_LsKey, JSON.stringify(periodCloseObj));
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      handleModalOpen();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen]);

  const periodCloseModal = 
    isModalOpen &&
    modalToBeDisplayed === 'periodCloseModal' && (
      <Modal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        iconSmall={false}
        width={700}       
        data-testid="period-close-modal-container"
      >
        <PeriodCloseModal onClose={handleModalClose} quarterLabel={quarterLabel} />
      </Modal>
    );

  const periodLockedModal = 
    isModalOpen &&
    modalToBeDisplayed === 'periodLockedModal' && (
      <Modal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        iconSmall={false}
        width={700}       
        data-testid="period-locked-modal-container"
      >
        <PeriodLockedModal onClose={handleModalClose} quarterLabel={quarterLabel} />
      </Modal>
    );

  return { periodCloseModal, periodLockedModal, isModalOpen };
}
