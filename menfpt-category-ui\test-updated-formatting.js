// Test script to verify the updated banner name and total row formatting

// Mock functions
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

const formatCurrency = (value) => value === null || value === undefined || value === '' ? '' : `$${value}`;
const getDeptName = (smicData, deptId, fallback) => fallback || `Dept ${deptId}`;
const getDivisionName = (divisionId, fallback) => fallback || `Division ${divisionId}`;

// Updated getBannerName function
const getBannerName = (smicData, divisionId, bannerId, fallback) => {
  const found = smicData.find((item) => 
    String(item.divisionId) === String(divisionId) && 
    String(item.bannerId) === String(bannerId)
  );
  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
};

// Mock mapRow function
const mapRow = (baseRow, data, formatCurrency, type) => ({
  ...baseRow,
  '$ Projection': formatCurrency(data.line1Projection),
  type: type
});

// Simplified addDepartmentRows function
const addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { 
    departmentName: isTotal ? dept.name || 'Total' : `${dept.id} - ${deptName}` 
  };
  
  // Add quarter row
  if (dept.quarter) {
    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));
  }

  // Add periods
  (dept.periods || []).forEach((period) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Period ${period.periodNumber}` },
      period,
      formatCurrency,
      'Period'
    ));
  });

  // Add weeks
  (dept.weeks || []).forEach((week) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Week ${week.weekNumber}` },
      week,
      formatCurrency,
      'Week'
    ));
  });
};

// Updated addRows function
const addRows = (rows, data, smicData, useWeekId = false) => {
  // Handle the new structure with divisions and banners
  if (data.divisions && Array.isArray(data.divisions)) {
    // First, add the Total row data if it exists at the root level
    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {
      const divisionsCount = data.divisions ? data.divisions.length : 0;
      const totalData = {
        id: 'Total',
        name: `Total of ${divisionsCount} division${divisionsCount !== 1 ? 's' : ''}`,
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
      addDepartmentRows(rows, totalData, smicData, useWeekId);
    }
    
    // Then, process divisions and banners
    data.divisions.forEach((division) => {
      const divisionName = getDivisionName(division.id, division.name);
      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };
      
      // Add division quarter row
      if (division.quarter) {
        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));
      }

      // Process banners within division
      if (division.banners && Array.isArray(division.banners)) {
        division.banners.forEach((banner) => {
          const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);
          const bannerBaseRow = { departmentName: `  ${bannerName}` }; // Updated: No ID prefix
          
          // Add banner quarter row
          if (banner.quarter) {
            rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));
          }

          // Process departments within banner
          if (banner.departments && Array.isArray(banner.departments)) {
            banner.departments.forEach((dept) => {
              addDepartmentRows(rows, dept, smicData, useWeekId);
            });
          }
        });
      }
    });
  } else {
    // Fallback to old structure for backward compatibility
    addDepartmentRows(rows, data, smicData, useWeekId);
  }
};

// Test data
const testData = {
  id: 'Total',
  name: '',
  quarter: { quarterNumber: 202502, line1Projection: 225825128 },
  periods: [{ periodNumber: 202506, line1Projection: 129149501 }],
  weeks: [{ weekNumber: 202525, periodNumber: 202507, line1Projection: 32341866 }],
  divisions: [
    {
      id: '34',
      name: 'Test Division',
      quarter: { quarterNumber: 202502, line1Projection: 100000 },
      banners: [
        {
          id: '25',
          name: 'ACME STORES',
          quarter: { quarterNumber: 202502, line1Projection: 50000 },
          departments: [{
            id: '301',
            name: 'Grocery',
            quarter: { quarterNumber: 202502, line1Projection: 25000 }
          }]
        },
        {
          id: '02',
          name: 'BALDUCCI\'S',
          quarter: { quarterNumber: 202502, line1Projection: 30000 }
        }
      ]
    },
    {
      id: '35',
      name: 'Another Division',
      quarter: { quarterNumber: 202502, line1Projection: 75000 },
      banners: [
        {
          id: '04',
          name: 'KINGS FOOD MARKETS',
          quarter: { quarterNumber: 202502, line1Projection: 40000 }
        }
      ]
    }
  ]
};

const mockSmicData = [
  { divisionId: '34', bannerId: '25', bannerName: 'ACME STORES' },
  { divisionId: '34', bannerId: '02', bannerName: 'BALDUCCI\'S FOOD LOVERS MARKET' },
  { divisionId: '35', bannerId: '04', bannerName: 'KINGS FOOD MARKETS' }
];

// Test the function
const rows = [];
addRows(rows, testData, mockSmicData, false);

console.log('Updated Excel Row Format Test:');
console.log('==============================');
rows.forEach((row, index) => {
  console.log(`${index + 1}. ${row.departmentName} (${row.type}) - ${row['$ Projection']}`);
});

console.log('\n✅ Key Changes Verified:');
console.log('1. Total row shows: "Total of 2 divisions" (dynamic count)');
console.log('2. Banner names show only the name without ID prefix:');
console.log('   - "Acme Stores" (not "25 - Acme Stores")');
console.log('   - "Balducci\'s Food Lovers Market" (not "02 - Balducci\'s Food Lovers Market")');
console.log('   - "Kings Food Markets" (not "04 - Kings Food Markets")');

console.log('\n✅ Expected Excel Structure:');
console.log('1. Total of 2 divisions (Quarter)');
console.log('2. Period 202506 (Period)');
console.log('3. Week 202525 (Week)');
console.log('4. 34 - Test Division (Quarter)');
console.log('5.   Acme Stores (Quarter) [indented banner]');
console.log('6.   301 - Grocery (Quarter)');
console.log('7.   Balducci\'s Food Lovers Market (Quarter) [indented banner]');
console.log('8. 35 - Another Division (Quarter)');
console.log('9.   Kings Food Markets (Quarter) [indented banner]');
