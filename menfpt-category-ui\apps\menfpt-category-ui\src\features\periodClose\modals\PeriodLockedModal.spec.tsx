import '@testing-library/jest-dom';
jest.mock('@albertsons/uds/molecule/Button', () => (props) => <button {...props} />);
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { PeriodLockedModal } from './PeriodLockedModal';

describe('PeriodLockedModal', () => {
  it('renders modal with correct text and calls onClose', () => {
    const onClose = jest.fn();
    render(<PeriodLockedModal onClose={onClose} quarterLabel="Q1 2024" />);
    expect(screen.getByText('Attention, please read this carefully!')).toBeInTheDocument();
    expect(screen.getByText(/Q1 2024 has been closed for any edits/)).toBeInTheDocument();
    const button = screen.getByRole('button', { name: /OK, I understand/i });
    fireEvent.click(button);
    expect(onClose).toHaveBeenCalled();
  });
}); 