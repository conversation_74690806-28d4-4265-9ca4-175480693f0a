
import { formatHelper } from './AllocatrInsightsHelper';

describe('formatHelper', () => {
  it('formats numbers correctly', () => {
    expect(formatHelper(1000)).toBe('1,000');
    expect(formatHelper(1234567)).toBe('1,234,567');
    expect(formatHelper(1234.56)).toBe('1,234.56');
    expect(formatHelper(1000000000)).toBe('1,000,000,000');
  });

  it('returns empty string for null/undefined', () => {
    expect(formatHelper(null)).toBe('');
    expect(formatHelper(undefined)).toBe('');
  });

  it('handles negative numbers', () => {
    expect(formatHelper(-500)).toBe('-500');
    expect(formatHelper(-1234.56)).toBe('-1,234.56');
  });

  it('handles zero', () => {
    expect(formatHelper(0)).toBe('0');
  });

  it('returns empty string for non-numeric input', () => {
    expect(formatHelper('abc')).toBe('');
    expect(formatHelper({})).toBe('');
    expect(formatHelper([])).toBe('');
    expect(formatHelper(NaN)).toBe('');
  });

  it('handles numbers with decimals', () => {
    expect(formatHelper(1234.5678)).toBe('1,234.5678');
    expect(formatHelper(-9876.54321)).toBe('-9,876.54321');
  });

  it('handles numbers as string input', () => {
    expect(formatHelper('1234')).toBe('1,234');
    expect(formatHelper('1234.56')).toBe('1,234.56');
    expect(formatHelper('-1234.56')).toBe('-1,234.56');
  });

  it('handles very large and very small numbers', () => {
    expect(formatHelper(1e12)).toBe('1,000,000,000,000');
    expect(formatHelper(-1e-6)).toBe('-0.000001');
  });

  it('handles exponential notation as string', () => {
    expect(formatHelper('1e6')).toBe('1,000,000');
    expect(formatHelper('-1e6')).toBe('-1,000,000');
  });

  it('returns empty string for whitespace and boolean', () => {
    expect(formatHelper('   ')).toBe('');
    expect(formatHelper(true)).toBe('');
    expect(formatHelper(false)).toBe('');
  });

  it('returns empty string for deeply nested objects/arrays', () => {
    expect(formatHelper([{ a: 1 }])).toBe('');
    expect(formatHelper({ a: { b: 2 } })).toBe('');
  });
});
