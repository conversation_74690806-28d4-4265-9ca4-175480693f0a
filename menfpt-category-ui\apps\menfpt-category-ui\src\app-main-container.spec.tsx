import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import NFPTContainer from './app-main-container';
import * as menfptCategoryAPI from './server/Api/menfptCategoryAPI';
import * as usePdfHelpHook from './hooks/usePdfHelp';
import { setDisplayDate, setUserInfo } from './server/Reducer/menfpt-category.slice';
import { setPeriodStatuses } from './features/periodClose/periodStatuses.slice';

// Mock child component
const MockChildComponent = () => <div data-testid="child-component">Child Component</div>;

// Mock Spinner
jest.mock('@albertsons/uds/molecule/Spinner', () => () => <div data-testid="spinner">Loading...</div>);

const mockStore = configureStore([]);

describe('Given NFPTContainer component', () => {
  let store: any;

  beforeEach(() => {
    store = mockStore({
      menfptCategory: {
        userInfo: null,
        displayDate: null,
      },
    });
    store.dispatch = jest.fn();
    jest.spyOn(usePdfHelpHook, 'usePdfHelp').mockImplementation(() => ({ pdfUrl: null }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('When it is loading data', () => {
    it('Then it should render the spinner if display date is loading', () => {
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: undefined, isLoading: true, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: undefined, isLoading: false, isError: false, refetch: jest.fn() } as any);

      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(screen.getByTestId('spinner')).toBeInTheDocument();
      expect(screen.queryByTestId('child-component')).not.toBeInTheDocument();
    });

    it('Then it should render the spinner if user info is loading', () => {
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: [], isLoading: false, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: undefined, isLoading: true, isError: false, refetch: jest.fn() } as any);

      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(screen.getByTestId('spinner')).toBeInTheDocument();
      expect(screen.queryByTestId('child-component')).not.toBeInTheDocument();
    });
  });

  describe('When data has been fetched successfully', () => {
    const mockDisplayDate = { date: '2024-01-01' };
    const mockUserInfo = { name: 'Test User' };

    beforeEach(() => {
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: [mockDisplayDate], isLoading: false, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: { userInfo: mockUserInfo }, isLoading: false, isError: false, refetch: jest.fn() } as any);
    });

    it('Then it should render children components', () => {
      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should dispatch setDisplayDate with the correct data', () => {
      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(store.dispatch).toHaveBeenCalledWith(setDisplayDate(mockDisplayDate));
    });

    it('Then it should dispatch setUserInfo with the correct data', () => {
      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(store.dispatch).toHaveBeenCalledWith(setUserInfo(mockUserInfo));
    });
    
    it('Then it should dispatch setPeriodStatuses on route change', () => {
        render(
          <Provider store={store}>
            <MemoryRouter initialEntries={['/some-route']}>
              <NFPTContainer>
                <MockChildComponent />
              </NFPTContainer>
            </MemoryRouter>
          </Provider>
        );
  
        expect(store.dispatch).toHaveBeenCalledWith(setPeriodStatuses({}));
      });
  });

  describe('When data fetching has edge cases', () => {
    it('Then it should not dispatch setDisplayDate if data is empty', () => {
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: [], isLoading: false, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: { userInfo: {} }, isLoading: false, isError: false, refetch: jest.fn() } as any);

      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(store.dispatch).not.toHaveBeenCalledWith(expect.objectContaining({ type: setDisplayDate.type }));
    });

    it('Then it should not dispatch setUserInfo if userInfo is not present', () => {
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: [{ date: '2024-01-01' }], isLoading: false, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: {}, isLoading: false, isError: false, refetch: jest.fn() } as any);

      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(store.dispatch).not.toHaveBeenCalledWith(expect.objectContaining({ type: setUserInfo.type }));
    });
  });

  describe('When usePdfHelp hook is used', () => {
    it('Then it should be called', () => {
      const usePdfHelpSpy = jest.spyOn(usePdfHelpHook, 'usePdfHelp');
      jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ data: [], isLoading: false, isError: false, refetch: jest.fn() } as any);
      jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ data: {}, isLoading: false, isError: false, refetch: jest.fn() } as any);

      render(
        <Provider store={store}>
          <MemoryRouter>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(usePdfHelpSpy).toHaveBeenCalled();
    });
  });

  describe('When on routes that should skip API calls', () => {
    it('Then it should skip API calls for access-denied route', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/access-denied']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should skip API calls for error route', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/error']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should skip API calls for 404 route', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/404']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should skip API calls for unauthorized route', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/unauthorized']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should skip API calls for maintenance route', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/maintenance']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should not skip API calls for regular routes', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: [{ date: '2024-01-01' }], 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: { userInfo: { name: 'Test User' } }, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/dashboard']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: false });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: false });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });

    it('Then it should handle nested paths with skip routes', () => {
      const displayDateQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetDisplayDateQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);
      const userInfoQuerySpy = jest.spyOn(menfptCategoryAPI, 'useGetUserInfoQuery').mockReturnValue({ 
        data: undefined, 
        isLoading: false, 
        isError: false, 
        refetch: jest.fn() 
      } as any);

      render(
        <Provider store={store}>
          <MemoryRouter initialEntries={['/menfpt/access-denied']}>
            <NFPTContainer>
              <MockChildComponent />
            </NFPTContainer>
          </MemoryRouter>
        </Provider>
      );

      expect(displayDateQuerySpy).toHaveBeenCalledWith({}, { skip: true });
      expect(userInfoQuerySpy).toHaveBeenCalledWith({ query: expect.any(String) }, { skip: true });
      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });
  });
});