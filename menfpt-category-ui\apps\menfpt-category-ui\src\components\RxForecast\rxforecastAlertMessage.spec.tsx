import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';
import RxForecastAlertMessage from './rxforecastAlertMessage';

// Mock UDS Alert component
jest.mock('@albertsons/uds/molecule/Alert', () => ({
  __esModule: true,
  default: ({ children, isOpen, variant, size }: any) => (
    <div 
      data-testid="uds-alert"
      data-is-open={isOpen}
      data-variant={variant}
      data-size={size}
      role="alert"
    >
      {children}
    </div>
  ),
}));

// Mock UDS Link component
jest.mock('@albertsons/uds/molecule/Link', () => ({
  __esModule: true,
  default: ({ children, href, className, target, rel, size, onClick }: any) => (
    <a 
      data-testid="uds-link"
      href={href}
      className={className}
      target={target}
      rel={rel}
      data-size={size}
      onClick={onClick}
    >
      {children}
    </a>
  ),
}));

// Mock environment variable utility
jest.mock('../../util/envVarsManager', () => ({
  getEnvParamVal: jest.fn(),
}));

describe('RxForecastAlertMessage', () => {
  const mockGetEnvParamVal = require('../../util/envVarsManager').getEnvParamVal;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default environment mock
    mockGetEnvParamVal.mockReturnValue('https://example.com/menfpt-category-ui');
  });
  it('renders the component correctly', () => {
    render(<RxForecastAlertMessage />);

    expect(screen.getByTestId('uds-alert')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('displays the info message correctly', () => {
    render(<RxForecastAlertMessage />);

    const infoMessage = "You are only uploading pharmacy department projection and forecast.";
    expect(screen.getByText(infoMessage)).toBeInTheDocument();
  });

  it('displays the template message correctly', () => {
    render(<RxForecastAlertMessage />);

    const templateMessage = "Refer this template for uploading the projection and forecast.";
    expect(screen.getByText(templateMessage, { exact: false })).toBeInTheDocument();
  });

  it('renders the View Template link with correct text', () => {
    render(<RxForecastAlertMessage />);

    const viewTemplateLink = screen.getByText('View Template');
    expect(viewTemplateLink).toBeInTheDocument();
    expect(viewTemplateLink).toHaveAttribute('data-testid', 'uds-link');
  });

  it('configures Alert component with correct props', () => {
    render(<RxForecastAlertMessage />);

    const alert = screen.getByTestId('uds-alert');
    expect(alert).toHaveAttribute('data-is-open', 'true');
    expect(alert).toHaveAttribute('data-variant', 'informational');
    expect(alert).toHaveAttribute('data-size', 'medium');
  });

  it('configures Link component with correct props', () => {
    render(<RxForecastAlertMessage />);

    const link = screen.getByTestId('uds-link');
    expect(link).not.toHaveAttribute('href'); // No href in current implementation
    expect(link).not.toHaveAttribute('target'); // No target in current implementation
    expect(link).not.toHaveAttribute('rel'); // No rel in current implementation
    expect(link).toHaveAttribute('data-size', 'medium');
    expect(link).toHaveClass('cursor-pointer');
  });

  it('applies correct CSS classes to info message container', () => {
    render(<RxForecastAlertMessage />);

    const infoMessage = "You are only uploading pharmacy department projection and forecast.";
    const infoDiv = screen.getByText(infoMessage);
    expect(infoDiv).toHaveClass('leading-6');
  });

  it('applies correct CSS classes to template message container', () => {
    render(<RxForecastAlertMessage />);

    const templateMessage = "Refer this template for uploading the projection and forecast.";
    const templateDiv = screen.getByText(templateMessage, { exact: false }).closest('div');
    
    expect(templateDiv).toHaveClass('text-[#1b6ebb]');
    expect(templateDiv).toHaveClass('font-nunito');
    expect(templateDiv).toHaveClass('font-semibold');
    expect(templateDiv).toHaveClass('leading-5');
  });

  it('has proper component structure', () => {
    const { container } = render(<RxForecastAlertMessage />);

    const alert = container.firstChild as HTMLElement;
    expect(alert).toHaveAttribute('data-testid', 'uds-alert');

    const childDivs = alert.querySelectorAll('div');
    expect(childDivs).toHaveLength(2); // Two div children inside Alert
  });

  it('renders link with click handler functionality', () => {
    render(<RxForecastAlertMessage />);

    const link = screen.getByTestId('uds-link');
    expect(link).not.toHaveAttribute('target'); // No target in current implementation
    expect(link).not.toHaveAttribute('rel'); // No rel in current implementation
    // The link should have an onClick handler (though we can't directly test the handler in this mock)
    expect(link).toBeInTheDocument();
  });

  it('renders link without href attribute (uses onClick instead)', () => {
    render(<RxForecastAlertMessage />);

    const link = screen.getByTestId('uds-link');
    expect(link).not.toHaveAttribute('href'); // Current implementation uses onClick instead of href
  });

  it('renders both message sections within the alert', () => {
    render(<RxForecastAlertMessage />);

    const alert = screen.getByTestId('uds-alert');
    const infoMessage = "You are only uploading pharmacy department projection and forecast.";
    const templateMessage = "Refer this template for uploading the projection and forecast.";

    expect(alert).toContainElement(screen.getByText(infoMessage));
    expect(alert).toContainElement(screen.getByText(templateMessage, { exact: false }));
  });

  it('applies informational alert styling', () => {
    render(<RxForecastAlertMessage />);

    const alert = screen.getByTestId('uds-alert');
    expect(alert).toHaveAttribute('data-variant', 'informational');
    expect(alert).toHaveAttribute('data-size', 'medium');
  });

  it('ensures alert is always open', () => {
    render(<RxForecastAlertMessage />);

    const alert = screen.getByTestId('uds-alert');
    expect(alert).toHaveAttribute('data-is-open', 'true');
  });

  it('renders template message and link in the same container', () => {
    render(<RxForecastAlertMessage />);

    const templateMessage = "Refer this template for uploading the projection and forecast.";
    const templateDiv = screen.getByText(templateMessage, { exact: false }).closest('div');
    const link = screen.getByTestId('uds-link');

    expect(templateDiv).toContainElement(link);
  });

  it('has correct text content in link', () => {
    render(<RxForecastAlertMessage />);

    const link = screen.getByTestId('uds-link');
    expect(link).toHaveTextContent('View Template');
  });

  it('uses medium size for both Alert and Link components', () => {
    render(<RxForecastAlertMessage />);

    const alert = screen.getByTestId('uds-alert');
    const link = screen.getByTestId('uds-link');

    expect(alert).toHaveAttribute('data-size', 'medium');
    expect(link).toHaveAttribute('data-size', 'medium');
  });

  it('applies custom text color to template message', () => {
    render(<RxForecastAlertMessage />);

    const templateMessage = "Refer this template for uploading the projection and forecast.";
    const templateDiv = screen.getByText(templateMessage, { exact: false }).closest('div');
    
    expect(templateDiv).toHaveClass('text-[#1b6ebb]'); // Blue color
  });

  it('uses correct font family and weight', () => {
    render(<RxForecastAlertMessage />);

    const templateMessage = "Refer this template for uploading the projection and forecast.";
    const templateDiv = screen.getByText(templateMessage, { exact: false }).closest('div');
    
    expect(templateDiv).toHaveClass('font-nunito');
    expect(templateDiv).toHaveClass('font-semibold');
  });

  it('renders without errors', () => {
    expect(() => render(<RxForecastAlertMessage />)).not.toThrow();
  });
  describe('Message Content', () => {
    it('contains all expected text content', () => {
      render(<RxForecastAlertMessage />);

      // Check for main info message
      expect(screen.getByText('You are only uploading pharmacy department projection and forecast.')).toBeInTheDocument();
      
      // Check for template message
      expect(screen.getByText('Refer this template for uploading the projection and forecast.')).toBeInTheDocument();
      
      // Check for link text
      expect(screen.getByText('View Template')).toBeInTheDocument();
    });

    it('displays messages in correct order', () => {
      const { container } = render(<RxForecastAlertMessage />);

      const alert = container.querySelector('[data-testid="uds-alert"]');
      const divs = alert?.querySelectorAll('div');
      
      expect(divs?.[0]).toHaveTextContent('You are only uploading pharmacy department projection and forecast.');
      expect(divs?.[1]).toHaveTextContent('Refer this template for uploading the projection and forecast.');
    });
  });

  describe('Link Functionality', () => {
    it('uses click handler instead of direct href', () => {
      render(<RxForecastAlertMessage />);

      const link = screen.getByTestId('uds-link');
      expect(link).not.toHaveAttribute('href'); // Current implementation uses onClick handler
    });

    it('does not use target or rel attributes (uses onClick handler)', () => {
      render(<RxForecastAlertMessage />);

      const link = screen.getByTestId('uds-link');
      expect(link).not.toHaveAttribute('target'); // Current implementation doesn't use target
      expect(link).not.toHaveAttribute('rel'); // Current implementation doesn't use rel
    });

    it('has cursor pointer styling', () => {
      render(<RxForecastAlertMessage />);

      const link = screen.getByTestId('uds-link');
      expect(link).toHaveClass('cursor-pointer');
    });
  });

  describe('Styling and Layout', () => {
    it('applies correct line heights', () => {
      render(<RxForecastAlertMessage />);

      const infoMessage = screen.getByText('You are only uploading pharmacy department projection and forecast.');
      const templateDiv = screen.getByText('Refer this template for uploading the projection and forecast.', { exact: false }).closest('div');

      expect(infoMessage).toHaveClass('leading-6');
      expect(templateDiv).toHaveClass('leading-5');
    });

    it('uses informational alert variant', () => {
      render(<RxForecastAlertMessage />);

      const alert = screen.getByTestId('uds-alert');
      expect(alert).toHaveAttribute('data-variant', 'informational');
    });
  });

  describe('Template Download Functionality', () => {
    it('calls handleViewTemplate when link is clicked', () => {
      render(<RxForecastAlertMessage />);

      const link = screen.getByTestId('uds-link');
      link.click();

      expect(mockGetEnvParamVal).toHaveBeenCalledWith('MENFPT_GRAPHQL_ENDPOINT');
    });
  });
});
