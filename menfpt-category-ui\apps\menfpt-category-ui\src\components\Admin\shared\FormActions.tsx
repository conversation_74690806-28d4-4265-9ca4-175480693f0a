import React from 'react';
import Button from '@albertsons/uds/molecule/Button';

const CtaBtnSmallVariableTertiary = ({ onClick }: { onClick?: () => void }) => (
  <div className="flex flex-col flex-shrink-0 justify-center items-center w-24 h-10 cursor-pointer" onClick={onClick}>
    <div className="flex flex-col flex-shrink-0 justify-center items-center gap-2.5 py-0 px-2 h-8 rounded-lg">
      <div className="flex justify-center items-center gap-2">
        <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 18L9 12L15 6" stroke="#1B6EBB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
        <div className="label Sans text-[#1b6ebb] text-center font-nunito font-semibold leading-5">Back</div>
      </div>
    </div>
  </div>
);

interface FormActionsProps {
  onCancel?: () => void;
  onSubmit?: () => void;
  onBack?: () => void;
  cancelText?: string;
  submitText?: string;
  cancelWidth?: number;
  submitWidth?: number;
  isSubmitting?: boolean;
  disabled?: boolean;
  showBack?: boolean;
} 

export const FormActions: React.FC<FormActionsProps> = ({
  onCancel,
  onSubmit,
  onBack,
  cancelText = 'Cancel',
  submitText = 'Save',
  cancelWidth = 120,
  submitWidth = 120,
  isSubmitting = false,
  disabled = false,
  showBack = false
}) => (
  <div className="flex justify-end gap-[8px] mt-4 items-center">
    <div>
      {showBack && (
        <CtaBtnSmallVariableTertiary onClick={onBack} />
      )}
    </div>
    
    <div className="flex gap-[8px]">
      <Button 
        type="button" 
        variant="secondary" 
        width={cancelWidth}
        onClick={onCancel}
      >
        {cancelText}
      </Button>
      <Button 
        type="submit" 
        width={submitWidth}
        onClick={onSubmit}
        disabled={disabled || isSubmitting}
      >
        {isSubmitting ? 'Saving...' : submitText}
      </Button>
    </div>
  </div>
);