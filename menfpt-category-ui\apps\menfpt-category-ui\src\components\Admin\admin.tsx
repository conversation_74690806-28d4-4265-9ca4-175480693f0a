import React, { useState } from 'react';
import UserModal from './addUserModal';
import EditUserAccessModal from './editUserAccess';
import DeactivateUserModal from './deactivateUserModal';

const Admin: React.FC = () => {
    const [isUserModalOpen, setIsUserModalOpen] = useState(false);
    const [isEditUserAccessModalOpen, setIsEditUserAccessModalOpen] = useState(false);
    const [isDeactivateUserModalOpen, setIsDeactivateUserModalOpen] = useState(false);

    return (
        <>
            <UserModal
                isOpen={isUserModalOpen}
                onClose={() => setIsUserModalOpen(false)}
                onSubmit={(values) => {
                    console.log('User added:', values);
                    setIsUserModalOpen(false);
                }}
            />
            <EditUserAccessModal
                isOpen={isEditUserAccessModalOpen}
                onClose={() => setIsEditUserAccessModalOpen(false)}
            />
            <DeactivateUserModal
                isOpen={isDeactivateUserModalOpen}
                onClose={() => setIsDeactivateUserModalOpen(false)}
            />
        </>
    );
};

export default Admin;
