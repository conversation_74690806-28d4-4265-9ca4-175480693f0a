import React from 'react';
import { worksheetFilterConfig } from '../../worksheetFilterConfig';

interface SuggestionItem {
  id: string | number;
  label: string;
}

interface SuggestionGroup {
  type: string;
  items: SuggestionItem[];
}

interface RoleSearchSuggestionsProps {
  isSearchInitialFocus: boolean;
  value: string;
  onChange: (value: string) => void;
  suggestions: SuggestionGroup[];
  onSuggestionSelect?: (item: SuggestionItem) => void;
  className?: string;
}

// Utility to convert a string to title case
const toTitleCase = (str: string) =>
  str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());

export const RoleSearchSuggestions: React.FC<RoleSearchSuggestionsProps> = ({
  isSearchInitialFocus,
  value,
  onChange,
  suggestions,
  onSuggestionSelect,
  className = '',
}) => {
  const renderHintBox = (
    <div className="hint-box bg-white rounded-3xl p-8 flex items-center shadow-md mb-4">
      <span className="text-gray-500  font-normal">
        Please enter at least {worksheetFilterConfig.deptRoleCascadeSearchMinChars} numbers / letters
      </span>
    </div>
  );

  const renderNoResults = (
    <div className="text-gray-500 text-center py-4">No matching results</div>
  );

  const highlightMatch = (label: string, keyword: string) => {
    if (!keyword) {
      return toTitleCase(label);
    }
    const titleCasedLabel = toTitleCase(label);
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = titleCasedLabel.split(regex);
    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="border border-sky-300 bg-sky-100 rounded px-1">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  const handleSuggestionSelect = (item: SuggestionItem) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(item);
    }
  };

  const renderSuggestionSection = (section: SuggestionGroup) => (
    <div key={section.type} className="mb-4">
      <div className="text-gray-500 font-semibold text-sm mb-1 flex items-center">
        {section.type} ({section.items.length})
        <span className="bg-primary text-white rounded-full w-5 h-5 inline-flex items-center justify-center text-xs ml-1">
          {section.items.length}
        </span>
      </div>
      {section.items.map((item) => (
        <div
          key={item.id}
          className="flex items-center py-1 border-b border-gray-100 cursor-pointer"
          onMouseDown={() => handleSuggestionSelect(item)}
          onTouchStart={() => handleSuggestionSelect(item)}
          onClick={() => handleSuggestionSelect(item)}
        >
          <span className="flex-1 text-gray-900 text-base">
            {highlightMatch(item.label, value)}
          </span>
        </div>
      ))}
    </div>
  );

  const renderSuggestionsDropdown = (
    <div className="suggestions-dropdown bg-white rounded-3xl shadow-md p-4 w-full">
      {(!suggestions || suggestions.length === 0 || suggestions.every(section => !section.items || section.items.length === 0)) ? (
        renderNoResults
      ) : (
        suggestions.map(renderSuggestionSection)
      )}
    </div>
  );

  return (
    <div className={`role-search-suggestions-container absolute right-0  z-50 top-10 ${className}`}>
      {isSearchInitialFocus ? renderHintBox : renderSuggestionsDropdown}
    </div>
  );
};
