{"name": "menfpt-category-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/menfpt-category-ui/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/menfpt-category-ui", "index": "apps/menfpt-category-ui/src/index.html", "baseHref": "/", "main": "apps/menfpt-category-ui/src/main.ts", "polyfills": "apps/menfpt-category-ui/src/polyfills.ts", "tsConfig": "apps/menfpt-category-ui/tsconfig.app.json", "assets": ["apps/menfpt-category-ui/src/favicon.ico", "apps/menfpt-category-ui/src/assets"], "styles": ["apps/menfpt-category-ui/src/styles.scss"], "scripts": [], "webpackConfig": "apps/menfpt-category-ui/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/menfpt-category-ui/src/environments/environment.ts", "with": "apps/menfpt-category-ui/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "webpackConfig": "apps/menfpt-category-ui/webpack.config.prod.js"}}}, "serve": {"executor": "@nrwl/react:module-federation-dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "menfpt-category-ui:build", "hmr": false, "port": 3010}, "configurations": {"development": {"buildTarget": "menfpt-category-ui:build:development"}, "production": {"buildTarget": "menfpt-category-ui:build:production", "hmr": false}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/menfpt-category-ui/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/menfpt-category-ui/jest.config.ts", "passWithNoTests": true}}, "serve-static": {"executor": "@nrwl/web:file-server", "defaultConfiguration": "development", "options": {"buildTarget": "menfpt-category-ui:build", "port": 3010, "spa": true}, "configurations": {"development": {"buildTarget": "menfpt-category-ui:build:development"}, "production": {"buildTarget": "menfpt-category-ui:build:production"}}}}, "tags": []}