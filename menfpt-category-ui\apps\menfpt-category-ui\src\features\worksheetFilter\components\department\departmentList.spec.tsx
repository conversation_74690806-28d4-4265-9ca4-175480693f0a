import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DepartmentList from './departmentList';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';

// Minimal mock reducer for departments_rn
const mockReducer = (state = { data: [] }, action: any) => state;
const getStore = (departmentsData: any[] = []) =>
  configureStore({
    reducer: {
      departments_rn: () => ({ data: departmentsData }),
    },
  });

const renderWithProvider = (ui: React.ReactElement, departmentsData: any[] = []) =>
  render(
    <MemoryRouter initialEntries={["/dashboard"]}>
      <Provider store={getStore(departmentsData)}>{ui}</Provider>
    </MemoryRouter>
  );

describe('DepartmentList', () => {
  const departments = [
    { num: 1, name: 'HR' },
    { num: 2, name: 'Finance' },
    { num: 3, name: 'Engineering' },
  ];

  it('renders department names', () => {
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={false}
        onDepartmentChange={jest.fn()}
      />,
      departments
    );
    departments.forEach(dept => {
      expect(
        screen.getByText((content) => content.toLowerCase() === dept.name.toLowerCase())
      ).toBeInTheDocument();
    });
  });

  it('calls onDepartmentChange when a department is clicked (single select)', () => {
    const onDepartmentChange = jest.fn();
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={false}
        onDepartmentChange={onDepartmentChange}
      />,
      departments
    );
    fireEvent.click(screen.getByText('Finance'));
    expect(onDepartmentChange).toHaveBeenCalledWith(departments[1]);
  });

  it('calls onDepartmentChange with array when a department is clicked (multi select)', () => {
    const onDepartmentChange = jest.fn();
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[]}
        onDepartmentChange={onDepartmentChange}
      />,
      departments
    );
    fireEvent.click(
      screen.getByText((content) => content.toLowerCase() === 'hr')
    );
    expect(onDepartmentChange).toHaveBeenCalledWith([departments[0]]);
  });

  it('shows select all checkbox when multi select is allowed', () => {
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[]}
        onDepartmentChange={jest.fn()}
      />,
      departments
    );
    expect(screen.getByText(/selected|select/i)).toBeInTheDocument();
  });

  it('shows empty state if no departments', () => {
    renderWithProvider(
      <DepartmentList
        departments={[]}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[]}
        onDepartmentChange={jest.fn()}
      />,
      []
    );
    expect(
      screen.getByText((content) => /select/i.test(content))
    ).toBeInTheDocument();
  });

  it('handles select all checkbox click', () => {
    const onDepartmentChange = jest.fn();
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[]}
        onDepartmentChange={onDepartmentChange}
      />,
      departments
    );
    fireEvent.click(screen.getByText(/select/i));
    expect(onDepartmentChange).toHaveBeenCalled();
  });

  it('handles indeterminate state for select all', () => {
    renderWithProvider(
      <DepartmentList
        departments={departments}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[departments[0]]}
        onDepartmentChange={jest.fn()}
      />,
      departments
    );
    expect(screen.getByText(/selected/i)).toBeInTheDocument();
  });

  it('handles disabled select all checkbox when no departments', () => {
    renderWithProvider(
      <DepartmentList
        departments={[]}
        isMultipleSelectionAllowed={true}
        selectedDepartment={[]}
        onDepartmentChange={jest.fn()}
      />,
      []
    );
    expect(screen.getByText(/select/i)).toBeInTheDocument();
  });
});
