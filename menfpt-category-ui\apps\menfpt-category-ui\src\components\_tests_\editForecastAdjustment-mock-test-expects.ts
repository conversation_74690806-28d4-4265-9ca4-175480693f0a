import { Adjustment } from "../../interfaces/edit-forecast-adjustments";

export const mock_week123_api_body: Adjustment = {
    deptId: "3010000",
    divisionIds: [
      "25",
    ],
    smicCategoryIds: [
      101,
      105,
    ],
    updatedBy: "ANAMA05",
    deskId: "25-3010000-ARIAN NAMAVAR-BROOKLYN REGO",
    weeks: [
      {
        fiscalWeekNbr: 202501,
        editedColumns: "salesPublic|suppliesPackaging",
        previousAggregatedData: {
          line1PublicToSalesNbr: 44835162,
          line1PublicToSalesPct: 0,
          line5BookGrossProfitNbr: 24088228,
          line5BookGrossProfitPct: 54,
          line5MarkDownsNbr: -10446424,
          line5MarkDownsPct: -23,
          line5ShrinkNbr: -1037388,
          line5ShrinkPct: -2,
          line6SuppliesPackagingNbr: -253503,
          line7RetailsAllowancesNbr: 3455123,
          line7RetailsSellingAllowancesNbr: 1000000,
          line7RetailsNonSellingAllowancesNbr: 1000000,
        },
        newAggregatedData: {
          line1PublicToSalesNbr: 44835163,
          line1PublicToSalesPct: -0.013436338698759058,
          line5BookGrossProfitNbr: 24088228.53726198,
          line5BookGrossProfitPct: 0.5372619820131351,
          line5MarkDownsNbr: -10446424.232996237,
          line5MarkDownsPct: -0.232996236302213,
          line5ShrinkNbr: -1037388.023137822,
          line5ShrinkPct: -0.023137822051362278,
          line6SuppliesPackagingNbr: -243503,
          // line7RetailsAllowancesNbr: 3525123,
          // line7RetailsAllowancesPct: 0.020259770780953385,
          line7RetailsAllowancesNbr: 3455123,
          line7RetailsSellingAllowancesNbr: 1010000,
          line7RetailsNonSellingAllowancesNbr: 1010000,
        },
        reason: "Extreme weather",
        comment: "This is a test comment for Groups 1,2,3 selected",
      },
      {
        fiscalWeekNbr: 202502,
        editedColumns: "salesPublic|suppliesPackaging|allowances",
        previousAggregatedData: {
          line1PublicToSalesNbr: 45752542,
          line1PublicToSalesPct: 0,
          line5BookGrossProfitNbr: 24624212,
          line5BookGrossProfitPct: 54,
          line5MarkDownsNbr: -11058699,
          line5MarkDownsPct: -24,
          line5ShrinkNbr: -975460,
          line5ShrinkPct: -2,
          line6SuppliesPackagingNbr: -249927,
          line7RetailsAllowancesNbr: 3292932,
          line7RetailsSellingAllowancesNbr: 1000000,
          line7RetailsNonSellingAllowancesNbr: 1000000,
        },
        newAggregatedData: {
          line1PublicToSalesNbr: 45752543,
          line1PublicToSalesPct: -0.018978168706649822,
          line5BookGrossProfitNbr: 24624212.538204238,
          line5BookGrossProfitPct: 0.5382042379197204,
          line5MarkDownsNbr: -11058699.241706766,
          line5MarkDownsPct: -0.24170676680653064,
          line5ShrinkNbr: -975460.0213203455,
          line5ShrinkPct: -0.02132034543567,
          line6SuppliesPackagingNbr: -239927,
          // line7RetailsAllowancesNbr: 3362932,
          // line7RetailsAllowancesPct: 0.021257651236041314,
          line7RetailsAllowancesNbr: 3292932,
          line7RetailsSellingAllowancesNbr: 1010000,
          line7RetailsNonSellingAllowancesNbr: 1010000,
        },
        reason: "Extreme weather",
        comment: "This is a test comment for Groups 1,2,3 selected",
      },
      {
        fiscalWeekNbr: 202503,
        editedColumns: "salesPublic|suppliesPackaging|allowances",
        previousAggregatedData: {
          line1PublicToSalesNbr: 45603498,
          line1PublicToSalesPct: 0,
          line5BookGrossProfitNbr: 24746361,
          line5BookGrossProfitPct: 54,
          line5MarkDownsNbr: -11361220,
          line5MarkDownsPct: -25,
          line5ShrinkNbr: -842248,
          line5ShrinkPct: -2,
          line6SuppliesPackagingNbr: -286941,
          line7RetailsAllowancesNbr: 3597287,
          line7RetailsSellingAllowancesNbr: 1000000,
          line7RetailsNonSellingAllowancesNbr: 1000000,
        },
        newAggregatedData: {
          line1PublicToSalesNbr: 45603499,
          line1PublicToSalesPct: -0.010275417698463999,
          line5BookGrossProfitNbr: 24746361.54264173,
          line5BookGrossProfitPct: 0.5426417289305308,
          line5MarkDownsNbr: -11361220.24913045,
          line5MarkDownsPct: -0.2491304504755315,
          line5ShrinkNbr: -842248.0184689342,
          line5ShrinkPct: -0.01846893411553649,
          line6SuppliesPackagingNbr: -276941,
          // line7RetailsAllowancesNbr: 3667287,
          // line7RetailsAllowancesPct: 0.019459109045233253,
          line7RetailsAllowancesNbr: 3597287,
          line7RetailsSellingAllowancesNbr: 1010000,
          line7RetailsNonSellingAllowancesNbr: 1010000,
        },
        reason: "Extreme weather",
        comment: "This is a test comment for Groups 1,2,3 selected",
      },
    ],
  }

export const mock_week1_api_body: Adjustment = {
    deptId: "3010000",
    divisionIds: [
      "25",
    ],
    smicCategoryIds: [
      101,
      105,
    ],
    updatedBy: "ANAMA05",
    deskId: "25-3010000-ARIAN NAMAVAR-BROOKLYN REGO",
    weeks: [
      {
        fiscalWeekNbr: 202501,
        editedColumns: "salesPublic|suppliesPackaging|allowances",
        previousAggregatedData: {
          line1PublicToSalesNbr: 44835162,
          line1PublicToSalesPct: 0,
          line5BookGrossProfitNbr: 24088228,
          line5BookGrossProfitPct: 54,
          line5MarkDownsNbr: -10446424,
          line5MarkDownsPct: -23,
          line5ShrinkNbr: -1037388,
          line5ShrinkPct: -2,
          line6SuppliesPackagingNbr: -253503,
          line7RetailsAllowancesNbr: 3455123,
          line7RetailsSellingAllowancesNbr: 1000000,
          line7RetailsNonSellingAllowancesNbr: 1000000
        },
        newAggregatedData: {
          line1PublicToSalesNbr: 44835163,
          line1PublicToSalesPct: -0.013436338698759058,
          line5BookGrossProfitNbr: 24088228.53726198,
          line5BookGrossProfitPct: 0.5372619820131351,
          line5MarkDownsNbr: -10446424.232996237,
          line5MarkDownsPct: -0.232996236302213,
          line5ShrinkNbr: -1037388.023137822,
          line5ShrinkPct: -0.023137822051362278,
          line6SuppliesPackagingNbr: -243503,
          line7RetailsAllowancesNbr: 3525123,
          line7RetailsSellingAllowancesNbr: 1010000,
          line7RetailsNonSellingAllowancesNbr: 1010000,
        },
        reason: "Extreme weather",
        comment: "This is a test comment",
      },
    ],
  }