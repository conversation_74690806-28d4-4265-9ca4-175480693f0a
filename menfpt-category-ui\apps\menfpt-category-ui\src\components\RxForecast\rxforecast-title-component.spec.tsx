import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock UploadDocument component BEFORE importing the component being tested
jest.mock('./uploadDocument', () => {
  const MockUploadDocument = () => {
    return <div data-testid="upload-document">Mock Upload Document</div>;
  };
  return MockUploadDocument;
});

// Mock HelpIcon component
jest.mock('../HelpIcon', () => ({ variant }: { variant: string }) => (
  <div data-testid="help-icon" data-variant={variant}>
    Help Icon
  </div>
));

// Import AFTER mocking
import RxForecastTitleComponent from './rxforecast-title-component';

describe('RxForecastTitleComponent', () => {
  it('renders the component correctly', () => {
    render(<RxForecastTitleComponent />);

    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
    expect(screen.getByTestId('help-icon')).toBeInTheDocument();
    expect(screen.getByTestId('upload-document')).toBeInTheDocument();
  });

  it('displays the correct title text', () => {
    render(<RxForecastTitleComponent />);

    expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('Rx Forecast');
  });

  it('renders HelpIcon with default variant', () => {
    render(<RxForecastTitleComponent />);

    const helpIcon = screen.getByTestId('help-icon');
    expect(helpIcon).toBeInTheDocument();
    expect(helpIcon).toHaveAttribute('data-variant', 'default');
  });

  it('has proper container structure', () => {
    const { container } = render(<RxForecastTitleComponent />);

    const outerDiv = container.firstChild as HTMLElement;
    expect(outerDiv).toHaveClass('px-6', 'pt-4');

    const titleContainer = outerDiv.querySelector('.flex.items-center.gap-2.ml-5') as HTMLElement;
    expect(titleContainer).toBeInTheDocument();
  });

  it('renders title and help icon in the same container', () => {
    render(<RxForecastTitleComponent />);

    const heading = screen.getByRole('heading', { level: 2 });
    const helpIcon = screen.getByTestId('help-icon');

    // Both should be in the same parent container
    expect(heading.parentElement).toBe(helpIcon.parentElement);
  });
});
