import React from 'react';
import { ActionBar }  from '@albertsons/uds/molecule/ActionBar';
import { Download, ClipboardCheck } from 'lucide-react'; // Or update to your icon source
 
const DashboardSidePanel: React.FC = () => {
  return (
    <div
      style={{
        position: 'fixed',
        right: 0,
        top: 0,
        height: '100vh',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'flex-end',
        padding: '16px',
      }}
    >
      <ActionBar
        vertical
        value="UDS ActionBar Component"
        iconButtons={[
          { icon: <Download />, label: 'Download', buttonProps: { onClick: () => {} } },
          { icon: <ClipboardCheck />, label: 'Clipboard', buttonProps: { onClick: () => {} } },
        ]}
      />
    </div>
  );
};
 
export default DashboardSidePanel;