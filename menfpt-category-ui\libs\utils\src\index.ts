export { formatToPST } from './timezoneInPST';
export { isFeatureEnabled } from './ks.config';
export { appConstants } from './constants';

export const toTitleCase = (str: string) =>
  str.replace(/(?:^|[\s-])([a-zA-Z])/g, (match) => match.toUpperCase())
     .replace(/([a-zA-Z])([a-zA-Z]+)/g, (match, p1, p2) => p1 + p2.toLowerCase());

export const truncate = (str: string, max: number = 15) =>
  str.length > max ? str.slice(0, max) + '..' : str;
