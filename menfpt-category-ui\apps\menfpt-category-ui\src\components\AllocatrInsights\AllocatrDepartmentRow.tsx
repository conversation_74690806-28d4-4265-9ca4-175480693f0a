import React, { forwardRef } from 'react';
import { DepartmentData } from '../../interfaces/allocatr-insights';
import { Grid2x2, SquareMinus, SquarePlus } from 'lucide-react';
import { formatCurrency, formatPercentage } from './utils/insightsFormatters';
import { borderClass } from './AllocatrInsightsHelper';
import { renderAllRows } from './utils/tableCell';

interface AllocatrDepartmentRowProps {
  department: DepartmentData;
  expanded: boolean;
  onToggleWeeks: (id: string) => void;
  expandedWeeks: boolean;
  alwaysSticky?: boolean;
  stickyTop?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  className?: string; // Allow custom className for sticky styling
  isQuarterActualUsed?: boolean; // Visual indicator for quarter actual used
}

const AllocatrDepartmentRow = forwardRef<HTMLTableRowElement, AllocatrDepartmentRowProps>(({ department, expanded, onToggleWeeks, expandedWeeks, alwaysSticky, stickyTop, onMouseEnter, onMouseLeave, className, isQuarterActualUsed }, ref) => (
  <tr
    ref={ref}
    key={`dept-${department.id}`}
    className={`department-row${className ? ` ${className}` : ''}${alwaysSticky ? ' sticky-row' : ''}`}
    style={{
          position: alwaysSticky ? 'sticky' : 'unset',
          top: alwaysSticky && typeof stickyTop === 'number' ? `${stickyTop}px` : undefined,
          zIndex: alwaysSticky ? 2 : undefined,
          backgroundColor: alwaysSticky ? '#E7F5FE' : '#F9FFF0',
    }}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    <td></td>
    <td className="department-cell">
      <div className="flex items-center justify-start h-full">
        <button className="cursor-pointer mr-1" onClick={() => onToggleWeeks(department.id)}>
        {expandedWeeks ? <SquareMinus size={14}/> : <SquarePlus size={14} />}
        </button>
        <div className="max-w-[180px] min-w-0 flex items-center">
          <span className="department-name-cell font-bold block w-full min-w-0 break-words" title={department.id === 'Total' ? 'Total' : `${department.name}`}>
            {department.id === 'Total' ?  "Total": `${department.id} - ${department.name}`}
          </span>
        </div>
      </div>
    </td>
    {renderAllRows(department.quarter,isQuarterActualUsed)}
  </tr>
));

export default AllocatrDepartmentRow;
