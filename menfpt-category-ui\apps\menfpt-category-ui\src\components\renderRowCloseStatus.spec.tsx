import React from 'react';
import { render, screen } from '@testing-library/react';
import RenderRowCloseStatus, { getPeriodStatusInfo } from './renderRowCloseStatus';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Mock icons for testing
jest.mock('../features/periodClose/periodIcons', () => ({
  certifiedIcon: () => <span data-testid="certified-icon">certified</span>,
  closeIcon: () => <span data-testid="close-icon">locked</span>,
}));

describe('getPeriodStatusInfo', () => {
  const periodStatuses = {
    certified: { '1': ['2', '3'], '2': ['1'] },
    locked: { '1': ['1'], '3': ['2'] },
    notLocked: { '4': ['1'] },
    notCertifiedButLocked: { '5': ['1'] },
  };

  it('returns certified status and icon for matching period and week', () => {
    const result = getPeriodStatusInfo(periodStatuses, '1', '2');
    expect(result.statusKey).toBe('certified');
    expect(result.icon).not.toBeNull();
  });

  it('returns locked status and icon for matching period and week', () => {
    const result = getPeriodStatusInfo(periodStatuses, '1', '1');
    expect(result.statusKey).toBe('locked');
    expect(result.icon).not.toBeNull();
  });

  it('returns notLocked status with no icon for matching period and week', () => {
    const result = getPeriodStatusInfo(periodStatuses, '4', '1');
    expect(result.statusKey).toBe('notLocked');
    expect(result.icon).toBeNull();
  });

  it('returns null for no match', () => {
    const result = getPeriodStatusInfo(periodStatuses, '10', '10');
    expect(result.statusKey).toBeNull();
    expect(result.icon).toBeNull();
  });

  it('returns correct status for period only', () => {
    const result = getPeriodStatusInfo(periodStatuses, '2');
    expect(result.statusKey).toBe('certified');
  });

  it('returns correct status for week only', () => {
    const result = getPeriodStatusInfo(periodStatuses, undefined, '2');
    expect(result.statusKey).toBe('certified');
  });

  it('treats weekNbr="0" as undefined', () => {
    const result = getPeriodStatusInfo(periodStatuses, '2', '0');
    expect(result.statusKey).toBe('certified');
  });
});

describe('RenderRowCloseStatus', () => {
  // Helper to create a mock store with the desired periodStatuses
  function renderWithStore(ui: React.ReactElement, periodStatuses: any) {
    const store = configureStore({
      reducer: (state = { periodStatuses_rn: { data: periodStatuses } }) => state,
    });
    return render(<Provider store={store}>{ui}</Provider>);
  }

  const periodStatuses = {
    certified: { '1': ['2'] },
    locked: { '1': ['1'] },
    notLocked: {},
    notCertifiedButLocked: {},
  };

  it('renders certified icon for certified status', () => {
    renderWithStore(<RenderRowCloseStatus periodNbr="1" weekNbr="2" />, periodStatuses);
    expect(screen.getByTestId('period-status-icon-certified')).toBeInTheDocument();
    expect(screen.getByTestId('certified-icon')).toBeInTheDocument();
  });

  it('renders locked icon for locked status', () => {
    renderWithStore(<RenderRowCloseStatus periodNbr="1" weekNbr="1" />, periodStatuses);
    expect(screen.getByTestId('period-status-icon-locked')).toBeInTheDocument();
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('renders nothing if no status found', () => {
    renderWithStore(<RenderRowCloseStatus periodNbr="10" weekNbr="10" />, periodStatuses);
    expect(screen.queryByTestId('period-status-icon-certified')).toBeNull();
    expect(screen.queryByTestId('period-status-icon-locked')).toBeNull();
  });

  it('renders only status key if onlyStatusKey is true', () => {
    renderWithStore(<RenderRowCloseStatus periodNbr="1" weekNbr="2" onlyStatusKey />, periodStatuses);
    expect(screen.getByTestId('period-status-key-certified')).toHaveTextContent('certified');
  });
}); 