import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AlertMessage from './AlertMessage';

// Example default props, update as needed based on your component
const defaultProps = {
  message: 'Test alert message',
  type: 'success' as 'success' | 'error', // or 'error' if applicable
  open: true,
};

describe('AlertMessage', () => {
  it('renders without crashing', () => {
    render(<AlertMessage {...defaultProps} />);
    expect(screen.getByText('Test alert message')).toBeInTheDocument();
  });

});
