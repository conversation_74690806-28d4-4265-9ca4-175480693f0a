import { useEffect } from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

export function useCascadeModeEffect(
  isDisplayDeptRoleCascade: boolean,
  handleCascadeSmDataChange: () => void,
  deps: any[]
) {
  useEffect(() => {
    if (!isDisplayDeptRoleCascade) return;
    handleCascadeSmDataChange();
    // eslint-disable-next-line
  }, [isDisplayDeptRoleCascade, ...deps]);
}

export function useNormalModeEffect(
  isDisplayDeptRoleCascade: boolean,
  handleNormalSmDataChange: () => void,
  deps: any[]
) {
  useEffect(() => {
    if (isDisplayDeptRoleCascade) return;
    handleNormalSmDataChange();
    // eslint-disable-next-line
  }, [isDisplayDeptRoleCascade, ...deps]);
} 