import React from 'react';
import DepartmentHeader from './departmentHeader';
import DepartmentSearch from './departmentSearch';
import DepartmentList from './departmentList';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

export function RenderDepartmentHeader({ showHeader, departmentHeaderStyles }: { showHeader: boolean; departmentHeaderStyles: string }) {
  if (!showHeader) return null;
  return <DepartmentHeader className={departmentHeaderStyles} />;
}

export function RenderDepartmentSearch({
  isDisplayDeptRoleCascade,
  showSearch,
  onSearchChange,
  searchQuery
}: {
  isDisplayDeptRoleCascade: boolean;
  showSearch: boolean;
  onSearchChange?: (query: string) => void;
  searchQuery: string;
}) {
  if (!showSearch || !onSearchChange) return null;
  return <DepartmentSearch value={searchQuery} onChange={onSearchChange} />;
}

export function RenderDepartmentList({
  departmentsToDisplay,
  selectedDepartment,
  isMultipleSelectionAllowed,
  onDepartmentChange,
  departmentItemStyles
}: {
  departmentsToDisplay: DropdownType[];
  selectedDepartment: DropdownType | DropdownType[] | undefined;
  isMultipleSelectionAllowed: boolean;
  onDepartmentChange: (dept: DropdownType | DropdownType[]) => void;
  departmentItemStyles: string;
}) {
  return (
    <DepartmentList
      departments={departmentsToDisplay}
      selectedDepartment={selectedDepartment}
      isMultipleSelectionAllowed={isMultipleSelectionAllowed}
      onDepartmentChange={onDepartmentChange}
      itemClassName={departmentItemStyles}
    />
  );
} 