import React from 'react';
import Checkbox from '@albertsons/uds/molecule/Checkbox';
import Radio from '@albertsons/uds/molecule/Radio';
import { toTitleCase } from '@ui/utils';

export interface SelectableItem {
  id: string | number;
  label: string;
  data?: any; // For storing additional data like the original object
}

interface SelectableListItemProps {
  item: SelectableItem;
  isSelected: boolean;
  isMultipleSelectionAllowed: boolean;
  onChange: (item: SelectableItem, checked?: boolean) => void;
  disabled?: boolean;
  className?: string;
  itemClassName?: string;
  labelClassName?: string;
  testId?: string;
  children?: React.ReactNode; // For nested content like ASM lists
  showChevron?: boolean;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  hasExpandableContent?: boolean; // New prop to indicate if item has expandable content
  isIndeterminate?: boolean; // New prop for indeterminate state
}

const SelectableListItem: React.FC<SelectableListItemProps> = ({
  item,
  isSelected,
  isMultipleSelectionAllowed,
  onChange,
  disabled = false,
  className = '',
  itemClassName = '',
  labelClassName = '',
  testId,
  children,
  showChevron = false,
  isExpanded = false,
  onToggleExpand,
  hasExpandableContent = false,
  isIndeterminate = false
}) => {
  const handleChange = (e?: React.ChangeEvent<HTMLInputElement>) => {
    if (isMultipleSelectionAllowed && e) {
      onChange(item, e.target.checked);
    } else {
      onChange(item);
    }
  };

  const ChevronIcon: React.FC = () => (
    <button
      onClick={(e) => {
        e.stopPropagation();
        onToggleExpand?.();
      }}
      className="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
      aria-label={isExpanded ? "Collapse" : "Expand"}
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={`transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
      >
        <path
          d="M6 12L10 8L6 4"
          stroke="#1B6EBB"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );

  return (
    <div key={`${item.id}-main`} >
      <div 
        key={item.id}
        className={itemClassName}
        data-checked={isSelected ? "True" : "False"}
        data-state="Default"
        title={item.label}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex-1">
            {isMultipleSelectionAllowed ? (
              <Checkbox
                label={item.label}
                checked={isIndeterminate ? false : isSelected}
                indeterminate={isIndeterminate}
                onChange={handleChange}
                disabled={disabled}
                className={labelClassName}
                data-testid={testId}
              >
                <span className="ellipsis-label" title={item.label}>
                  {item.label}
                </span>
              </Checkbox>
            ) : (
              <Radio
                label={item.label}
                checked={isSelected}
                onChange={handleChange}
                disabled={disabled}
                className={labelClassName}
                data-testid={testId}
              >
                <span className="ellipsis-label" title={item.label}>
                  {item.label}
                </span>
              </Radio>
            )}
          </div>
          {showChevron && hasExpandableContent && <ChevronIcon />}
        </div>
      </div>
      {children}
    </div>
  );
};

export default SelectableListItem;
