import { format, utcToZonedTime } from 'date-fns-tz';
import { uploadFilePharmaQuery } from '../../server/Query/uploadFilePharmaQuery';
import { uploadFilePharmaReq } from '../../interfaces/uploadFilePharma';
import { FileItem, UploadValidationResult, UploadRequest } from './uploadDocument.types';

const MAX_FILE_SIZE_MB = 25;

export const validateFiles = (files: File[] | FileList): UploadValidationResult => {
  const fileArr: FileItem[] = [];
  let oversized = false;
  const oversizedFiles: string[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB >= MAX_FILE_SIZE_MB) {
      oversized = true;
      oversizedFiles.push(file.name);
      continue;
    }
    fileArr.push({ file, status: 'inProgress', isLoading: true });
  }

  return { fileArr, oversized, oversizedFiles };
};

export const readFileAsArrayBuffer = (file: File): Promise<number[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const arrayBuffer = e.target?.result as ArrayBuffer;
      const byteArray = new Uint8Array(arrayBuffer);
      const fileContent = Array.from(byteArray);
      resolve(fileContent);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

export const generateFileName = (originalName: string): string => {
  const timeZone = 'America/Los_Angeles';
  const currentDatePT = format(utcToZonedTime(new Date(), timeZone), 'yyyy-MM-dd', { timeZone });
  const timestamp = Date.now();
  return `${originalName.replace(/\.xlsx$/i, '')}_cx_${currentDatePT}_${timestamp}.xlsx`;
};

export const createUploadRequest = (
  file: File, 
  fileContent: number[], 
  userName: string, 
  fiscalWeekNbr: number
): UploadRequest => {
  return {
    query: uploadFilePharmaQuery,
    variables: {
      fileUploadToBlob: {
        fileName: generateFileName(file.name),
        fileContent,
        uploadDataPersistenceEnabled: true,
        user: userName.replace(/\s*\(Contractor\)\s*$/, '').trim(),
        fiscalWeekNbr
      } as uploadFilePharmaReq,
    }
  };
}; 