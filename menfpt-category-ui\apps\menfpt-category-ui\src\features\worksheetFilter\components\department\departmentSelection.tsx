import React, { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { useDisplayDeptRoleCascade } from '../../worksheetFilterRouteUtils';
import { departmentHeaderStyles, departmentItemStyles } from './departmentSelection.styles';
import { getSelectedDepartments, handleMultipleDepartmentSelection } from './departmentSelection.utils';
import { safeOnSmDataChange, handleCascadeSmDataChange, handleNormalSmDataChange, handleDepartmentChange } from './departmentSelection.handlers';
import { useCascadeModeEffect, useNormalModeEffect } from './departmentSelection.effects';
import { RenderDepartmentHeader, RenderDepartmentSearch, RenderDepartmentList } from './departmentSelection.render';
import SmRoleUsersList from '../roles/smRoleUsersList';
import { SmDataType } from '../../types/smTypes';
interface DepartmentSelectionProps {
  departments?: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  isMultipleSelectionAllowed: boolean;
  onDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  onSmDataChange?: (smData: SmDataType) => void;
  showSearch?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  showHeader?: boolean;
}

export const DepartmentSelection: React.FC<DepartmentSelectionProps> = ({
  departments: propDepartments,
  selectedDepartment,
  isMultipleSelectionAllowed,
  onDepartmentChange,
  onSmDataChange,
  showSearch = false,
  searchQuery = '',
  onSearchChange,
  showHeader = true
}) => {
  const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();
  const dispatch = useDispatch();
  const deptRoleSuggestionsState = useSelectorWrap('deptRoleSuggestions_rn');
  const departmentsState = useSelectorWrap('departments_rn');
  const allDepartments = departmentsState.data || [];
  let departmentsToDisplay = propDepartments || allDepartments;
  
  if (
    isDisplayDeptRoleCascade &&
    deptRoleSuggestionsState.data?.cascadeSearchSelectedItemType === 'department' &&
    deptRoleSuggestionsState.data?.cascadeSearchSelectedItemId
  ) {
    const selectedId = deptRoleSuggestionsState.data.cascadeSearchSelectedItemId;
    departmentsToDisplay = allDepartments.filter(
      (dept: DropdownType) => String(dept.num) === selectedId
    );
  }
  const lastSmDataRef = useRef<SmDataType | null>(null);
  const safeSmDataChange = (newData: SmDataType) => safeOnSmDataChange(lastSmDataRef, onSmDataChange, newData);
  useCascadeModeEffect(
    isDisplayDeptRoleCascade,
    () => handleCascadeSmDataChange(deptRoleSuggestionsState, allDepartments, safeSmDataChange),
    [deptRoleSuggestionsState.data?.cascadeSearchSelectedItemId, deptRoleSuggestionsState.data?.cascadeSearchSelectedItemType, allDepartments, onSmDataChange]
  );
  useNormalModeEffect(
    isDisplayDeptRoleCascade,
    () => handleNormalSmDataChange(selectedDepartment, allDepartments, safeSmDataChange),
    [selectedDepartment, allDepartments, onSmDataChange]
  );
  const onDeptChange = (department: DropdownType | DropdownType[]) => {
    if (isMultipleSelectionAllowed && !Array.isArray(department)) {
      const selectedDepts = getSelectedDepartments(selectedDepartment);
      const updatedDepts = handleMultipleDepartmentSelection(department, selectedDepts);
      handleDepartmentChange(
        updatedDepts,
        selectedDepartment,
        isMultipleSelectionAllowed,
        allDepartments,
        onDepartmentChange,
        onSmDataChange,
        dispatch
      );
    } else {
      handleDepartmentChange(
        department,
        selectedDepartment,
        isMultipleSelectionAllowed,
        allDepartments,
        onDepartmentChange,
        onSmDataChange,
        dispatch
      );
    }
  };
  return (
    <>
      <RenderDepartmentHeader showHeader={showHeader} departmentHeaderStyles={departmentHeaderStyles} />
      <RenderDepartmentList
        departmentsToDisplay={departmentsToDisplay}
        selectedDepartment={selectedDepartment}
        isMultipleSelectionAllowed={isMultipleSelectionAllowed}
        onDepartmentChange={onDeptChange}
        departmentItemStyles={departmentItemStyles}
      />
    </>
  );
};