import { routeConstants } from "../../util/routeConstants";

export const worksheetFilterConfig = {
  isDisplayTimeFrame: [routeConstants.dashboard], //Routes where time frame needs to be displayed. 
  isDisplayDeptRoleCascade:[""], //Routes where Roles needs to be displayed based on the dept selected 
  isAllowMultipleDivisonsSelection: [routeConstants.dashboard], //Routes where multiple division selection is allowed.: 
  isAllowMultipleDeptsSelection: [routeConstants.dashboard],
  deptRoleCascadeSearchMinChars: 5,
  isDisplayDesk: [""], //Routes where desk needs to be displayed
  lsKeyAdjustmentPg: "filterPreferences_adjustment",
  lsKeyDashboardPg: "filterPreferences_dashboard"
};
