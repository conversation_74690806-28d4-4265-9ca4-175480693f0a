// Kill switch configuration supporting per-feature, per-location rules
// Each feature can be a boolean (global) or an object with location-based rules

interface FeatureConfig {
  [feature: string]:
    | boolean
    | {
        default: boolean;
        locations?: {
          // Key: substring or regex pattern to match window.location.href
          [locationPattern: string]: boolean;
        };
      };
}

// Example config:
export const killSwitchConfig: FeatureConfig = {
  featureA: true, // globally enabled
  featureB: false, // globally disabled
  "periodClose": {
    default: false,
    locations: {
      'dev': true, // enabled for URLs containing this substring
      'localhost': true, // enabled for localhost
    },
  },
};

/**
 * Checks if a feature is enabled based on the kill switch config and current window location.
 * @param feature The feature name to check
 * @returns true if enabled, false otherwise
 */
export function isFeatureEnabled(feature: string): boolean {
  const config = killSwitchConfig[feature];
  if (typeof config === 'boolean') {
    return config;
  }
  if (typeof window === 'undefined' || !window.location) {
    // If not in a browser environment, fallback to default
    return config?.default ?? false;
  }
  const href = window.location.href;
  if (config?.locations) {
    for (const pattern in config.locations) {
      if (href.includes(pattern)) {
        return config.locations[pattern];
      }
    }
  }
  return config?.default ?? false;
}
