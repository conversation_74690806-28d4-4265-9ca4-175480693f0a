import React from "react";
import Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';
import HelpIcon from '../HelpIcon';
import AccessControlCard  from '../../components/Admin/access-control-card'
import Facilities from '../Facilities/ClosingFacilities';
import './access-control-table.css';
enum TabsLabels {
  ACCESS_CONTROL = 'Access control',
  FACILITIES= 'Facilities',
}
const ADMIN_TITLE = 'Admin';
const AdminTabs = () => {
 const classes = 'flex justify-center items-center h-58';
  return (
    <div className="h-full">
      <div className="flex items-center justify-between px-2 py-2 overflow-x-auto height-48 bg-[#F1F4F9]">
         <div className="flex-1">
            <div className="flex left">
                <h2 className="text-2xl font-bold text-gray-800 ml-[20px]">
                    {ADMIN_TITLE}
                </h2>
                    <HelpIcon variant="default" />
            </div>
        </div>
      </div>
      <div className="admin-tab pt-2 full-height-48 bg-[#F1F4F9]">
        <Tabs
        className="uds-molecule-tab h-full"
        variant="light"
        >
          <Tab  className="">
            <Tab.Header>
              <div className="admin-tab-label">
                {TabsLabels.ACCESS_CONTROL}
              </div>
            </Tab.Header>
               <Tab.Content>
                <AccessControlCard/>
               </Tab.Content> 
          </Tab>
          <Tab>
            <Tab.Header>
              <div className="rxforecast-tab-label font-bold">
                {TabsLabels.FACILITIES}
              </div>
            </Tab.Header>
            <Tab.Content>
              <Facilities />
            </Tab.Content>
          </Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminTabs;

