import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Home from './home';
import { useCombinedFiltersAndQuarters } from '../features/worksheetFilter/useCombinedFiltersAndQuarters';

// Mock child components
jest.mock('./dashboard-title-component', () => () => <div data-testid="dashboard-title-component" />);
jest.mock('./dashboard-tabs', () => () => <div data-testid="dashboard-tabs" />);
jest.mock('./worksheet-table-container', () => () => <div data-testid="worksheet-table-container" />);
jest.mock('../features/worksheetFilter/useCombinedFiltersAndQuarters', () => ({
  useCombinedFiltersAndQuarters: jest.fn()
}));
jest.mock('../features/worksheetFilter/worksheetFilterContainer', () => ({
  WorksheetFilterContainer: ({ FiltersList, ...props }: any) => (
    <div 
      data-testid="worksheet-filter-container" 
      filterslist={JSON.stringify(FiltersList)}
      {...Object.keys(props).reduce((acc, key) => {
        if (key !== 'isFilterModalOpen' && key !== 'setIsFilterModalOpen' && key !== 'openFilterModal') {
          acc[key] = props[key];
        }
        return acc;
      }, {} as any)}
    />
  )
}));
jest.mock('../components/HelpIcon', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="help-icon" {...props} />
}));

describe('Home', () => {
  const mockUseCombinedFiltersAndQuarters = useCombinedFiltersAndQuarters as jest.MockedFunction<typeof useCombinedFiltersAndQuarters>;

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Set default mock implementation
    mockUseCombinedFiltersAndQuarters.mockReturnValue({
      worksheetFiltersList: [{ id: 1, name: 'Test Filter' }],
      filterLoading: false,
      filterData: [{ id: 1, name: 'Test Filter' }],
      quartersData: [],
      shouldDisplayTimeFrame: false,
      isError: false
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders all main dashboard components', () => {
    render(<Home />);
    expect(screen.getByTestId('dashboard-title-component')).toBeInTheDocument();
    expect(screen.getByTestId('worksheet-filter-container')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-tabs')).toBeInTheDocument();
    expect(screen.getByTestId('help-icon')).toBeInTheDocument();
  });

  it('shows loading state when filterLoading is true', () => {
    mockUseCombinedFiltersAndQuarters.mockReturnValue({
      worksheetFiltersList: [],
      filterLoading: true,
      filterData: [],
      quartersData: [],
      shouldDisplayTimeFrame: false,
      isError: false
    });
    
    render(<Home />);
    expect(screen.getByTestId('dashboard-title-component')).toBeInTheDocument();
    expect(screen.getByTestId('worksheet-filter-container')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-tabs')).toBeInTheDocument();
    expect(screen.getByTestId('help-icon')).toBeInTheDocument();
  });

  it('passes correct props to WorksheetFilterContainer', () => {
    const mockFilterData = [{ id: 1, name: 'Filter 1' }, { id: 2, name: 'Filter 2' }];
    mockUseCombinedFiltersAndQuarters.mockReturnValue({
      worksheetFiltersList: mockFilterData,
      filterLoading: false,
      filterData: mockFilterData,
      quartersData: [],
      shouldDisplayTimeFrame: false,
      isError: false
    });

    render(<Home />);
    const filterContainer = screen.getByTestId('worksheet-filter-container');
    expect(filterContainer).toHaveAttribute('filterslist', JSON.stringify(mockFilterData));
  });

});
