import HistoryTimeline from './historyTimeline';
import { useGetPublishHistoryMutation } from '../server/Api/menfptCategoryAPI';
import { menfptGetForecastChangeLogQuery } from '../server/Query/menfptForecastChangeLog';
import { ForecastChangeLog, ForecastChangeLogReq } from '../interfaces/forecast-adjustments';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import Drawer from '@albertsons/uds/molecule/Drawer';
import { MenfptWorkSheetFilterListQuery } from "../server/Query/menfptWorkSheetFiltersQuery";
import { useEffect, useState } from 'react';
import Spinner from '@albertsons/uds/molecule/Spinner';
import { useExtractBannerId } from '../util/filterUtils';

interface DrawerProps {
    isOpen: boolean;
    onClose?: () => void;
    setOpen: (isOpen: boolean) => void;
    position?: string;
}

const HistoryDrawer: React.FunctionComponent<DrawerProps> = ({ isOpen, setOpen, position }) => {
    const [data, setData] = useState<ForecastChangeLog[]>([]);
    const [getPublishHistoryListMutation] = useGetPublishHistoryMutation();
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
    const bannerId = useExtractBannerId();

    useEffect(() => {
        if (isOpen) {
            fetchPublishHistory();
        }
    }, [isOpen]);

    const fetchPublishHistory = async () => {
        setLoading(true);
        setError(null);
        const keyAttributeName = appliedFilters.department ? "Department" : "DESK";
        const keyAttributeValue = appliedFilters.department
            ? [String(appliedFilters?.department?.num)]
            : [String(appliedFilters?.desk?.num)];
        const filterArgs = {
            query: menfptGetForecastChangeLogQuery,
            variables: {
                forecastChangeLogReqest: {
                    keyAttributeName: keyAttributeName,
                    keyAttributeValue: keyAttributeValue,
                    divisionId: appliedFilters?.division?.map((div: any) => String(div.num)) || [],
                    ...(bannerId && { bannerId }),
                } as ForecastChangeLogReq,
            }
        };

        try {
            const response: any = await getPublishHistoryListMutation(filterArgs);
            if (response?.errors) {
                console.error('GraphQL Errors:', response.errors);
                setError('Failed to fetch data. Please try again.');
                return;
            }
            const historyList = response?.data?.getForecastChangeLog || [];
            setData(historyList);
        } catch (error) {
            console.error('Error while fetching publish history:', error);
        }
        finally {
            setLoading(false);
        }

    };

    return (
        <Drawer
            noPadding
            anchor={position === "right" ? "right" : "left"}
            isOpen={isOpen}
            width={"608px"}
            setOpen={setOpen}
            hideBackdrop={false}
            header={<div>Audit History</div>}
        >
            {loading ? (
                <Spinner />
            ) : error ? (
                <div className="error-message">{error}</div>
            ) : (
                <div className="history-main-container">
                    <HistoryTimeline data={data} />
                </div>
            )}
        </Drawer>
    );
};

export default HistoryDrawer;

