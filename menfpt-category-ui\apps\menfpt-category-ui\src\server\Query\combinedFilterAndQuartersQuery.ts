export const CombinedFilterAndQuartersQuery = `
query CombinedFilterAndQuarters($workSheetFilterRequest: WorkSheetFilterReq, $timeRangeInYearReq: TimeRangeInYearReq!) {
  getWorkSheetFilter(workSheetFilterRequest: $workSheetFilterRequest) {
    smicData {
      divisionId
      divisionName
      bannerId
      bannerName
      deptId
      deptName
      smicGroupCd
      smicGroupDesc
      smicCategoryCd
      smicCategoryDesc
      smicCategoryId
      retailSectionCd
      retailSectionName
      deskId
      deskName
    }
    userId
    userName
    userEmail
    userRole
    createdTimeStamp
    updatedTimeStamp
    createdBy
    updatedBy
  }
  getAllTimeRangeInYear(timeRangeInYearReq: $timeRangeInYearReq) {
    fiscalYearNumber
    fiscalQuarterNumber
    fiscalQuarterStartDate
    fiscalQuarterEndDate
    fiscalPeriodNumber
    fiscalPeriodStartDate
    fiscalPeriodEndDate
    fiscalWeekNumber
    fiscalWeekStartDate
    fiscalWeekEndDate
  }
}`;
