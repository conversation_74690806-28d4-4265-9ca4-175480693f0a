import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { RoleSearchSuggestions } from './RoleSearchSuggestions';

// Mock worksheetFilterConfig
jest.mock('../../worksheetFilterConfig', () => ({
  worksheetFilterConfig: { deptRoleCascadeSearchMinChars: 5 },
}));

describe('RoleSearchSuggestions', () => {
  const baseProps = {
    isSearchInitialFocus: false,
    value: '',
    onChange: jest.fn(),
    suggestions: [],
    onSuggestionSelect: jest.fn(),
    className: '',
  };

  it('should render hint box when isSearchInitialFocus is true', () => {
    render(<RoleSearchSuggestions {...baseProps} isSearchInitialFocus={true} />);
    expect(screen.getByText(/Please enter at least 5 numbers \/ letters/i)).toBeInTheDocument();
  });

  it('should render no results when suggestions are empty', () => {
    render(<RoleSearchSuggestions {...baseProps} />);
    expect(screen.getByText(/No matching results/i)).toBeInTheDocument();
  });

  it('should render no results when all suggestion groups are empty', () => {
    render(
      <RoleSearchSuggestions
        {...baseProps}
        suggestions={[{ type: 'Group1', items: [] }]}
      />
    );
    expect(screen.getByText(/No matching results/i)).toBeInTheDocument();
  });

  it('should render suggestion groups and items', () => {
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
          { id: 2, label: 'Developer' },
        ],
      },
    ];
    render(<RoleSearchSuggestions {...baseProps} suggestions={suggestions} value="" />);
    expect(screen.getByText('Roles (2)')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
    // Badge count
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('should highlight matching keyword in suggestion label', () => {
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(<RoleSearchSuggestions {...baseProps} suggestions={suggestions} value="Man" />);
    // Highlighted part should have border and bg
    const highlighted = screen.getByText('Man');
    expect(highlighted).toHaveClass('border-sky-300');
    expect(highlighted).toHaveClass('bg-sky-100');
  });

  it('should call onSuggestionSelect when suggestion is clicked', () => {
    const onSuggestionSelect = jest.fn();
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(
      <RoleSearchSuggestions
        {...baseProps}
        suggestions={suggestions}
        onSuggestionSelect={onSuggestionSelect}
      />
    );
    fireEvent.mouseDown(screen.getByText('Manager'));
    expect(onSuggestionSelect).toHaveBeenCalledWith({ id: 1, label: 'Manager' });
  });

  it('should call onSuggestionSelect on touch start', () => {
    const onSuggestionSelect = jest.fn();
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(
      <RoleSearchSuggestions
        {...baseProps}
        suggestions={suggestions}
        onSuggestionSelect={onSuggestionSelect}
      />
    );
    fireEvent.touchStart(screen.getByText('Manager'));
    expect(onSuggestionSelect).toHaveBeenCalledWith({ id: 1, label: 'Manager' });
  });

  it('should call onSuggestionSelect on click', () => {
    const onSuggestionSelect = jest.fn();
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(
      <RoleSearchSuggestions
        {...baseProps}
        suggestions={suggestions}
        onSuggestionSelect={onSuggestionSelect}
      />
    );
    fireEvent.click(screen.getByText('Manager'));
    expect(onSuggestionSelect).toHaveBeenCalledWith({ id: 1, label: 'Manager' });
  });

  it('should render with custom className', () => {
    render(<RoleSearchSuggestions {...baseProps} className="custom-class" />);
    const container = document.querySelector('.role-search-suggestions-container');
    expect(container).toHaveClass('custom-class');
  });

  it('should render label as is if no keyword is provided', () => {
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(<RoleSearchSuggestions {...baseProps} suggestions={suggestions} value="" />);
    expect(screen.getByText('Manager')).toBeInTheDocument();
  });

  it('should handle edge case: suggestion label does not contain keyword', () => {
    const suggestions = [
      {
        type: 'Roles',
        items: [
          { id: 1, label: 'Manager' },
        ],
      },
    ];
    render(<RoleSearchSuggestions {...baseProps} suggestions={suggestions} value="XYZ" />);
    // Should not highlight anything
    expect(screen.getByText('Manager')).toBeInTheDocument();
  });
}); 