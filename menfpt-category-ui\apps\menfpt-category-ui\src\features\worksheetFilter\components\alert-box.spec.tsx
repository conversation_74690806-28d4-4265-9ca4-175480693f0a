import React from 'react';
import { render, screen } from '@testing-library/react';
import AlertBox from './alert-box';

describe('AlertBox', () => {
  it('renders the message', () => {
    render(<AlertBox message="Test Message" />);
    expect(screen.getByText('Test Message')).toBeInTheDocument();
  });

  it('renders the icon when provided', () => {
    render(<AlertBox message="Test Message" icon={<span>Icon</span>} />);
    expect(screen.getByText('Icon')).toBeInTheDocument();
  });

  it('applies default styles', () => {
    render(<AlertBox message="Test Message" />);
    const alertBox = screen.getByTestId('alert-box');
    const styles = window.getComputedStyle(alertBox);
    expect(styles.backgroundColor).toBe('rgb(250, 217, 127)');
    expect(styles.color).toBe('rgb(171, 66, 5)');
    const borderColor = styles.borderColor;
    expect([
      'rgb(171, 66, 5)',
      '#ab4205',
      'rgb(171, 66, 5, 1)'
    ]).toContain(borderColor.toLowerCase());
  });

  it('applies custom styles', () => {
    render(
      <AlertBox
        message="Test Message"
        backgroundColor="rgb(0, 0, 0)"
        textColor="rgb(255, 255, 255)"
        borderColor="rgb(255, 0, 0)"
      />
    );
    const alertBox = screen.getByTestId('alert-box');
    const styles = window.getComputedStyle(alertBox);
    expect(styles.backgroundColor).toBe('rgb(0, 0, 0)');
    expect(styles.color).toBe('rgb(255, 255, 255)');
    expect(styles.borderColor).toBe('rgb(255, 0, 0)');
  });
});