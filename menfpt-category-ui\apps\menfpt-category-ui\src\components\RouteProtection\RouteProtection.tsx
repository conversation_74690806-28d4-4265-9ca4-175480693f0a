import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { USER_ROLES } from '../../constants';

interface RouteProtectionProps {
  children: React.ReactNode;
  restrictedRoutes?: string[];
  pharmacyOnlyRoutes?: string[];
}

// Function to get page name from pathname
const getPageNameFromPath = (pathname: string): string => {
  const pathSegments = pathname.split('/').filter(segment => segment);
  const lastSegment = pathSegments[pathSegments.length - 1];
  
  // Map path segments to readable page names
  const pageNameMap: { [key: string]: string } = {
    'dashboard': 'Dashboard',
    'adjustment-worksheet': 'Adjustment Worksheet',
    'rx-forecast': 'Rx Forecast',
    'access-denied': 'Access Denied'
  };
  
  return pageNameMap[lastSegment] || lastSegment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const RouteProtection: React.FC<RouteProtectionProps> = ({ 
  children, 
  restrictedRoutes = ['/dashboard', '/adjustment-worksheet'],
  pharmacyOnlyRoutes = ['/rx-forecast']
}) => {
  const { data: userInfo } = useSelectorWrap('userInfo_rn');
  const location = window.location.pathname;

  // Check if current route is restricted and user has pharmacy role
  const isRestrictedRoute = restrictedRoutes.some(route => location.includes(route.replace('/', '')));
  const isPharmacyUser = userInfo?.userRole === USER_ROLES.PHARMACY;

  // Check if current route is pharmacy-only and user is NOT pharmacy
  const isPharmacyOnlyRoute = pharmacyOnlyRoutes.some(route => location.includes(route.replace('/', '')));
  const isNotPharmacyUser = !isPharmacyUser;

  // Get the page name for the access denied message
  const pageName = getPageNameFromPath(location);

  // Redirect pharmacy users from restricted routes
  if (isRestrictedRoute && isPharmacyUser) {
    return <Navigate to={`../access-denied?page=${encodeURIComponent(pageName)}`} replace />;
  }

  // Redirect non-pharmacy users from pharmacy-only routes
  if (isPharmacyOnlyRoute && isNotPharmacyUser) {
    return <Navigate to={`../access-denied?page=${encodeURIComponent(pageName)}`} replace />;
  }

  return <>{children}</>;
};

export default RouteProtection; 
