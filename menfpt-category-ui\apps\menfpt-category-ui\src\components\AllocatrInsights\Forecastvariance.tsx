interface DeepSubtractFunction {
  (objA: any, objB: any): any;
  __firstWeekId?: string;
  __skipSubtraction?: boolean;
  __isCurrentQuarter?: boolean;
}

const deepSubtract: DeepSubtractFunction = function (objA: any, objB: any): any {
  // Numbers are subtracted
  if (typeof objA === 'number' && typeof objB === 'number') {
    const result = deepSubtract.__skipSubtraction ? 0 : objA - objB;
    return parseFloat(result.toFixed(4)); // Ensure two decimal places
  }

  // Arrays — deep subtract each index
  if (Array.isArray(objA) && Array.isArray(objB)) {
    return objA.map((item, idx) => {
      const isWeek = item?.id?.startsWith('Week-');
      const isFirstWeek = isWeek && item?.id === deepSubtract.__firstWeekId;
      // Only skip subtraction for first week if isCurrentQuarter is true
      const prev = deepSubtract.__skipSubtraction;
      if (deepSubtract.__isCurrentQuarter) {
        deepSubtract.__skipSubtraction = isFirstWeek;
      } else {
        deepSubtract.__skipSubtraction = false;
      }
      const result = deepSubtract(item, objB[idx]);
      deepSubtract.__skipSubtraction = prev;
      return result;
    });
  }

  // Plain objects — iterate over all keys
  if (objA && typeof objA === 'object' && objB && typeof objB === 'object') {
    const result: any = {};
    const keys = new Set([...Object.keys(objA), ...Object.keys(objB)]);

    keys.forEach((key) => {
      if (key === 'periodNumber') {
        result[key] = objA[key];
      } else if (key in objA && key in objB) {
        result[key] = deepSubtract(objA[key], objB[key]);
      } else if (key in objA) {
        result[key] = objA[key];
      } else if (key in objB) {
        result[key] = Array.isArray(objB[key])
          ? []
          : typeof objB[key] === 'object'
          ? {}
          : null;
      }
    });

    return result;
  }

  // Fallback — return left-side value
  return objA;
};

export function subtractForecastVarianceData(
  withSnapshot: any[],
  withoutSnapshot: any[],
  isCurrentQuarter: boolean
): any[] {
  if (!Array.isArray(withSnapshot) || !Array.isArray(withoutSnapshot)) return [];

  return withSnapshot.map((item, idx) => {
    // Automatically get first week ID
    const firstWeekId = item?.weeks?.[0]?.id;
    deepSubtract.__firstWeekId = firstWeekId;
    deepSubtract.__skipSubtraction = false;
    deepSubtract.__isCurrentQuarter = isCurrentQuarter;

    return deepSubtract(item, withoutSnapshot[idx]);
  });
}