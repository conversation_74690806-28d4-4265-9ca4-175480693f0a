{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\pages\\\\dashboard-tabs.tsx\";\nimport React, { useState, useRef } from 'react';\n// import { createPortal } from 'react-dom';\nimport Report from './report';\nimport \"./dashboard-tabs.scss\";\nimport Drawer from '@albertsons/uds/molecule/Drawer';\nimport Button from '@albertsons/uds/molecule/Button';\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\n// Update the import path and casing to match the actual file location\nimport { SelectWeek } from './../components/SnapShotDropDown/release-week-select';\nimport { CircleAlert } from 'lucide-react';\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';\nimport Icon from '@albertsons/uds/molecule/Link';\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg';\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\nimport { getNowInPST } from '../util/dateUtils';\nimport { format } from 'date-fns-tz';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar TabsLabels = /*#__PURE__*/function (TabsLabels) {\n  TabsLabels[\"LEADING_INDICATORS\"] = \"Leading Indicators\";\n  TabsLabels[\"PERFORMANCE_SUMMARY\"] = \"Performance Summary\";\n  TabsLabels[\"FORECAST_VARIANCE\"] = \"Performance Variance\";\n  return TabsLabels;\n}(TabsLabels || {}); // const tabClassNames = {\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\n// };\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\n\n// Helper function to check if data is valid for download\nconst hasValidData = data => {\n  var _data$divisions;\n  if (!data) return false;\n  // Handle both old array format and new object format\n  if (Array.isArray(data)) {\n    return data.length > 0;\n  }\n  // For object format, check if it has divisions or id\n  return ((_data$divisions = data.divisions) == null ? void 0 : _data$divisions.length) > 0 || data.id;\n};\nconst DashboardTabs = () => {\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [selectedWeek, setSelectedWeek] = useState(null);\n  const [performanceSummaryData, setPerformanceSummaryData] = useState(null);\n  const [forecastVarianceData, setForecastVarianceData] = useState(null);\n  const [dashboardLoading, setDashboardLoading] = useState(true);\n  const {\n    data: worksheetFilters = {}\n  } = useSelectorWrap('workSheetFilterList_rn');\n  const [showMessage, setShowMessage] = useState(false);\n  const [tooltipPosition, setTooltipPosition] = useState(null);\n  const alertIconRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const smicData = worksheetFilters.smicData || [];\n\n  // Safely access displayDate with a fallback\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\n  const displayDate = (displayDateSelector == null ? void 0 : displayDateSelector.data) || {};\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n\n  // const handleTabClick = (tabName: Tabs) => {\n  //   setSelectedTab(tabName);\n  // };\n\n  const handleSyncMonitorClick = () => {\n    setIsDrawerOpen(true);\n  };\n  const handleWeekChange = item => {\n    setSelectedWeek(item);\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\n  };\n  const handlePerformanceSummaryData = data => {\n    setPerformanceSummaryData(data);\n    setDashboardLoading(false);\n  };\n  const handleForecastVarianceData = data => {\n    setForecastVarianceData(data);\n    setDashboardLoading(false);\n  };\n  const renderTabContent = tab => {\n    switch (tab) {\n      case TabsLabels.LEADING_INDICATORS:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Report, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.PERFORMANCE_SUMMARY:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.PERFORMANCE_SUMMARY,\n            onDataLoaded: handlePerformanceSummaryData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.FORECAST_VARIANCE:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.FORECAST_VARIANCE,\n            onDataLoaded: handleForecastVarianceData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const visibleTabs = [TabsLabels.LEADING_INDICATORS, TabsLabels.PERFORMANCE_SUMMARY, TabsLabels.FORECAST_VARIANCE];\n  const classes = 'flex justify-center items-center h-48 text';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-2 py-2 overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\",\n          style: {\n            margin: '5px 10px',\n            padding: '5px',\n            width: '600px',\n            borderColor: 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            initialTab: visibleTabs.indexOf(selectedTab),\n            variant: \"light\",\n            onChange: idx => setSelectedTab(visibleTabs[idx]),\n            className: \"w-full border-transparent dashboard-tab\",\n            children: visibleTabs.map((tab, idx) => /*#__PURE__*/_jsxDEV(Tab, {\n              className: classes,\n              children: /*#__PURE__*/_jsxDEV(Tab.Header, {\n                children: tab === TabsLabels.FORECAST_VARIANCE ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  tabIndex: 2,\n                  onBlur: () => setShowMessage(false),\n                  style: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    gap: '4px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tool-tip-initilizer-top\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      zIndex: 9999,\n                      anchor: \"top\",\n                      variant: \"dark\",\n                      className: 'uds-tooltip-top',\n                      label: ' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.',\n                      children: /*#__PURE__*/_jsxDEV(CircleAlert, {\n                        size: 16,\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        color: \" #1B6EBB\",\n                        onClick: e => {\n                          e.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 150,\n                        columnNumber: 19\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 25\n                  }, this), tab]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 23\n                }, this) : tab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          before: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-4 h-4 flex items-center text-[#1B6EBB]\",\n            children: [\" \", /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 75\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this),\n          className: `flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\n              ${dashboardLoading || !hasValidData(performanceSummaryData) && !hasValidData(forecastVarianceData) ? 'opacity-50 pointer-events-none' : ''}`,\n          onClick: dashboardLoading || selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length || selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length ? undefined : () => {\n            if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\n              handleDownloadExcel(performanceSummaryData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n            } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\n              handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\n            }\n          },\n          children: \"Download as Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-6\",\n          children: /*#__PURE__*/_jsxDEV(SelectWeek, {\n            weekChange: handleWeekChange,\n            selectedTab: selectedTab\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"sync-button\",\n            size: \"xs\",\n            variant: \"secondary\",\n            onClick: handleSyncMonitorClick,\n            children: \"EPBCS Sync Monitor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: renderTabContent(selectedTab)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      isOpen: isDrawerOpen,\n      setOpen: setIsDrawerOpen,\n      hideBackdrop: false,\n      width: \"608px\",\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"EPBCS Sync Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(EPBCSSyncMonitor, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\nexport default DashboardTabs;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Report", "Drawer", "<PERSON><PERSON>", "useSelectorWrap", "Tabs", "Tab", "EPBCSSyncMonitor", "AllocatrInsights", "SelectWeek", "Circle<PERSON>lert", "<PERSON><PERSON><PERSON>", "Icon", "ReactComponent", "Download", "handleDownloadExcel", "getNowInPST", "format", "jsxDEV", "_jsxDEV", "TabsLabels", "downloadedDate", "hasValidData", "data", "_data$divisions", "Array", "isArray", "length", "divisions", "id", "DashboardTabs", "selectedTab", "setSelectedTab", "PERFORMANCE_SUMMARY", "isDrawerOpen", "setIsDrawerOpen", "selectedWeek", "setSelectedWeek", "performanceSummaryData", "setPerformanceSummaryData", "forecastVarianceData", "setForecastVarianceData", "dashboardLoading", "setDashboardLoading", "worksheetFilters", "showMessage", "setShowMessage", "tooltipPosition", "setTooltipPosition", "alertIconRef", "tooltipRef", "smicData", "displayDateSelector", "displayDate", "appliedFilters", "handleSyncMonitorClick", "handleWeekChange", "item", "handlePerformanceSummaryData", "handleForecastVarianceData", "renderTabContent", "tab", "LEADING_INDICATORS", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataLoaded", "FORECAST_VARIANCE", "visibleTabs", "classes", "className", "style", "margin", "padding", "width", "borderColor", "initialTab", "indexOf", "variant", "onChange", "idx", "map", "Header", "tabIndex", "onBlur", "display", "alignItems", "gap", "position", "zIndex", "anchor", "label", "size", "cursor", "color", "onClick", "e", "stopPropagation", "before", "undefined", "weekChange", "isOpen", "<PERSON><PERSON><PERSON>", "hideBackdrop", "header"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\n// import { createPortal } from 'react-dom';\r\nimport Report from './report';\r\nimport LaggingIndicatorPage from './lagging-indicator-page';\r\nimport \"./dashboard-tabs.scss\";\r\nimport Drawer from '@albertsons/uds/molecule/Drawer';\r\nimport Button from '@albertsons/uds/molecule/Button';\r\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\r\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\r\nimport Tag from '@albertsons/uds/molecule/Tag';\r\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\r\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\r\n// Update the import path and casing to match the actual file location\r\nimport {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';\r\nimport { CircleAlert } from 'lucide-react';\r\nimport { useCurrentQuarterNbr } from '../features/calendarServiceUtils';\r\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';\r\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; \r\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\r\nimport { getNowInPST } from '../util/dateUtils';\r\nimport { format } from 'date-fns-tz';\r\n\r\nenum TabsLabels {\r\n  LEADING_INDICATORS = 'Leading Indicators',\r\n  PERFORMANCE_SUMMARY = 'Performance Summary',\r\n  FORECAST_VARIANCE = 'Performance Variance'\r\n}\r\n\r\n// const tabClassNames = {\r\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\r\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\r\n// };\r\n\r\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\r\n\r\n// Helper function to check if data is valid for download\r\nconst hasValidData = (data: any) => {\r\n  if (!data) return false;\r\n  // Handle both old array format and new object format\r\n  if (Array.isArray(data)) {\r\n    return data.length > 0;\r\n  }\r\n  // For object format, check if it has divisions or id\r\n  return data.divisions?.length > 0 || data.id;\r\n};\r\n\r\nconst DashboardTabs = () => {\r\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);\r\n  const [performanceSummaryData, setPerformanceSummaryData] = useState<any>(null);\r\n  const [forecastVarianceData, setForecastVarianceData] = useState<any>(null);\r\n  const [dashboardLoading, setDashboardLoading] = useState(true);\r\n  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');\r\n   const [showMessage, setShowMessage] = useState(false);\r\n  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);\r\n  const alertIconRef = useRef<HTMLDivElement>(null);\r\n  const tooltipRef = useRef<HTMLDivElement>(null);\r\n  const smicData = worksheetFilters.smicData || [];\r\n\r\n  // Safely access displayDate with a fallback\r\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\r\n  const displayDate = displayDateSelector?.data || {};\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n\r\n  // const handleTabClick = (tabName: Tabs) => {\r\n  //   setSelectedTab(tabName);\r\n  // };\r\n\r\n  const handleSyncMonitorClick = () => {\r\n    setIsDrawerOpen(true);\r\n  };\r\n\r\n  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {\r\n    setSelectedWeek(item);\r\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\r\n  };\r\n\r\n  const handlePerformanceSummaryData = (data: any) => {\r\n    setPerformanceSummaryData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n\r\n  const handleForecastVarianceData = (data: any) => {\r\n    setForecastVarianceData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n  const renderTabContent = (tab: TabsLabels) => {\r\n    switch (tab) {\r\n      case TabsLabels.LEADING_INDICATORS:\r\n        return <div><Report /></div>;\r\n      case TabsLabels.PERFORMANCE_SUMMARY:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>\r\n      case TabsLabels.FORECAST_VARIANCE:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const visibleTabs = [\r\n    TabsLabels.LEADING_INDICATORS,\r\n    TabsLabels.PERFORMANCE_SUMMARY,\r\n    TabsLabels.FORECAST_VARIANCE\r\n  ];\r\n  const classes = 'flex justify-center items-center h-48 text';\r\n\r\n  return (\r\n    <div>\r\n\r\n      <div className=\"flex items-center justify-between px-2 py-2 overflow-x-auto\">\r\n        <div className=\"tabs-container\">\r\n\r\n          <div\r\n            className=\"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\"\r\n            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}\r\n          >\r\n\r\n            <Tabs\r\n              initialTab={visibleTabs.indexOf(selectedTab)}\r\n              variant='light'\r\n              onChange={idx => setSelectedTab(visibleTabs[idx])}\r\n              className='w-full border-transparent dashboard-tab'\r\n            >\r\n              {visibleTabs.map((tab, idx) => (\r\n                <Tab className={classes} key={tab}>\r\n                  <Tab.Header>\r\n\r\n                    {tab === TabsLabels.FORECAST_VARIANCE ? (\r\n                      <span\r\n                        tabIndex={2}\r\n                        onBlur={() => setShowMessage(false)}\r\n                        style={{\r\n                          display: 'inline-flex',\r\n                          alignItems: 'center',\r\n                          gap: '4px',\r\n                          position: 'relative'\r\n                        }}>\r\n\r\n                        <div \r\n                          className=\"relative inline-block\"\r\n                        >\r\n                          <span className='tool-tip-initilizer-top'></span>\r\n                        <Tooltip\r\n                  zIndex={9999}\r\n                  anchor='top'\r\n                  variant='dark'\r\n                  className={'uds-tooltip-top'}\r\n                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>\r\n                  <CircleAlert\r\n                    size={16}\r\n                    style={{ cursor: 'pointer' }}\r\n                    color=\" #1B6EBB\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                        </div>\r\n                        \r\n                        {tab}\r\n                      </span>\r\n                    ) : (\r\n                      tab\r\n                    )}\r\n                  </Tab.Header>\r\n              </Tab>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </div>\r\n        <div className=\"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\">\r\n          <Icon\r\n            before={\r\n              <span className=\"w-4 h-4 flex items-center text-[#1B6EBB]\"> <Download/> </span>\r\n            }\r\n            className={`flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\r\n              ${(dashboardLoading || (!hasValidData(performanceSummaryData) && !hasValidData(forecastVarianceData))) ? 'opacity-50 pointer-events-none': '' }`}\r\n            onClick={\r\n              dashboardLoading ||\r\n              (selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length) ||\r\n              (selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length)\r\n                ? undefined\r\n                : () => {\r\n                    if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\r\n                      handleDownloadExcel(performanceSummaryData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\r\n                    } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\r\n                      handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\r\n                    }\r\n                  }\r\n            }\r\n          >Download as Excel\r\n          </Icon>\r\n        </div>\r\n      <div className='flex items-center gap-4'>\r\n           <div className='mr-6'>\r\n        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>\r\n      </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              className=\"sync-button\"\r\n              size=\"xs\"\r\n              variant=\"secondary\"\r\n              onClick={handleSyncMonitorClick}\r\n            >\r\n              EPBCS Sync Monitor\r\n            </Button>\r\n          </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        {renderTabContent(selectedTab)}\r\n      </div>\r\n\r\n      <Drawer\r\n        anchor=\"right\"\r\n        isOpen={isDrawerOpen}\r\n        setOpen={setIsDrawerOpen}\r\n        hideBackdrop={false}\r\n        width=\"608px\"\r\n        header={<div>EPBCS Sync Monitor</div>}\r\n      >\r\n        <EPBCSSyncMonitor />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,iCAAiC;AACpD,OAAOC,MAAM,MAAM,iCAAiC;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAOC,IAAI,IAAIC,GAAG,QAAQ,+BAA+B;AAEzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E;AACA,SAAUC,UAAU,QAAQ,sDAAsD;AAClF,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAOC,OAAO,MAAM,kCAAkC;AAAC,OAAOC,IAAI,MAAM,+BAA+B;AACvG,SAASC,cAAc,IAAIC,QAAQ,QAAQ,uCAAuC;AAClF,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,IAEhCC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA,EAAVA,UAAU,SAMf;AACA;AACA;AACA;AAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACD,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;;AAE1D;AACA,MAAMM,YAAY,GAAIC,IAAS,IAAK;EAAA,IAAAC,eAAA;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB;EACA,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACI,MAAM,GAAG,CAAC;EACxB;EACA;EACA,OAAO,EAAAH,eAAA,GAAAD,IAAI,CAACK,SAAS,qBAAdJ,eAAA,CAAgBG,MAAM,IAAG,CAAC,IAAIJ,IAAI,CAACM,EAAE;AAC9C,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAACqB,UAAU,CAACa,mBAAmB,CAAC;EAC9E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAyE,IAAI,CAAC;EAC9H,MAAM,CAACuC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxC,QAAQ,CAAM,IAAI,CAAC;EAC/E,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAM,IAAI,CAAC;EAC3E,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM;IAAEwB,IAAI,EAAEqB,gBAAgB,GAAG,CAAC;EAAE,CAAC,GAAGxC,eAAe,CAAC,wBAAwB,CAAC;EAChF,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAMkD,YAAY,GAAGjD,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMkD,UAAU,GAAGlD,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMmD,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,IAAI,EAAE;;EAEhD;EACA,MAAMC,mBAAmB,GAAGhD,eAAe,CAAC,gBAAgB,CAAC;EAC7D,MAAMiD,WAAW,GAAG,CAAAD,mBAAmB,oBAAnBA,mBAAmB,CAAE7B,IAAI,KAAI,CAAC,CAAC;EACnD,MAAM;IAAEA,IAAI,EAAE+B;EAAe,CAAC,GAAGlD,eAAe,CAAC,kBAAkB,CAAC;;EAEpE;EACA;EACA;;EAEA,MAAMmD,sBAAsB,GAAGA,CAAA,KAAM;IACnCpB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqB,gBAAgB,GAAIC,IAAqE,IAAK;IAClGpB,eAAe,CAACoB,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMC,4BAA4B,GAAInC,IAAS,IAAK;IAClDgB,yBAAyB,CAAChB,IAAI,CAAC;IAC/BoB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMgB,0BAA0B,GAAIpC,IAAS,IAAK;IAChDkB,uBAAuB,CAAClB,IAAI,CAAC;IAC7BoB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMiB,gBAAgB,GAAIC,GAAe,IAAK;IAC5C,QAAQA,GAAG;MACT,KAAKzC,UAAU,CAAC0C,kBAAkB;QAChC,oBAAO3C,OAAA;UAAA4C,QAAA,eAAK5C,OAAA,CAAClB,MAAM;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9B,KAAK/C,UAAU,CAACa,mBAAmB;QACjC,oBAAOd,OAAA;UAAA4C,QAAA,eAAK5C,OAAA,CAACX,gBAAgB;YAACuB,WAAW,EAAEX,UAAU,CAACa,mBAAoB;YAACmC,YAAY,EAAEV;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChI,KAAK/C,UAAU,CAACiD,iBAAiB;QAC/B,oBAAOlD,OAAA;UAAA4C,QAAA,eAAK5C,OAAA,CAACX,gBAAgB;YAACuB,WAAW,EAAEX,UAAU,CAACiD,iBAAkB;YAACD,YAAY,EAAET;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5H;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMG,WAAW,GAAG,CAClBlD,UAAU,CAAC0C,kBAAkB,EAC7B1C,UAAU,CAACa,mBAAmB,EAC9Bb,UAAU,CAACiD,iBAAiB,CAC7B;EACD,MAAME,OAAO,GAAG,4CAA4C;EAE5D,oBACEpD,OAAA;IAAA4C,QAAA,gBAEE5C,OAAA;MAAKqD,SAAS,EAAC,6DAA6D;MAAAT,QAAA,gBAC1E5C,OAAA;QAAKqD,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAE7B5C,OAAA;UACEqD,SAAS,EAAC,8IAA8I;UACxJC,KAAK,EAAE;YAAEC,MAAM,EAAE,UAAU;YAAEC,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAC,OAAO;YAAEC,WAAW,EAAE;UAAc,CAAE;UAAAd,QAAA,eAGzF5C,OAAA,CAACd,IAAI;YACHyE,UAAU,EAAER,WAAW,CAACS,OAAO,CAAChD,WAAW,CAAE;YAC7CiD,OAAO,EAAC,OAAO;YACfC,QAAQ,EAAEC,GAAG,IAAIlD,cAAc,CAACsC,WAAW,CAACY,GAAG,CAAC,CAAE;YAClDV,SAAS,EAAC,yCAAyC;YAAAT,QAAA,EAElDO,WAAW,CAACa,GAAG,CAAC,CAACtB,GAAG,EAAEqB,GAAG,kBACxB/D,OAAA,CAACb,GAAG;cAACkE,SAAS,EAAED,OAAQ;cAAAR,QAAA,eACtB5C,OAAA,CAACb,GAAG,CAAC8E,MAAM;gBAAArB,QAAA,EAERF,GAAG,KAAKzC,UAAU,CAACiD,iBAAiB,gBACnClD,OAAA;kBACEkE,QAAQ,EAAE,CAAE;kBACZC,MAAM,EAAEA,CAAA,KAAMxC,cAAc,CAAC,KAAK,CAAE;kBACpC2B,KAAK,EAAE;oBACLc,OAAO,EAAE,aAAa;oBACtBC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,KAAK;oBACVC,QAAQ,EAAE;kBACZ,CAAE;kBAAA3B,QAAA,gBAEF5C,OAAA;oBACEqD,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,gBAEjC5C,OAAA;sBAAMqD,SAAS,EAAC;oBAAyB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDhD,OAAA,CAACR,OAAO;sBACdgF,MAAM,EAAE,IAAK;sBACbC,MAAM,EAAC,KAAK;sBACZZ,OAAO,EAAC,MAAM;sBACdR,SAAS,EAAE,iBAAkB;sBAC7BqB,KAAK,EAAE,0HAA2H;sBAAA9B,QAAA,eAClI5C,OAAA,CAACT,WAAW;wBACVoF,IAAI,EAAE,EAAG;wBACTrB,KAAK,EAAE;0BAAEsB,MAAM,EAAE;wBAAU,CAAE;wBAC7BC,KAAK,EAAC,UAAU;wBAChBC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACrB;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,EAELN,GAAG;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GAEPN;cACD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC,GAxCeN,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyC9B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJhD,OAAA;QAAKqD,SAAS,EAAC,oEAAoE;QAAAT,QAAA,eACjF5C,OAAA,CAACP,IAAI;UACHwF,MAAM,eACJjF,OAAA;YAAMqD,SAAS,EAAC,0CAA0C;YAAAT,QAAA,GAAC,GAAC,eAAA5C,OAAA,CAACL,QAAQ;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC/E;UACDK,SAAS,EAAE;AACvB,gBAAiB9B,gBAAgB,IAAK,CAACpB,YAAY,CAACgB,sBAAsB,CAAC,IAAI,CAAChB,YAAY,CAACkB,oBAAoB,CAAE,GAAI,gCAAgC,GAAE,EAAE,EAAI;UACnJyD,OAAO,EACLvD,gBAAgB,IACfX,WAAW,KAAKX,UAAU,CAACa,mBAAmB,IAAI,CAACK,sBAAsB,CAACX,MAAO,IACjFI,WAAW,KAAKX,UAAU,CAACiD,iBAAiB,IAAI,CAAC7B,oBAAoB,CAACb,MAAO,GAC1E0E,SAAS,GACT,MAAM;YACJ,IAAItE,WAAW,KAAKX,UAAU,CAACa,mBAAmB,EAAE;cAClDlB,mBAAmB,CAACuB,sBAAsB,EAAEa,QAAQ,EAAEG,cAAc,EAAE,wDAAwDjC,cAAc,OAAO,CAAC;YACtJ,CAAC,MAAM,IAAIU,WAAW,KAAKX,UAAU,CAACiD,iBAAiB,EAAE;cACvDtD,mBAAmB,CAACyB,oBAAoB,EAAEW,QAAQ,EAAEG,cAAc,EAAE,qDAAqDjC,cAAc,OAAO,CAAC;YACjJ;UACF,CACL;UAAA0C,QAAA,EACF;QACD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRhD,OAAA;QAAKqD,SAAS,EAAC,yBAAyB;QAAAT,QAAA,gBACnC5C,OAAA;UAAKqD,SAAS,EAAC,MAAM;UAAAT,QAAA,eACxB5C,OAAA,CAACV,UAAU;YAAC6F,UAAU,EAAE9C,gBAAiB;YAACzB,WAAW,EAAEA;UAAY;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACFhD,OAAA;UAAKqD,SAAS,EAAC,yBAAyB;UAAAT,QAAA,eACtC5C,OAAA,CAAChB,MAAM;YACLqE,SAAS,EAAC,aAAa;YACvBsB,IAAI,EAAC,IAAI;YACTd,OAAO,EAAC,WAAW;YACnBiB,OAAO,EAAE1C,sBAAuB;YAAAQ,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhD,OAAA;MAAKqD,SAAS,EAAC,iBAAiB;MAAAT,QAAA,EAC7BH,gBAAgB,CAAC7B,WAAW;IAAC;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAENhD,OAAA,CAACjB,MAAM;MACL0F,MAAM,EAAC,OAAO;MACdW,MAAM,EAAErE,YAAa;MACrBsE,OAAO,EAAErE,eAAgB;MACzBsE,YAAY,EAAE,KAAM;MACpB7B,KAAK,EAAC,OAAO;MACb8B,MAAM,eAAEvF,OAAA;QAAA4C,QAAA,EAAK;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eAEtC5C,OAAA,CAACZ,gBAAgB;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAED,eAAerC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}