export const formatToPST = (timestamp: string): string => {
    try {
        const date = new Date(timestamp);
        return new Intl.DateTimeFormat('en-US', {
            timeZone: 'America/Los_Angeles',
            year: '2-digit',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
        }).format(date) + ' PST';
    } catch (error) {
        console.error('Error formatting timestamp to PST:', error);
        return '';
    }
};