import React from 'react';
import Search from '@albertsons/uds/molecule/Search';
import { usePersistentSearchQuery } from '../../hooks/usePersistentSearchQuery';
import '../../../searchIcon.scss';

interface DepartmentSearchProps {
  value: string;
  onChange: (query: string) => void;
}

const DepartmentSearch: React.FC<DepartmentSearchProps> = ({ value, onChange }) => {
  const [searchQuery, handleSearchQuery, handleBlur] = usePersistentSearchQuery(value);

  // Keep parent in sync if needed
  React.useEffect(() => {
    if (searchQuery !== value) {
      onChange(searchQuery);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  return (
    <div className="mb-2">
      <Search
        value={searchQuery}
        onChange={handleSearchQuery}
        className="searchIconLeft"
        onBlur={handleBlur}
      />
    </div>
  );
};

export default DepartmentSearch;