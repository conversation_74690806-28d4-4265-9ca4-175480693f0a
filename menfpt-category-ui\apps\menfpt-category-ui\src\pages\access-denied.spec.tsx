import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import AccessDenied from './access-denied';

// Mock useSearchParams
let mockSearchParams: URLSearchParams;
const mockSetSearchParams = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useSearchParams: () => [mockSearchParams, mockSetSearchParams],
}));

describe('AccessDenied', () => {
  beforeEach(() => {
    mockSearchParams = new URLSearchParams();
    jest.clearAllMocks();
  });

  describe('Default Behavior', () => {
    it('should render access denied message with default page name', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('We are sorry, but you don\'t have access to this page')).toBeInTheDocument();
      expect(screen.getByText('Back to home page')).toBeInTheDocument();
    });

    it('should have correct styling classes', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const container = screen.getByText('Access Denied').closest('div').parentElement?.parentElement?.parentElement;
      expect(container).toHaveClass('flex', 'flex-col', 'flex-shrink-0', 'justify-center', 'items-center', 'gap-5', 'min-h-screen');
    });

    it('should have proper semantic structure', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      // Check for main heading
      const heading = screen.getByText('Access Denied');
      expect(heading).toHaveClass('text-[#0d2d49]', 'text-center', 'font-nunito', 'text-[2rem]', 'font-bold', 'leading-10');
    });

    it('should have proper container layout', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const container = screen.getByText('Access Denied').closest('div').parentElement?.parentElement?.parentElement;
      expect(container).toHaveClass('flex', 'flex-col', 'flex-shrink-0', 'justify-center', 'items-center', 'gap-5', 'min-h-screen');
    });
  });

  describe('Dynamic Page Name Display', () => {
    it('should display specific page name when provided in URL', () => {
      mockSearchParams.set('page', 'Dashboard');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to Dashboard')).toBeInTheDocument();
    });

    it('should display Rx Forecast page name when provided in URL', () => {
      mockSearchParams.set('page', 'Rx Forecast');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to Rx Forecast')).toBeInTheDocument();
    });

    it('should display Adjustment Worksheet page name when provided in URL', () => {
      mockSearchParams.set('page', 'Adjustment Worksheet');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to Adjustment Worksheet')).toBeInTheDocument();
    });

    it('should handle URL encoded page names', () => {
      mockSearchParams.set('page', 'Custom%20Page%20Name');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to Custom%20Page%20Name')).toBeInTheDocument();
    });

    it('should handle special characters in page names', () => {
      mockSearchParams.set('page', 'Page with & special characters');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to Page with & special characters')).toBeInTheDocument();
    });

    it('should fallback to default message when page parameter is empty', () => {
      mockSearchParams.set('page', '');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to this page')).toBeInTheDocument();
    });

    it('should fallback to default message when page parameter is null', () => {
      // Don't set any page parameter
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('We are sorry, but you don\'t have access to this page')).toBeInTheDocument();
    });
  });

  describe('Content Structure', () => {
    it('should have proper content ordering', () => {
      mockSearchParams.set('page', 'Dashboard');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const container = screen.getByText('Access Denied').closest('div');
      const children = container?.children;
      
      if (children && children.length > 0) {
        const firstChild = children[0];
        const secondChild = children[1];
        const thirdChild = children[2];
        
        // First child should contain the main heading
        expect(firstChild).toHaveTextContent('Access Denied');
        
        // Second child should contain the error message
        expect(secondChild).toHaveTextContent('We are sorry, but you don\'t have access to Dashboard');
        
        // Third child should contain the link
        expect(thirdChild).toHaveTextContent('Back to home page');
      }
    });

    it('should have proper text styling for all elements', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const heading = screen.getByText('Access Denied');
      expect(heading).toHaveClass('text-[#0d2d49]', 'text-center', 'font-nunito', 'text-[2rem]', 'font-bold', 'leading-10');
      
      const errorMessage = screen.getByText(/We are sorry/);
      expect(errorMessage).toHaveClass('text-[#2b303c]', 'text-center', 'font-nunito', 'text-sm', 'leading-6');
    });
  });

  describe('Link Functionality', () => {
    it('should have correct href with memsp-ui-shell suffix', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const link = screen.getByText('Back to home page');
      expect(link).toHaveAttribute('href', `${window.location.origin}/memsp-ui-shell/`);
    });
  });

  describe('Accessibility', () => {
    it('should be accessible with proper text content', () => {
      mockSearchParams.set('page', 'Dashboard');
      
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('We are sorry, but you don\'t have access to Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Back to home page')).toBeInTheDocument();
    });

    it('should have proper contrast and readability', () => {
      render(
        <BrowserRouter>
          <AccessDenied />
        </BrowserRouter>
      );
      
      const heading = screen.getByText('Access Denied');
      expect(heading).toHaveClass('text-[#0d2d49]'); // Dark blue color for good contrast
      
      const message = screen.getByText(/We are sorry/);
      expect(message).toHaveClass('text-[#2b303c]'); // Dark gray color for good contrast
    });
  });
}); 