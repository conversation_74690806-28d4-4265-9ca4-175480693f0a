import React from 'react';
import ViewMoreFacilities from './ViewMoreFacilities';

interface DynamicCardBottomProps {
  facilityType: string;
  count: number;
  showViewMore: boolean;
  viewMoreCount: number;
  onViewMore: () => void;
}

const DynamicCardBottom: React.FC<DynamicCardBottomProps> = ({
  showViewMore,
  viewMoreCount,
  onViewMore,
}) => (
  <div
    className="flex-shrink-0 w-full h-8 rounded-bl-lg rounded-br-lg border-r border-b border-l border-[#c8daeb] bg-[#F7F8FA] flex justify-end items-center"
  >
    {showViewMore && (
      <ViewMoreFacilities count={viewMoreCount} onClick={onViewMore} />
    )}
  </div>
);

export default DynamicCardBottom;