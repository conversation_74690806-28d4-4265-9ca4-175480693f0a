import React from 'react';
import ReadOnlyList from '../readOnlyList';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

interface ModalReadOnlyListProps {
  items: DropdownType[];
  isDisplayDesk: boolean;
  title: string;
}

const ModalReadOnlyList: React.FC<ModalReadOnlyListProps> = ({
  items,
  isDisplayDesk,
  title
  
}) => (
  <div className={`flex flex-col overflow-hidden`}>
    <ReadOnlyList items={items} title={title} />
  </div>
);

export default ModalReadOnlyList;