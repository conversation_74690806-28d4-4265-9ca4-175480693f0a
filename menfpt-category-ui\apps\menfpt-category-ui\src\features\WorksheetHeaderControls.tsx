import React, { useState, useEffect, useRef } from 'react';
import Button from '@albertsons/uds/molecule/Button';
import ForecastEdit from '../components/ForecastEdit/editForecastAdjustment';
import HistoryDrawer from './historyDrawer';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { useDispatch } from 'react-redux';
import { setAlertState, setAppliedFilter, setEditAdjustmentPermission } from '../server/Reducer/menfpt-category.slice';
import './WorksheetHeaderControls.css';
import { WorksheetFilterContainer } from './worksheetFilter/worksheetFilterContainer';
import { extractDivisionsFromFiltersList } from './worksheetFilter/divisionUtils';
import { DropdownType } from '../interfaces/worksheetFilter';
import HelpIcon from '../components/HelpIcon';
import { worksheetFilterConfig } from './worksheetFilter/worksheetFilterConfig';
import { extractCurrentRoute } from './worksheetFilter/worksheetFilterRouteUtils';
import { getStorageKeyByRoute } from './worksheetFilter/worksheetFilterUtils';
import { QuarterTabs } from '../components/quarterTabs';
import { useShouldDisableEditForecastButton } from './periodClose/periodClose.flags';
import { deserializeSmData, serializeSmData } from './worksheetFilter/utils/serializationUtils';
import { SmDataType } from './worksheetFilter/types/smTypes';
interface WorksheetFilterProps {
  division: any;
  department: any;
  desk: any;
  category: any;
  selectedSm?: SmDataType;
}

type AlertState = {
  success: boolean;
  error: boolean;
};

const ROLE_ASM = 'ASM';

export const WorksheetHeaderControls: React.FunctionComponent<any> = ({ FiltersList, onQuarterChange }) => {
  const { data: displayDate } = useSelectorWrap('displayDate_rn') || '';
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: roleMappingInfo} = useSelectorWrap('roleMappingInfo_rn') || '';
  const { data: editAdjustmentPermission} = useSelectorWrap('editAdjustmentPermission_rn');
  const disabledEditButton = useShouldDisableEditForecastButton();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState<boolean>(false);
  const positionRef = useRef("right");
  const [selectedQuarterNbr, setSelectedQuarterNbr] = useState<number | null>(null);
  const dispatch = useDispatch();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const openFilterModal = () => setIsFilterModalOpen(true);

  useEffect(() => {
    // Only proceed if FiltersList is a valid, non-empty array with valid divisions
    const hasValidDivisions = Array.isArray(FiltersList) && extractDivisionsFromFiltersList(FiltersList).length > 0;
    if (!hasValidDivisions) {
      // Do not empty or update divisions in local storage if API response is invalid
      return;
    }
    const storedPreferences = localStorage.getItem(getStorageKeyByRoute());
    if (storedPreferences && Object.keys(JSON.parse(storedPreferences)).length >= 1) {
        try {
            const parsedState: WorksheetFilterProps = JSON.parse(storedPreferences);
            const validDivisions = extractDivisionsFromFiltersList(FiltersList).map(div => div.num);
            // Filter out invalid divisions from localStorage
            const filteredDivisions = parsedState.division.filter((div: DropdownType) =>
                validDivisions.includes(div.num)
            );
            // If there are invalid divisions, update localStorage
            if (filteredDivisions.length !== parsedState.division.length) {              
              const updatedPreferences = {
                  ...parsedState,
                  division: filteredDivisions,
              };
              localStorage.setItem(getStorageKeyByRoute(), JSON.stringify(updatedPreferences));
            }
            // If divisions are empty after filtering, show the filter modal
            if (filteredDivisions.length === 0) {
              setIsFilterModalOpen(true);
            }
            dispatch(setAppliedFilter({
              division: filteredDivisions,
              department: parsedState.department,
              desk: parsedState.desk,
              category: parsedState.category,
              selectedSm: serializeSmData(parsedState.selectedSm ?? new Map())
            }));
        } catch (error) {
            console.error("Failed to parse localStorage data:", error);
        }
    } else {
        setIsFilterModalOpen(true);
    }
  }, [dispatch, FiltersList]);

  const shouldDisableEditButton = () => {
    const selectedSm = deserializeSmData(appliedFilters?.selectedSm);
    if (selectedSm.size > 0) {
      // chek for all selectedSm if any of them has size > 0
      for (const sm of selectedSm.keys()) {
        if ((selectedSm.get(sm)?.size ?? 0) > 0) {
          return true;
        }
      }
    }
    if (roleMappingInfo?.userRole === ROLE_ASM) {
      return true;
    }
    if (appliedFilters?.desk ) {
      return true;
    }
    return false;
  }

  useEffect(() => {
    const disableButton = shouldDisableEditButton();
    dispatch(setEditAdjustmentPermission({disabled: disableButton }));
  }, [appliedFilters, roleMappingInfo]);

  const setAlert = (alert: AlertState) => {
    dispatch(setAlertState(alert));
  }

  const historyModalOpen = (_isOpen: boolean, position: string) => {
    setIsHistoryModalOpen(true);
    positionRef.current = position;
  }

  const handleQuarterChange = (quarterNbr: number) => {
    setSelectedQuarterNbr(quarterNbr);
    if (onQuarterChange) {
      onQuarterChange(quarterNbr);
    }
  };

  return (
    <div className='px-7 sticky top-0 bg-gray-206 z-[2] pb-3'>
      <div className="flex justify-between items-center space-x-4">
        {displayDate &&
          displayDate?.fiscalYearNumber &&
          displayDate?.fiscalQuarterNumber && (
            <div className="flex items-center">
              <div className="AdjustmentHeader width=[358px]">
                 {`Adjustment worksheet ${
                  selectedQuarterNbr
                    ? String(selectedQuarterNbr).slice(0, 4)
                    : displayDate?.fiscalYearNumber
                } Q${
                  selectedQuarterNbr
                    ? parseInt(String(selectedQuarterNbr).slice(-2), 10)
                    : displayDate?.fiscalQuarterNumber % 100
                }`}
              </div>
              <div>
                <HelpIcon />
              </div>
            </div>
          )}
       <WorksheetFilterContainer 
         FiltersList={FiltersList}
         isFilterModalOpen={isFilterModalOpen}
         setIsFilterModalOpen={setIsFilterModalOpen}
         openFilterModal={openFilterModal}
       />
      </div>
      <div className="flex items-center text-[14px] self-start w-full">
        <QuarterTabs onQuarterChange={handleQuarterChange} />
        <div className="flex items-center space-x-4">
        {displayDate &&
          displayDate?.fiscalYearNumber &&
          displayDate?.fiscalQuarterNumber && (
            <div className='dateHeader'>
            <span>Today:</span>
            <span className="ml-2">Week {Math.floor(displayDate?.fiscalWeekNumber % 100)}</span>
            <span className="ml-2">Q{Math.floor(displayDate?.fiscalQuarterNumber % 100)}</span>
            <span> FY{displayDate?.fiscalYearNumber} </span>
            <span className="italic ml-2">
                (End in{' '}
                <span>
                  {new Date(displayDate.fiscalWeekEndDate)
                    .toLocaleDateString('en-US', {
                      day: '2-digit',
                      month: '2-digit',
                    })
                    .replace(/ /g, '/')}
                </span>
                )
              </span>
            </div>
          )}
          </div>
        <div className="flex items-center space-x-4 ml-auto">
          <Button
            className="text-[14px] font-bold bg-gray-206 border-0"
            width={110}
            size="sm"
            fixed={true}
            onClick={() => {
              setIsHistoryModalOpen(true);
              positionRef.current = 'right';
            }}
            variant="tertiary"
          >
            Audit History
          </Button>
          <Button
            className="text-[14px] font-bold"
            width={104}
            size="sm"
            onClick={() => { setIsOpen(true); setAlert({success: false, error: false});}}
            variant="secondary"
            disabled={editAdjustmentPermission?.disabled || disabledEditButton}
          >
            Edit forecast
          </Button>
        </div>
      </div>
      <ForecastEdit
        isOpen={isOpen}
        setOpen={setIsOpen}
        historyModalOpen={historyModalOpen}
      />
      <HistoryDrawer
        isOpen={isHistoryModalOpen}
        setOpen={setIsHistoryModalOpen}
        position={positionRef.current}
      />
    </div>
  );
};


