import React, { useState, useEffect } from "react";
import { ListFilter, RotateCw, Download, Plus } from "lucide-react";
import Search from "@albertsons/uds/molecule/Search";
import '../Admin/admin-tabs.css';
import Alert from '@albertsons/uds/molecule/Alert';

interface AccessControlCardHeaderProps {
  title: string;
  count: number;
  location: string;
  onSearch: (term: string) => void;
  onFilter: () => void;
  onReset: () => void;
  onDownload: () => void;
  onAddUser: () => void;
  showSuccessAlert?: boolean;
}

const AccessControlCardHeader: React.FC<AccessControlCardHeaderProps> = ({
  title,
  count,
  location,
  onSearch,
  onFilter,
  onReset,
  onDownload,
  onAddUser,
  showSuccessAlert = false,
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  // Debounce search input before triggering parent callback
  useEffect(() => {
    const timeout = setTimeout(() => {
      onSearch(searchTerm);
    }, 300);
    return () => clearTimeout(timeout);
  }, [searchTerm, onSearch]);

  return (
    <div className="flex items-center justify-between p-4 w-full">
      <h2 className="text-lg font-semibold text-dark-text">
        {title} - {count}{" "}
        <span className="font-normal text-gray-700">({location})</span>
      </h2>
      <div className="flex items-center gap-3">
        <div className="w-64">
          <Search
            placeholder="Search the list..."
            value={searchTerm}
            onChange={(val: string) => setSearchTerm(val)}
          />
        </div>
        <div className="h-5 w-px bg-gray-300" />
        <button
          onClick={onFilter}
          className="flex items-center text-[#1B6EBB] text-sm"
        >
          <ListFilter size={16} className="mr-1" /> Filters
        </button>
        <button
          onClick={onReset}
          className="flex items-center text-gray-400 text-sm"
        >
          <RotateCw size={16} className="mr-1" /> Reset
        </button>
        <div className="h-5 w-px bg-gray-300" />
        <button
          onClick={onDownload}
          className="flex items-center text-[#1B6EBB] text-sm"
        >
          Download List <Download size={16} className="ml-1" />
        </button>
        <div className="h-5 w-px bg-gray-300" />
        <button
          onClick={onAddUser}
          className="flex items-center text-[#1B6EBB] text-sm"
        >
          <Plus size={16} className="mr-1" /> Add User
        </button>
      </div>
      {showSuccessAlert && (
        <Alert isOpen={true} sticky={true} autoClose={true} dismissible={true} variant='success'>
          <div>User Added Successfully</div>
        </Alert>
      )}
    </div>
  );
};

export default AccessControlCardHeader;
