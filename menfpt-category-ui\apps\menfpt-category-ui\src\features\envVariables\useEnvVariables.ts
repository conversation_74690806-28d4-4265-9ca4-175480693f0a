import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useGetEnvVariablesQuery } from '../../server/Api/menfptCategoryAPI';
import { setEnvVariables, setEnvVariablesError, clearEnvVariables } from './envVariables.slice';

export const useEnvVariables = () => {
  const dispatch = useDispatch();
  const { data, error, isLoading, refetch } = useGetEnvVariablesQuery();

  useEffect(() => {
    if (isLoading) {
      dispatch(clearEnvVariables());
    } else if (error) {
      dispatch(setEnvVariablesError());
    } else if (data) {
      dispatch(setEnvVariables(data));
    }
  }, [data, error, isLoading, dispatch]);

  return {
    data,
    error,
    isLoading,
    refetch,
  };
}; 