import React, { useState, useRef, useEffect } from 'react';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import InfoTooltip from '../../components/InfoTooltip';
import { Info, SquarePen, ChevronDown } from 'lucide-react';
import Select, { Option } from '@albertsons/uds/molecule/Select'; 
import mockData from './MockData.json';
import FacilitiesTableCard from './FacilitiesTableCard';
import DynamicCardBottom from './DynamicCardBottom';
import DatePicker from '@albertsons/uds/molecule/DatePicker';

type NewFacility = {
  id: number;
  facility: string;
  assetType: string;
  siteName: string;
  posStartDate: string;
  modelBased: string;
  financialRepo: string;
  editFacility: string;
  lastEditedBy: string;
};

const newItems: NewFacility[] = mockData.newFacilities;

const selectItems = [
  { name: 'First', num: 1 },
  { name: 'Second', num: 2 },
  { name: 'Third', num: 3 },
  { name: 'Fourth', num: 4 },
];

const NewFacilities = () => {
  const [datePickerOpenId, setDatePickerOpenId] = useState<number | null>(null);
  const [facilities, setFacilities] = useState<NewFacility[]>(newItems);
  const [datePickerPosition, setDatePickerPosition] = useState<{ left: number; top: number }>({ left: 0, top: 0 });
  const datePickerRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const cellRefs = useRef<{ [key: number]: HTMLSpanElement | null }>({});

  const modelBasedCellRefs = useRef<{ [key: number]: HTMLSpanElement | null }>({});

  const [selectOpenId, setSelectOpenId] = useState<number | null>(null);
  const [selectPosition, setSelectPosition] = useState<{ left: number; top: number; width?: number }>({ left: 0, top: 0 });
  const selectRef = useRef<HTMLDivElement | null>(null);
  const chevronRefs = useRef<{ [key: number]: HTMLSpanElement | null }>({});

  useEffect(() => {
    if (datePickerOpenId === null) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target as Node)
      ) {
        setDatePickerOpenId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [datePickerOpenId]);

  useEffect(() => {
    if (datePickerOpenId === null) return;
    const cell = cellRefs.current[datePickerOpenId];
    const container = containerRef.current;
    if (cell && container) {
      const cellRect = cell.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      setDatePickerPosition({
        left: cellRect.left - containerRect.left,
        top: cellRect.top - containerRect.top,
      });
    }
  }, [datePickerOpenId]);

  useEffect(() => {
    if (selectOpenId === null) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setSelectOpenId(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [selectOpenId]);

  useEffect(() => {
    if (selectOpenId === null) return;
    const chevron = chevronRefs.current[selectOpenId];
    const container = containerRef.current;
    if (chevron && container) {
      const chevronRect = chevron.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      setSelectPosition({
        left: chevronRect.left - containerRect.left,
        top: chevronRect.bottom - containerRect.top,
        width: chevronRect.width,
      });
    }
  }, [selectOpenId]);

  useEffect(() => {
    if (selectOpenId === null) return;
    const cell = modelBasedCellRefs.current[selectOpenId];
    const container = containerRef.current;
    if (cell && container) {
      const cellRect = cell.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      setSelectPosition({
        left: cellRect.left - containerRect.left,
        top: cellRect.bottom - containerRect.top,
        width: cellRect.width,
      });
    }
  }, [selectOpenId]);

  const handleSquarePenClick = (id: number) => {
    setDatePickerOpenId(id);
  };

  const handleDateChange = ([date]: Date[], id: number) => {
    const formatDate = (d: Date) => {
      const mm = String(d.getMonth() + 1).padStart(2, '0');
      const dd = String(d.getDate()).padStart(2, '0');
      const yyyy = d.getFullYear();
      return `${mm}/${dd}/${yyyy}`;
    };

    setFacilities((prev) =>
      prev.map((facility) =>
        facility.id === id
          ? { ...facility, financialRepo: date ? formatDate(date) : '' }
          : facility
      )
    );
    setDatePickerOpenId(null);
  };

  const newColumns: Column<NewFacility>[] = [
    {
      id: 'facility',
      label: (
        <div className="facility-header">
          <span>Facility</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Number and Name of facility'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'facility',
      hideFromMenu: true,
    },
    {
      id: 'assetType',
      label: (
        <div className="facility-header">
          <span>Asset Type</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Type of facility'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'assetType',
      hideFromMenu: true,
    },
    {
      id: 'siteName',
      label: (
        <div className="facility-header">
          <span>Site Name and A...</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Facility name and address'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: (item: NewFacility) => (
        <span className="flex items-center min-h-[48px] px-0 py-0 w-[140px]">
          <span className="truncate w-full" title={item.siteName}>
            {item.siteName}
          </span>
        </span>
      ),
      hideFromMenu: true,
    },
    {
      id: 'posStartDate',
      label: (
        <div className="facility-header">
          <span>POS Start Date</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Date Point Of Sale is operational'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'posStartDate',
      hideFromMenu: true,
    },
    {
      id: 'modelBased',
      label: (
        <div className="facility-header">
          <span>Model based...</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Facility that this facility is modeled after'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: (item: NewFacility) => (
        <span
          ref={el => (modelBasedCellRefs.current[item.id] = el)}
          className="flex items-center min-h-[48px] relative"
        >
          <span className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
            {item.modelBased || <span className="text-gray-400">Select</span>}
          </span>
          <span
            ref={el => (chevronRefs.current[item.id] = el)}
            className="ml-2 cursor-pointer flex items-center"
            onClick={e => {
              e.stopPropagation();
              setSelectOpenId(item.id);
            }}
          >
            <ChevronDown size={18} />
          </span>
        </span>
      ),
      hideFromMenu: true,
    },
    {
      id: 'financialRepo',
      label: (
        <div
          className="facility-header flex items-center h-full min-h-[48px] p-0 m-0"
        >
          <span className="flex items-center p-0 m-0">
            Financial repo...
          </span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Date at which a facility is included in financial reporting'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: (row: NewFacility) => (
        <span
          ref={el => (cellRefs.current[row.id] = el)}
          className="flex items-center h-full min-h-[48px] p-0 m-0"
        >
          {row.financialRepo}
        </span>
      ),
      hideFromMenu: true,
    },
    {
      id: 'editFacility',
      label: (
        <div className="facility-header">
          <span>Edit Facility</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'Make changes to this facility'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      hideFromMenu: true,
      align: 'center',
      value: (row: NewFacility) => (
        <span
          onClick={() => handleSquarePenClick(row.id)}
          className="cursor-pointer flex justify-center items-center h-full w-full"
        >
          <SquarePen className="text-[#1B6EBB]" />
        </span>
      ),
      sortable: false,
      width: '7%',
    },
    {
      id: 'lastEditedBy',
      label: (
        <div className="facility-header">
          <span>Last Edited by</span>
          <span className="ml-2 data-popper-bottom-margin">
            <InfoTooltip
              label={'User, date and time of last edit'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'lastEditedBy',
      hideFromMenu: true,
    },
  ];

  return (
    <div ref={containerRef} className="relative">
      {datePickerOpenId !== null && (
        <div
          ref={datePickerRef}
          className="absolute z-[9999] bg-white shadow-lg p-4 rounded-lg"
          style={{
            left: datePickerPosition.left,
            top: datePickerPosition.top + 50,
          }}
        >
          <DatePicker
            onChange={(dateArr) => handleDateChange(dateArr, datePickerOpenId)}
          />
        </div>
      )}

      {selectOpenId !== null && (
        <div
          ref={selectRef}
          className="absolute z-[10001] bg-white shadow-lg rounded-lg min-w-[180px] transition-[top] duration-100 p-2"
          style={{
            left: selectPosition.left,
            top: selectPosition.top,
            width: selectPosition.width || 180,
          }}
        >
          <Select
            search
            noTags
            itemText="name"
            placeholder="Select"
            className="w-full select-no-border"
            value={selectItems.find(opt => opt.name === facilities.find(f => f.id === selectOpenId)?.modelBased)}
            onChange={selected => {
              setFacilities(prev => {
                const updated = prev.map(fac =>
                  fac.id === selectOpenId
                    ? { ...fac, modelBased: selected?.name ?? '' }
                    : fac
                );
                return updated;
              });
              setSelectOpenId(null);
            }}
          >
            {selectItems.map((option, idx) => (
              <Option key={idx} item={option} />
            ))}
          </Select>
        </div>
      )}

      <FacilitiesTableCard
        title="New Facility - 3"
        updatedText="Updated 1 day ago"
        columns={newColumns}
        items={facilities}
        itemKey="id"
        footer={
          <DynamicCardBottom
            facilityType="new"
            count={facilities.length}
            showViewMore={false}
            viewMoreCount={0}
            onViewMore={() => {}}
          />
        }
      />
    </div>
  );
};

export default NewFacilities;
