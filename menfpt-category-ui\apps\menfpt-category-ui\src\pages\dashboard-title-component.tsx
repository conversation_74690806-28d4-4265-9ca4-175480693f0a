import Select from "@albertsons/uds/molecule/Select";
import { useEffect, useState } from "react";
import { DropdownType, TitleFormData } from "../interfaces/worksheetFilter";
import { useDispatch } from "react-redux";
import { mockDivisionItems, mockDepartmentItems, mockSmicItems, mockQuarterItems } from "../mock/mockDashboardData";
import { useSelectorWrap } from "../rtk/rtk-utilities";
import { useGetmenfptCategoryTitleMutation } from "../server/Api/menfptCategoryAPI";
import { MenfptCategoryQuery } from "../server/Query/menfptCategoryQuery";
import { setMenfptCategory } from "../server/Reducer/menfpt-category.slice";
import HelpIcon from '../components/HelpIcon';

const DEFAULT_TITLE = 'Allocatr Insights';

const TitleComponent: React.FunctionComponent<any> = 
    () => 
        {

const [selectedQuarter, setSelectedQuarter] = useState<DropdownType | null>(null);
const [selectedDivision, setSelectedDivision] = useState<DropdownType[] | null>(null);
const [selectedDepartment, setSelectedDepartment] = useState<DropdownType | null>(null);
const [selectedSmics, setSelectedSmics] = useState<DropdownType | null>(null);

const [getMenfptCategoryTitle] = useGetmenfptCategoryTitleMutation();
    const { data: menfptCategoryTitle } = useSelectorWrap('menfptCategory_rn')||'';
    const dispatch = useDispatch();

    useEffect(() => { 
        // let menfptCategoryTitle = getMenfptTitle();
        // menfptCategoryTitle.then((response: any) => {
        //   dispatch(setMenfptCategory(response?.data?.nfptTest));
        // });
    }, []);

    const getMenfptTitle = async () => {
        const titleArgs = {
          query: MenfptCategoryQuery,
        };
        const getFeatureFlags: any = await getMenfptCategoryTitle(titleArgs);
        return getFeatureFlags;
    };

const titleFormData: TitleFormData = {
    division: mockDivisionItems,
    department: mockDepartmentItems,    
    smics: mockSmicItems,
    quarter: mockQuarterItems,
};

const [dashboardFilterData, setDashboardFilterData] = useState<TitleFormData>(titleFormData);


const [selectedTopFilters, setSelectedTopFilters] = useState<TitleFormData[]>();

// set title form to titleFormData once the component renders  
useEffect(() => {
    setDashboardFilterData(titleFormData);
}, []);


// print value of selectedTopFilters whenever it changes
useEffect(() => {
    console.log(selectedTopFilters);
}, [selectedTopFilters]);

useEffect(() => {
    setDashboardFilterData(titleFormData);
}, []);

useEffect(() => {
    console.log({ selectedQuarter, selectedDivision, selectedDepartment, selectedSmics });
}, [selectedQuarter, selectedDivision, selectedDepartment, selectedSmics]);

const featureFlagsState = useSelectorWrap('featureFlags_rn');
const FeatureFlags = featureFlagsState?.data?.dashboardFilter || false;



return (
    <div className="flex">
        <div className="flex-1">
            <div className="flex left">
                <h2 className="text-2xl font-bold text-gray-800 ml-[20px]">
                    {menfptCategoryTitle?.message ? menfptCategoryTitle?.message : DEFAULT_TITLE}
                </h2>
                    <HelpIcon variant="default" />
            </div>
        </div>
        {FeatureFlags && (
        <div className="flex-1 flex justify-between ">
            <Select 
                items={dashboardFilterData?.quarter}
                
                search={true}
                onChange={(val: DropdownType) => setSelectedQuarter(val)}
                menuWidth={175}
                size="lg"
                className="border rounded p-2"
                placeholder="Quarter"
                itemText={(item: any) => (item?.name ? item?.name : '')}
            />
            <Select
                multiple
                checkboxes
                items={dashboardFilterData?.division}
                
                search={true}
                onChange={(val: DropdownType[]) => setSelectedDivision(val)}
                menuWidth={175}
                width={'sm'}
                className="border rounded max-h-14 overflow-y-scroll"
                size="sm"
                placeholder="Divisions"
                itemText={(item: any) => (item?.name ? item?.name : '')}
            />
            <Select 
                items={dashboardFilterData?.department}
                
                search={true}
                onChange={(val: DropdownType) => setSelectedDepartment(val)}
                menuWidth={175}
                className="border rounded p-2"
                size="lg"
                placeholder="Departments"
                itemText={(item: any) => (item?.name ? item?.name : '')}
            />
            <Select 
                items={dashboardFilterData?.smics}
                size="lg"
                search={true}
                className="border rounded p-2 max-h-14"
                placeholder="Categories"
                onChange={(val: DropdownType) => setSelectedSmics(val)}
                itemText={(item: any) => (item?.name ? item?.name : '')}
            />
        </div>
        )}
    </div>
);
};

export default TitleComponent;