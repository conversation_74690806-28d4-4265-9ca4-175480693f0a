import { DropdownType } from '../../interfaces/worksheetFilter';
import { SmDataType } from './types/smTypes';

export interface FilterItem {
  id: string;
  name: string;
  value: any;
  deskName: string;
  deptId: string;
}

export interface WorksheetFilterProps {
  FiltersList: any[];
}

export interface FilterState {
  division: DropdownType[];
  department?: DropdownType | DropdownType[];
  desk?: DropdownType;
  category: DropdownType[];
  timeframe?: DropdownType;
  selectedSm?: Map<string, any[]>;
}

export interface AppliedFilterState {
  division: DropdownType[];
  department?: DropdownType | DropdownType[] | undefined;
  desk?: DropdownType | undefined;
  timeframe?: DropdownType | undefined;
  periods?: DropdownType[];
  weeks?:DropdownType[];
  selectedSm?: SmDataType;
  selectedWeeks?: { periodNum: number; weekNum: number }[];
  filterPg?: string;
}

export interface WorksheetFilterContextType {
  filterState: FilterState;
  appliedFilters: AppliedFilterState;
  setFilterState: (state: FilterState) => void;
  setAppliedFilters: (filters: AppliedFilterState) => void;
  openFilterModal: () => void;
  closeFilterModal: () => void;
  isFilterModalOpen: boolean;
}

export interface WorksheetFilterDisplayProps {
  appliedFilters: AppliedFilterState;
  openFilterModal: () => void;
  division: DropdownType[];
}

export interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  FiltersList: any[];
  appliedFilters: AppliedFilterState;
  onApply: (filters: AppliedFilterState) => void;
}
