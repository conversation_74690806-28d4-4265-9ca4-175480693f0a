// Test script to verify that Total row appears first in Excel output
// This simulates the addRows function logic to verify row order

// Mock functions
const formatCurrency = (value) => value === null || value === undefined || value === '' ? '' : `$${value}`;
const getDeptName = (smicData, deptId, fallback) => fallback || `Dept ${deptId}`;
const getDivisionName = (divisionId, fallback) => fallback || `Division ${divisionId}`;
const getBannerName = (bannerId, fallback) => fallback || `Banner ${bannerId}`;

// Mock mapRow function - simplified version
const mapRow = (baseRow, data, formatCurrency, type) => ({
  ...baseRow,
  '$ Projection': formatCurrency(data.line1Projection),
  type: type
});

// Simplified addDepartmentRows function
const addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}` };
  
  // Add quarter row
  if (dept.quarter) {
    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));
  }

  // Add periods
  (dept.periods || []).forEach((period) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Period ${period.periodNumber}` },
      period,
      formatCurrency,
      'Period'
    ));
  });

  // Add weeks
  (dept.weeks || []).forEach((week) => {
    rows.push(mapRow(
      { ...baseRow, departmentName: `Week ${week.weekNumber}` },
      week,
      formatCurrency,
      'Week'
    ));
  });
};

// Main addRows function (simplified version of the updated logic)
const addRows = (rows, data, smicData, useWeekId = false) => {
  // Handle the new structure with divisions and banners
  if (data.divisions && Array.isArray(data.divisions)) {
    // First, add the Total row data if it exists at the root level
    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {
      const totalData = {
        id: data.id || 'Total',
        name: data.name || '',
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
      addDepartmentRows(rows, totalData, smicData, useWeekId);
    }
    
    // Then, process divisions and banners
    data.divisions.forEach((division) => {
      const divisionName = getDivisionName(division.id, division.name);
      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };
      
      // Add division quarter row
      if (division.quarter) {
        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));
      }

      // Process banners within division
      if (division.banners && Array.isArray(division.banners)) {
        division.banners.forEach((banner) => {
          const bannerName = getBannerName(banner.id, banner.name);
          const bannerBaseRow = { departmentName: `  ${banner.id} - ${bannerName}` };
          
          // Add banner quarter row
          if (banner.quarter) {
            rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));
          }

          // Process departments within banner
          if (banner.departments && Array.isArray(banner.departments)) {
            banner.departments.forEach((dept) => {
              addDepartmentRows(rows, dept, smicData, useWeekId);
            });
          }
        });
      }
    });
  } else {
    // Fallback to old structure for backward compatibility
    addDepartmentRows(rows, data, smicData, useWeekId);
  }
};

// Test data based on your API response
const testData = {
  id: 'Total',
  name: '',
  quarter: { quarterNumber: 202502, line1Projection: 225825128 },
  periods: [{ periodNumber: 202506, line1Projection: 129149501 }],
  weeks: [{ weekNumber: 202525, periodNumber: 202507, line1Projection: 32341866 }],
  divisions: [{
    id: '34',
    name: 'Test Division',
    quarter: { quarterNumber: 202502, line1Projection: 100000 },
    banners: [{
      id: '25',
      name: 'Test Banner',
      quarter: { quarterNumber: 202502, line1Projection: 50000 },
      departments: [{
        id: '301',
        name: 'Grocery',
        quarter: { quarterNumber: 202502, line1Projection: 25000 },
        periods: [{ periodNumber: 202506, line1Projection: 12000 }],
        weeks: [{ weekNumber: 202525, periodNumber: 202506, line1Projection: 3000 }]
      }]
    }]
  }]
};

// Test the function
const rows = [];
addRows(rows, testData, [], false);

console.log('Excel Row Order Test:');
console.log('====================');
rows.forEach((row, index) => {
  console.log(`${index + 1}. ${row.departmentName} (${row.type}) - ${row['$ Projection']}`);
});

console.log('\n✅ Expected Order:');
console.log('1. Total (Quarter) - should be FIRST');
console.log('2. Period 202506 (Period)');
console.log('3. Week 202525 (Week)');
console.log('4. 34 - Test Division (Quarter)');
console.log('5. 25 - Test Banner (Quarter)');
console.log('6. 301 - Grocery (Quarter)');
console.log('7. Period 202506 (Period)');
console.log('8. Week 202525 (Week)');

console.log('\n✅ Test completed. Total row should now appear at the beginning of the Excel file.');
