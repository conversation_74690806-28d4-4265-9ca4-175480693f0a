import React from 'react';
import { ChevronRight } from 'lucide-react';

type ViewMoreFacilitiesProps = {
  count: number;
  onClick?: () => void;
};

const ViewMoreFacilities: React.FC<ViewMoreFacilitiesProps> = ({ count, onClick }) => (
  <span
    className="flex items-center gap-2 text-[#1B6EBB]  cursor-pointer font-bold ml-4 px-3 py-1 rounded"
    onClick={onClick}
  >
    <span>View {count} facilities more</span>
    <ChevronRight width={24} height={24} />
  </span>
);

export default ViewMoreFacilities;