import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ConfirmationModal from './confirmationModal';

describe('ConfirmationModal', () => {
  const defaultProps = {
    isOpen: true,
    title: 'Confirm Action',
    message: 'Are you sure?',
    confirmText: 'Confirm',
    setIsOpen: jest.fn(),
  };

  it('renders the modal with title and message', () => {
    render(<ConfirmationModal {...defaultProps} />);
    expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
  });

  it('renders confirm button', () => {
    render(<ConfirmationModal {...defaultProps} />);
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    expect(confirmButton).toBeInTheDocument();
  });

  it('clicks confirm button without error', () => {
    render(<ConfirmationModal {...defaultProps} />);
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    fireEvent.click(confirmButton);
    // No assertion needed, just ensure click does not throw
  });

  it('calls setIsOpen(false) when cancel button is clicked', () => {
    render(<ConfirmationModal {...defaultProps} />);
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    expect(defaultProps.setIsOpen).toHaveBeenCalledWith(false);
  });

  it('does not render when isOpen is false', () => {
    render(<ConfirmationModal {...defaultProps} isOpen={false} />);
    expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
  });

  it('renders with custom confirmText', () => {
    render(<ConfirmationModal {...defaultProps} confirmText="Yes, Proceed" />);
    expect(screen.getByText('Yes, Proceed')).toBeInTheDocument();
  });

  // Skipped: Modal's onClose cannot be triggered directly in this test without access to the close icon or backdrop.

  it('renders all expected elements and classes', () => {
    render(<ConfirmationModal {...defaultProps} />);
    // Modal root
    expect(screen.getByTestId('modal-id')).toBeInTheDocument();
    // Title and message
    expect(screen.getByText('Confirm Action')).toHaveClass('text-center');
    expect(screen.getByText('Are you sure?')).toHaveClass('text-center');
    // Buttons
    expect(screen.getByRole('button', { name: /cancel/i })).toHaveClass('mr-2');
    expect(screen.getByRole('button', { name: /confirm/i })).toHaveClass('ml-2');
  });
});