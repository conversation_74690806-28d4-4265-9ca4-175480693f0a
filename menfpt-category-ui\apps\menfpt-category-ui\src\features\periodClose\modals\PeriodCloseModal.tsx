import Button from '@albertsons/uds/molecule/Button';

export const PeriodCloseModal = ({
  onClose,
  quarterLabel,
}: {
  onClose: () => void;
  quarterLabel: string;
}) => {
  return (
    <div data-testid="period-close-modal"  className="mx-[60px]">
      <div className="text-center select-none font-bold text-[28px] mt-[74px]  mb-[10px]">
        Attention, please read this carefully!
      </div>
      <div className=" text-[#2b303c] text-center text-xl leading-7">
        As we switch into a new period, the changes can be made to the previous
        Quarter until Monday 10 am PT and any changes after that are not
        possible.
      </div>
      <div className="flex items-center justify-center w-full  mt-[24px] mb-20">
        <Button
          width={200}
          size="lg"
          className="ml-2 whitespace-nowrap"
          onClick={onClose}
          data-testid="period-close-ok-button"
        >
          OK, I understand
        </Button>
      </div>
    </div>
  );
};
