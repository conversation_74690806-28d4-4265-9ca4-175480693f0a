import { formatPercentage } from '../../components/AllocatrInsights/utils/insightsFormatters';

function formatNegativeCurrency(val: any, formatCurrency: (v: any) => string) {
  const num = Number(val);
  if (val === null || val === undefined || val === '' || isNaN(num)) return '';
  if (num < 0) {
    return `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;
  }
  return formatCurrency(val);
}
function formatActualOrForecast(val: any, formatCurrency: (v: any) => string) {
  const num = Number(val);
  if (val === null || val === undefined || val === '' || isNaN(num)) return '';
  if (num < 0) {
    return `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;
  }
  return formatCurrency(val);
}
function displayDashIfZero(val: any, formatter: (v: any) => string) {
  const num = Number(val);
  if (num === 0) return '--';
  return formatter(val);
}

export function mapRow(
  baseRow: any,
  data: any,
  formatCurrency: (v: any) => string,
  type = '',
  period = '',
  week = ''
) {
  return {
    ...baseRow,
    '$ Projection': displayDashIfZero(data.line1Projection, formatCurrency),
    '$ Last Year': displayDashIfZero(data.lastYear, formatCurrency),
    '$Actual/Merch.Forecast': displayDashIfZero(data.actualOrForecast, formatCurrency),
    'Keeper% (Includes ID)': formatPercentage(data.idPercentage), 
    '$ vs LY': formatCurrency(data.vsLY?.value), 
    '$ vs Projection': formatCurrency(data.vsProjection?.value), 
    '$ Projection (BGP)': displayDashIfZero(data.bookGrossProfit?.projectionValue,formatCurrency), 
    '% Projection (BGP)': displayDashIfZero(data.bookGrossProfit?.projectionPct,formatPercentage), 
    '$Actual/Merch.Forecast (BGP)': displayDashIfZero(data.bookGrossProfit?.actualOrForecast,formatCurrency), 
    '%Actual/Merch.Forecast': formatPercentage(data.bookGrossProfit?.percentActualOrForecast), 
    '% vs Projection': formatPercentage(data.bookGrossProfit?.vsProjection), 
    '$ Projection (Markdown)': displayDashIfZero(Math.abs(data.markdown?.projectionValue ?? 0),formatCurrency), 
    '% Projection (Markdown)': displayDashIfZero(data.markdown?.projectionPct,formatPercentage),
    '$Actual/Merch.Forecast (Markdown)': displayDashIfZero(data.markdown?.actualOrForecast,v=>formatActualOrForecast(v, formatCurrency)),
    '%Actual/Merch.Forecast (Markdown)': formatPercentage(data.markdown?.percentActualOrForecast),
    '% vs Projection (Markdowns)':formatPercentage(data.markdown?.vsProjection),
    '$ Projection (Shrink)': displayDashIfZero(Math.abs(data.shrink?.projectionValue ?? 0),formatCurrency), 
    '% Projection (Shrink)': displayDashIfZero(data.shrink?.projectionPct,formatPercentage), 
    '$Actual/Merch.Forecast (Shrink)': displayDashIfZero(data.shrink?.actualOrForecast,v=>formatActualOrForecast(v, formatCurrency)),
    '%Actual/Merch.Forecast (Shrink)': formatPercentage(data.shrink?.percentActualOrForecast),
    '% Projection (Shrinks)': formatPercentage(data.shrink?.vsProjection), 
    '$ Projection (RGP)': displayDashIfZero(data.line5?.projectionValue,formatCurrency), 
    '% Projection (RGP)': displayDashIfZero(data.line5?.projectionPct,formatPercentage),
    '$Actual/Merch.Forecast (RGP)': displayDashIfZero(data.line5?.actualOrForecast, formatCurrency), 
    '%Actual/Merch.Forecast (RGP)': formatPercentage(data.line5?.percentActualOrForecast), 
    '$ vs Projection (RGP)': formatCurrency(data.line5?.vsProjection), 
    '% vs Projection (RGP)': formatPercentage(data.line5?.percentVsProjection), 
    '$ Projection (Supplies Packaging)': displayDashIfZero(data.line6?.projection, v => formatNegativeCurrency(v, formatCurrency)),
    '$Actual/Merch.Forecast (Supplies Packaging)': displayDashIfZero(data.line6?.actualOrForecast, v => formatNegativeCurrency(v, formatCurrency)),
    '$ vs Projection (Supplies Packaging)': formatCurrency(data.line6?.vsProjection), 
    '$ Projection (Retail Allowance)': displayDashIfZero(data.line7?.projection,formatCurrency), 
    '$Actual/Merch.Forecast (Retail Allowance)': displayDashIfZero(data.line7?.actualOrForecast,formatCurrency), 
    '$ vs Projection (Retail Allowance)': formatCurrency(data.line7?.vsProjection), 
    '$ Projection(Sales)': displayDashIfZero(data.line8?.projectionValue,formatCurrency), 
    '% Projection(Sales)': displayDashIfZero(data.line8?.projectionPct,formatPercentage), 
    '$Actual/Merch.Forecast (Sales)': displayDashIfZero(data.line8?.actualOrForecast,formatCurrency), 
    '%Actual/Merch.Forecast (Sales)': formatPercentage(data.line8?.percentActualOrForecast), 
    '$ vs Projection (Sales)': formatCurrency(data.line8?.vsProjection), 
    '% vs Projection (Sales)': formatPercentage(data.line8?.percentVsProjection), 
  };
}

export const handleDownloadClick = (
  dashboardData: any[],
  smicData: any,
  appliedFilters: any,
  handleDownloadExcel: (
    dashboardData: any[],
    smicData: any,
    appliedFilters: any
  ) => void
) => {
  if (!dashboardData || dashboardData.length === 0) {
    alert('No dashboard data to export!');
    return;
  }
  handleDownloadExcel(dashboardData,smicData, appliedFilters );
};

export const getParentHeaderRow = (quarterDisplay: string) => [
  quarterDisplay, // A (1)
  'Line 1 (Sales to Public)', '', '', '', '', '', // B-G (6)
  'Line 5: Book Gross Profit', '', '', '', '',    // H-L (5)
  'Line 5: Markdown', '', '', '', '',             // M-Q (5)
  'Line 5: Shrink %', '', '', '', '',             // R-V (5)
  'Line 5 (Realized Gross Profit): Total', '', '', '', '', '', // W-AB (6)
  'Line 6 (Supplies Packaging)', '', '',          // AC-AE (3)
  'Line 7 (Retail Allowance)', '', '',            // AF-AH (3)
  'Line 8 (Realized Gross Profit Before Other Revenue - Sales)', '', '', '', '', '' // AI-AN (6)
];

export const COMMON_HEADERS = [
  '', // Department/Period/Week 
  '$ Projection',
  '$ Last Year',
  '$Actual/Merch.Forecast',
  'Keeper% (Includes ID)',
  '$ vs LY',
  '$ vs Projection', //book gross profit
  '$Projection',
  '% Projection',
  '$Actual/Merch.Forecast',
  '%Actual/Merch.Forecast',
  '% vs Projection',//markdown
  '$Projection',
  '% Projection',
  '$Actual/Merch.Forecast',
  '%Actual/Merch.Forecast',
  '% vs Projection',//shrink
  '$Projection',
  '% Projection',
  '$Actual/Merch.Forecast',
  '%Actual/Merch.Forecast',
  '% vs Projection',//RGP
  '$Projection',
  '% Projection',
  '$Actual/Merch.Forecast',
  '%Actual/Merch.Forecast',
  '$ vs Projection',
  '% vs Projection',//line6
  '$ Projection',
  '$Actual/Merch.Forecast',
  '$ vs Projection',//line7
  '$ Projection',
  '$Actual/Merch.Forecast',
  '$ vs Projection',//line8
  '$ Projection',
  '% Projection',
  '$Actual/Merch.Forecast',
  '%Actual/Merch.Forecast',
  '$ vs Projection',
  '% vs Projection',
];

export const VS_PROJECTION_HEADERS = [
  '$ vs LY',
'$ vs Projection',
  '% vs Projection',
  '% vs Projection (Book Gross Profit)',
  '% vs Projection (Markdown)',
  '% vs Projection (Shrink)',
  '$ vs Projection (RGP)',
  '% vs Projection (RGP)',
  '$ vs Projection (Supplies Packaging)',
  '$ vs Projection (Retail Allowance)',
  '$ vs Projection (Sales)',
  '% vs Projection (Sales)'
];
export const VS_PROJECTION_DOLLAR_HEADERS = [
  '$ vs LY',
  '$ vs Projection', 
  '$ vs Projection (RGP)',
  '$ vs Projection (Supplies Packaging)',
  '$ vs Projection (Retail Allowance)',
  '$ vs Projection (Sales)'
];
