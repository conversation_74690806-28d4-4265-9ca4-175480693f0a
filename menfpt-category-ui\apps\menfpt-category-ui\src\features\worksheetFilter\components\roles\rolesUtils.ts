import { SmDataType } from "../../types/smTypes";

export const filterDesksByDivisionAndDept = (selectedDeptId, deptListForSelectedDivision) => {
  return deptListForSelectedDivision.filter(obj => {
    return selectedDeptId?.includes(obj.num) || obj.num === selectedDeptId;
  });
};

export const getSmDataByDivisionAndDept = ({ selectedDeptId, deptListForSelectedDivision }) => {
  if (!selectedDeptId || !deptListForSelectedDivision || !Array.isArray(deptListForSelectedDivision) || deptListForSelectedDivision.length === 0) {
    return [];
  }
  const selectedDepartmentsData = filterDesksByDivisionAndDept(selectedDeptId, deptListForSelectedDivision);
  if (!selectedDepartmentsData || selectedDepartmentsData.length === 0) {
    return [];
  }
  return extractSmDataFromDesks(selectedDepartmentsData);
};

export const extractSmDataFromDesks = (selectedDepartmentsData) => {  
  //get deskName from the selectedDepartmentsData
  const deskNamesForSelectedDept = selectedDepartmentsData.flatMap(obj => obj.deskNameArr || []);
  
  // Create a map to group ASMs by SM
  const smAsmMap: SmDataType = new Map();
  
  // Process each desk name and group ASMs by SM
  deskNamesForSelectedDept.forEach(desk => {
    const parts = desk.split('-');
    const sm = parts[2];
    const asm = parts[3];

    let asmSet = smAsmMap.get(sm);
    if (!asmSet) {
      asmSet = new Set();
    }
    if (asm) {
      asmSet.add(asm);
    }
    smAsmMap.set(sm, asmSet);
  });
  
  return smAsmMap;
};

export const getAsmListForSelectedSM = ({selectedSm, smData}) => {
  const asmListForSelectedSM = smData.filter(obj => obj.sm === selectedSm).map(obj => obj.asmArr).flat();
  return asmListForSelectedSM;
}

export const formatName = (name: string | string[]) => {
  const formatString = (str: string) => {
    return str.toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (Array.isArray(name)) {
    return formatString(name.join(' '));
  }
  return formatString(name);
};