{"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/utils/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/utils/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/utils/jest.config.ts", "passWithNoTests": true}}}}