import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import AllocatrInsights from './AllocatrInsights';
import * as api from '../../../src/server/Api/menfptCategoryAPI';



// Mocks
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: (key: string) => {
    if (key === 'appliedFilter_rn') return { data: { filterPg: 'dashboard', department: [{ num: 1 }], timeframe: { num: 1, fiscalQuarterNumber: 1, fiscalYear: 2025 }, periods: [{ num: 1 }], selectedWeeks: [{ weekNum: 1 }] } };
    if (key === 'workSheetFilterList_rn') return { data: { smicData: [] } };
    if (key === 'saveWeekSelection_rn') return { data: { value: '2025/01/01', name: 'Week 1' } };
    if (key === 'dataForQrtrDisplayedInTable_rn') return { data: [{ fiscalWeekNumber: 1 }] };
    if (key === 'displayDate_rn') return { data: { fiscalWeekNumber: 1 } };
    return { data: [] };
  }
}));
jest.mock('../../../src/util/filterUtils', () => ({
  useExtractBannerId: () => ["123", "456"]
}));
jest.mock('../../../src/server/Api/menfptCategoryAPI', () => ({
  useGetAllocatrTableDataMutation: () => [jest.fn().mockResolvedValue({ data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 1, name: 'Dept1' }] } } })],
  useGetDisplayDateQuery: () => ({ data: { data: { getDisplayDate: { weeksForQtr: [{ fiscalWeekNumber: 1 }] } } } })
}));
jest.mock('@albertsons/uds/molecule/Spinner', () => () => <div data-testid="spinner">Spinner</div>);
jest.mock('./AllocatrInsightsTable', () => (props: unknown) => <div data-testid="table">{JSON.stringify((props as { departments?: unknown }).departments)}</div>);
jest.mock('../../../src/features/worksheetFilter/worksheetFilterConfig', () => ({ worksheetFilterConfig: { lsKeyDashboardPg: 'dashboard' } }));
jest.mock('./AllocatrInsightsHelper', () => ({
  __esModule: true,
  getSmicsForDeptsAndDivisions: () => [],
  getAllCategoriesFromFilter: () => []
}));
jest.mock('./AllocatrInsightsHelper', () => ({
  getSmicsForDeptsAndDivisions: () => [],
  getAllCategoriesFromFilter: () => []
}));
jest.mock('../../../src/features/calendarServiceUtils', () => ({
  createQtrPayloadForCalendar: jest.fn(),
  handleCalendarApiResp: jest.fn(),
  useCurrentQuarterNbr: () => 1
}));
jest.mock('react-redux', () => ({ useDispatch: () => jest.fn() }));
jest.mock('../quarterDetails.slice', () => ({ setDataForQrtrDisplayedInTable: jest.fn() }));
jest.mock('../../../src/util/dateUtils', () => ({ addDaysToDate: (date: string, days: number) => date }));
jest.mock('./utils/getLastFriday', () => ({
  getLastFriday: () => '2025-01-01T00:00:00.000',
  getLastFridayInQuarter: () => '2025-01-01',
  getNextMondayAfterQuarter: () => '2025-01-08',
  getFridayOfSelectedWeek: () => '2025-01-03'
}));
jest.mock('./Forecastvariance', () => ({ subtractForecastVarianceData: jest.fn(() => [{ id: 2, name: 'Variance' }]) }));
jest.mock('date-fns-tz', () => ({
  utcToZonedTime: (date: unknown, tz: string) => date,
  format: (date: unknown, fmt: string, opts: unknown) => '2025-01-01 00:00:00 PST'
}));

describe('AllocatrInsights', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it('renders loading spinner', () => {
    // Force loading state
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);
    render(<AllocatrInsights selectedTab="Performance Summary" />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
    jest.restoreAllMocks();
  });

  it('renders table for Performance Summary and calls onDataLoaded', async () => {
    const onDataLoaded = jest.fn();
    const mockTrigger = jest.fn().mockResolvedValue({
      data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 1, name: 'Dept1' }] } }
    });
    jest.spyOn(api, 'useGetAllocatrTableDataMutation').mockImplementation(() => [
      mockTrigger,
      {
        isLoading: false,
        isError: false,
        data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 1, name: 'Dept1' }] } },
        reset: jest.fn()
      }
    ]);
    render(<AllocatrInsights selectedTab="Performance Summary" onDataLoaded={onDataLoaded} />);
    await waitFor(() => expect(screen.getByTestId('table')).toBeInTheDocument(), { timeout: 10000 });
    expect(onDataLoaded).toHaveBeenCalled();
  }, 15000);
it('renders table for Forecast Variance and calls onDataLoaded',
  async () => {
    const onDataLoaded = jest.fn();
    const mockTrigger = jest.fn().mockResolvedValue({
      data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 2, name: 'Variance' }] } }
    });
    jest.spyOn(api, 'useGetAllocatrTableDataMutation').mockImplementation(() => [
      mockTrigger,
      {
        isLoading: false, // Ensure loading is false so spinner is not shown
        isError: false,
        data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 2, name: 'Variance' }] } },
        reset: jest.fn()
      }
    ]);
    // Mock useState to ensure loading is false
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [false, jest.fn()]);
    render(<AllocatrInsights selectedTab="Performance Variance" onDataLoaded={onDataLoaded} />);
    await waitFor(() => expect(screen.getByTestId('table')).toBeInTheDocument(), { timeout: 10000 });
    expect(onDataLoaded).toHaveBeenCalled();
    jest.restoreAllMocks();
  },
  15000
);

  it(
    'renders empty table if no departments',
    async () => {
      const onDataLoaded = jest.fn();
      const mockTrigger = jest.fn().mockResolvedValue({
        data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [] } }
      });
      jest.spyOn(api, 'useGetAllocatrTableDataMutation').mockImplementation(() => [
        mockTrigger,
        {
          isLoading: false,
          isError: false,
          data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [] } },
          reset: jest.fn()
        }
      ]);
      render(<AllocatrInsights selectedTab="Performance Summary" onDataLoaded={onDataLoaded} />);
      await waitFor(() => expect(screen.getByTestId('table')).toBeInTheDocument(), { timeout: 10000 });
      expect(onDataLoaded).toHaveBeenCalledWith([]);
    },
    15000
  );
  it('handles error state (isError=true)', async () => {
    const onDataLoaded = jest.fn();
    const mockTrigger = jest.fn().mockResolvedValue({
      data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [] } }
    });
    jest.spyOn(api, 'useGetAllocatrTableDataMutation').mockImplementation(() => [
      mockTrigger,
      {
        isLoading: false,
        isError: true,
        data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [] } },
        reset: jest.fn()
      }
    ]);
    render(<AllocatrInsights selectedTab="Performance Summary" onDataLoaded={onDataLoaded} />);
    await waitFor(() => expect(screen.getByTestId('table')).toBeInTheDocument(), { timeout: 10000 });
    expect(onDataLoaded).toHaveBeenCalledWith([]);
  });

  it('renders without onDataLoaded prop', async () => {
    const mockTrigger = jest.fn().mockResolvedValue({
      data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 3, name: 'NoCallback' }] } }
    });
    jest.spyOn(api, 'useGetAllocatrTableDataMutation').mockImplementation(() => [
      mockTrigger,
      {
        isLoading: false,
        isError: false,
        data: { getAllocatrDashboardTableData: { allocatrDashboardTableData: [{ id: 3, name: 'NoCallback' }] } },
        reset: jest.fn()
      }
    ]);
    render(<AllocatrInsights selectedTab="Performance Summary" />);
    await waitFor(() => expect(screen.getByTestId('table')).toBeInTheDocument(), { timeout: 15000 });
  }, 15000);

});
