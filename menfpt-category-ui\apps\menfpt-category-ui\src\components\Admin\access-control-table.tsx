import React, { useMemo } from 'react';
import Table from '@albertsons/uds/molecule/Table';
import Tag from '@albertsons/uds/molecule/Tag';
import { SquarePen, Info } from 'lucide-react';
import InfoTooltip from '../../components/InfoTooltip';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import './access-control-table.css';

interface AccessControlTableProps {
  isAdmin: boolean;
  items: any[];
  onEditAccess?: (userData: any) => void;
}

const AccessControlTable = ({ isAdmin, items, onEditAccess }: AccessControlTableProps) => {
  const controlColumns = useMemo<Column<any>[]>(() => {
    const cols: Column<any>[] = [
      {
        id: 'userName',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>User Name</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Associate Name'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'userName',
        sortable: false,
        className: 'border-r-0',
        width: '5%',
      },
      {
        id: 'role',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Role</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Current assigned Role'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'role',
        sortable: false,
        width: '11%',
      },
      {
        id: 'accessStatus',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Access Status</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Active vs Inactive Status'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: (item) => (
          <Tag
            preset={item.accessStatus === 'Active' ? 'blue' : 'gray'}
            className={
              item.accessStatus === 'Active'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-800'
            }
            label={item.accessStatus}
          />
        ),
        sortable: false,
        width: '11%',
      },
      {
        id: 'effectiveStart',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Effective Start</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Start date for user access'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'effectiveStart',
        sortable: false,
        width: '11%',
      },
      {
        id: 'effectiveEnd',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Effective End</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'End date for user access'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'effectiveEnd',
        sortable: false,
        width: '11%',
      },
      {
        id: 'oracleDepartment',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Oracle Department</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Department as defined by Oracle'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'oracleDepartment',
        sortable: false,
        width: '2%',
      },
      {
        id: 'desk',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Desk</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Desk as defined by Oracle'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: 'desk',
        sortable: false,
        width: '11%',
      },
    ];

    if (isAdmin) {
      cols.push({
        id: 'editAccess',
        label: (
          <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
            <span>Edit Access</span>
            <span className="ml-2 data-popper-bottom-margin">
              <InfoTooltip
                label={'Edits User access'}
                icon={<Info size={16} color="#1B6EBB" />}
                anchor="top"
                variant="dark"
                className="uds-tooltip-top"
              />
            </span>
          </div>
        ),
        align: 'left',
        value: (item) => (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onEditAccess && onEditAccess(item);
            }}
            className="text-[#1B6EBB] hover:text-blue-700 cursor-pointer"
            type="button"
          >
            <SquarePen size={16} />
          </button>
        ),
        sortable: false,
        width: '7%',
      });
    }

    cols.push({
      id: 'lastUpdatedBy',
      label: (
        <div className="ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex">
          <span>Last Updated By</span>
          <span className="ml-2">
            <InfoTooltip
              label={'User, date and time of last edit'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="left"
              variant="dark"
              className="uds-tooltip-left"
            />
          </span>
        </div>
      ),
      align: 'left',
      value: 'lastUpdatedBy',
      sortable: false,
      width: '11%',
    });

    return cols;
  }, [isAdmin]);

  return (
    <div className="access-control-table">
      <Table
        id="table"
        itemKey={(item) => item.userName}
        items={items}
        columns={controlColumns}
        noPagination={false}
        noHeader={true}
        dividers="horizontal"
        backfill={false}
        pageSize={13}
      />
    </div>
  );
};

export default AccessControlTable;
