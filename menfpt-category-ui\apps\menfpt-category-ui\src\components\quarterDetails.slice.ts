import { createGenericSlice } from "../rtk/rtk-slice";


//Quarter details for the quarter being displayed in the table. Used for period close.
export const dataForQrtrDisplayedInTableSlice = createGenericSlice({
  name: 'dataForQrtrDisplayedInTable_rn',
  initialState: { status: 'loading', data: {} },
})({
  setDataForQrtrDisplayedInTable(state, { payload }) {
    state.data = payload;
  },
  clearDataForQrtrDisplayedInTable(state) {
    state.status = 'loading';
    state.data = {};
  },
});


export const { setDataForQrtrDisplayedInTable, clearDataForQrtrDisplayedInTable } = dataForQrtrDisplayedInTableSlice.actions;


//Detailed calendar  details for the last quarter. Used for period close.
export const lastQrtrDataSlice = createGenericSlice({
  name: 'lastQrtrData_rn',
  initialState: { status: 'loading', data: {} },
})({
  setLastQrtrData(state, { payload }) {
    state.data = payload;
  },
  clearLastQrtrData(state) {
    state.status = 'loading';
    state.data = {};
  },
});


export const { setLastQrtrData, clearLastQrtrData } = lastQrtrDataSlice.actions;

