import { DropdownType } from '../../interfaces/worksheetFilter';
import { SmDataType } from './types/smTypes';
import { FilterItem } from './worksheetFilterTypes';

const formDeskNameFromFilterSelection = ({
  selectedDivision,
  selectedDepartment,
  selectedSm,
  selectedAsm,
}: {
  selectedDivision: DropdownType | null;
  selectedDepartment: DropdownType | null;
  selectedSm: string | null;
  selectedAsm: string | null;
}): string => {
  const parts: (string | number)[] = [];

  if (selectedDivision?.num) {
    parts.push(selectedDivision.num);

    if (selectedDepartment?.num) {
      parts.push(selectedDepartment.num);

      if (selectedSm) {
        parts.push(selectedSm);

        if (selectedAsm) {
          parts.push(selectedAsm);
        }
      }
    }
  }

  return parts.join('-');
};

const addUniqueCategory = (categoryList: DropdownType[], item: any) => {
  const cat = {
    name: `${item?.smicCategoryId} - ${item?.smicCategoryDesc}`,
    num: item?.smicCategoryId,
  };
  if (!categoryList.some((c) => c.num === cat.num)) {
    categoryList.push(cat);
  }
};

/**
 * Gets categories based on department-related filters
 */
export const getCategoriesForDepartmentReltdFilters = ({
  FiltersList = [],
  selectedDivision = null,
  selectedDepartment,
  selectedSm = null,
}: {
  FiltersList?: FilterItem[];
  selectedDivision?: DropdownType | DropdownType[] | null;
  selectedDepartment?: DropdownType | DropdownType[];
  selectedSm?: SmDataType | null;
} = {}): DropdownType[] => {
  if (!selectedDepartment || !FiltersList?.length) {
    return [];
  }

  const categoryList: DropdownType[] = [];

  // Handle both single department and array of departments
  const departmentNums = Array.isArray(selectedDepartment)
    ? selectedDepartment.map((dept) => dept.num)
    : [selectedDepartment.num];

  const deskNamesToBeMatched: string[] = [];

  // Helper function to generate and add desk name
  const addDeskName = (div: DropdownType, dept: DropdownType, sm: string | null = null, asm: string | null = null) => {
    const deskName = formDeskNameFromFilterSelection({
      selectedDivision: div,
      selectedDepartment: dept,
      selectedSm: sm,
      selectedAsm: asm,
    });
    if (deskName) {
      deskNamesToBeMatched.push(deskName);
    }
  };

  // Generate desk names for all division-department combinations
  const departments = Array.isArray(selectedDepartment) ? selectedDepartment : [selectedDepartment];
  const divisions = Array.isArray(selectedDivision) ? selectedDivision : (selectedDivision ? [selectedDivision] : []);
  
  divisions.forEach(div => {
    departments.forEach(dept => {
      if (selectedSm && selectedSm.size > 0) {
        // If SM data is available, iterate through all SM and ASM combinations
        selectedSm.forEach((asmList, sm) => {
          if (asmList && asmList.size > 0) {
            // Generate desk names for each ASM under this SM
            asmList.forEach(asm => addDeskName(div, dept, sm, asm));
          } else {
            // SM exists but no ASM data - generate with null ASM
            addDeskName(div, dept, sm);
          }
        });
      } else {
        // No SM data available - generate with null SM and ASM
        addDeskName(div, dept);
      }
    });
  });

  FiltersList.forEach((item) => {
    // First check if we have a specific desk name filter to apply
    if (deskNamesToBeMatched.some(deskName => item.deskName.includes(deskName))) {

      addUniqueCategory(categoryList, item);
    }
    // Otherwise filter by department IDs
    else if (departmentNums.includes(Number(item.deptId))) {
      addUniqueCategory(categoryList, item);
    }
  });
  return categoryList;
};

/**
 * Gets categories for a selected desk
 */
export const getCategoriesForDesk = (
  FiltersList: FilterItem[],
  selectedDesk: DropdownType
): DropdownType[] => {
  if (!selectedDesk || !FiltersList?.length) {
    return [];
  }

  const categoryList: DropdownType[] = [];

  FiltersList.forEach((item) => {
    if (item.deskName === selectedDesk.name) {
      addUniqueCategory(categoryList, item);
    }
  });

  return categoryList;
};

/**
 * Gets categories for a specific division, department, SM, and ASM combination
 */
export const getCategoriesForDivisionDeptSMASM = (
  FiltersList: FilterItem[],
  divisionId: string,
  deptId: string,
  sm: string,
  asm: string
): DropdownType[] => {
  if (!divisionId || !deptId || !sm || !asm || !FiltersList?.length) {
    return [];
  }

  const deskName = `${divisionId}-${deptId}-${sm}-${asm}`;
  const categoryList: DropdownType[] = [];

  FiltersList.forEach((item) => {
    if (item.deskName === deskName) {
      addUniqueCategory(categoryList, item);
    }
  });

  return categoryList;
};