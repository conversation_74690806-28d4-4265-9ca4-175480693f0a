Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
[1m[33mCould not find a version for "events" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "clsx" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux-thunk" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "react-router" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "powerbi-client" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
<i> [1m[32m[webpack-dev-server] Project is running at:[39m[22m
<i> [1m[32m[webpack-dev-server] Loopback: [36mhttp://localhost:3010/[39m, [36mhttp://[::1]:3010/[39m[39m[22m
<i> [1m[32m[webpack-dev-server] 404s will fallback to '[36m/index.html[39m'[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mWeb Development Server is listening at http://localhost:3010/[22m


[createGlobPatternsForDependencies] WARNING: There was no ProjectGraph available to read from, returning an empty array of glob patterns

Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB [1m[32m[rendered][39m[22m
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB [1m[32m[rendered][39m[22m
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 958 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared) [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes [1m[32m[rendered][39m[22m
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/pages/dashboard-tabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/EPBCSSyncMonitor.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/udsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/quarterTabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/bottomdropdowm.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/editForecast.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/forecastTimestampsFooter.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/tooltipStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/historyTimeline.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/weekSelection.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/AllocatrInsights/AllocatrInsightsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/timeframe/timeframeSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/deptDesk/departmentDeskSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/searchIcon.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (85ae47102504e58e)
[ [32mready[39m ] http://localhost:3010
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 958 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (6769cf887969aa8f)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (22030ac1b3bb5128)
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /menfpt/dashboard[39m[22m
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (e9521ebb03fe50e6)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (5e82a6f287041b5a)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (e9521ebb03fe50e6)
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /menfpt/dashboard[39m[22m
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (e9521ebb03fe50e6)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (477f7f127a3afc9a)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_yup_index_esm_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /node_modules_file-saver_dist_FileSaver_min_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /node_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ed48cbb88e881835)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (371f327b385b30eb)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (afd9c4dbcb7bb479)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /src_bootstrap_tsx.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /favicon.ico[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (790db57a142e8ea3)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 959 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (96bdc4b5c629352d)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (4edd467bd3a794b9)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m185:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  Type 'any[]' is not assignable to type 'never[]'.
    Type 'any' is not assignable to type 'never'.
  [0m [90m 183 |[39m                     [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 184 |[39m                       console[33m.[39mlog([32m'Using existing performance data:'[39m[33m,[39m performanceSummaryData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 185 |[39m                       resolve(performanceSummaryData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 186 |[39m                     } [36melse[39m {
   [90m 187 |[39m                       console[33m.[39mlog([32m'No performance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 188 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m213:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  [0m [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 213 |[39m                       resolve(forecastVarianceData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 214 |[39m                     } [36melse[39m {
   [90m 215 |[39m                       console[33m.[39mlog([32m'No variance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 216 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m236:55[22m[39m
[90mTS2571: [39mObject is of type 'unknown'.
  [0m [90m 234 |[39m               } [36mcatch[39m (error) {
   [90m 235 |[39m                 console[33m.[39merror([32m'Download error:'[39m[33m,[39m error)[33m;[39m
  [31m[1m>[22m[39m[90m 236 |[39m                 alert([32m'Error preparing download: '[39m [33m+[39m (error[33m.[39mmessage [33m||[39m [32m'Unknown error'[39m))[33m;[39m
   [90m     |[39m                                                       [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 237 |[39m               }
   [90m 238 |[39m             }}
   [90m 239 |[39m           [33m>[39m[33mDownload[39m [36mas[39m [33mExcel[39m[0m

Found [31m[1m3 errors[22m[39m in 1443 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (3f75dd930ff74ddd)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m185:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  Type 'any[]' is not assignable to type 'never[]'.
    Type 'any' is not assignable to type 'never'.
  [0m [90m 183 |[39m                     [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 184 |[39m                       console[33m.[39mlog([32m'Using existing performance data:'[39m[33m,[39m performanceSummaryData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 185 |[39m                       resolve(performanceSummaryData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 186 |[39m                     } [36melse[39m {
   [90m 187 |[39m                       console[33m.[39mlog([32m'No performance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 188 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m213:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  [0m [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 213 |[39m                       resolve(forecastVarianceData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 214 |[39m                     } [36melse[39m {
   [90m 215 |[39m                       console[33m.[39mlog([32m'No variance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 216 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m236:55[22m[39m
[90mTS2571: [39mObject is of type 'unknown'.
  [0m [90m 234 |[39m               } [36mcatch[39m (error) {
   [90m 235 |[39m                 console[33m.[39merror([32m'Download error:'[39m[33m,[39m error)[33m;[39m
  [31m[1m>[22m[39m[90m 236 |[39m                 alert([32m'Error preparing download: '[39m [33m+[39m (error[33m.[39mmessage [33m||[39m [32m'Unknown error'[39m))[33m;[39m
   [90m     |[39m                                                       [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 237 |[39m               }
   [90m 238 |[39m             }}
   [90m 239 |[39m           [33m>[39m[33mDownload[39m [36mas[39m [33mExcel[39m[0m

Found [31m[1m3 errors[22m[39m in 2003 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (35cf6577e6ff4f42)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m185:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  Type 'any[]' is not assignable to type 'never[]'.
    Type 'any' is not assignable to type 'never'.
  [0m [90m 183 |[39m                     [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 184 |[39m                       console[33m.[39mlog([32m'Using existing performance data:'[39m[33m,[39m performanceSummaryData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 185 |[39m                       resolve(performanceSummaryData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 186 |[39m                     } [36melse[39m {
   [90m 187 |[39m                       console[33m.[39mlog([32m'No performance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 188 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m213:31[22m[39m
[90mTS2345: [39mArgument of type 'any[]' is not assignable to parameter of type 'never[] | PromiseLike<never[]>'.
  [0m [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 213 |[39m                       resolve(forecastVarianceData)[33m;[39m
   [90m     |[39m                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 214 |[39m                     } [36melse[39m {
   [90m 215 |[39m                       console[33m.[39mlog([32m'No variance data found in state, please try refreshing the page'[39m)[33m;[39m
   [90m 216 |[39m                       resolve([])[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m236:55[22m[39m
[90mTS2571: [39mObject is of type 'unknown'.
  [0m [90m 234 |[39m               } [36mcatch[39m (error) {
   [90m 235 |[39m                 console[33m.[39merror([32m'Download error:'[39m[33m,[39m error)[33m;[39m
  [31m[1m>[22m[39m[90m 236 |[39m                 alert([32m'Error preparing download: '[39m [33m+[39m (error[33m.[39mmessage [33m||[39m [32m'Unknown error'[39m))[33m;[39m
   [90m     |[39m                                                       [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 237 |[39m               }
   [90m 238 |[39m             }}
   [90m 239 |[39m           [33m>[39m[33mDownload[39m [36mas[39m [33mExcel[39m[0m

Found [31m[1m3 errors[22m[39m in 2686 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (9d073245af586a55)
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /menfpt/dashboard[39m[22m
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m178:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 176 |[39m                   
   [90m 177 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 178 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 179 |[39m                     [90m// Get fresh data[39m
   [90m 180 |[39m                     [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-tab="'[39m [33m+[39m [33mTabsLabels[39m[33m.[39m[33mPERFORMANCE_SUMMARY[39m [33m+[39m [32m'"]'[39m)[33m;[39m
   [90m 181 |[39m                     [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m209:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  [0m [90m 207 |[39m                   
   [90m 208 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 209 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 210 |[39m                     [90m// If we already have data, use it[39m
   [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m[0m

Found [31m[1m2 errors[22m[39m in 1752 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (d5a0d9797ea2b1dc)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m178:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 176 |[39m                   
   [90m 177 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 178 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 179 |[39m                     [90m// Get fresh data[39m
   [90m 180 |[39m                     [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-tab="'[39m [33m+[39m [33mTabsLabels[39m[33m.[39m[33mPERFORMANCE_SUMMARY[39m [33m+[39m [32m'"]'[39m)[33m;[39m
   [90m 181 |[39m                     [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m209:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  [0m [90m 207 |[39m                   
   [90m 208 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 209 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 210 |[39m                     [90m// If we already have data, use it[39m
   [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m[0m

Found [31m[1m2 errors[22m[39m in 1067 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (9d9bfa72dd75d610)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m178:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 176 |[39m                   
   [90m 177 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 178 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 179 |[39m                     [90m// Get fresh data[39m
   [90m 180 |[39m                     [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-tab="'[39m [33m+[39m [33mTabsLabels[39m[33m.[39m[33mPERFORMANCE_SUMMARY[39m [33m+[39m [32m'"]'[39m)[33m;[39m
   [90m 181 |[39m                     [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m209:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  [0m [90m 207 |[39m                   
   [90m 208 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 209 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 210 |[39m                     [90m// If we already have data, use it[39m
   [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m[0m

Found [31m[1m2 errors[22m[39m in 1464 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (2640dac571f29fdf)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m178:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 176 |[39m                   
   [90m 177 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 178 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 179 |[39m                     [90m// Get fresh data[39m
   [90m 180 |[39m                     [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-tab="'[39m [33m+[39m [33mTabsLabels[39m[33m.[39m[33mPERFORMANCE_SUMMARY[39m [33m+[39m [32m'"]'[39m)[33m;[39m
   [90m 181 |[39m                     [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m209:19[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  [0m [90m 207 |[39m                   
   [90m 208 |[39m                   [90m// Use a promise to wait for the data[39m
  [31m[1m>[22m[39m[90m 209 |[39m                   dataToDownload [33m=[39m [36mawait[39m [36mnew[39m [33mPromise[39m[33m<[39m[33many[39m[][33m>[39m((resolve) [33m=>[39m {
   [90m     |[39m                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 210 |[39m                     [90m// If we already have data, use it[39m
   [90m 211 |[39m                     [36mif[39m (forecastVarianceData [33m&&[39m forecastVarianceData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 212 |[39m                       console[33m.[39mlog([32m'Using existing variance data:'[39m[33m,[39m forecastVarianceData[33m.[39mlength[33m,[39m [32m'rows'[39m)[33m;[39m[0m

Found [31m[1m2 errors[22m[39m in 1296 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (dabcc4300723e5a8)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (c141a59d69832acd)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ec5363d4232fb171)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (456cb101c3c21ce9)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m177:63[22m[39m
[90mTS2339: [39mProperty '__reactInstance' does not exist on type 'Element'.
  [0m [90m 175 |[39m                   [90m// This is a more direct approach than waiting for state updates[39m
   [90m 176 |[39m                   [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-testid="allocatr-insights"]'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 177 |[39m                   [36mconst[39m allocatrInstance [33m=[39m allocatrComponent[33m?[39m[33m.[39m__reactInstance[33m;[39m
   [90m     |[39m                                                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 178 |[39m                   
   [90m 179 |[39m                   [90m// If direct component access isn't working, just use what we have[39m
   [90m 180 |[39m                   [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m195:52[22m[39m
[90mTS2339: [39mProperty '__ALLOCATR_DATA__' does not exist on type 'Window & typeof globalThis'.
  [0m [90m 193 |[39m                     [36mtry[39m {
   [90m 194 |[39m                       [90m// Call directly to handleDownloadExcel with dashboard data[39m
  [31m[1m>[22m[39m[90m 195 |[39m                       [36mconst[39m dashboardData [33m=[39m window[33m.[39m__ALLOCATR_DATA__ [33m||[39m [][33m;[39m
   [90m     |[39m                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 196 |[39m                       [36mif[39m (dashboardData [33m&&[39m dashboardData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 197 |[39m                         console[33m.[39mlog([32m'Found dashboard data in global variable:'[39m[33m,[39m dashboardData[33m.[39mlength[33m,[39m [32m'items'[39m)[33m;[39m
   [90m 198 |[39m                         handleDownloadExcel([0m

Found [31m[1m2 errors[22m[39m in 1606 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (322e856363d525b1)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m177:63[22m[39m
[90mTS2339: [39mProperty '__reactInstance' does not exist on type 'Element'.
  [0m [90m 175 |[39m                   [90m// This is a more direct approach than waiting for state updates[39m
   [90m 176 |[39m                   [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-testid="allocatr-insights"]'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 177 |[39m                   [36mconst[39m allocatrInstance [33m=[39m allocatrComponent[33m?[39m[33m.[39m__reactInstance[33m;[39m
   [90m     |[39m                                                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 178 |[39m                   
   [90m 179 |[39m                   [90m// If direct component access isn't working, just use what we have[39m
   [90m 180 |[39m                   [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m195:52[22m[39m
[90mTS2339: [39mProperty '__ALLOCATR_DATA__' does not exist on type 'Window & typeof globalThis'.
  [0m [90m 193 |[39m                     [36mtry[39m {
   [90m 194 |[39m                       [90m// Call directly to handleDownloadExcel with dashboard data[39m
  [31m[1m>[22m[39m[90m 195 |[39m                       [36mconst[39m dashboardData [33m=[39m window[33m.[39m__ALLOCATR_DATA__ [33m||[39m [][33m;[39m
   [90m     |[39m                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 196 |[39m                       [36mif[39m (dashboardData [33m&&[39m dashboardData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 197 |[39m                         console[33m.[39mlog([32m'Found dashboard data in global variable:'[39m[33m,[39m dashboardData[33m.[39mlength[33m,[39m [32m'items'[39m)[33m;[39m
   [90m 198 |[39m                         handleDownloadExcel([0m

Found [31m[1m2 errors[22m[39m in 996 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (67d1391778d71d66)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m177:63[22m[39m
[90mTS2339: [39mProperty '__reactInstance' does not exist on type 'Element'.
  [0m [90m 175 |[39m                   [90m// This is a more direct approach than waiting for state updates[39m
   [90m 176 |[39m                   [36mconst[39m allocatrComponent [33m=[39m document[33m.[39mquerySelector([32m'[data-testid="allocatr-insights"]'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 177 |[39m                   [36mconst[39m allocatrInstance [33m=[39m allocatrComponent[33m?[39m[33m.[39m__reactInstance[33m;[39m
   [90m     |[39m                                                               [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 178 |[39m                   
   [90m 179 |[39m                   [90m// If direct component access isn't working, just use what we have[39m
   [90m 180 |[39m                   [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m195:52[22m[39m
[90mTS2339: [39mProperty '__ALLOCATR_DATA__' does not exist on type 'Window & typeof globalThis'.
  [0m [90m 193 |[39m                     [36mtry[39m {
   [90m 194 |[39m                       [90m// Call directly to handleDownloadExcel with dashboard data[39m
  [31m[1m>[22m[39m[90m 195 |[39m                       [36mconst[39m dashboardData [33m=[39m window[33m.[39m__ALLOCATR_DATA__ [33m||[39m [][33m;[39m
   [90m     |[39m                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 196 |[39m                       [36mif[39m (dashboardData [33m&&[39m dashboardData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 197 |[39m                         console[33m.[39mlog([32m'Found dashboard data in global variable:'[39m[33m,[39m dashboardData[33m.[39mlength[33m,[39m [32m'items'[39m)[33m;[39m
   [90m 198 |[39m                         handleDownloadExcel([0m

Found [31m[1m2 errors[22m[39m in 2067 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 961 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (51980d8ac91dfef1)
[36mType-checking in progress...[39m
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (b846600a71e485e2)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m248:73[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 246 |[39m                       [90m// Force refresh data from AllocatrInsights component[39m
   [90m 247 |[39m                       [36mconst[39m insightsComponent [33m=[39m document[33m.[39mquerySelector([32m'.allocatr-insights'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m     |[39m                                                                         [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
   [90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m 251 |[39m                         [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m250:49[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m     |[39m                                                 [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 251 |[39m                         
   [90m 252 |[39m                         [90m// Try again with refreshed data[39m
   [90m 253 |[39m                         [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

Found [31m[1m2 errors[22m[39m in 1743 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (1c19e9a052af78c6)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m248:73[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 246 |[39m                       [90m// Force refresh data from AllocatrInsights component[39m
   [90m 247 |[39m                       [36mconst[39m insightsComponent [33m=[39m document[33m.[39mquerySelector([32m'.allocatr-insights'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m     |[39m                                                                         [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
   [90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m 251 |[39m                         [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m250:49[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m     |[39m                                                 [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 251 |[39m                         
   [90m 252 |[39m                         [90m// Try again with refreshed data[39m
   [90m 253 |[39m                         [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

Found [31m[1m2 errors[22m[39m in 1221 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (21cd86a0f135507f)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m248:73[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 246 |[39m                       [90m// Force refresh data from AllocatrInsights component[39m
   [90m 247 |[39m                       [36mconst[39m insightsComponent [33m=[39m document[33m.[39mquerySelector([32m'.allocatr-insights'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m     |[39m                                                                         [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
   [90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m 251 |[39m                         [0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m250:49[22m[39m
[90mTS2339: [39mProperty 'refresh' does not exist on type 'Element'.
  [0m [90m 248 |[39m                       [36mif[39m (insightsComponent [33m&&[39m [36mtypeof[39m insightsComponent[33m.[39mrefresh [33m===[39m [32m'function'[39m) {
   [90m 249 |[39m                         console[33m.[39mlog([32m'Found AllocatrInsights component, forcing refresh...'[39m)[33m;[39m
  [31m[1m>[22m[39m[90m 250 |[39m                         [36mawait[39m insightsComponent[33m.[39mrefresh()[33m;[39m
   [90m     |[39m                                                 [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 251 |[39m                         
   [90m 252 |[39m                         [90m// Try again with refreshed data[39m
   [90m 253 |[39m                         [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {[0m

Found [31m[1m2 errors[22m[39m in 2410 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 962 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (76842d97151bb08b)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 962 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (34d2b3de70ec1582)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 965 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (1536e516b9778557)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m80:21[22m[39m
[90mTS2345: [39mArgument of type '{ id: any; name: any; quarter: { line1Projection: any; lastYear: any; actualOrForecast: any; idPercentage: any; vsLY: { value: any; }; vsProjection: { value: any; }; }; banners: { id: string; name: string; quarter: {}; departments: never[]; }[]; }' is not assignable to parameter of type 'never'.
  [0m [90m 78 |[39m         }[33m;[39m
   [90m 79 |[39m         divisions[divId] [33m=[39m division[33m;[39m
  [31m[1m>[22m[39m[90m 80 |[39m         result[33m.[39mpush(division)[33m;[39m
   [90m    |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 81 |[39m       }
   [90m 82 |[39m     }
   [90m 83 |[39m     [90m// Check if it's a department (format: "3070000 - Tobacco")[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m90:18[22m[39m
[90mTS2339: [39mProperty 'banners' does not exist on type 'never'.
  [0m [90m 88 |[39m       [90m// Add to each division's default banner[39m
   [90m 89 |[39m       result[33m.[39mforEach(division [33m=>[39m {
  [31m[1m>[22m[39m[90m 90 |[39m         division[33m.[39mbanners[[35m0[39m][33m.[39mdepartments[33m.[39mpush({
   [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 91 |[39m           id[33m:[39m deptId[33m,[39m
   [90m 92 |[39m           name[33m:[39m deptName[33m,[39m
   [90m 93 |[39m           quarter[33m:[39m {[0m

Found [31m[1m2 errors[22m[39m in 1610 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 965 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (bd0f54bb91ec1a08)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m80:21[22m[39m
[90mTS2345: [39mArgument of type '{ id: any; name: any; quarter: { line1Projection: any; lastYear: any; actualOrForecast: any; idPercentage: any; vsLY: { value: any; }; vsProjection: { value: any; }; }; banners: { id: string; name: string; quarter: {}; departments: never[]; }[]; }' is not assignable to parameter of type 'never'.
  [0m [90m 78 |[39m         }[33m;[39m
   [90m 79 |[39m         divisions[divId] [33m=[39m division[33m;[39m
  [31m[1m>[22m[39m[90m 80 |[39m         result[33m.[39mpush(division)[33m;[39m
   [90m    |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 81 |[39m       }
   [90m 82 |[39m     }
   [90m 83 |[39m     [90m// Check if it's a department (format: "3070000 - Tobacco")[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m90:18[22m[39m
[90mTS2339: [39mProperty 'banners' does not exist on type 'never'.
  [0m [90m 88 |[39m       [90m// Add to each division's default banner[39m
   [90m 89 |[39m       result[33m.[39mforEach(division [33m=>[39m {
  [31m[1m>[22m[39m[90m 90 |[39m         division[33m.[39mbanners[[35m0[39m][33m.[39mdepartments[33m.[39mpush({
   [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 91 |[39m           id[33m:[39m deptId[33m,[39m
   [90m 92 |[39m           name[33m:[39m deptName[33m,[39m
   [90m 93 |[39m           quarter[33m:[39m {[0m

Found [31m[1m2 errors[22m[39m in 1037 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 965 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (5e82e2c3e06d5087)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m80:21[22m[39m
[90mTS2345: [39mArgument of type '{ id: any; name: any; quarter: { line1Projection: any; lastYear: any; actualOrForecast: any; idPercentage: any; vsLY: { value: any; }; vsProjection: { value: any; }; }; banners: { id: string; name: string; quarter: {}; departments: never[]; }[]; }' is not assignable to parameter of type 'never'.
  [0m [90m 78 |[39m         }[33m;[39m
   [90m 79 |[39m         divisions[divId] [33m=[39m division[33m;[39m
  [31m[1m>[22m[39m[90m 80 |[39m         result[33m.[39mpush(division)[33m;[39m
   [90m    |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 81 |[39m       }
   [90m 82 |[39m     }
   [90m 83 |[39m     [90m// Check if it's a department (format: "3070000 - Tobacco")[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m90:18[22m[39m
[90mTS2339: [39mProperty 'banners' does not exist on type 'never'.
  [0m [90m 88 |[39m       [90m// Add to each division's default banner[39m
   [90m 89 |[39m       result[33m.[39mforEach(division [33m=>[39m {
  [31m[1m>[22m[39m[90m 90 |[39m         division[33m.[39mbanners[[35m0[39m][33m.[39mdepartments[33m.[39mpush({
   [90m    |[39m                  [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 91 |[39m           id[33m:[39m deptId[33m,[39m
   [90m 92 |[39m           name[33m:[39m deptName[33m,[39m
   [90m 93 |[39m           quarter[33m:[39m {[0m

Found [31m[1m2 errors[22m[39m in 1917 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 965 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (e4b6870772680d23)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 965 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (bbd3b3101ffc162d)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (b6d70e0f52c2dfac)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ca286f0ad999e4de)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (7423003ccae49a0c)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:62[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:100[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m306:51[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
  [31m[1m>[22m[39m[90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m     |[39m                                                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m310:21[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m
  [31m[1m>[22m[39m[90m 310 |[39m                     dataToUse [33m=[39m performanceSummaryData[33m;[39m
   [90m     |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 311 |[39m                     console[33m.[39mlog([32m"Using data from state:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 312 |[39m                   }
   [90m 313 |[39m                   [0m

Found [31m[1m4 errors[22m[39m in 2979 ms.
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /node_modules_file-saver_dist_FileSaver_min_js.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (8b76025ada9a023c)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:62[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:100[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m306:51[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
  [31m[1m>[22m[39m[90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m     |[39m                                                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m310:21[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m
  [31m[1m>[22m[39m[90m 310 |[39m                     dataToUse [33m=[39m performanceSummaryData[33m;[39m
   [90m     |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 311 |[39m                     console[33m.[39mlog([32m"Using data from state:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 312 |[39m                   }
   [90m 313 |[39m                   [0m

Found [31m[1m4 errors[22m[39m in 1608 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (c50411a4b329e9ca)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:62[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:100[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m306:51[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
  [31m[1m>[22m[39m[90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m     |[39m                                                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m310:21[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m
  [31m[1m>[22m[39m[90m 310 |[39m                     dataToUse [33m=[39m performanceSummaryData[33m;[39m
   [90m     |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 311 |[39m                     console[33m.[39mlog([32m"Using data from state:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 312 |[39m                   }
   [90m 313 |[39m                   [0m

Found [31m[1m4 errors[22m[39m in 2711 ms.
<i> [1m[32m[webpack-dev-server] Gracefully shutting down. To force exit, press ^C again. Please wait...[39m[22m
