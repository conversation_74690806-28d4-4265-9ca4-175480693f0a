import React, { forwardRef } from 'react';
import { DivisionData } from '../../interfaces/allocatr-insights';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { renderAllRows } from './utils/tableCell';

interface AllocatrDivisionRowProps {
  division: DivisionData;
  expanded: boolean;
  onToggle: (id: string) => void;
  isQuarterActualUsed?: boolean;
}

const AllocatrDivisionRow = forwardRef<HTMLTableRowElement, AllocatrDivisionRowProps>(({ division, expanded, onToggle, isQuarterActualUsed }, ref) => (
  <tr ref={ref} key={`division-$division.id`} className="division-row bg-[#C4DFF8]">
    <td>
        <button className="cursor-pointer" onClick={() => onToggle(division.id)}>
          {expanded ? <ChevronUp size={14}/> : <ChevronDown size={14} />}
        </button>
    </td>
    <td className="division-cell">
      <div className="flex items-center justify-start h-full">
        <div className="max-w-[180px] min-w-0 flex items-center">
          <span className="division-name-cell font-bold block w-full min-w-0 break-words" title={division.name}>
           {division.id} - {division.name}
          </span>
        </div>
      </div>
    </td>
    {division.quarter
      ? renderAllRows(division.quarter, isQuarterActualUsed)
      : Array.from({ length: 29 }).map((_, i) => <td key={i}></td>)}
  </tr>
));

export default AllocatrDivisionRow;