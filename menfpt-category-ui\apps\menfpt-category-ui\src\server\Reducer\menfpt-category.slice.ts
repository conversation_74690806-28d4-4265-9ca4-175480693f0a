import { getStorageKeyByRoute } from '../../features/worksheetFilter/worksheetFilterUtils';
import { createGenericSlice } from '../../rtk/rtk-slice';

export const menfptCategorySlice = createGenericSlice({
  name: 'menfptCategory_rn',
  initialState: { status: 'loading', data: {} },
})({
  setMenfptCategory(state, { payload }) {
    state.data = payload;
  },
});

export const { setMenfptCategory } = menfptCategorySlice.actions;

export const workSheetFilterListSlice = createGenericSlice({
  name: "workSheetFilterList_rn",
  initialState: { status: "loading", data: {} },
})({
   setWorkSheetFilterList(state, { payload }) {
    state.data = payload;
  },
});

export const { setWorkSheetFilterList } = workSheetFilterListSlice.actions;
export const displayDateSlice = createGenericSlice({
  name: 'displayDate_rn',
  initialState: { status: 'loading', data: {} },
})({
  setDisplayDate(state, { payload }) {
    state.data = payload;
  },
});

export const { setDisplayDate } = displayDateSlice.actions;

export const userInfoSlice = createGenericSlice({
  name: 'userInfo_rn',
  initialState: { status: 'loading', data: {} },
})({
  setUserInfo(state, { payload }) {
    state.data = payload;
  },
});
export const { setUserInfo } = userInfoSlice.actions;

export const publishHistorySlice = createGenericSlice({
  name: 'publishHistory_rn',
  initialState: { status: 'loading', data: {} },
})({
  setPublishHistory(state, { payload }) {
    state.data = payload;
  },
});

export const { setPublishHistory } = publishHistorySlice.actions;

export const appliedFilterSlice = createGenericSlice({
  name: 'appliedFilter_rn',
  initialState: { status: 'loading', data: {} },
})({
  setAppliedFilter(state, { payload }) {
    state.data = { ...payload, filterPg: getStorageKeyByRoute() };
  },
});
export const { setAppliedFilter } = appliedFilterSlice.actions;

export const adjustmentWorkSheetFilterSlice = createGenericSlice({
  name: 'adjustmentWorkSheetFilter_rn',
  initialState: { status: 'loading', data: {} },
})({
  setAdjustmentWorkSheetFilter(state, { payload }) {
    state.data = payload;
  },
});

export const { setAdjustmentWorkSheetFilter } = adjustmentWorkSheetFilterSlice.actions;

export const featureFlags = createGenericSlice({
  name: 'featureFlags_rn',
  initialState: { status: 'loading', data: {dashboardFilter: false} },
})({
  setFeatureFlags(state, { payload }) {
    state.data = payload;
  },
});

export const { setFeatureFlags } = featureFlags.actions;

export const quartersSlice = createGenericSlice({
  name: 'quartersInYr_rn',
  initialState: { status: 'loading', data: [] },
})({
  setQuarters(state, { payload }) {
    state.data = payload;
  },
});

export const { setQuarters } = quartersSlice.actions;

export const alertState = createGenericSlice({
  name: 'alertState_rn',
  initialState: { status: 'loading', data: {error: false, success: false}},
})({
  setAlertState(state, { payload }) {
    state.data = payload;
  },
});

export const {setAlertState} = alertState.actions;

export const roleMappingInfo = createGenericSlice({
  name: 'roleMappingInfo_rn',
  initialState: { status: 'loading', data: {}},
})({
  setRoleMappingInfo(state, { payload }) {
    state.data = payload;
  },
});

export const {setRoleMappingInfo} = roleMappingInfo.actions;

export const editAdjustmentPermission = createGenericSlice({
  name: 'editAdjustmentPermission_rn',
  initialState: { status: 'loading', data: {disabled: false}},
})({
  setEditAdjustmentPermission(state, { payload }) {
    state.data = payload;
  },
});

export const {setEditAdjustmentPermission} = editAdjustmentPermission.actions;

export const helpPdfUrl = createGenericSlice({
  name: 'helpPdfUrl_rn',
  initialState: { status: 'loading', data: {error: false, success:false}},
})({
  setHelpPdfUrl(state, { payload }) {
    state.data = payload;
  }
});
export const {setHelpPdfUrl} = helpPdfUrl.actions;