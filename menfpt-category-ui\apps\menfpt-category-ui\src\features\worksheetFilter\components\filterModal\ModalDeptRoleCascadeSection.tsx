// import Search from '@albertsons/uds/molecule/Search';
// import React, { useEffect } from 'react';
// import { useDispatch } from 'react-redux';
// import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
// import "../../../searchIcon.scss";
// import { setRoleCascadeSearchFocus } from '../../roleCascadeSearchFocus.slice';
// import { worksheetFilterConfig } from '../../worksheetFilterConfig';
// import { useDisplayDeptRoleCascade } from '../../worksheetFilterRouteUtils';
// import { RoleSearchSuggestions } from '../suggestions/RoleSearchSuggestions';
// import { clearCascadeSearchSelectedItem, setSelectedDepartmentFromSuggestion } from '../suggestions/deptRoleSuggestions.slice';
// import AsmRoleUsersList from '../roles/asmRoleUsersList';
// import { setAsmDataForSelectedSm, setSelectedAsm } from '../roles/rolesFilter.slice'; // Added import
// import { getAsmListForSelectedSM, getSmDataByDivisionAndDept } from '../roles/rolesUtils';
// import styles from './ModalDeptRoleCascadeSection.module.scss';


// interface ModalDeptRoleCascadeSectionProps {
//   isSuggestionsVisible: boolean;
//   setIsSuggestionsVisible: (visible: boolean) => void;
//   isSearchFocused: boolean;
//   setIsSearchFocused: (focused: boolean) => void;
//   searchContainerRef: React.RefObject<HTMLDivElement>;
//   getSearchValue: () => string;
//   getSearchHandler: () => (query: string) => void;
//   activeTabInFilter: any;
// }

// const ModalDeptRoleCascadeSection: React.FC<ModalDeptRoleCascadeSectionProps> = ({
//   isSuggestionsVisible,
//   setIsSuggestionsVisible,
//   isSearchFocused,
//   setIsSearchFocused,
//   searchContainerRef,
//   getSearchValue,
//   getSearchHandler,
//   activeTabInFilter,
// }) => {
//   console.log('sar-ModalDeptRoleCascadeSection-render', JSON.stringify({
//     searchValue: getSearchValue(),
//     isSuggestionsVisible,
//     isSearchFocused,
//     activeTabInFilter
//   }));
//   const { data } = useSelectorWrap('deptRoleSuggestions_rn');
//   const suggestions = data?.suggestions || [];
//   const dispatch = useDispatch();
//   const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();
//   const departmentsState = useSelectorWrap('departments_rn');
//   const departments = departmentsState.data || [];
//   const minChars = worksheetFilterConfig.deptRoleCascadeSearchMinChars;

//   // Helper: is the search value long enough for suggestions?
//   const isMinCharsMet = getSearchValue().length >= minChars;
//   // Helper: should we show the suggestions dropdown at all?
//   const showRoleSearchSuggestions = isSuggestionsVisible && activeTabInFilter[0] !== 'desk';
//   // Helper: should we show the hint box?
//   const isInitialFocus = isSearchFocused && !isMinCharsMet;

//   const handleSuggestionSelect = (item: any) => {
//     console.log('sar-handleSuggestionSelect', JSON.stringify(item));
//     if (item?.originalType === 'department' && item?.id) {
//       console.log("def-dispatch-dept", JSON.stringify({ itemId: item.id, itemType: 'department', isCascade: isDisplayDeptRoleCascade }));
//       // Select department only
//       console.log('[handleSuggestionSelect] Department selection triggered:', item.id);
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: item.id, itemType: 'department', isCascade: isDisplayDeptRoleCascade }));
//       console.log('[SuggestionSelect] Department selected:', item.id);
//     } else if (item?.originalType === 'SM' && item?.id) {
//       // Parse deptId and sm from id (format: deptId-sm)
//       const [deptId, ...smParts] = item.id.split('-');
//       const sm = smParts.join('-');
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: deptId, itemType: 'department', isCascade: isDisplayDeptRoleCascade }));
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: item.id, itemType: 'sm', isCascade: isDisplayDeptRoleCascade }));
//       // Get full ASM list for this SM
//       const smData = getSmDataByDivisionAndDept({ selectedDeptId: deptId, deptListForSelectedDivision: departments });
//       const asmForSelectedSm = getAsmListForSelectedSM({ selectedSm: sm, smData });
//       dispatch(setAsmDataForSelectedSm({ selectedSm: sm, asmForSelectedSm, smDataForSelectedDept: smData }));
//       console.log('[SuggestionSelect] SM selected:', { deptId, sm, asmForSelectedSm, rawId: item.id });
//     } else if (item?.originalType === 'ASM' && item?.id) {
//       // Parse deptId, sm, asm from id (format: deptId-sm-asm)
//       const [deptId, ...rest] = item.id.split('-');
//       const sm = rest.slice(0, rest.length - 1).join('-');
//       const asm = rest[rest.length - 1];
//       // Get full ASM list for this SM
//       const smData = getSmDataByDivisionAndDept({ selectedDeptId: deptId, deptListForSelectedDivision: departments });
//       const asmForSelectedSm = getAsmListForSelectedSM({ selectedSm: sm, smData });
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: deptId, itemType: 'department', isCascade: isDisplayDeptRoleCascade }));
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: `${deptId}-${sm}`, itemType: 'sm', isCascade: isDisplayDeptRoleCascade }));
//       dispatch(setSelectedDepartmentFromSuggestion({ itemId: item.id, itemType: 'ASM', isCascade: isDisplayDeptRoleCascade }));
//       dispatch(setAsmDataForSelectedSm({ selectedSm: sm, asmForSelectedSm, smDataForSelectedDept: smData }));
//       dispatch(setSelectedAsm([asm]));
//       console.log('[SuggestionSelect] ASM selected:', { deptId, sm, asm, asmForSelectedSm, rawId: item.id });
//     }
//   };

//   // Add robust blur handler
//   const handleBlur = (e: React.FocusEvent<HTMLDivElement>) => {
//     console.log('sar-handleBlur', JSON.stringify({
//       relatedTarget: e.relatedTarget ? (e.relatedTarget as HTMLElement).outerHTML : null
//     }));
//     if (
//       searchContainerRef.current &&
//       e.relatedTarget &&
//       searchContainerRef.current.contains(e.relatedTarget as Node)
//     ) {
//       // Focus is still within the container, do nothing
//       return;
//     }
//     setIsSearchFocused(false);
//     setIsSuggestionsVisible(false);
//   };

//   return (
//     <div className="min-h-0 flex flex-col  min-w-[240px] px-5 bg-white pt-[43px]">
//       <div
      
//         className="relative"
//         tabIndex={-1}
//         onFocus={() => {
//           setIsSearchFocused(true);
//           setIsSuggestionsVisible(true);
//           dispatch(setRoleCascadeSearchFocus(true));
//         }}
        
//       >
//         {(() => {
//           const widthClass = `${styles['modal-cascade-search']} ${isSearchFocused ? styles['modal-cascade-search--focused'] : ''}`;
//           return (
//             <>
//               <div className={`absolute right-0 ${widthClass}`}   ref={searchContainerRef}>
//                 <Search 
//                   value={getSearchValue()} 
//                   noClear = {true}
//                   onChange={getSearchHandler()} 
//                   className={`${widthClass} searchIconLeft`}
//                   onBlur={e => {
//                     handleBlur(e);
//                    setTimeout(() => {
//                       dispatch(setRoleCascadeSearchFocus(false));
//                     }, 100);
//                   }}
//                 />
//               </div>
//               {showRoleSearchSuggestions && (
//                   <RoleSearchSuggestions
//                     isSearchInitialFocus={isInitialFocus}
//                     value={getSearchValue()}
//                     onChange={getSearchHandler()}
//                     suggestions={isMinCharsMet ? suggestions : []}
//                     onSuggestionSelect={handleSuggestionSelect}
//                     className={widthClass}
//                   />
//               )}
//             </>
//           );
//         })()}
//       </div>
//       {activeTabInFilter[0] === 'department' && (
//         <div className="mt-2.5">
//           <AsmRoleUsersList activeSearchQuery={getSearchValue()} />
//         </div>
//       )}
//     </div>
//   );
// };

// export default ModalDeptRoleCascadeSection; 