import React, { useState, useRef, useEffect } from 'react';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import './Facilities.css'; 
import InfoTooltip from '../InfoTooltip';
import { Info, Pen } from 'lucide-react'; 
import NewFacilitiesCard from './NewFacilities'; 
import mockData from './MockData.json';
import FacilitiesTableCard from './FacilitiesTableCard';
import KeeperFacilities from './KeeperFacilities';
import IdFacilities from './IdFacilities';
import DynamicCardBottom from './DynamicCardBottom';
import DatePicker from '@albertsons/uds/molecule/DatePicker';

type Facility = {
  id: number;
  facility: string;
  assetType: string;
  siteName: string;
  posStartDate: string;
  notificationDate: string;
  excluded: string;
  posCloseDate: string;
  lastEditedBy: string;
};

const items: Facility[] = mockData.closingFacilities;

const Facilities = () => {
  const [datePickerOpenId, setDatePickerOpenId] = useState<number | null>(null);
  const [facilityItems, setFacilityItems] = useState<Facility[]>(items);
  const [datePickerPosition, setDatePickerPosition] = useState<{ left: number; top: number }>({ left: 0, top: 0 });
  const datePickerRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const cellRefs = useRef<{ [key: number]: HTMLSpanElement | null }>({});

  useEffect(() => {
    if (datePickerOpenId === null) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target as Node)
      ) {
        setDatePickerOpenId(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [datePickerOpenId]);

  useEffect(() => {
    if (datePickerOpenId === null) return;
    const cell = cellRefs.current[datePickerOpenId];
    const container = containerRef.current;
    if (cell && container) {
      const cellRect = cell.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      setDatePickerPosition({
        left: cellRect.left - containerRect.left,
        top: cellRect.top - containerRect.top,
      });
    }
  }, [datePickerOpenId]);

  const handlePenClick = (id: number) => {
    setDatePickerOpenId(id);
  };

  const handleDateChange = ([date]: Date[], id: number) => {
    const formatDate = (d: Date) => {
      const mm = String(d.getMonth() + 1).padStart(2, '0');
      const dd = String(d.getDate()).padStart(2, '0');
      const yyyy = d.getFullYear();
      return `${mm}/${dd}/${yyyy}`;
    };
    setFacilityItems((prev) =>
      prev.map((facility) =>
        facility.id === id
          ? { ...facility, excluded: date ? formatDate(date) : '' }
          : facility
      )
    );
    setDatePickerOpenId(null);
  };

  const columns: Column<Facility>[] = [
    {
      id: 'facility',
      label: (
        <div className="facility-header"> 
          <span>Facility</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Number and Name of facility'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'facility',
      hideFromMenu: true,
    },
    {
      id: 'assetType',
      label: (
        <div className="facility-header">
          <span>Asset Type</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Type of facility'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'assetType',
      hideFromMenu: true,
    },
    {
      id: 'siteName',
      label: (
        <div className="facility-header"> 
          <span>Site Name and A...</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Facility name and address'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: (item: Facility) => (
        <span className="flex items-center min-h-[48px] px-0 py-0 w-[140px]">
          <span className="truncate w-full" title={item.siteName}>
            {item.siteName}
          </span>
        </span>
      ),
      hideFromMenu: true,
    },
    {
      id: 'posStartDate',
      label: (
        <div className="facility-header">
          <span>POS Start date</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Date Point Of Sale is operational'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'posStartDate',
      hideFromMenu: true,
    },
    {
      id: 'notificationDate',
      label: (
        <div className="facility-header">
          <span>Notification d...</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Legal notification date of physical facility closing'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'notificationDate',
      hideFromMenu: true,
    },
    {
      id: 'excluded',
      label: (
        <div
          className="facility-header flex items-center min-h-[48px] p-0 m-0"
        >
          <span className="flex items-center p-0 m-0">
            Excluded fro...
          </span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Date at which a facility is excluded from financial reporting'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: (item: Facility) => (
        <span
          ref={el => (cellRefs.current[item.id] = el)}
          className="flex items-center min-h-[48px] p-0 m-0 h-full"
        >
          {item.excluded}
          <Pen
            size={14}
            className="ml-2 text-[#1B6EBB] cursor-pointer"
            onClick={() => handlePenClick(item.id)}
          />
        </span>
      ),
      hideFromMenu: true,
    },
    {
      id: 'posCloseDate',
      label: (
        <div className="facility-header"> 
          <span>POS Close...</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'Date at which Point Of Sale\nis no longer operational and facility is physically closed'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'posCloseDate',
      hideFromMenu: true,
    },
    {
      id: 'lastEditedBy',
      label: (
        <div className="facility-header">
          <span>Last Edited by</span>
          <span className="ml-2 data-popper-bottom-margin">
            <span className='tool-tip-initilizer-top'></span>
            <InfoTooltip
              label={'User, date and time of last edit'}
              icon={<Info size={16} color="#1B6EBB" />}
              anchor="top"
              variant="dark"
              className="uds-tooltip-top"
            />
          </span>
        </div>
      ),
      value: 'lastEditedBy',
      hideFromMenu: true,
    },
  ];

  return (
    <div ref={containerRef} className="relative">
      {datePickerOpenId !== null && (
        <div
          ref={datePickerRef}
          className="absolute z-[9999] bg-white shadow-lg p-4 rounded-lg"
          style={{
            left: datePickerPosition.left,
            top: datePickerPosition.top + 50,
          }}
        >
          <DatePicker
            onChange={(dateArr) => handleDateChange(dateArr, datePickerOpenId)}
          />
        </div>
      )}

      <FacilitiesTableCard
        title="Closing Facility - 3"
        updatedText="Updated 2 days ago"
        columns={columns}
        items={facilityItems}
        itemKey="id"
        footer={
          <DynamicCardBottom
            facilityType="closing"
            count={facilityItems.length}
            showViewMore={false}
            viewMoreCount={0}
            onViewMore={() => {}}
          />
        }
      />
    </div>
  );
};

export const FacilitiesPage = () => (
  <div className="w-screen min-h-screen bg-[#F1F4F9]">
    <div className="flex flex-col  px-4">
      <Facilities />
      <NewFacilitiesCard />
      <KeeperFacilities />
      <IdFacilities />
    </div>
  </div>
);

export default FacilitiesPage;
