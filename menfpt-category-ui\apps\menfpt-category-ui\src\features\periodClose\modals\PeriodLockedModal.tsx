import Button from '@albertsons/uds/molecule/Button';

export const PeriodLockedModal = ({
  onClose,
  quarterLabel,
}: {
  onClose: () => void;
  quarterLabel: string;
}) => {
  return (
    <div data-testid="period-locked-modal" className="mx-[60px]">
      <div className="text-center select-none font-bold text-[28px] mt-[74px]  mb-[10px]">
        Attention, please read this carefully!
      </div>
      <div className=" text-[#2b303c] text-center text-xl leading-7">
        {quarterLabel} has been closed for any edits and the metrics are
        available only to view
      </div>
      <div className="flex items-center justify-center w-full  mt-[24px] mb-20">
        <Button
          width={200}
          size="lg"
          className="ml-2 whitespace-nowrap"
          onClick={onClose}
          data-testid="period-locked-ok-button"
        >
          OK, I understand
        </Button>
      </div>
    </div>
  );
};
