import Menu from '@albertsons/uds/molecule/Menu';
import Modal from '@albertsons/uds/molecule/Modal';
import Spinner from '@albertsons/uds/molecule/Spinner';
import Table from '@albertsons/uds/molecule/Table';
import { Button } from '@albertsons/uds/molecule/Button';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import clsx from 'clsx';
import { ChevronsDownUp, ChevronsUpDown, EllipsisVertical } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { formatToPST } from '../../../../libs/utils/src';
import StatusCellIcon from './StatusCellIcon';
import './udsTable.css';
import "./udsTable.scss";
import { useWeeksToBeDisabledForQuarter } from '../features/periodClose/periodClose.flags';
import ForecastTimestampsFooter from './ForecastTimestampsFooter';
import { Info } from 'lucide-react';
import InfoTooltip  from '../components/InfoTooltip'
import { useEnvVariables } from '../../src/features/envVariables';

interface Forecast {
  aggregatedLevel: string;
  subRow: string;
  mainRow: string;
  line1PublicToSalesNbr: number;
  line1PublicToSalesPct: number;
  line5BookGrossProfitNbr: number;
  line5BookGrossProfitPct: number;
  line5MarkDownsNbr: number;
  line5MarkDownsPct: number;
  line5ShrinkNbr: number;
  line5ShrinkPct: number;
  line5RealGrossProfitNbr: number;
  line5RealGrossProfitPct: number;
  line6SuppliesPackagingNbr: number;
  line7RetailsAllowancesNbr: number;
  line7RetailsAllowancesPct: number;
  line7RetailsSellingAllowancesNbr: number;
  line7RetailsSellingAllowancesPct: number;
  line7RetailsNonSellingAllowancesNbr: number;
  line7RetailsNonSellingAllowancesPct: number;
  line8RealGrossProfitNbr: number;
  line8RealGrossProfitPct: number;
  fiscalYearNbr?: number;
  fiscalQuarterNbr?: number;
  fiscalPeriodNbr?: number;
  fiscalWeekNbr?: number;
  fiscalWeekEnding?: string;
  comment: string;
  forecastType: string;
}

type Props = {
  data: Map<string, Forecast[]>;
  currentWeek: Number;
  resetWeek: (fiscalWeekNbr: number) => void;
  resetPermission: boolean;
};

type CellAttributes = {
  subRows: Forecast[];
  valueProperty: string;
  expanded?: boolean;
  percentageProperty?: string;
  showCurrency?: boolean;
};

const VariantFields: string[] = ['ACT to PROJ', 'FCST to PROJ', 'WoW FCST var'];

const MERCH_FORECAST = "Merch. Forecast";

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  currencySign: 'accounting',
  maximumFractionDigits: 0,
});

const toCurrency = (num: number) => {
  return formatter.format(num);
};
let projectionSourceTs: string | undefined;
let dsForecastUpdatedTs: string | undefined;
let dsForecastSourceTs: string | undefined;
let lastYearActualsUpdatedTs: string | undefined;

const UdsTable: React.FC<Props & { onEditForecast: (weekNumber: any) => void }> = ({ data = new Map(), currentWeek = 0, resetWeek, resetPermission, onEditForecast }) => {
  const weeksToBeDisabled = useWeeksToBeDisabledForQuarter();
  const { data: envVariables } = useEnvVariables();
  const lineOne = envVariables?.GetEnvVariables?.variables?.LINE1_TOOLTIP_TEXT;
  const lineFive = envVariables?.GetEnvVariables?.variables?.LINE5_TOOLTIP_TEXT;
  const lineSix = envVariables?.GetEnvVariables?.variables?.LINE6_TOOLTIP_TEXT;
  const lineSeven = envVariables?.GetEnvVariables?.variables?.LINE7_TOOLTIP_TEXT;
  const lineEight = envVariables?.GetEnvVariables?.variables?.LINE8_TOOLTIP_TEXT;
  data.forEach((forecastArr) => {
    forecastArr.forEach((item) => {
      if (item.aggregatedLevel === "Quarter to Date") {
        if (item.subRow === "Projection") {
          projectionSourceTs = (item as any).sourceTs;
        }
        if (item.subRow === "DS Forecast") {
          dsForecastUpdatedTs = (item as any).updatedTs;
          dsForecastSourceTs = (item as any).sourceTs;
        }
        if (item.subRow === "Actual to Date") {
          lastYearActualsUpdatedTs = (item as any).updatedTs;
        }
      }
    });
  });

  const timestampFooter = (
    <ForecastTimestampsFooter
      projectionSourceTs={projectionSourceTs}
      dsForecastSourceTs={dsForecastUpdatedTs}
      lastYearActualsUpdatedTs={lastYearActualsUpdatedTs}
      formatToPST={formatToPST}
    />
  );
  const buttonRef = useRef<Set<any>>(new Set());
  const [currentWeekExpandButton, setCurrentWeekExpandButton] = useState('');
  const [expandAllDataAttributes, setExpandAllDataAttributes] = useState(false);
  const [resetLoader, setResetLoader] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [modalItem, setModalItem] = useState<any>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleClick = () => {
    buttonRef.current.forEach((button: any) => {
      if (
        button &&
        button.className.includes(expandAllDataAttributes.toString())
      ) {
        if (
          expandAllDataAttributes &&
          !button.className.includes('current-week')
        ) {
          button.click();
        }
        if (!expandAllDataAttributes) {
          button.click();
        }
      }
    });
    setExpandAllDataAttributes(!expandAllDataAttributes);
  };

  const getColor = (cellLabel, cellValue) => {
    if (VariantFields.includes(cellLabel)) {
      return cellValue > 0 ? 'text-[#105F0E]' : 'text-[#BF2912]';
    } else if (cellLabel === MERCH_FORECAST) {
      return 'text-[#1B6EBB]'
    }
    return '';
  };
  const renderDataColumn = ({
    subRows,
    valueProperty,
    showCurrency = true,
    percentageProperty = '',
    expanded,
  }: CellAttributes) => {
    return (
      <div className="grid gap-3 py-2 grid-cols-1">
        {(subRows?.length > 0) && subRows.filter((item: any) => item.subRow !== "Base").slice(0, 2)
        .map((i: any, index: number) => (
          <div
            key={`${index}-${i?.mainRow}-${i?.subRow}`}
            className={clsx("flex justify-between 2xl:justify-start 2xl:gap-x-2 mx-1 items-start h-full",
            )}
          >
            {/* Main value part */}
            <div className={`flex flex-col text-sm/16 font-normal`}>
              <span className={`${getColor(i?.subRow, i[valueProperty])} flex items-center`}>
                {i?.[valueProperty] !== null && i?.[valueProperty] !== undefined
                  ? showCurrency
                    ? toCurrency(i?.[valueProperty])
                    : i?.[valueProperty]
                  : ''}
              </span>

              {/* Only if it's Merch Forecast */}
              {i?.subRow === MERCH_FORECAST && valueProperty === 'line7RetailsAllowancesNbr' && (
                <div className="flex flex-col text-xs text-black mt-0.5">
                  <span
                    className="leading-none tooltip-text"
                    title={i?.line7RetailsSellingAllowancesNbr !== null && i?.line7RetailsSellingAllowancesNbr !== undefined
                      ? `Selling: ${toCurrency(i?.line7RetailsSellingAllowancesNbr)}`
                      : ''}
                  >
                    {i?.line7RetailsSellingAllowancesNbr !== null && i?.line7RetailsSellingAllowancesNbr !== undefined
                      && `Selling: ${toCurrency(i?.line7RetailsSellingAllowancesNbr)}`
                      }
                  </span>
                  <span
                    className="leading-none tooltip-text"
                    title={i?.line7RetailsNonSellingAllowancesNbr !== null && i?.line7RetailsNonSellingAllowancesNbr !== undefined
                      ? `Non-Selling: ${toCurrency(i?.line7RetailsNonSellingAllowancesNbr)}`
                      : ''}
                  >
                    {i?.line7RetailsNonSellingAllowancesNbr !== null && i?.line7RetailsNonSellingAllowancesNbr !== undefined
                      && `Non-s...: ${toCurrency(i?.line7RetailsNonSellingAllowancesNbr)}`
                      }
                  </span>
                </div>
              )}
            </div>

            {/* Percentage Part */}
            <div className={`flex items-center`}>
              {percentageProperty && (
                <i className="text-xs pt-0.5 font-normal text-[#5A697B]">
                  {i?.[percentageProperty] === null || i?.[percentageProperty] === undefined
                    ? ''
                    : percentageProperty === 'line1PublicToSalesPct' && i?.subRow === MERCH_FORECAST
                    ? `${i?.[percentageProperty].toFixed(2)}%LY`
                    : `${i?.[percentageProperty]?.toFixed(2)}%`}
                </i>
              )}
            </div>
          </div>
        ))}

        {/* Expanded Rows */}
        {expanded && (subRows?.length > 0) &&
          subRows.filter((item: any) => item.subRow !== "Base").slice(2)
          .map((i: any, index: number) => (
            <div
              key={`${index}-${i?.mainRow}-${i?.subRow}`}
              className={clsx(
                "2xl:justify-start 2xl:gap-x-2",
                "flex justify-between ml-1 h-full",
                {"items-start": i?.subRow === MERCH_FORECAST},
                {' mb-6  ': i?.subRow === MERCH_FORECAST && valueProperty !== 'line7RetailsAllowancesNbr' },
                {'': i?.subRow === MERCH_FORECAST && valueProperty === 'line7RetailsAllowancesNbr'}
              )}
            >
              <div className={clsx(
                'flex flex-col text-sm/16 font-normal items-start',
                {
                  '': i?.subRow === MERCH_FORECAST && valueProperty === 'line7RetailsAllowancesNbr',
                }
                )}>
               
                <span className={clsx(
                  getColor(i?.subRow, i[valueProperty]),
                  'flex items-center'
                )}>
                  {i?.[valueProperty] !== null && i?.[valueProperty] !== undefined
                    ? showCurrency
                      ? toCurrency(i?.[valueProperty])
                      : i?.[valueProperty]
                    : ''}
                </span>

                {i?.subRow === MERCH_FORECAST && (
                  <div className="flex flex-col text-xs text-black">
                    <span
                      className="leading-none tooltip-text"
                      title={valueProperty === 'line7RetailsAllowancesNbr' && i?.line7RetailsSellingAllowancesNbr !== null && i?.line7RetailsSellingAllowancesNbr !== undefined
                        ? `Selling: ${toCurrency(i?.line7RetailsSellingAllowancesNbr)}`
                        : ''}
                    >
                      {valueProperty === 'line7RetailsAllowancesNbr' && i?.line7RetailsSellingAllowancesNbr !== null && i?.line7RetailsSellingAllowancesNbr !== undefined
                        && `Selling: ${toCurrency(i?.line7RetailsSellingAllowancesNbr)}`
                        }
                    </span>
                    <span
                      className="leading-none tooltip-text"
                      title={valueProperty === 'line7RetailsAllowancesNbr' && i?.line7RetailsNonSellingAllowancesNbr !== null && i?.line7RetailsNonSellingAllowancesNbr !== undefined
                        ? `Non-Selling: ${toCurrency(i?.line7RetailsNonSellingAllowancesNbr)}`
                        : ''}
                    >
                      {valueProperty === 'line7RetailsAllowancesNbr' && i?.line7RetailsNonSellingAllowancesNbr !== null && i?.line7RetailsNonSellingAllowancesNbr !== undefined
                        && `Non-s...: ${toCurrency(i?.line7RetailsNonSellingAllowancesNbr)}`
                        }
                    </span>
                  </div>
                )}
              </div>

                <div className={clsx(
                  `flex items-center`,
                  { 'pt-1': valueProperty === 'line7RetailsAllowancesNbr' }
                )}>
                {percentageProperty && (
                  <i className={clsx(
                    "text-xs font-normal text-[#5A697B]",
                    "pt-0.5",
                    )}>
                    {i?.[percentageProperty] === null || i?.[percentageProperty] === undefined
                      ? ''
                      : percentageProperty === 'line1PublicToSalesPct' && i?.subRow === MERCH_FORECAST
                      ? `${i?.[percentageProperty].toFixed(2)}%LY`
                      : `${i?.[percentageProperty]?.toFixed(2)}%`}
                  </i>
                )}
              </div>
            </div>
          ))}
      </div>
    );
  };
  const getExpandButton = (item, toggle, expanded, currentWeek) => {
    return (
      <button
        ref={(el) => {
          if (item?.fiscalWeekNbr === currentWeek) {
            setCurrentWeekExpandButton("" + item?.fiscalWeekNbr);
          } buttonRef.current.add(el)
        }}
        className={`expanding-button-${expanded} ${item?.fiscalWeekNbr === currentWeek ? 'current-week' : ''
          }`}
        onClick={() => {
          toggle && toggle();
        }}
      >
        {expanded ? <ChevronsDownUp size={18} /> : <ChevronsUpDown size={18} />}
      </button>
    );
  }

  const getRowMenu = (item) => {   
    const isDisable_periodClose = weeksToBeDisabled.includes(String(item?.fiscalWeekNbr));
    return (
      <Menu
        items={[
          {
            className:'min-w-[210px] max-h-[38px] text-gray-800 text-sm font-normal leading-none justify-start',
            label: resetLoader ? <Spinner variant='solid' size='xs' /> : 'Reset Merchant Adjustment',
            onItemClick: () => {
              setModalItem(item);
              setIsOpen(true);
            },
            disabled: resetLoader || resetPermission || isDisable_periodClose,
          },
          {
            className:'min-w-[210px] max-h-[38px] text-gray-800 text-sm font-normal leading-none justify-start',
            label: 'Edit Forecast',
            onItemClick: () => {
              onEditForecast(item?.fiscalWeekNbr); 
            },
            disabled: resetLoader || resetPermission || isDisable_periodClose,
          },
        ]}
        trigger={<EllipsisVertical size={18}/>}
      />
    );
  }

  // Helper to format period and week numbers
  function formatPeriodOrWeek(num?: number) {
    if (num === undefined || num === null) return '';
    // If period >= 6, use last two digits
    if (num >= 6) {
      return String(num).slice(-2).replace(/^0+/, ''); // Remove leading zeros
    }
    // Otherwise, just show as number (no leading zero)
    return String(Number(num));
  }

  // Helper to format mainRow as 'Period 1', 'Period 07', 'Week 24', etc.
  function formatMainRowLabel(mainRow: string) {
    if (!mainRow) return '';
    // Match 'Period', 'Week', or 'Weeks' (optionally plural), optional space, then 6 or more digits or just digits
    // This covers cases like 'Period 202507', 'Week202524', 'Weeks202524', 'Period7', etc.
    const match = mainRow.match(/^(Period|Week|Weeks)\s?(\d{6,}|\d+)$/);
    if (match) {
      // Normalize 'Weeks' to 'Week' for display
      const label = match[1].replace('Weeks', 'Week');
      const digits = match[2];
      // If 6 or more digits, use last two; otherwise, use as is
      const num = digits.length >= 6 ? digits.slice(-2) : digits;
      return `${label} ${num}`;
    }
    // Fallback: return the original string if it doesn't match expected patterns
    return mainRow;
  }

  const renderAggregatedLevelColumn = (
    row: any,
    currentWeek: Number,
    toggle?: () => void,
    expanded?: boolean
  ) => {   
    const rowData = data.get(row);   
    const item: Forecast = rowData?.find(item => item.fiscalWeekEnding);    
    row = row.replace(/(\D)(\d)/, '$1 $2');
    if (row.includes('Weeks')) {
      row = row.replace(/\bWeeks\b/, 'Week');
    }
    //console.log("mev-rowData",rowData, rowData.fiscalPeriodNbr, rowData.fiscalWeekNbr);
    // Format period and week numbers for display

    const displayWeek = formatPeriodOrWeek(item?.fiscalWeekNbr);
   
    return (
      <div className="flex flex-col h-full justify-between align-center pr-2">
        <div className="flex h-full">
          <StatusCellIcon rowData={rowData} />
        <div className="pt-2">
            <div className={`flex justify-between ${row.split(' ')[0].toLowerCase()}`}>
            
              <span
                //Condition change from week 4 to current week
                className={`text-sm text-start ${item?.fiscalWeekNbr === currentWeek ? 'bg-[#EBF3FA] rounded-md text-sky-700' : ''}`}
              >
                   {formatMainRowLabel(row)}
              </span>
              <span className="text-xs font-normal text-gray-202">
                {getExpandButton(item, toggle, expanded, currentWeek)}
              </span>
            </div>
            <div className="flex justify-between text-xs font-normal text-gray-202 text-left pt-4">
              <span>{item?.fiscalWeekNbr ? `FW #${displayWeek}` : ''}</span>
              {item?.fiscalWeekNbr && getRowMenu(item)}
            </div>
        </div>
      </div>
      </div>
    );
  };

  const renderForecastLabelColumn = (
    subRows: Forecast[],
    expanded?: boolean
  ) => {
    return (
      <div className="grid TitleWeekRowForecast grid-cols-1">
        {(subRows?.length > 0) && subRows.filter((i: any) => i.subRow !== "Base").slice(0, 2).map((i: any, index: number) => (
          <div
            key={`${index}-${i?.mainRow}-${i?.subRow}`}
            className={clsx(
              "flex justify-between items-start",
            )}
          >
            <div
              key={`${index}-${i?.mainRow}-${i?.subRow}`}
              className="flex p-1 BodyDataXSRegular"
            >
              {i.subRow}
            </div>
          </div>
        ))}
        {expanded &&
          (subRows?.length > 0) && subRows.filter((i: any) => i.subRow !== "Base").slice(2).map((i: any, index: number) => (
            <div
              key={`${index}-${i?.mainRow}-${i?.subRow}`}
              className={clsx(
                "flex justify-between items-start",
                {" mb-5": i?.subRow === MERCH_FORECAST}
              )}
            >
              <div
                key={`${index}-${i?.mainRow}-${i?.subRow}`}
                className="flex p-1 BodyDataXSRegular"
              >
                {i.subRow}
              </div>
            </div>
          ))}
      </div>
    );
  };

  const mainColumns = useMemo<Column<any>[]>(() => {
    if (data.size > 0) {
      return [
        {
          id: 'aggregatedLevel',
          label: (
            <div className="flex absolute top-[14px] items-center pl-2 text-[#1B6EBB]">
              <div className="flex-grow font-bold text-sm leading-4">
                {expandAllDataAttributes
                  ? 'Reset to default'
                  : 'Expand all data attributes'}
              </div>
              <div className="flex-shrink-0 ml-2">
                {expandAllDataAttributes ? (
                  <ChevronsDownUp
                    size={18}
                    className="cursor-pointer"
                    onClick={handleClick}
                  />
                ) : (
                  <ChevronsUpDown
                    size={18}
                    className="cursor-pointer"
                    onClick={handleClick}
                  />
                )}
              </div>
            </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderAggregatedLevelColumn(item, currentWeek, toggle, expanded),
          sortable: false,
          align: 'center',
          width: '7%',
          className: 'border-r-0 worksheet-table',
        },
        {
          id: 'forecastHeadings',
          label: '',
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderForecastLabelColumn(data.get(item), expanded),
          align: 'left',
          width: '5%',
          className: 'border-l-0',
        },
        {
          id: 'line1',
          label: (
            <div className='ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex'><span>Line 1</span>
            <span className='ml-2'>
               <span className='tool-tip-initilizer-top'></span>
           <InfoTooltip label={lineOne} icon={<Info size={16} color="#1B6EBB" />} anchor="top" variant="dark" className="uds-tooltip-top"/>
             </span>
              </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line1PublicToSalesNbr',
              expanded,
              percentageProperty: 'line1PublicToSalesPct'
            }),
          align: 'left',
          width: '11%',
        },
        {
          id: 'grossProfit',
          label: 'Book Gross Profit',
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line5BookGrossProfitNbr',
              expanded,
              percentageProperty: 'line5BookGrossProfitPct',
            }),
          sortable: false,
          align: 'left',
          width: '11%',
        },
        {
          id: 'makrdown',
          label: 'Markdown',
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line5MarkDownsNbr',
              expanded,
              percentageProperty: 'line5MarkDownsPct',
            }),
          sortable: false,
          align: 'left',
          width: '11%',
        },
        {
          id: 'shrink',
          label: 'Shrink',
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line5ShrinkNbr',
              expanded,
              percentageProperty: 'line5ShrinkPct',
            }),
          align: 'left',
          width: '11%',
        },
        {
          id: 'line5',
          label: (
            <div className='ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex'><span>Line 5</span>
            <span className='ml-2'>
               <span className='tool-tip-initilizer-top'></span>
           <InfoTooltip label={lineFive} icon={<Info size={16} color="#1B6EBB" />} anchor="top" variant="dark" className="uds-tooltip-top"/>
             </span>
              </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line5RealGrossProfitNbr',
              expanded,
              percentageProperty: 'line5RealGrossProfitPct'
            }),
          align: 'left',
          width: '11%',
        },
        {
          id: 'line6',
          label: (
            <div className='ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex'><span>Line 6</span>
            <span className='ml-2'>
               <span className='tool-tip-initilizer-top'></span>
           <InfoTooltip label={lineSix} icon={<Info size={16} color="#1B6EBB" />} anchor="top" variant="dark" className="uds-tooltip-top"/>
             </span>
              </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line6SuppliesPackagingNbr',
              expanded,
              percentageProperty: 'line6SuppliesPackagingPct'
            }),
          align: 'left',
          width: '8%',
        },
        {
          id: 'line7',
          label: (
            <div className='ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex'><span>Line 7</span>
            <span className='ml-2'>
               <span className='tool-tip-initilizer-top'></span>
           <InfoTooltip label={lineSeven} icon={<Info size={16} color="#1B6EBB" />} anchor="top" variant="dark" className="uds-tooltip-top"/>
             </span>
              </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line7RetailsAllowancesNbr',
              expanded
            }),
          align: 'left',
          width: '9%',
        },
        {
          id: 'line8',
          label: (
            <div className='ml-3 font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate mr-3 flex'><span>Line 8</span>
            <span className='ml-2'>
           <InfoTooltip label={lineEight} icon={<Info size={16} color="#1B6EBB" />} anchor="left" variant="dark" className="uds-tooltip-left"/>
             </span>
              </div>
          ),
          value: (item: any, toggle?: () => void, expanded?: boolean) =>
            renderDataColumn({
              subRows: data.get(item),
              valueProperty: 'line8RealGrossProfitNbr',
              expanded,
              percentageProperty: 'line8RealGrossProfitPct'
            }),
          align: 'left',
          width: '11%',
        },
      ];
    }
    return [];
  }, [data, expandAllDataAttributes, currentWeek, resetLoader, resetPermission, weeksToBeDisabled]);

  useEffect(() => {
    buttonRef.current.forEach((button: any) => {
      if (button && button.className.includes('current-week')) {
        button.click();
      }
      // const firstHeader = document.querySelector('thead tr:first-child th:first-child') as HTMLElement;
      // if (firstHeader) {
      //   firstHeader.setAttribute('colspan', '2');
      // }

    });
  }, [currentWeekExpandButton]);

  /*** Adding the below code for implementing stripped variant of the table
   * since uds component introduces extra <tr> on expand, stripped variant collapses on expansion, if we handle this with only css change.
   * This needs to be fixed in uds.
   * Below code identifies the extra tr introcuded with 'td[colspan]' and manually work through its css. This is the only solution.
   */

  useEffect(() => {
    const rows = document.querySelectorAll("table tr");
    // const headerRow = document.querySelector("thead");
    // headerRow?.classList.add('sticky', 'top-0', 'z-50');
    let visibleRowIndex = 0;


    rows.forEach((row,index) => {
      if (index === 0) {
        row.classList.remove('bg-gray-206');
        row.classList.add('bg-[#DCDFEA]');
        // row.classList.add('sticky', 'top-0', 'z-10');
        return;
      }
      const hasColSpan = row.querySelector("td[colspan]")
      if (!hasColSpan) {
      /** Its a data row */
      row.classList.remove('bg-white', 'bg-gray-100')
      row.classList.add(visibleRowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-100')
      visibleRowIndex++
      } else {
      /** Dummy row: match previous row's background */
      const prev = row.previousElementSibling;
      if (prev) {
        const bg = prev.classList.contains('bg-gray-100') ? 'bg-gray-100' : 'bg-white';
        row.classList.add(bg)
      }
      }

      /** Add hover effect */
      row.classList.add('hover:bg-[#E0EDFB]');
    })
  }, [data])
  return (
    <div className="w-full">
      <>
        <Modal
          isOpen={isOpen}
          onClose={() => { setIsOpen(false); setShowSuccess(false); setModalItem(null); }}
          iconSmall={false}
          data-testid="confirmation-modal"
          className="!min-w-[800px] !max-w-[800px] !min-h-[308px] !max-h-[308px] relative bg-white">
          <div className="uds-modal-center-content">
            <div className="text-center select-none text-[20px] font-bold mb-8">Are you sure you'd like to reset Merchant Forecast <br /> this week? </div>
            <div className="flex items-center justify-center gap-4">
              <Button width={130} size="lg" variant="secondary" onClick={() => { setIsOpen(false); setModalItem(null); }} data-testid="confirmation-cancel-button"> No, Keep Working</Button>
              <Button
                width={120}
                size="lg"
                data-testid="confirmation-confirm-button"
                onClick={() => {
                  setIsOpen(false);
                  setShowSuccess(false);
                  setResetLoader(true);
                  resetWeek(modalItem?.mainRow);
                }}
              >Yes, I am sure</Button>
            </div>
          </div>
        </Modal>
      </>
      <div>
        <div className="px-2 sticky top-[73px] bg-gray-206 z-[1] pb-3" id="timestamp-footer"> {timestampFooter}
      </div>
        <div>
          <Table
            items={Array.from(data.keys())}
            columns={mainColumns}
            backfill={false}
            noPagination={true}
            noHeader={true}
             dividers="vertical"
            itemKey={(item) => item}
            expansion={() => <></>}
          />

        </div>
      </div>
    </div>
  );
};

export default UdsTable;
