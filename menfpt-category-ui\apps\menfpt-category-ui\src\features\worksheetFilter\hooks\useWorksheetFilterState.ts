import { useCallback, useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DropdownType } from '../../../interfaces/worksheetFilter';
import { useSelectorWrap } from '../../../rtk/rtk-utilities';
import {
  useDisplayDeptRoleCascade,
  useTimeframeDisplay,
  useMultipleDepartmentsSelection
} from '../worksheetFilterRouteUtils';
import { extractDivisionsFromFiltersList } from '../divisionUtils';
import { getDepartmentsForDivisions, updateDepartmentsAndDesks } from '../departmentUtils';
import { getDesksForDivision } from '../deskUtils';
import {
  getCategoriesForDepartmentReltdFilters,
  getCategoriesForDesk
} from '../categoryOperations';
import { AppliedFilterState } from '../worksheetFilterTypes';
import { setSelectedSm, setSmDataForSelectedDept } from '../components/roles/rolesFilter.slice';
import { getSmDataByDivisionAndDept, getAsmListForSelectedSM } from '../components/roles/rolesUtils';
import { setDepartments } from '../components/department/departments.slice';
import { setActiveTabInFilter } from '../worksheetFilter.slice';
import { SmDataType } from '../types/smTypes';
import { createEmptySerializedSmData, deserializeSmData, serializeSmData } from '../utils/serializationUtils';

/**
 * Ensures only unique category items are in the list based on the 'num' property
 * @param categoryList The list of categories to deduplicate
 * @returns A new array with only unique category items
 */
const getUniqueCategoryList = (categoryList: DropdownType[]): DropdownType[] => {
  return Array.from(
    new Map(categoryList.map(item => [item.num, item])).values()
  );
};

interface SmData {
  sm: any;
  asmArr: unknown[];
}

export interface FilterStateHookResult {
  // State
  shouldDisplayTimeFrame: boolean;
  isDisplayDeptRoleCascade: boolean;
  selectedDivision: DropdownType[];
  selectedDepartment: DropdownType | DropdownType[] | undefined;
  selectedDesk: DropdownType | undefined;
  selectedTimeframe: DropdownType | undefined;
  selectedPeriods: DropdownType[] | undefined;
  selectedWeeks: { periodNum: number; weekNum: number }[];
  departments: DropdownType[];
  desks: DropdownType[];
  categories: DropdownType[];
  divisions: DropdownType[];
  activeTabInFilter: any;
  selectedSm: SmDataType;

  // Handlers
  handleDivisionChange: (divisions: DropdownType[]) => void;
  handleDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  handleDeskChange: (desk: DropdownType) => void;
  handleTimeframeChange: (timeframe: DropdownType) => void;
  handlePeriodChange: (period: DropdownType[]) => void;
  handleWeekChange:(week: { periodNum: number; weekNum: number }[])=>void;
  resetFilters: () => void;
  applyFilters: () => void;
  onCancel: () => void;
}

export const useWorksheetFilterState = (
  isOpen: boolean,
  onClose: () => void,
  FiltersList: any[],
  appliedFilters: AppliedFilterState,
  onApply: (filters: AppliedFilterState) => void
): FilterStateHookResult => {
  const dispatch = useDispatch();
  const shouldDisplayTimeFrame = useTimeframeDisplay();
  const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();
  const isMultipleDepartmentsSelection = useMultipleDepartmentsSelection();

  const { data: activeTabInFilter } = useSelectorWrap('activeTabInFilter_rn') || { data: ['department'] };
  const { data: selectedSmSerialized } = useSelectorWrap('selectedSm_rn') || {};
  const selectedSm = deserializeSmData(selectedSmSerialized);

  const [selectedDivision, setSelectedDivision] = useState<DropdownType[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<
    DropdownType | DropdownType[] | undefined
  >(undefined);
  const [selectedDesk, setSelectedDesk] = useState<DropdownType | undefined>(
    undefined
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState<
    DropdownType | undefined
  >(undefined);
  const [selectedPeriods, setSelectedPeriods] = useState<
    DropdownType[]
  >([]);
  const [selectedWeeks, setSelectedWeeks] = useState<
    { periodNum: number; weekNum: number }[]
  >([]);
  const departmentsState = useSelectorWrap('departments_rn');
  const departments = useMemo(() => departmentsState.data || [], [departmentsState.data]);
  const [desks, setDesks] = useState<DropdownType[]>([]);
  const [divisions, setDivisions] = useState<DropdownType[]>([]);
  
  // Memoize categories to prevent unnecessary recalculations
  const categories = useMemo(() => {
    if (!FiltersList?.length) {
      return [];
    }

    if (selectedDepartment) {
      // Get categories for the selected department(s)
      const categoryList = getCategoriesForDepartmentReltdFilters({
        FiltersList,
        selectedDivision,
        selectedDepartment,
        selectedSm,
      });

      // Ensure only unique category items are in the list
      return getUniqueCategoryList(categoryList);
    }

    if (selectedDesk) {
      const categoryList = getCategoriesForDesk(FiltersList, selectedDesk);
      // Ensure only unique category items are in the list
      return getUniqueCategoryList(categoryList);
    }

    return [];
  }, [FiltersList, selectedDepartment, selectedDesk, selectedDivision, selectedSm]);
  
  // Track if user has interacted with department selection
  const [hasUserInteractedWithDepartments, setHasUserInteractedWithDepartments] = useState(false);

  const deptRoleSuggestionsState = useSelector((state: any) => state.deptRoleSuggestions_rn);

  // Initialize divisions from FiltersList
  useEffect(() => {
    if (FiltersList?.length > 0) {
      const divisionList = extractDivisionsFromFiltersList(FiltersList);
      setDivisions(divisionList);
    }
  }, [FiltersList]);

  // Initialize activeTabInFilter if it's undefined (since departmentDeskTabs.tsx is commented out)
  useEffect(() => {
    if (!activeTabInFilter || activeTabInFilter.length === 0) {
      dispatch(setActiveTabInFilter(['department']));
    }
  }, [activeTabInFilter, dispatch]);

  // Initialize state from applied filters when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedDivision(appliedFilters.division);
      setSelectedDepartment(appliedFilters.department);
      setSelectedDesk(appliedFilters.desk);
      setSelectedTimeframe(appliedFilters.timeframe);
      setSelectedPeriods(appliedFilters.periods || []);
      setSelectedWeeks(appliedFilters.selectedWeeks||[])

      // Initialize SM and ASM selections from applied filters
      // Get SM data from the selected department
        
        let smData: SmDataType = new Map();
        if (appliedFilters.department && FiltersList?.length > 0) {
          
          const departmentList = getDepartmentsForDivisions(FiltersList, appliedFilters.division);
          const selectedDeptId = Array.isArray(appliedFilters.department)
            ? appliedFilters.department.map(dept => dept.num)
            : appliedFilters.department.num;

          // Use the imported getSmDataByDivisionAndDept function
          const smDataResult = getSmDataByDivisionAndDept({
            selectedDeptId,
            deptListForSelectedDivision: departmentList
          });
          smData = smDataResult instanceof Map ? smDataResult : new Map();
        }

        dispatch(setSmDataForSelectedDept(serializeSmData(smData)));

        if (appliedFilters?.selectedSm && appliedFilters.selectedSm.size > 0) {
          dispatch(setSelectedSm(serializeSmData(appliedFilters.selectedSm)));
        }
    }
  }, [isOpen, appliedFilters, FiltersList, dispatch]);

  // Update departments when divisions change
  const setDepartmentsCb = useCallback((departments: DropdownType[]) => {
    dispatch(setDepartments(departments));
  }, [dispatch]);

  useEffect(() => {
    updateDepartmentsAndDesks(
      selectedDivision,
      FiltersList,
      setDepartmentsCb,
      setDesks,
      setSelectedDepartment,
      setSelectedDesk,
      getDesksForDivision
    );
  }, [selectedDivision, FiltersList, setDepartmentsCb]);

  // Clear category when department or desk changes
  useEffect(() => {
    if (selectedDepartment) {
      setSelectedDesk(undefined);
    }
  }, [selectedDepartment]);

  // Preselect all departments if multiple selection is allowed and none are selected,
  // but only if the user hasn't interacted with department selection yet
  // useEffect(() => {
  //   if (
  //     isMultipleDepartmentsSelection &&
  //     departments.length > 0 &&
  //     (!selectedDepartment || (Array.isArray(selectedDepartment) && selectedDepartment.length === 0)) &&
  //     !hasUserInteractedWithDepartments
  //   ) {
  //     setSelectedDepartment(departments);
  //     setHasUserInteractedWithDepartments(true); // Prevent infinite loop
  //   }
  // }, [isMultipleDepartmentsSelection, departments, selectedDepartment, hasUserInteractedWithDepartments]);

  useEffect(() => {
    const selectedDeptId = deptRoleSuggestionsState?.data?.selectedDepartment;
    if (selectedDeptId) {
      const foundDept = departments.find((d: any) => String(d.num) === String(selectedDeptId));
      if (foundDept) {
        setSelectedDepartment(foundDept);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deptRoleSuggestionsState?.data?.selectedDepartment]);

  const handleDivisionChange = (divisions: DropdownType[]) => {
    setSelectedDivision(divisions);
    // Clear department, SM, and ASM selections when division changes
    setSelectedDepartment(undefined);
    setSelectedDesk(undefined);
    setHasUserInteractedWithDepartments(false); // Reset interaction flag on division change

    // Clear SM and ASM selections
    dispatch(setSelectedSm(createEmptySerializedSmData()));
    dispatch(setSmDataForSelectedDept(createEmptySerializedSmData()));
  };

  const handleDepartmentChange = (
    department: DropdownType | DropdownType[]
  ) => {
    setHasUserInteractedWithDepartments(true); // Mark that user has interacted
    setSelectedDepartment(department);
    setSelectedDesk(undefined);

    // Clear SM and ASM selections when department changes
    dispatch(setSelectedSm(createEmptySerializedSmData()));
    dispatch(setSmDataForSelectedDept(createEmptySerializedSmData()));
  };

  const handleDeskChange = (desk: DropdownType) => {
    setSelectedDesk(desk);
    setSelectedDepartment(undefined);

    // Clear SM and ASM selections when desk changes
    dispatch(setSelectedSm(createEmptySerializedSmData()));
    dispatch(setSmDataForSelectedDept(createEmptySerializedSmData()));
  };

  const onCancel = () => {
    setSelectedDivision(appliedFilters.division);
    setSelectedDepartment(appliedFilters.department);
    setSelectedDesk(appliedFilters.desk);
    setSelectedTimeframe(appliedFilters.timeframe);
    setHasUserInteractedWithDepartments(false); // Reset interaction flag
    // Reset SM and ASM to previously applied values if they exist
    if (appliedFilters.selectedSm && appliedFilters.selectedSm.size > 0) {
      dispatch(setSelectedSm(serializeSmData(appliedFilters.selectedSm)));
    } else {
      dispatch(setSelectedSm(createEmptySerializedSmData()));
    }
    onClose();
  };

  const resetFilters = () => {
    setSelectedDivision([]);
    setSelectedDepartment([]);
    setSelectedDesk(undefined);
    setSelectedTimeframe(undefined);
    setSelectedPeriods([]);
    setSelectedWeeks([]);
    setHasUserInteractedWithDepartments(false);
    dispatch(setSmDataForSelectedDept([]));
    dispatch(setSelectedSm(createEmptySerializedSmData()));
  };

  const applyFilters = () => {
    onApply({
      division: selectedDivision,
      department: selectedDepartment,
      desk: selectedDesk,
      timeframe: selectedTimeframe,
      periods: selectedPeriods,
      selectedWeeks: selectedWeeks,
      selectedSm: selectedSm
    });
  };

  const handleTimeframeChange = (timeframe: DropdownType) => {
    setSelectedTimeframe(timeframe);
    setSelectedPeriods([]);
    setSelectedWeeks([]);
  };

  const handlePeriodChange = (periods: DropdownType[]) => {
    setSelectedPeriods(periods);
  };
  const handleWeekChange = (weeks: { periodNum: number; weekNum: number }[]) => {
    setSelectedWeeks(weeks);
  };

  return {
    shouldDisplayTimeFrame,
    isDisplayDeptRoleCascade,
    selectedDivision,
    selectedDepartment,
    selectedDesk,
    selectedTimeframe,
    selectedPeriods,
    selectedWeeks,
    departments,
    desks,
    categories,
    divisions,
    activeTabInFilter,
    selectedSm,
    handleDivisionChange,
    handleDepartmentChange,
    handleDeskChange,
    handleTimeframeChange,
    handlePeriodChange,
    handleWeekChange,
    resetFilters,
    applyFilters,
    onCancel
  };
};
