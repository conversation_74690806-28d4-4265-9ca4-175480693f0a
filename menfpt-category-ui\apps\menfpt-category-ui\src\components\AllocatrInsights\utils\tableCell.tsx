import React from 'react';
import { formatCurrency, formatPercentage } from './insightsFormatters';
import { borderClass } from '../AllocatrInsightsHelper';
import { DepartmentData, PeriodData, QuarterData, WeekData } from 'apps/menfpt-category-ui/src/interfaces/allocatr-insights';
import ActualUsedIndicator from '../ActualUsedIndicator';

type FormatterType = 'currency' | 'percentage' | 'none';

/**
 * Renders a table cell with formatted value and appropriate styling
 */
export const renderCell = (
  value: any,
  options: {
    formatter?: FormatterType;
    showColor?: boolean;
    hasBorder?: boolean;
    indicator?: React.ReactNode;
    hypenForFalsy?: boolean; // NEW OPTION
  } = {}
) => {
  const { 
    formatter = 'none', 
    showColor = false, 
    hasBorder = false, 
    indicator,
    hypenForFalsy = false
  } = options;
  
  // Format the value
  let formattedValue;
  if (hypenForFalsy ? !value : value == null) {
    formattedValue = <b>--</b>;
  } else {
    if (formatter === 'currency') formattedValue = formatCurrency(value);
    else if (formatter === 'percentage') formattedValue = formatPercentage(value);
    else formattedValue = value;
  }
  
  // Determine the CSS classes
  const classes = [
    'table-cell',
    hasBorder ? borderClass : '',
    showColor && value < 0 ? 'text-red-600' : '',
    showColor && value > 0 ? 'text-green-600' : ''
  ].filter(Boolean).join(' ');
  
  return <td className={classes} style={{ position: indicator ? 'relative' : undefined }}>
    {indicator && <span style={{ position: 'absolute', top: 0, left: 0,}}>{indicator}</span>}
    {formattedValue}
  </td>;
};
export const renderAllRows = (data: QuarterData | PeriodData | WeekData, isActualUsed?: boolean) => {
    return [
        // Line 1 (Sales to Public)
        renderCell(data?.line1Projection, { formatter: 'currency', hypenForFalsy: true }),
        renderCell(data?.lastYear, { formatter: 'currency', hypenForFalsy: true }),
        renderCell(data?.actualOrForecast, { formatter: 'currency', hypenForFalsy: true, ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.idPercentage, { formatter: 'percentage', ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.vsLY?.value, { formatter: 'currency', showColor: true, ...(isActualUsed ? { indicator: <ActualUsedIndicator  /> } : {}) }),
        renderCell(data?.vsProjection?.value, { formatter: 'currency', showColor: true, hasBorder: true,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        
        // Book Gross Profit
        renderCell(data?.bookGrossProfit?.projectionValue, { formatter: 'currency', hypenForFalsy: true  }),
        renderCell(data?.bookGrossProfit?.projectionPct, { formatter: 'percentage', hypenForFalsy: true  }),
        renderCell(data?.bookGrossProfit?.actualOrForecast, { formatter: 'currency', hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.bookGrossProfit?.percentActualOrForecast, { formatter: 'percentage', ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.bookGrossProfit?.vsProjection, { formatter: 'percentage', showColor: true, hasBorder: true, ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        
        // Markdown
        renderCell(data?.markdown?.projectionValue, { formatter: 'currency', hypenForFalsy: true  }),
        renderCell(data?.markdown?.projectionPct, { formatter: 'percentage', hypenForFalsy: true  }),
        renderCell(data?.markdown?.actualOrForecast, { formatter: 'currency',hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.markdown?.percentActualOrForecast, { formatter: 'percentage', ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.markdown?.vsProjection, { formatter: 'percentage', showColor: true, hasBorder: true,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),

        // Shrink
        renderCell(data?.shrink?.projectionValue, { formatter: 'currency', hypenForFalsy: true  }),
        renderCell(data?.shrink?.projectionPct, { formatter: 'percentage', hypenForFalsy: true  }),
        renderCell(data?.shrink?.actualOrForecast, { formatter: 'currency', hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.shrink?.percentActualOrForecast, { formatter: 'percentage' ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.shrink?.vsProjection, { formatter: 'percentage', showColor: true, hasBorder: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),

        // Line 5
        renderCell(data?.line5?.projectionValue, { formatter: 'currency' ,hypenForFalsy: true  }),
        renderCell(data?.line5?.projectionPct, { formatter: 'percentage' ,hypenForFalsy: true  }),
        renderCell(data?.line5?.actualOrForecast, { formatter: 'currency' ,hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line5?.percentActualOrForecast, { formatter: 'percentage', ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line5?.vsProjection, { formatter: 'currency', showColor: true, ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line5?.percentVsProjection, { formatter: 'percentage', showColor: true, hasBorder: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),

        // Line 6
        renderCell(data?.line6?.projection, { formatter: 'currency', hypenForFalsy: true  }),
        renderCell(data?.line6?.actualOrForecast, { formatter: 'currency' ,hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator/> } : {}) }),
        renderCell(data?.line6?.vsProjection, { formatter: 'currency', showColor: true, hasBorder: true, ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),

        // Line 7
        renderCell(data?.line7?.projection, { formatter: 'currency' ,hypenForFalsy: true  }),
        renderCell(data?.line7?.actualOrForecast, { formatter: 'currency' ,hypenForFalsy: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line7?.vsProjection, { formatter: 'currency', showColor: true, hasBorder: true ,...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),

        // Line 8
        renderCell(data?.line8?.projectionValue, { formatter: 'currency',hypenForFalsy: true }),
        renderCell(data?.line8?.projectionPct, { formatter: 'percentage', hypenForFalsy: true }),
        renderCell(data?.line8?.actualOrForecast, { formatter: 'currency', hypenForFalsy: true , ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line8?.percentActualOrForecast, { formatter: 'percentage',  ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line8?.vsProjection, { formatter: 'currency', showColor: true,  ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) }),
        renderCell(data?.line8?.percentVsProjection, { formatter: 'percentage', showColor: true, hasBorder: true,  ...(isActualUsed ? { indicator: <ActualUsedIndicator /> } : {}) })
    ];
  };