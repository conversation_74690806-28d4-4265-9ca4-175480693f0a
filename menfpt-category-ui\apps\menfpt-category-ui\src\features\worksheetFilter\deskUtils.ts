import { DropdownType } from '../../interfaces/worksheetFilter';

export const getDesksForDivision = (
  FiltersList: any[],
  selectedDivision: DropdownType
): DropdownType[] => {
  if (!selectedDivision || !FiltersList?.length) {
    return [];
  }

  const deskList: DropdownType[] = [];
  const deskMap = new Map<string, Set<string>>();

  // First pass: collect all desk names
  FiltersList.forEach((item: any) => {
    if (item.divisionId === selectedDivision.num) {
      const deskId = item?.deskId;
      if (!deskMap.has(deskId)) {
        deskMap.set(deskId, new Set());
      }
      if (item?.deskName) {
        deskMap.get(deskId)?.add(item.deskName);
      }
    }
  });

  // Second pass: create desk objects with deskNameArr
  FiltersList.forEach((item: any) => {
    if (item.divisionId === selectedDivision.num) {
      const desk = {
        name: `${item?.deskName}`,
        num: item?.deskId,
        deskNameArr: Array.from(deskMap.get(item?.deskId) || []),
      };
      if (!deskList.some((d) => d.num === desk.num)) {
        deskList.push(desk);
      }
    }
  });
  return deskList;
};
