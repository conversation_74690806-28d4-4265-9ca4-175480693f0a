import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { DropdownType } from '../../interfaces/worksheetFilter';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { setAlertState, setAppliedFilter } from '../../server/Reducer/menfpt-category.slice';
import '../WorksheetHeaderControls.css';
import { getCategoriesForDepartmentReltdFilters, getCategoriesForDesk } from './categoryOperations';
import { filterValidDivisions, getDivisionsFromFiltersList } from './divisionUtils';
import WorksheetFilterDisplay from './worksheetFilterDisplay';
import WorksheetFilterModal from './worksheetFilterModal';
import { useTimeframeDisplay } from './worksheetFilterRouteUtils';
import { AppliedFilterState, WorksheetFilterProps } from './worksheetFilterTypes';
import { getStorageKeyByRoute } from './worksheetFilterUtils';
import { getCurrentTimeframe } from './utils/quarterUtils';
import { deserializeSmData, serializeSmData } from './utils/serializationUtils';

export const WorksheetFilterContainer: React.FC<WorksheetFilterProps & {
  isFilterModalOpen: boolean;
  setIsFilterModalOpen: (open: boolean) => void;
  openFilterModal: () => void;
}> = ({ FiltersList, isFilterModalOpen, setIsFilterModalOpen, openFilterModal }) => {
  const { data: displayDate } = useSelectorWrap('displayDate_rn') || {};
  const { data: quartersList } = useSelectorWrap('quartersInYr_rn') || { data: [] };
  const { data: alertState } = useSelectorWrap('alertState_rn');
  const dispatch = useDispatch();
  const shouldDisplayTimeFrame = useTimeframeDisplay();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState<boolean>(false);
  const [division, setDivision] = useState<DropdownType[]>([]);
  const positionRef = useRef("right");

  // Applied filters state
  const defaultTimeframe = getCurrentTimeframe(displayDate, quartersList);
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilterState>({
    division: [],
    department: undefined,
    desk: undefined,
    timeframe: shouldDisplayTimeFrame ? defaultTimeframe : undefined, // Use dynamic default timeframe
  });

  // Initialize divisions from FiltersList
  useEffect(() => {
    if (FiltersList?.length > 0) {
      const divisionList = getDivisionsFromFiltersList(FiltersList);
      setDivision(divisionList);
    }
  }, [FiltersList]);

  // Check for stored filter preferences
  useEffect(() => {    
    if (FiltersList && FiltersList.length > 0) {
      loadStoredPreferences();
    }
  }, [shouldDisplayTimeFrame, dispatch, FiltersList]);

const loadStoredPreferences = () => {
  const storedPreferences = localStorage.getItem(getStorageKeyByRoute());
  if (!storedPreferences || Object.keys(JSON.parse(storedPreferences)).length < 1) {
    setIsFilterModalOpen(true);
    return;
  }

  try {
    const parsedState: any = JSON.parse(storedPreferences);
    const filteredDivisions = filterValidDivisions(parsedState.division, FiltersList);
    
    updateInvalidDivisions(parsedState, filteredDivisions);
    applyStoredPreferences(parsedState, filteredDivisions);
  } catch (error) {
    console.error("Failed to parse localStorage data:", error);
  }
};

const updateInvalidDivisions = (parsedState: any, filteredDivisions: DropdownType[]) => {
  if (filteredDivisions.length !== parsedState.division.length) {
    const updatedPreferences = {
      ...parsedState,
      division: filteredDivisions,
    };
    localStorage.setItem(getStorageKeyByRoute(), JSON.stringify(updatedPreferences));
  }
};

const applyStoredPreferences = (parsedState: any, filteredDivisions: DropdownType[]) => {
  const defaultTimeframe = getCurrentTimeframe(displayDate, quartersList);
  const timeframe = shouldDisplayTimeFrame
    ? (parsedState.timeframe || defaultTimeframe)
    : undefined;
  const periods = shouldDisplayTimeFrame ?  parsedState.periods : [];
  const selectedWeeks = shouldDisplayTimeFrame ? parsedState.selectedWeeks : [];

  dispatch(setAppliedFilter({
    division: filteredDivisions,
    department: parsedState.department,
    desk: parsedState.desk,
    category: parsedState.category,
    timeframe: timeframe,
    periods,
    selectedWeeks,
    selectedSm: serializeSmData(parsedState.selectedSm ?? new Map()),
  }));

  setAppliedFilters({
    division: filteredDivisions,
    department: parsedState.department,
    desk: parsedState.desk,
    timeframe: timeframe,
    periods,
    selectedWeeks,
    selectedSm: deserializeSmData(parsedState.selectedSm ?? {}),
  });
};

  const getCategoriesForFilters = (newFilters: AppliedFilterState): DropdownType[] => {
    if (newFilters.department && FiltersList?.length > 0) {
      return getCategoriesForDepartmentReltdFilters({
        FiltersList,
        selectedDivision: newFilters.division,
        selectedDepartment: newFilters.department,
        selectedSm: newFilters.selectedSm || null,
      });
    }
    if (newFilters.desk && FiltersList?.length > 0) {
      return getCategoriesForDesk(FiltersList, newFilters.desk);
    }
    return [];
  };

const getTimeframeForRoute = (timeframe: any) => {
  return shouldDisplayTimeFrame ? timeframe : undefined;
};

const getPeriodsForRoute = (timeframe: any) => {
  return shouldDisplayTimeFrame ? timeframe : undefined;
};
const getWeeksForRoute = (timeframe: any) => {
  return shouldDisplayTimeFrame ? timeframe : undefined;
};

const handleApplyFilters = (newFilters: AppliedFilterState) => {
  const categories = getCategoriesForFilters(newFilters);
  const timeframe = getTimeframeForRoute(newFilters.timeframe);
  const periods = getPeriodsForRoute(newFilters.periods);
  const selectedWeeks = getWeeksForRoute(newFilters.selectedWeeks);
  
  updateAppliedFilters(newFilters, categories, timeframe, periods, selectedWeeks);
  setIsFilterModalOpen(false);
};

const updateAppliedFilters = (
  newFilters: AppliedFilterState,
  categories: DropdownType[],
  timeframe: any,
  periods: DropdownType[],
  selectedWeeks: { periodNum: number; weekNum: number }[]
) => {
  dispatch(setAppliedFilter({
    division: newFilters.division,
    department: newFilters.department,
    desk: newFilters.desk,
    timeframe,
    periods,
    selectedWeeks,
    category: categories,
    selectedSm: serializeSmData(newFilters.selectedSm ?? new Map()),
  }));

  setAppliedFilters({
    ...newFilters,
    timeframe,
    periods,
    selectedWeeks,
    selectedSm: newFilters.selectedSm
  });
};

  const historyModalOpen = (isOpen: boolean, position: string) => {
    setIsHistoryModalOpen(true);
    positionRef.current = position;
  };

  const resetEditMessage = () => dispatch(setAlertState({ success: false, error: false }));

  return (
    <div className='pl-7' data-testid='worksheet-filter-container'>
      <WorksheetFilterDisplay
        appliedFilters={appliedFilters}
        openFilterModal={openFilterModal}
        division={division}
      />

      <WorksheetFilterModal
        isOpen={isFilterModalOpen}
        onClose={() => { setIsFilterModalOpen(false); resetEditMessage(); }}
        FiltersList={FiltersList}
        appliedFilters={appliedFilters}
        onApply={handleApplyFilters}
      />
    </div>
  );
};

