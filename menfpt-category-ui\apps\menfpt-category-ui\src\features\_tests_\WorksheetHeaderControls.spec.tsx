import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { app_store } from '../../rtk/store';
import {WorksheetHeaderControls} from '../WorksheetHeaderControls';
import '@testing-library/jest-dom';
import * as rtkUtilities from '../../rtk/rtk-utilities';
import * as periodCloseFlags from '../periodClose/periodClose.flags';

// Mock ResizeObserver for test environment
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock the modules
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('../periodClose/periodClose.flags', () => ({
  useShouldDisableEditForecastButton: jest.fn(),
  useWeeksToBeDisabledForQuarter: jest.fn(() => []), // Add this mock to fix the error
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => jest.fn(),
  useSelector: (selector: any) => {
    // Mock the deptRoleSuggestions state that's accessed via useSelector
    const mockState = {
      deptRoleSuggestions_rn: {
        data: {
          cascadeSearchSelectedItemId: null,
          cascadeSearchSelectedItemType: null,
        }
      }
    };
    return selector(mockState);
  },
}));

// Mock the worksheet filter hooks to prevent infinite updates
jest.mock('../worksheetFilter/hooks/useWorksheetFilterState', () => ({
  useWorksheetFilterState: () => ({
    shouldDisplayTimeFrame: false,
    isDisplayDeptRoleCascade: false,
    selectedDivision: [{
      num: 5,
      name: 'Denver',
      isSelected: true
    }],
    selectedDepartment: {
      num: 311,
      name: 'GM/HBC',
      isSelected: true
    },
    selectedDesk: {
      num: 1,
      name: '05-ANGELA CEJA',
      isSelected: true
    },
    selectedTimeframe: undefined,
    selectedPeriods: [],
    selectedWeeks: [],
    departments: [{
      num: 311,
      name: 'GM/HBC',
      isSelected: false
    }],
    desks: [{
      num: 1,
      name: '05-ANGELA CEJA',
      isSelected: false
    }],
    categories: [{
      num: 3425,
      name: 'PROPANE -NS',
      isSelected: false
    }],
    divisions: [{
      num: 5,
      name: 'Denver',
      isSelected: false
    }],
    activeTabInFilter: null,
    selectedSm: null,
    selectedAsm: null,
    handleDivisionChange: jest.fn(),
    handleDepartmentChange: jest.fn(),
    handleDeskChange: jest.fn(),
    handleTimeframeChange: jest.fn(),
    handlePeriodChange: jest.fn(),
    handleWeekChange: jest.fn(),
    resetFilters: jest.fn(),
    applyFilters: jest.fn(),
  }),
}));

jest.mock('../worksheetFilter/departmentUtils', () => ({
  updateDepartmentsAndDesks: jest.fn(),
}));

// Mock the entire worksheetFilterContainer to prevent infinite loops
jest.mock('../worksheetFilter/worksheetFilterContainer', () => {
  const React = require('react');
  return {
    WorksheetFilterContainer: function MockWorksheetFilterContainer(props) {
      const [isModalOpen, setIsModalOpen] = React.useState(false);
      
      React.useEffect(() => {
        if (props.isFilterModalOpen !== undefined) {
          setIsModalOpen(props.isFilterModalOpen);
        }
      }, [props.isFilterModalOpen]);
      
      const handleChangeClick = () => {
        setIsModalOpen(true);
        if (props.setIsFilterModalOpen) {
          props.setIsFilterModalOpen(true);
        }
        if (props.openFilterModal) {
          props.openFilterModal();
        }
      };
      
      const handleApplyClick = () => {
        setIsModalOpen(false);
        if (props.setIsFilterModalOpen) {
          props.setIsFilterModalOpen(false);
        }
      };
      
      const handleCancelClick = () => {
        setIsModalOpen(false);
        if (props.setIsFilterModalOpen) {
          props.setIsFilterModalOpen(false);
        }
      };
      
      return React.createElement('div', { 'data-testid': 'worksheet-filter-container' }, [
        React.createElement('button', { 
          key: 'change-btn',
          onClick: handleChangeClick,
          type: 'button'
        }, 'Change'),
        isModalOpen && React.createElement('div', { 
          key: 'modal',
          'data-testid': 'filter-modal'
        }, [
          React.createElement('div', { key: 'division-tab' }, 'Division'),
          React.createElement('label', { 
            key: 'division-5',
            htmlFor: 'division-5'
          }, [
            React.createElement('input', { 
              key: 'input',
              type: 'checkbox',
              id: 'division-5',
              'aria-label': '5 - Denver'
            }),
            ' 5 - Denver'
          ]),
          React.createElement('button', { 
            key: 'apply-btn',
            onClick: handleApplyClick,
            type: 'button'
          }, 'Apply'),
          React.createElement('button', { 
            key: 'cancel-btn',
            onClick: handleCancelClick,
            type: 'button'
          }, 'Cancel'),
          React.createElement('div', { 
            key: 'alert',
            'data-testid': 'division-alert' 
          }, 'Please select division(s)')
        ])
      ]);
    }
  };
});

// Mock the worksheet filter context
jest.mock('../worksheetFilter/worksheetFilterContext', () => ({
  useWorksheetFilterContext: () => ({
    divisions: [{
      num: 5,
      name: 'Denver',
      isSelected: false
    }],
    departments: [{
      num: 311,
      name: 'GM/HBC',
      isSelected: false
    }],
    desks: [{
      num: 1,
      name: '05-ANGELA CEJA',
      isSelected: false
    }],
    categories: [{
      num: 3425,
      name: 'PROPANE -NS',
      isSelected: false
    }],
    selectedDivisions: [{
      num: 5,
      name: 'Denver',
      isSelected: true
    }],
    selectedDepartments: [{
      num: 311,
      name: 'GM/HBC',
      isSelected: true
    }],
    selectedDesks: [{
      num: 1,
      name: '05-ANGELA CEJA',
      isSelected: true
    }],
    selectedCategories: [{
      num: 3425,
      name: 'PROPANE -NS',
      isSelected: true
    }],
    setSelectedDivisions: jest.fn(),
    setSelectedDepartments: jest.fn(),
    setSelectedDesks: jest.fn(),
    setSelectedCategories: jest.fn(),
    smUsers: [],
    asmUsers: [],
    selectedSmUsers: [],
    selectedAsmUsers: [],
    setSelectedSmUsers: jest.fn(),
    setSelectedAsmUsers: jest.fn(),
  }),
}));

const mockWorksheetFilterData = [
    {
        "divisionId": 5,
        "divisionName": "Denver",
        "deptId": 311,
        "deptName": "GM/HBC",
        "smicGroupCd": 34,
        "smicGroupDesc": "CHARCOAL, LOGS & OTHER FUEL",
        "smicCategoryCd": 25,
        "smicCategoryDesc": "PROPANE -NS",
        "smicCategoryId": 3425,
        "retailSectionCd": 322,
        "retailSectionName": "HOME CARE GMHBC",
        "deskId": "23-05",
        "deskName": "05-ANGELA CEJA"
    },
               
    {
        "divisionId": 20,
        "divisionName": "Southern",
        "deptId": 312,
        "deptName": "GM/HBC",
        "smicGroupCd": 71,
        "smicGroupDesc": "SOCIAL EXPRESSIONS",
        "smicCategoryCd": 35,
        "smicCategoryDesc": "CALENDARS",
        "smicCategoryId": 7135,
        "retailSectionCd": 311,
        "retailSectionName": "GENERAL MERCHANDISE",
        "deskId": "23-05",
        "deskName": "05-ANGELA CEJA"
    },
    {
        "divisionId": 5,
        "divisionName": "Denver",
        "deptId": 312,
        "deptName": "TestDept",
        "smicGroupCd": 71,
        "smicGroupDesc": "SOCIAL EXPRESSIONS",
        "smicCategoryCd": 35,
        "smicCategoryDesc": "CALENDARS",
        "smicCategoryId": 7135,
        "retailSectionCd": 311,
        "retailSectionName": "GENERAL MERCHANDISE",
        "deskId": "23-05",
        "deskName": "05-ANGELA CEJA"
    },
    {
        "divisionId": 5,
        "divisionName": "Denver",
        "deptId": 311,
        "deptName": "GM/HBC",
        "smicGroupCd": 71,
        "smicGroupDesc": "SOCIAL EXPRESSIONS",
        "smicCategoryCd": 35,
        "smicCategoryDesc": "CALENDARS",
        "smicCategoryId": 7135,
        "retailSectionCd": 311,
        "retailSectionName": "GENERAL MERCHANDISE",
        "deskId": "23-05",
        "deskName": "05-ANGELA CEJA"
    }
    
]

describe('WorksheetHeaderControls', () => {
  const mockUseSelectorWrap = jest.mocked(rtkUtilities.useSelectorWrap);
  const mockUseShouldDisableEditForecastButton = jest.mocked(periodCloseFlags.useShouldDisableEditForecastButton);

  beforeEach(() => {
    // Set up default mocks
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'displayDate_rn':
          return {
            data: {
              fiscalYearNumber: 2025,
              fiscalQuarterNumber: 3,
              fiscalWeekNumber: 31,
              fiscalWeekEndDate: '08/22/2025',
            },
          };
        case 'appliedFilter_rn':
          return {
            data: {
              division: [],
              department: [],
              desk: [],
              category: [],
              selectedSm: '',
              selectedAsm: [],
            },
          };
        case 'adjustmentWorkSheetFilter_rn':
          return {
            data: {
              'div_5_dept_311': { divisionId: 5, deptId: 311, data: 'sample' },
              'div_20_dept_312': { divisionId: 20, deptId: 312, data: 'sample' },
            },
          };
        case 'userInfo_rn':
          return {
            data: {
              userId: 'testuser',
              role: 'SM',
            },
          };
        case 'roleMappingInfo_rn':
          return {
            data: {
              userRole: 'SM',
            },
          };
        case 'editAdjustmentPermission_rn':
          return {
            data: {
              disabled: false,
            },
          };
        case 'departments_rn':
          return {
            data: [{
              num: 311,
              name: 'GM/HBC',
              isSelected: false
            }],
          };
        case 'activeTabInFilter_rn':
          return {
            data: ['division'],
          };
        case 'asmDataForSelectedSm_rn':
          return {
            data: {
              selectedSm: null,
              asmForSelectedSm: [],
              smDataForSelectedDept: []
            },
          };
        case 'selectedAsm_rn':
          return {
            data: [],
          };
        default:
          return { data: null };
      }
    });
    
    mockUseShouldDisableEditForecastButton.mockReturnValue(false);
    
    // Mock localStorage
    Storage.prototype.getItem = jest.fn(() => null);
    Storage.prototype.setItem = jest.fn();
    
    jest.clearAllMocks();
  });

  test('renders adjustment worksheet with fiscal year and quarter', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText(/Adjustment worksheet 2025 Q3/)).toBeInTheDocument();
  });

  test('renders today information with week and quarter', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Today:')).toBeInTheDocument();
    expect(screen.getByText('Week 31')).toBeInTheDocument();
    expect(screen.getByText('Q3')).toBeInTheDocument();
    expect(screen.getByText('FY2025')).toBeInTheDocument();
  });

  test('renders Audit History button', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData ?? []} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Audit History')).toBeInTheDocument();
  });

  test('renders Edit forecast button', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Edit forecast')).toBeInTheDocument();
  });

  test('opens audit history modal when button is clicked', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    fireEvent.click(screen.getByText('Audit History'));
    // Check that modal state changes
  });

  test('opens edit forecast modal when button is clicked', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    fireEvent.click(screen.getByText('Edit forecast'));
    // Check that modal state changes
  });

  test('disables edit forecast button when ASM is selected', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'displayDate_rn':
          return {
            data: {
              fiscalYearNumber: 2025,
              fiscalQuarterNumber: 3,
              fiscalWeekNumber: 31,
              fiscalWeekEndDate: '08/22/2025',
            },
          };
        case 'appliedFilter_rn':
          return {
            data: {
              selectedAsm: ['John Doe'],
              division: [],
              department: [],
              desk: [],
              category: [],
              selectedSm: '',
            },
          };
        case 'adjustmentWorkSheetFilter_rn':
          return {
            data: {
              'div_5_dept_311': { divisionId: 5, deptId: 311, data: 'sample' },
              'div_20_dept_312': { divisionId: 20, deptId: 312, data: 'sample' },
            },
          };
        case 'userInfo_rn':
          return {
            data: {
              userId: 'testuser',
              role: 'SM',
            },
          };
        case 'roleMappingInfo_rn':
          return {
            data: {
              userRole: 'SM',
            },
          };
        case 'editAdjustmentPermission_rn':
          return {
            data: {
              disabled: true,
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Edit forecast')).toBeDisabled();
  });

  test('disables edit forecast button when user role is ASM', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'displayDate_rn':
          return {
            data: {
              fiscalYearNumber: 2025,
              fiscalQuarterNumber: 3,
              fiscalWeekNumber: 31,
              fiscalWeekEndDate: '08/22/2025',
            },
          };
        case 'appliedFilter_rn':
          return {
            data: {
              division: [],
              department: [],
              desk: [],
              category: [],
              selectedSm: '',
              selectedAsm: [],
            },
          };
        case 'adjustmentWorkSheetFilter_rn':
          return {
            data: {
              'div_5_dept_311': { divisionId: 5, deptId: 311, data: 'sample' },
              'div_20_dept_312': { divisionId: 20, deptId: 312, data: 'sample' },
            },
          };
        case 'userInfo_rn':
          return {
            data: {
              userId: 'testuser',
              role: 'ASM',
            },
          };
        case 'roleMappingInfo_rn':
          return {
            data: {
              userRole: 'ASM',
            },
          };
        case 'editAdjustmentPermission_rn':
          return {
            data: {
              disabled: true,
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Edit forecast')).toBeDisabled();
  });

  test('disables edit forecast button when desk is selected', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'displayDate_rn':
          return {
            data: {
              fiscalYearNumber: 2025,
              fiscalQuarterNumber: 3,
              fiscalWeekNumber: 31,
              fiscalWeekEndDate: '08/22/2025',
            },
          };
        case 'appliedFilter_rn':
          return {
            data: {
              division: [],
              department: [],
              desk: [{ num: 1, name: 'Desk 1' }],
              category: [],
              selectedSm: '',
              selectedAsm: [],
            },
          };
        case 'adjustmentWorkSheetFilter_rn':
          return {
            data: {
              'div_5_dept_311': { divisionId: 5, deptId: 311, data: 'sample' },
              'div_20_dept_312': { divisionId: 20, deptId: 312, data: 'sample' },
            },
          };
        case 'userInfo_rn':
          return {
            data: {
              userId: 'testuser',
              role: 'SM',
            },
          };
        case 'roleMappingInfo_rn':
          return {
            data: {
              userRole: 'SM',
            },
          };
        case 'editAdjustmentPermission_rn':
          return {
            data: {
              disabled: true,
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText('Edit forecast')).toBeDisabled();
  });

  test('handles quarter change callback', () => {
    const mockOnQuarterChange = jest.fn();
    
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} onQuarterChange={mockOnQuarterChange} />
        </Provider>
      </Router>
    );
    
    // Since we can't easily test the QuarterTabs component interaction
    // we'll just verify the component renders without error
    expect(screen.getByText(/Adjustment worksheet/)).toBeInTheDocument();
  });

  test('handles localStorage with valid data', () => {
    const validStorageData = JSON.stringify({
      division: [{ num: 5, name: 'Denver' }],
      department: [],
      desk: [],
      category: [],
      selectedSm: '',
      selectedAsm: [],
    });
    
    Storage.prototype.getItem = jest.fn(() => validStorageData);
    
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText(/Adjustment worksheet/)).toBeInTheDocument();
  });

  test('handles localStorage with invalid divisions', () => {
    const invalidStorageData = JSON.stringify({
      division: [{ num: 999, name: 'Invalid Division' }],
      department: [],
      desk: [],
      category: [],
      selectedSm: '',
      selectedAsm: [],
    });
    
    Storage.prototype.getItem = jest.fn(() => invalidStorageData);
    
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByText(/Adjustment worksheet/)).toBeInTheDocument();
  });

  

  test('opens filter modal when Change link is clicked', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    fireEvent.click(screen.getByText('Change'));
    expect(screen.getByText('Division')).toBeInTheDocument();
  });

  test('displays alert when no division is selected and Apply is clicked', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    fireEvent.click(screen.getByText('Change'));
    fireEvent.click(screen.getByText('Apply'));
    expect(screen.getByText('Please select division(s)')).toBeInTheDocument();
  });

//   test('applies filters correctly', () => {
//     render(
//       <Router>
//         <Provider store={app_store}>
//           <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
//         </Provider>
//       </Router>
//     );
//     fireEvent.click(screen.getByText('Change'));
//     fireEvent.click(screen.getByLabelText('5 - Denver'));
//     fireEvent.click(screen.getByText('Apply'));
//     expect(screen.getByText('Division: 5 - Denver')).toBeInTheDocument();
//   });

  test('resets filters correctly', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <WorksheetHeaderControls FiltersList={mockWorksheetFilterData} />
        </Provider>
      </Router>
    );
    fireEvent.click(screen.getByText('Change'));
    fireEvent.click(screen.getByLabelText('5 - Denver'));
    fireEvent.click(screen.getByText('Cancel'));
    fireEvent.click(screen.getByText('Change'));
    expect(screen.getByLabelText('5 - Denver')).not.toBeChecked();
  });

});
