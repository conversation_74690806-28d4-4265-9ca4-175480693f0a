# menfpt-category-ui 
The MENFPT microfrontend app of the Merchant Supplier Portal application. 
## Table of Contents 
-   Description
-   Getting Started
-   Technology Stack
-   Installation
-   Usage
-   Contributing
-   Contact 
## Description 
The app is a client-side rendered app. The app is served from the node-server located at `./apps/menfpt-category-ui-static-server` 
## Getting Started 
This section provides instructions on how to set up and work on the project. 
### Technology Stack 
-   [ReactJS](https://reactjs.org/)
-   [Webpack Module Federation](https://webpack.js.org/concepts/module-federation/)
-   [TypeScript](https://www.typescriptlang.org/)
-   [RTK](https://redux-toolkit.js.org/)
-   [RTK Query](https://redux-toolkit.js.org/rtk-query/overview)
-   [Tailwind CSS](https://tailwindcss.com/)
-   [Jest](https://jestjs.io/)
-   [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
-   [Express](https://expressjs.com/)
-   [NX](https://nx.dev/) 


To run a specific testfile:
npx nx test menfpt-category-ui --testFile=apps/menfpt-category-ui/src/features/worksheetFilter/components/roles/rolesUtils.spec.ts --watch=false


