import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DepartmentDeskTabs } from './departmentDeskTabs';
import { Provider } from 'react-redux';
import { app_store } from '../../../../rtk/store';
import { MemoryRouter } from 'react-router-dom';

const departments = [
  { name: 'Dept 1', num: 1 },
  { name: 'Dept 2', num: 2 },
];
const desks = [
  { name: 'Desk 1', num: 1 },
  { name: 'Desk 2', num: 2 },
];
const smDataForSelectedDept = [
  { sm: 'SM1', asmArr: ['ASM1', 'ASM2'] },
];

describe('DepartmentDeskTabs', () => {
  let onDepartmentChange: jest.Mock;
  let onDeskChange: jest.Mock;
  let onDepartmentSearch: jest.Mock;
  let onDeskSearch: jest.Mock;

  beforeEach(() => {
    onDepartmentChange = jest.fn();
    onDeskChange = jest.fn();
    onDepartmentSearch = jest.fn();
    onDeskSearch = jest.fn();
  });

  it('renders department and desk tabs', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={departments[0]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    // There are multiple elements with text 'Department', so use getAllByText and check length
    expect(screen.getAllByText('Department').length).toBeGreaterThan(0);
    expect(screen.getByText('Desk')).toBeInTheDocument();
  });

  it('calls onDepartmentChange when department changes', () => {
    // This test assumes DepartmentSelection triggers onDepartmentChange
    // You may need to mock DepartmentSelection for a real test
    // For now, just check the prop is passed
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={departments[0]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    // Simulate department change
    // fireEvent or act would be used if DepartmentSelection was not a stub
    expect(onDepartmentChange).not.toHaveBeenCalled();
  });

  it('calls onDeskChange when desk changes', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={departments[0]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    expect(onDeskChange).not.toHaveBeenCalled();
  });

  it('shows SmRoleUsersList when isDisplayDeptRoleCascade is true', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={departments[0]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={true}
          />
        </MemoryRouter>
      </Provider>
    );
    // SmRoleUsersList is rendered, but no department is selected, so it shows the empty state
    expect(screen.getByText('Select a department to view')).toBeInTheDocument();
  });

  it('handles empty departments and desks', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={[]}
            desks={[]}
            selectedDepartment={undefined}
            selectedDesk={undefined}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={[]}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    // There are multiple elements with text 'Department', so use getAllByText and check length
    expect(screen.getAllByText('Department').length).toBeGreaterThan(0);
    expect(screen.getByText('Desk')).toBeInTheDocument();
  });

  it('handles multiple department selection', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={[departments[0], departments[1]]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={true}
            searchQueryDepartment=""
            searchQueryDesk=""
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    // There are multiple elements with text 'Department', so use getAllByText and check length
    expect(screen.getAllByText('Department').length).toBeGreaterThan(0);
  });

  it('handles search queries for department and desk', () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <DepartmentDeskTabs
            departments={departments}
            desks={desks}
            selectedDepartment={departments[0]}
            selectedDesk={desks[0]}
            isMultipleDeptsSelectionAllowed={false}
            searchQueryDepartment="searchDept"
            searchQueryDesk="searchDesk"
            onDepartmentChange={onDepartmentChange}
            onDeskChange={onDeskChange}
            onDepartmentSearch={onDepartmentSearch}
            onDeskSearch={onDeskSearch}
            smDataForSelectedDept={smDataForSelectedDept}
            isDisplayDeptRoleCascade={false}
          />
        </MemoryRouter>
      </Provider>
    );
    // There are multiple elements with text 'Department', so use getAllByText and check length
    expect(screen.getAllByText('Department').length).toBeGreaterThan(0);
    expect(screen.getByText('Desk')).toBeInTheDocument();
  });
}); 