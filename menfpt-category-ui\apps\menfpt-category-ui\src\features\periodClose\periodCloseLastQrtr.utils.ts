import { periodCloseDaysConfig } from 'libs/features/src/lib/periodClose/periodClose.config';
import { generateQuartersForYear } from '../worksheetFilter/utils/quarterUtils';
import { getNowInPST } from '../../util/dateUtils';

function getAllowedDaysWindow(): number {
  return (
    periodCloseDaysConfig.actualDaysForCertification +
    periodCloseDaysConfig.bufferDaysForCertification +
    periodCloseDaysConfig.bufferDaysForLock
  );
}

function isWithinAllowedWindow(startDate: string | Date, allowedDays: number): boolean {
  const start = new Date(startDate);
  const now = getNowInPST();
  const diffDays = Math.floor((now.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  return diffDays >= 0 && diffDays <= allowedDays;
}

function getPreviousQuarterNumber(
  currentQuarterNbr: number,
  currentYear: number
): number | null {
  const quartersList = generateQuartersForYear(currentYear);
  const currentIdx = quartersList.findIndex(
    (q: any) => q.fiscalQuarterNumber === currentQuarterNbr
  );
  if (currentIdx === -1) return null;
  if (currentIdx === 0) {
    // First quarter of the year, get last quarter from previous year
    const prevYrQuartersList = generateQuartersForYear(currentYear - 1);
    return prevYrQuartersList[prevYrQuartersList.length - 1].fiscalQuarterNumber;
  }
  return quartersList[currentIdx - 1].fiscalQuarterNumber;
}

export function getLastQuarterNbr({
  qrtrNbrDisplayedInTable,
  currentQuarterNbr,
  currentQuarterStartDate,
}: {
  qrtrNbrDisplayedInTable: number;
  currentQuarterNbr: number;
  currentQuarterStartDate: string | Date;
}): number | null {
  if (
    typeof currentQuarterNbr !== 'number' ||
    typeof qrtrNbrDisplayedInTable !== 'number' ||
    !currentQuarterStartDate
  ) {
    return null;
  }
  if (currentQuarterNbr !== qrtrNbrDisplayedInTable) {
    return null;
  }
  const allowedDays = getAllowedDaysWindow();
  if (!isWithinAllowedWindow(currentQuarterStartDate, allowedDays)) {
    return null;
  }
  const currentYear = getNowInPST().getFullYear();
  return getPreviousQuarterNumber(currentQuarterNbr, currentYear);
}

/* export function getLastQuarterData({
  qrtrNbrDisplayedInTable,
  currentQuarterNbr,
  quartersList
}){
  const lastQuarterNbr = getLastQuarterNbr({
    qrtrNbrDisplayedInTable,
    currentQuarterNbr,
    
  });
  
 
} */
//generate statuses

/* const quarterNbr = getLastQuarterData({
  qrtrNbrDisplayedInTable: getRequestedQuarterNbr(),
  currentQuarterNbr,
  quartersList,
  getRequestedQuarterNbr
});
 */
