// Formats a number with commas, returns '' for null/undefined
export function formatHelper(value: number | string | null | undefined): string {
  if (value === null || value === undefined) return '';
  let num: number;
  if (typeof value === 'string') {
    // Accept string numbers, including exponential notation
    num = Number(value);
    if (value.trim() === '' || isNaN(num)) return '';
  } else if (typeof value === 'number') {
    if (isNaN(value)) return '';
    num = value;
  } else {
    return '';
  }
  // Preserve decimal precision if present
  const parts = String(num).split('.');
  if (parts.length === 2) {
    // Use toLocaleString for integer part, append decimals
    return Number(parts[0]).toLocaleString('en-US') + '.' + parts[1];
  }
  return num.toLocaleString('en-US');
}
export const getSmicsForDeptsAndDivisions = (deptIds: string[], division: string[], smicData: any) => {
    const ids = smicData.filter((item: any) => division.includes(item.divisionId))
    .filter((_item: any) => deptIds.includes(_item.deptId))
    .map((smic: any) => smic.smicCategoryId ? smic.smicCategoryId.toString() : ''
    );
    return Array.from(new Set(ids));
};

export const getAllCategoriesFromFilter = (appliedFilters): string[] => {
  let categories = []  
  categories = appliedFilters?.category?.map((item: any) => {
    return item?.num?.toString();
  });
  return categories;
}

export const borderClass = "!border-r-[4px] !border-r-[#9DA4AE]";