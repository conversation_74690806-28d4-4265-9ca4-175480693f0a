Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
[1m[33mCould not find a version for "events" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "clsx" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux-thunk" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "react-router" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "powerbi-client" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
<i> [1m[32m[webpack-dev-server] Project is running at:[39m[22m
<i> [1m[32m[webpack-dev-server] Loopback: [36mhttp://localhost:3010/[39m, [36mhttp://[::1]:3010/[39m[39m[22m
<i> [1m[32m[webpack-dev-server] 404s will fallback to '[36m/index.html[39m'[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mWeb Development Server is listening at http://localhost:3010/[22m


[createGlobPatternsForDependencies] WARNING: There was no ProjectGraph available to read from, returning an empty array of glob patterns

Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB [1m[32m[rendered][39m[22m
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB [1m[32m[rendered][39m[22m
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared) [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes [1m[32m[rendered][39m[22m
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/pages/dashboard-tabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/udsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/bottomdropdowm.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/quarterTabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/editForecast.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/EPBCSSyncMonitor.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/forecastTimestampsFooter.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/AllocatrInsights/AllocatrInsightsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/weekSelection.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/historyTimeline.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/tooltipStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/timeframe/timeframeSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/deptDesk/departmentDeskSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/searchIcon.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (41072f68b5927f8c)
[ [32mready[39m ] http://localhost:3010
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /.well-known/appspecific/com.chrome.devtools.json[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (80fb47ec05819be7)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (84973a37f8931a43)
[36mType-checking in progress...[39m
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ac7e685b3f3da36c)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (73273d5aa39589e7)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (73273d5aa39589e7)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (73273d5aa39589e7)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (b9c958524cfe699b)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (09796838d406823f)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 964 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (594a560f4ca7a49d)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (72440baf7d53c1f2)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (df4cbb66e0f8157a)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (2b45919b6c376457)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:62[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:100[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m306:51[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
  [31m[1m>[22m[39m[90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m     |[39m                                                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m310:21[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m
  [31m[1m>[22m[39m[90m 310 |[39m                     dataToUse [33m=[39m performanceSummaryData[33m;[39m
   [90m     |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 311 |[39m                     console[33m.[39mlog([32m"Using data from state:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 312 |[39m                   }
   [90m 313 |[39m                   [0m

Found [31m[1m4 errors[22m[39m in 1408 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (a9249207401adcd5)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:62[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m304:100[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 302 |[39m                   [36mlet[39m dataToUse [33m=[39m [][33m;[39m
   [90m 303 |[39m                   
  [31m[1m>[22m[39m[90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m     |[39m                                                                                                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
   [90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m306:51[22m[39m
[90mTS2339: [39mProperty '__react_instance' does not exist on type 'Element'.
  [0m [90m 304 |[39m                   [36mif[39m (allocatrComponent [33m&&[39m allocatrComponent[33m.[39m__react_instance [33m&&[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata) {
   [90m 305 |[39m                     [90m// Direct access to React component data if available[39m
  [31m[1m>[22m[39m[90m 306 |[39m                     dataToUse [33m=[39m allocatrComponent[33m.[39m__react_instance[33m.[39mdata[33m;[39m
   [90m     |[39m                                                   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 307 |[39m                     console[33m.[39mlog([32m"Using data directly from component:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m[0m

[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx[22m:[32m[1m310:21[22m[39m
[90mTS2322: [39mType 'any[]' is not assignable to type 'never[]'.
  Type 'any' is not assignable to type 'never'.
  [0m [90m 308 |[39m                   } [36melse[39m [36mif[39m (performanceSummaryData [33m&&[39m performanceSummaryData[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 309 |[39m                     [90m// Check if it's already in the right format[39m
  [31m[1m>[22m[39m[90m 310 |[39m                     dataToUse [33m=[39m performanceSummaryData[33m;[39m
   [90m     |[39m                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 311 |[39m                     console[33m.[39mlog([32m"Using data from state:"[39m[33m,[39m dataToUse)[33m;[39m
   [90m 312 |[39m                   }
   [90m 313 |[39m                   [0m

Found [31m[1m4 errors[22m[39m in 2191 ms.
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_react-router-dom_dist_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_react-redux_es_index_js.js[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 962 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (784747db9bc9e87a)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (c5450eb70ad45608)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (8b6646f9fb228736)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (e4c5ea4ff92e27c8)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (b1d149e5db8f8302)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (10b06a2f7ae6595e)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (8d88b9affc1460e6)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /src_bootstrap_tsx.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /favicon.ico[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (4c261d8e050fb91c)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (4a5f7110457ce7f4)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (675756ace26f1e0d)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /libs_utils_src_index_ts.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_date-fns-tz_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client_dist_powerbi_js.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (f7dbf7467b47703a)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (a91d781ec2385960)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (01eba0ec5e02cc2c)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 966 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (9b8ab5405ad42125)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (cfa216f6f6c5d780)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (2701addcc2d653fa)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (f9bb3644fca4fc2b)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (0a8cf860bb524e31)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 967 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ba3c5261e18abcbc)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (3e64ffad547d9f9d)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /menfpt/dashboard[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (c8547620537eda22)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ed5af4c0d50307df)
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /menfpt/dashboard[39m[22m
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.2 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 963 KiB (javascript) 630 bytes (consume-shared) 52.2 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.2 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (ed5af4c0d50307df)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /src_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.25 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.24 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 630 bytes (share-init) 27.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 630 bytes (share-init) 27 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 630 bytes (share-init) 18.8 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 759 KiB (javascript) 504 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 630 bytes (share-init) 18.8 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1m[31mERROR[39m[22m in [1m./src/pages/dashboard-tabs.tsx[39m[22m
[1mModule build [1m[31mfailed[39m[22m[1m (from ../../node_modules/@nrwl/webpack/src/utils/web-babel-loader.js):
[1m[31mError[39m[22m[1m: ENOENT: no such file or directory, open 'C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\dashboard-tabs.tsx'[39m[22m

webpack compiled with [1m[31m1 error[39m[22m (4f2f0f8280c1eb5f)
[36mType-checking in progress...[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_react-router-dom_dist_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_react-redux_es_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /node_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client_dist_powerbi_js.js.map[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (2cf5db7c7a34b7f5)
[36mType-checking in progress...[39m
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/home.tsx[22m:[32m[1m2:27[22m[39m
[90mTS2307: [39mCannot find module './dashboard-tabs' or its corresponding type declarations.
  [0m [90m 1 |[39m [36mimport[39m [33mDashboardTitleComponent[39m [36mfrom[39m [32m"./dashboard-title-component"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m [33mDashboardTabs[39m [36mfrom[39m [32m"./dashboard-tabs"[39m[33m;[39m
   [90m   |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 3 |[39m
   [90m 4 |[39m [36mimport[39m [33mTableContainer[39m [36mfrom[39m [32m"./worksheet-table-container"[39m[33m;[39m
   [90m 5 |[39m [36mimport[39m { useCombinedFiltersAndQuarters } [36mfrom[39m [32m"../features/worksheetFilter/useCombinedFiltersAndQuarters"[39m[33m;[39m[0m

Found [31m[1m1 error[22m[39m in 14016 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (78230e5d4f688d22)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/home.tsx[22m:[32m[1m2:27[22m[39m
[90mTS2307: [39mCannot find module './dashboard-tabs' or its corresponding type declarations.
  [0m [90m 1 |[39m [36mimport[39m [33mDashboardTitleComponent[39m [36mfrom[39m [32m"./dashboard-title-component"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m [33mDashboardTabs[39m [36mfrom[39m [32m"./dashboard-tabs"[39m[33m;[39m
   [90m   |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 3 |[39m
   [90m 4 |[39m [36mimport[39m [33mTableContainer[39m [36mfrom[39m [32m"./worksheet-table-container"[39m[33m;[39m
   [90m 5 |[39m [36mimport[39m { useCombinedFiltersAndQuarters } [36mfrom[39m [32m"../features/worksheetFilter/useCombinedFiltersAndQuarters"[39m[33m;[39m[0m

Found [31m[1m1 error[22m[39m in 1379 ms.
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (50e930f5d41d7a18)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/home.tsx[22m:[32m[1m2:27[22m[39m
[90mTS2307: [39mCannot find module './dashboard-tabs' or its corresponding type declarations.
  [0m [90m 1 |[39m [36mimport[39m [33mDashboardTitleComponent[39m [36mfrom[39m [32m"./dashboard-title-component"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m [33mDashboardTabs[39m [36mfrom[39m [32m"./dashboard-tabs"[39m[33m;[39m
   [90m   |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 3 |[39m
   [90m 4 |[39m [36mimport[39m [33mTableContainer[39m [36mfrom[39m [32m"./worksheet-table-container"[39m[33m;[39m
   [90m 5 |[39m [36mimport[39m { useCombinedFiltersAndQuarters } [36mfrom[39m [32m"../features/worksheetFilter/useCombinedFiltersAndQuarters"[39m[33m;[39m[0m

Found [31m[1m1 error[22m[39m in 1098 ms.
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js.map[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_prop-types_index_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /vendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (89483b1daf65a296)
[31m[1mERROR[22m[39m in [1m./apps/menfpt-category-ui/src/pages/home.tsx[22m:[32m[1m2:27[22m[39m
[90mTS2307: [39mCannot find module './dashboard-tabs' or its corresponding type declarations.
  [0m [90m 1 |[39m [36mimport[39m [33mDashboardTitleComponent[39m [36mfrom[39m [32m"./dashboard-title-component"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m [33mDashboardTabs[39m [36mfrom[39m [32m"./dashboard-tabs"[39m[33m;[39m
   [90m   |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 3 |[39m
   [90m 4 |[39m [36mimport[39m [33mTableContainer[39m [36mfrom[39m [32m"./worksheet-table-container"[39m[33m;[39m
   [90m 5 |[39m [36mimport[39m { useCombinedFiltersAndQuarters } [36mfrom[39m [32m"../features/worksheetFilter/useCombinedFiltersAndQuarters"[39m[33m;[39m[0m

Found [31m[1m1 error[22m[39m in 1015 ms.
