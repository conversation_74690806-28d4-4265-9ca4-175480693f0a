import { FormConfig } from "./formConfig";

export const deactivateUserFormConfig: FormConfig = {
  role: {
    label: 'Role',
    placeholder: '',
    tooltip: 'Assigned Role',
    type: 'display' as const, 
    inputType: 'text' as const,
    required: false,
    disabled: true,
    anchor: 'top',
    infoIcon:true
  },
  userName: {
    label: 'User Name',
    placeholder: '',
    tooltip: 'Associate Name',
    type: 'display' as const,
    inputType: 'text' as const,
    required: true,
    disabled: true,
    anchor: 'top',
    infoIcon:true
  },
   ldap: {
    label: 'LDAP',
    placeholder: '',
    tooltip: 'Lightweight Directory Access Protocol',
    type: 'display' as const,
    inputType: 'text' as const,
    required: false,
    disabled: true,
    anchor: 'right',
    infoIcon:true
  },
  manager: {
    label: 'Manager Name',
    placeholder: '',
    tooltip: 'Reporting Manager Name',
    type: 'display' as const,
    inputType: 'text' as const,
    required: true,
    disabled: true,
    anchor: 'top',
    infoIcon:true
  },
  effectiveStartDate: {
    label: 'Effective Start Date',
    placeholder: '',
    tooltip: 'Date when user access started',
    type: 'display' as const,
    inputType: 'text' as const,
    required: false,
    disabled: true,
    anchor: 'top',
    infoIcon:true
  },
  effectiveEndDate: {
    label: 'Select Effective End Date',
    placeholder: '08/23/2025',
    tooltip: 'Select when user access should end',
    type: 'dateSelector' as const, 
    inputType: 'date' as const,
    required: false,
    disabled: false,
    anchor: 'top',
    infoIcon:false
  }
};