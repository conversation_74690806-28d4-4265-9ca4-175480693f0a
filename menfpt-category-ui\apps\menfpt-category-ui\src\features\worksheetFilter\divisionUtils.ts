import { DropdownType, SmicDataDetails } from '../../interfaces/worksheetFilter';

export const extractDivisionsFromFiltersList = (filtersList: SmicDataDetails[]) => {
  const divisions = new Map<number, DropdownType>();
  filtersList && filtersList.forEach((item: SmicDataDetails) => {
    if(divisions.has(item.divisionId)) {
      const existingDivision: any = divisions.get(item.divisionId) || {};
      const existingBanners: any = existingDivision?.banners || [];
      if(!existingBanners.some(b => b.num === item.bannerId)) {
        existingBanners.push({
          name: `${item?.bannerName}`,
          num: item?.bannerId || 0,
        });
      }
      existingDivision.banners = existingBanners;
      divisions.set(item.divisionId, existingDivision);
    } else {
      divisions.set(item.divisionId, {
        name: `${item?.divisionId} - ${item?.divisionName}`,
        num: item?.divisionId || 0,
        banners: [{
          name: `${item?.bannerName}`,
          num: item?.bannerId || 0,
        }],
      } as DropdownType);
    }
  });
  return divisions.size > 0 ? Array.from(divisions.values()) : [];
};

/**
 * Filters divisions to only include those that exist in the valid divisions list
 * @param divisions The divisions to filter
 * @param filtersList The list of filters containing valid divisions
 * @returns Filtered array of divisions
 */
export const filterValidDivisions = (
  divisions: DropdownType[],
  filtersList: SmicDataDetails[]
): DropdownType[] => {
  const validDivisions = extractDivisionsFromFiltersList(filtersList).map(
    (div) => div.num
  );

  // Filter out invalid divisions
  return divisions.filter((div: DropdownType) =>
    validDivisions.includes(div.num)
  );
};

export const getDivisionsFromFiltersList = (
  FiltersList: SmicDataDetails[]
): DropdownType[] => {
  if (!FiltersList?.length) {
    return [];
  }

  const divisionList: DropdownType[] = [];
  FiltersList.forEach((item: SmicDataDetails) => {
    const div = {
      name: `${item?.divisionId} - ${item?.divisionName}`,
      num: item?.divisionId,
    };
    if (!divisionList.some((d) => d.num === div.num)) {
      divisionList.push(div);
    }
  });
  return divisionList;
};
