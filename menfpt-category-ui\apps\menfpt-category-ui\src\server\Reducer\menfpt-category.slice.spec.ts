import {
  menfptCategorySlice,
  setMenfptCategory,
  workSheetFilterListSlice,
  setWorkSheetFilterList,
  displayDateSlice,
  setDisplayDate,
  userInfoSlice,
  setUserInfo,
  publishHistorySlice,
  setPublishHistory,
  appliedFilterSlice,
  setAppliedFilter,
  adjustmentWorkSheetFilterSlice,
  setAdjustmentWorkSheetFilter,
  featureFlags,
  setFeatureFlags,
  quartersSlice,
  setQuarters,
  alertState,
  setAlertState,
  roleMappingInfo,
  setRoleMappingInfo,
  editAdjustmentPermission,
  setEditAdjustmentPermission,
  helpPdfUrl,
  setHelpPdfUrl,
} from './menfpt-category.slice';

jest.mock('../../features/worksheetFilter/worksheetFilterUtils', () => ({
  getStorageKeyByRoute: jest.fn().mockReturnValue('test-route'),
}));

describe('menfpt-category slices', () => {
  describe('menfptCategorySlice', () => {
    it('should handle setMenfptCategory', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { id: 1, name: 'test' };
      const nextState = menfptCategorySlice.reducer(initialState, setMenfptCategory(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('workSheetFilterListSlice', () => {
    it('should handle setWorkSheetFilterList', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { filter: 'test' };
      const nextState = workSheetFilterListSlice.reducer(initialState, setWorkSheetFilterList(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('displayDateSlice', () => {
    it('should handle setDisplayDate', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { date: '2025-06-18' };
      const nextState = displayDateSlice.reducer(initialState, setDisplayDate(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('userInfoSlice', () => {
    it('should handle setUserInfo', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { user: 'test-user' };
      const nextState = userInfoSlice.reducer(initialState, setUserInfo(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('publishHistorySlice', () => {
    it('should handle setPublishHistory', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { history: [] };
      const nextState = publishHistorySlice.reducer(initialState, setPublishHistory(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('appliedFilterSlice', () => {
    it('should handle setAppliedFilter', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { filter: 'applied' };
      const nextState = appliedFilterSlice.reducer(initialState, setAppliedFilter(payload));
      expect(nextState.data).toEqual({ ...payload, filterPg: 'test-route' });
    });
  });

  describe('adjustmentWorkSheetFilterSlice', () => {
    it('should handle setAdjustmentWorkSheetFilter', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { filter: 'adjustment' };
      const nextState = adjustmentWorkSheetFilterSlice.reducer(initialState, setAdjustmentWorkSheetFilter(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('featureFlags', () => {
    it('should handle setFeatureFlags', () => {
      const initialState = { status: 'loading', data: { dashboardFilter: false } };
      const payload = { dashboardFilter: true };
      const nextState = featureFlags.reducer(initialState, setFeatureFlags(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('quartersSlice', () => {
    it('should handle setQuarters', () => {
      const initialState = { status: 'loading', data: [] };
      const payload = [{ id: 1, name: 'Q1' }];
      const nextState = quartersSlice.reducer(initialState, setQuarters(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('alertState', () => {
    it('should handle setAlertState', () => {
      const initialState = { status: 'loading', data: { error: false, success: false } };
      const payload = { error: true, success: false };
      const nextState = alertState.reducer(initialState, setAlertState(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('roleMappingInfo', () => {
    it('should handle setRoleMappingInfo', () => {
      const initialState = { status: 'loading', data: {} };
      const payload = { role: 'admin' };
      const nextState = roleMappingInfo.reducer(initialState, setRoleMappingInfo(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('editAdjustmentPermission', () => {
    it('should handle setEditAdjustmentPermission', () => {
      const initialState = { status: 'loading', data: { disabled: false } };
      const payload = { disabled: true };
      const nextState = editAdjustmentPermission.reducer(initialState, setEditAdjustmentPermission(payload));
      expect(nextState.data).toEqual(payload);
    });
  });

  describe('helpPdfUrl', () => {
    it('should handle setHelpPdfUrl', () => {
      const initialState = { status: 'loading', data: { error: false, success: false } };
      const payload = { url: 'http://example.com/help.pdf' };
      const nextState = helpPdfUrl.reducer(initialState, setHelpPdfUrl(payload));
      expect(nextState.data).toEqual(payload);
    });
  });
});