import React, { useState, useEffect } from 'react';
import { Formik } from 'formik';
import { DropdownType } from '../../interfaces/worksheetFilter';
import { AddUserSchema } from './shared/validationSchemas';
import { generateLDAP } from './shared/formUtils';
import { useFormData } from './shared/useFormData';
import { defaultFormFields } from './shared/formConfig';
import { DynamicFormFields } from './shared/DynamicFormFields';
import { FormActions } from './shared/FormActions';

interface AddUserFormProps {
  onBack?: () => void;
  onCancel?: () => void;
  onSubmit?: (values: any) => void;
}

export const AddUserForm: React.FC<AddUserFormProps> = ({ 
  onBack, 
  onCancel, 
  onSubmit 
}) => {
  const [FiltersList, setFiltersList] = useState<any[]>([]);
  const [selectedDivision, setSelectedDivision] = useState<DropdownType | null>(null);
  const { departments, desks } = useFormData({ selectedDivision, FiltersList });
  const formConfig = {
    ...defaultFormFields,
    department: {
      ...defaultFormFields.department,
      options: departments
    },
    desk: {
      ...defaultFormFields.desk,
      options: desks
    }
  };

  return (
    <Formik
      initialValues={{
        role: '',
        userName: '',
        ldap: '',
        manager: '',
        department: '',
        desk: '',
      }}
      validationSchema={AddUserSchema}
      validateOnChange={false}
      validateOnBlur
      onSubmit={(values) => {
        if (onSubmit) {
          onSubmit(values);
        } else {
          alert(JSON.stringify(values, null, 2));
        }
      }}
    >
      {({ values, errors, handleChange, handleSubmit, setFieldValue, validateField }) => {
        useEffect(() => {
          if (values.userName) {
            const generatedLDAP = generateLDAP(values.userName);
            setFieldValue('ldap', generatedLDAP);
          } else {
            setFieldValue('ldap', '');
          }
        }, [values.userName, setFieldValue]);

        return (
          <form onSubmit={handleSubmit}>
            <DynamicFormFields
              formConfig={formConfig}
              values={values}
              errors={errors}
              handleChange={handleChange}
              setFieldValue={setFieldValue}
              validateField={validateField}
            />
            <FormActions 
              onBack={onBack}
              onCancel={onCancel}
              onSubmit={handleSubmit}
              showBack={!!onBack}
            />
          </form>
        );
      }}
    </Formik>
  );
};