// export const calculateGrossProfit = (
//     baselineGrossProfit: number,
//     baselineSalesPublic: number,
//     adjustedForecast: number
//   ): number => {
//     return baselineSalesPublic
//       ? (baselineGrossProfit / baselineSalesPublic) * adjustedForecast
//       : 0;
//   };

import { useState } from "react";
import { Adjustment } from "../../interfaces/edit-forecast-adjustments";

  
//   export const calculateMarkdownValue = (
//     baselineSalesPublic: number,
//     baselineMarksDown: number,
//     salesPublicValue: number
//   ): number => {
//     return baselineSalesPublic
//       ? Math.abs((baselineMarksDown / baselineSalesPublic) * salesPublicValue) -
//           baselineMarksDown
//       : 0;
//   };
  
//   export const calculateShrinkValue = (
//     baselineSalesPublic: number,
//     baselineTotalShrink: number,
//     salesPublicValue: number
//   ): number => {
//     return baselineSalesPublic
//       ? Math.abs((baselineTotalShrink / baselineSalesPublic) * salesPublicValue) -
//           baselineTotalShrink
//       : 0;
//   };
  
//   export const calculatePercentages = (
//     grossProfit: number,
//     adjustedForecast: number,
//     markdownValue: number,
//     shrinkValue: number
//   ): { grossProfitPcnt: number; marksDownPcnt: number; totalShrinkPcnt: number } => {
//     const grossProfitPcnt = grossProfit / adjustedForecast;
//     const marksDownPcnt = markdownValue / adjustedForecast;
//     const totalShrinkPcnt = shrinkValue / adjustedForecast;
  
//     return {
//       grossProfitPcnt: Number(grossProfitPcnt.toFixed(2)),
//       marksDownPcnt: Number(marksDownPcnt.toFixed(2)),
//       totalShrinkPcnt: Number(totalShrinkPcnt.toFixed(2)),
//     };
//   };

export const useEditForecastBffBody = () => {
    const [editForecastBffBody, setEditForecastBffBody] = useState<Adjustment>();
    return { editForecastBffBody, setEditForecastBffBody };
}
  
const calculateSalesPublicPcnt = (salesPublicValue, baselineSalesPublic) => {
return salesPublicValue / baselineSalesPublic;
};

const calculateGrossProfit = (baselineGrossProfit, baselineSalesPublic, adjustedForecast) => {
return baselineSalesPublic
    ? ((baselineGrossProfit / baselineSalesPublic) * adjustedForecast)
    : 0;
};

const calculateMarkdownValue = (baselineMarksDown, baselineSalesPublic, salesPublicValue) => {
return baselineSalesPublic
    ? (baselineMarksDown / baselineSalesPublic) * salesPublicValue
    : 0;
};

const calculateShrinkValue = (baselineTotalShrink, baselineSalesPublic, salesPublicValue) => {
return baselineSalesPublic
    ? (baselineTotalShrink / baselineSalesPublic) * salesPublicValue
    : 0;
};

const calculateGrossProfitPcnt = (baselineGrossProfit, adjustedForecast) => {
return baselineGrossProfit / adjustedForecast;
};

const calculateMarksDownPcnt = (markdownValue, adjustedForecast) => {
return markdownValue / adjustedForecast;
};

const calculateTotalShrinkPcnt = (shrinkValue, adjustedForecast) => {
return shrinkValue / adjustedForecast;
};

const safeScrollIntoView = (element: Element | null, options?: ScrollIntoViewOptions) => {
    if (element && typeof element.scrollIntoView === 'function') {
      element.scrollIntoView(options);
    }
  };

const scrollToFirstError = (errors: any, selectors: Record<string, string>,fieldOrder : string[]) => {
    // Define the order of fields as they appear in your form
    
    // Find the first field with an error based on form layout
    const firstErrorField = fieldOrder.find(field => errors[field]);
    
    if (!firstErrorField) return;
    
    const selector = selectors[firstErrorField];
    
    // Find the element and scroll to it
    setTimeout(() => {
      const errorElement = document.querySelector(selector);
      safeScrollIntoView(errorElement, {
        behavior: 'smooth',
        block: 'center'
      });
    }, 100);
  };

export {
    calculateSalesPublicPcnt,
    calculateGrossProfit,
    calculateMarkdownValue,
    calculateShrinkValue,
    calculateGrossProfitPcnt,
    calculateMarksDownPcnt,
    calculateTotalShrinkPcnt,
    safeScrollIntoView,
    scrollToFirstError
};