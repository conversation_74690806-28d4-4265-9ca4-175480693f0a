export interface DepartmentConfig {
  id: string;
  name: string;
}
export interface AllocatrDashboardReq {
  currentFiscalYearNbr: number;
  quarterNbr: number;
  deptIds: string[];
  divisionIds: string[];
  bannerId?: string[];
  smicCategoryIds: string[];
  type:string;
  weekNumbers: number[];
  filteredWeekNumbers: number[];
  periodNumbers?: number[];
}

export interface AllocatrInsightsResponse {
  id: string;
  name: string;
  quarter?: QuarterData;
  periods?: PeriodData[];
  weeks?: WeekData[];
  divisions?: DivisionData[];
}

export interface DivisionData {
  id: string;
  name: string;
  quarter?: QuarterData;
  banners?: BannerData[];
}

export interface BannerData {
  id: string;
  name: string;
  quarter?: QuarterData;
  departments?: DepartmentData[];
}

export interface DepartmentData {
  id: string;
  name: string;
  quarter: QuarterData;
  periods?: PeriodData[];
  weeks?: WeekData[];
  expanded?: boolean;
  hasMerchantForecast?: boolean;
}

export interface PeriodData extends BaseData {
  periodNumber: number;
}

export interface WeekData extends BaseData {
  fiscalWeekLabel: string; // e.g., "Week 1 (fiscal wk 1)"
  weekNumber: number;
  periodNumber: number;
  isActualUsed?: boolean;
}

export interface QuarterData extends BaseData {
  quarterNumber: number;
}

export interface BaseData {
  id: string;
  line1Projection: number;
  lastYear: number;
  actualOrForecast: number;
  idPercentage: number;
  vsLY: {
    value: number;
    percentage: number;
  };
  vsProjection: {
    value: number;
    percentage: number;
  };
  bookGrossProfit: {
    projectionValue: number;
    projectionPct: number;
    actualOrForecast: number;
    percentActualOrForecast: number;
    vsProjection: number;
  };
  markdown: {
    projectionValue: number;
    projectionPct: number;
    actualOrForecast: number;
    percentActualOrForecast: number;
    vsProjection: number;
  };
  shrink: {
    projectionValue: number;
    projectionPct: number;
    actualOrForecast: number;
    percentActualOrForecast: number;
    vsProjection: number;
  };
  line5: {
    actualOrForecast: number;
    percentActualOrForecast: number;
    projectionValue: number;
    projectionPct: number;
    vsProjection: number;
    percentVsProjection: number;
  };
  line6: {
    projection: number;
    actualOrForecast: number;
    vsProjection: number;
  };
  line7: {
    projection: number;
    actualOrForecast: number;
    vsProjection: number;
  };
  line8: {
    actualOrForecast: number;
    percentActualOrForecast: number;
    projectionValue: number;
    projectionPct: number;
    vsProjection: number;
    percentVsProjection: number;
  };
  hasMerchantForecast?: boolean;
}

export interface AllocatrInsightsState {
  departments: DepartmentData[];
  departmentConfig: DepartmentConfig[];
  loading: boolean;
  error: string | null;
  activeTab: 'leadingIndicators' | 'performanceSummary';
  selectedFiscalWeek: string;
}
