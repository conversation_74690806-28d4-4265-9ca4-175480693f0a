import Alert from '@albertsons/uds/molecule/Alert';
import React from 'react';

interface AlertMessageProps {
  title?: string; 
  message: string;
  type: 'success' | 'error';
  open: boolean;
  onClose?: () => void;
}

const RxForecastAlerts: React.FC<AlertMessageProps> = ({
  title = 'Document upload fail!',
  message,
  type,
  open,
  onClose
}) => {
  return (
    <div className="left-19 z-50">
      <Alert
        timeout={4}
        isOpen={open}
        sticky={true}
        variant={type}
        autoClose={true}
        dismissible={true}
        onClose={onClose}
      >
        <div className="flex flex-col">
          <span className="font-semibold text-base">{title}</span>
          <span className="text-sm">{message}</span>
        </div>
      </Alert>
    </div>
  );
};

export default RxForecastAlerts;
