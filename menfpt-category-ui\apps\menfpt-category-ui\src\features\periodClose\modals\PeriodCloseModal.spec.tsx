import '@testing-library/jest-dom';
jest.mock('@albertsons/uds/molecule/Button', () => (props) => <button {...props} />);
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { PeriodCloseModal } from './PeriodCloseModal';

describe('PeriodCloseModal', () => {
  it('renders modal with correct text and calls onClose', () => {
    const onClose = jest.fn();
    render(<PeriodCloseModal onClose={onClose} quarterLabel="Q1 2024" />);
    expect(screen.getByText('Attention, please read this carefully!')).toBeInTheDocument();
    expect(screen.getByText(/As we switch into a new period/)).toBeInTheDocument();
    const button = screen.getByRole('button', { name: /OK, I understand/i });
    fireEvent.click(button);
    expect(onClose).toHaveBeenCalled();
  });
}); 