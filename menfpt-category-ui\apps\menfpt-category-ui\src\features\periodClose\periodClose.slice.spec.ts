import { prevQuarterTabSlice, setPrevQuarterTabData } from './periodClose.slice';

describe('prevQuarterTabSlice', () => {
  it('should set prevQuarterTab data', () => {
    const initialState = { status: 'loading', data: { isDisplayLastQtrTab: false, lastQtrNbr: null } };
    const payload = { isDisplayLastQtrTab: true, lastQtrNbr: 5 };
    const state = prevQuarterTabSlice.reducer(initialState, setPrevQuarterTabData(payload));
    expect(state.data).toEqual(payload);
  });
}); 