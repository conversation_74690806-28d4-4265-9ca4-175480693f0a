
export enum ADJ_SUB_ROW {
    LAST_YEAR_ACTUAL = "Last year actual",
    ACTUAL_TO_DATE = "Actual to Date",
    ACTUALS = "Actuals",
    FORECAST = "DS Forecast",
    PROJECTION = "Projection",
    WOW_FCST_VAR = "Wow FCST var",
    ACT_TO_PROJ = "ACT to PROJ",
    MERCH_FORECAST = "Merch. Forecast",
}

export interface FormFieldNames {
    salesPublic: number;
    grossProfit: number;
    marksDown: number;
    totalShrink: number;
    suppliesPackaging: number;
    allowances: number;
    selling: number;
    nonSelling: number;
  }



export interface Adjustment {
    smicCategoryIds: number[]
    deptId: string
    deskId: string
    divisionIds: string[]
    bannerId?: string[]
    updatedBy: string
    weeks: Week[],
    isReset?: boolean
}

export interface Week {
    fiscalWeekNbr: number
    editedColumns: string
    previousAggregatedData: AdjustmentData
    newAggregatedData: AdjustmentData
    reason: string
    comment: string
}

export interface AdjustmentData {
    line1PublicToSalesNbr: number
    line1PublicToSalesPct: number
    line5BookGrossProfitNbr: number
    line5BookGrossProfitPct: number
    line5MarkDownsNbr: number
    line5MarkDownsPct: number
    line5ShrinkNbr: number
    line5ShrinkPct: number
    line6SuppliesPackagingNbr: number | null
    line7RetailsAllowancesNbr: number | null
    line7RetailsSellingAllowancesNbr?: number | null
    line7RetailsNonSellingAllowancesNbr?: number | null
}

export interface SaveAdjustmentResponse {
    success: boolean
    message: string
}
