import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface GenericState<T> {
  status: string;
  data: T | null;
}

describe('RTK Slice', () => {
  const initialState: GenericState<any> = {
    status: '',
    data: null,
  };

  const name = 'testSlice';

  it('should handle start action', () => {
    const { reducer } = createSlice({
      name,
      initialState,
      reducers: {
        start: {
          reducer(state: GenericState<any>, action: PayloadAction<any>) {
            state.status = 'loading';
          },
          prepare(payload: any) {
            return { payload };
          },
        },
      },
    });

    const state = reducer(initialState, { type: `${name}/start`, payload: 'testPayload' });

    expect(state.status).toBe('loading');
  });
  

  it('should handle success action', () => {
    const { reducer } = createSlice({
      name,
      initialState,
      reducers: {
        success(state: GenericState<any>, action: PayloadAction<any>) {
          state.data = action.payload;
          state.status = 'finished';
        },
      },
    });

    const state = reducer(initialState, { type: `${name}/success`, payload: 'testPayload' });

    expect(state.data).toBe('testPayload');
    expect(state.status).toBe('finished');
  });
});