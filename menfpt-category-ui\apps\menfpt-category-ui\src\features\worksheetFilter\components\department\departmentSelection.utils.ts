import { DropdownType } from '../../../../interfaces/worksheetFilter';

export function getSelectedDepartments(selectedDepartment?: DropdownType | DropdownType[]): DropdownType[] {
  if (Array.isArray(selectedDepartment)) return selectedDepartment;
  if (selectedDepartment) return [selectedDepartment];
  return [];
}

export function handleMultipleDepartmentSelection(
  department: DropdownType,
  selectedDepts: DropdownType[]
): DropdownType[] {
  const isDeptSelected = selectedDepts.some((dept) => dept.num === department.num);
  if (isDeptSelected) {
    return selectedDepts.filter((dept) => dept.num !== department.num);
  }
  return [...selectedDepts, department];
}

export function shouldUpdateSelection(
  isMultiple: boolean,
  currentSelected: DropdownType | DropdownType[] | undefined,
  foundDept: DropdownType
): boolean {
  if (isMultiple) {
    return !(
      Array.isArray(currentSelected) &&
      currentSelected.length === 1 &&
      currentSelected[0]?.num === foundDept.num
    );
  }
  return !(
    currentSelected &&
    !Array.isArray(currentSelected) &&
    (currentSelected as DropdownType).num === foundDept.num
  );
}

export function shouldClearSelection(
  isMultiple: boolean,
  currentSelected: DropdownType | DropdownType[] | undefined
): boolean {
  if (isMultiple) {
    return !(
      Array.isArray(currentSelected) &&
      currentSelected.length === 0
    );
  }
  return false;
} 