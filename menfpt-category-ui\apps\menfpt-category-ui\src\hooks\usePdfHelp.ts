import { useState, useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setHelpPdfUrl } from '../server/Reducer/menfpt-category.slice';
import { getEndpoints } from '../util/apiEndpoints';

export const usePdfHelp = () => {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const dispatch = useDispatch();

  const fetchPdf = useCallback(async () => {
    try {
      const endpoints = getEndpoints();     
      
      // Fetch the view endpoint to get the PDF URL
      const response = await fetch(endpoints.view);
      if (!response.ok) {
        throw new Error(`Failed to fetch view URL: ${response.status}`);
      }
  
      const data = await response.json();
      const pdfUrl = data.url;      
      
      setPdfUrl(pdfUrl);
    } catch (error) {
      console.error('Error handling the help document:', error);
    }
  }, []);

  useEffect(() => {
    fetchPdf();
  }, [fetchPdf]);

  useEffect(() => {
    if (pdfUrl) {
      dispatch(setHelpPdfUrl(pdfUrl));
      console.log('PDF URL set in Redux store:', pdfUrl);
    }
  }, [dispatch, pdfUrl]);

  return { pdfUrl };
};