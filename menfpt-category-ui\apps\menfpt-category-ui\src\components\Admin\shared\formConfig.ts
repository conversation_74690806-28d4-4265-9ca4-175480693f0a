import { DropdownType } from '../../../interfaces/worksheetFilter';

export interface FormFieldConfig {
  label: string;
  placeholder: string;
  tooltip: string;
  required?: boolean;
  type?: 'input' | 'select' | 'textarea' | 'display' | 'dateSelector';
  inputType?: 'text' | 'date';
  disabled?: boolean;
  options?: any[];
  itemText?: string;
  anchor?: 'top' | 'bottom' | 'left' | 'right';
  infoIcon?:boolean;
}

export interface FormConfig {
  [key: string]: FormFieldConfig;
}
export const defaultFormFields: FormConfig = {
  role: {
    label: 'Role ',
    placeholder: 'Select option',
    tooltip: 'Assigned Role',
    type: 'select',
    required: true,
    itemText: 'name',
    anchor: 'top',
    options: [
      { name: 'Sale Manager', num: 1 },
      { name: 'Finance', num: 2 },
      { name: 'VPMM', num: 3 },
      { name: 'Assistant Sale Manager', num: 4 },
    ],
    infoIcon:true,
  },
  userName: {
    label: 'User Name ',
    placeholder: 'Enter First and Last Name',
    tooltip: 'Associate Name',
    type: 'input',
    inputType: 'text',
    required: true,
    anchor: 'top',
    infoIcon:true,
  },
  ldap: {
    label: 'LDAP ',
    placeholder: 'LDAP',
    tooltip: 'Lightweight Directory Access Protocol',
    type: 'display' as const,
    inputType: 'text',
    required: false,
    disabled: true,
    anchor: 'right',
    infoIcon:true,
  },
  manager: {
    label: 'Manager ',
    placeholder: 'Select Manager',
    tooltip: 'Reporting Manager Name',
    type: 'display' as const,
    inputType: 'text',
    required: true,
    disabled: true,
    anchor: 'top',
    infoIcon:true,
  },
  department: {
    label: 'Department ',
    placeholder: 'Select Department',
    tooltip: 'User Assigned Oracle Department',
    type: 'select',
    required: false,
    itemText: 'name',
    anchor: 'top',
    options: [] ,
    infoIcon:true,
  },
  desk: {
    label: 'Desk ',
    placeholder: 'Select Desk',
    tooltip: 'User Assigned Oracle Desks',
    type: 'select',
    required: true,
    itemText: 'name',
    anchor: 'top',
    options: [] ,
    infoIcon:true,
  }
};