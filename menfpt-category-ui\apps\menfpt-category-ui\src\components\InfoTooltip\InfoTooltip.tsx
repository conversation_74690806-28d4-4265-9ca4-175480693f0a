import Tooltip from '@albertsons/uds/molecule/Tooltip';
import React from 'react';

type TooltipAnchor = 'top' | 'bottom' | 'left' | 'right'; // Adjust if others are valid
type TooltipVariant = 'dark' | 'light'; // Adjust based on actual design system

interface InfoTooltipProps {
  label: string;
  icon: React.ReactNode;
  anchor?: TooltipAnchor;
  variant?: TooltipVariant;
  className?: string;
  zIndex?: number;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({
  label,
  icon,
  anchor = 'top',
  variant = 'dark',
  className = '',
  zIndex = 999,
}) => {
  return (
    <Tooltip
      label={label}
      anchor={anchor}
      variant={variant}
      className={className}
      zIndex={zIndex}
    >
      {icon}
    </Tooltip>
  );
};

export default InfoTooltip;