// Simple test script to verify the new divisions and banners functionality
// This script simulates the new API response structure and tests the Excel download

const { handleDownloadExcel, getDivisionName, getBannerName } = require('./apps/menfpt-category-ui/src/components/DashboardDownloadExcel/DashboardDownloadExcel.tsx');

// Mock data based on the API response structure you provided
const mockApiResponse = {
  "id": "Total",
  "name": "",
  "quarter": {
    "id": "Quarter-202502",
    "quarterNumber": 202502,
    "line1Projection": 225825128.5914994,
    "lastYear": 224884093.76394215,
    "actualOrForecast": 223075434.5662158,
    "idPercentage": -0.008042628393384125,
    "vsLY": {
      "value": -1808659.197726339,
      "percentage": -0.008042628393384125
    },
    "vsProjection": {
      "value": -2749694.025283575,
      "percentage": -0.012176209274998639
    },
    "bookGrossProfit": {
      "projectionValue": 102305200.75781177,
      "projectionPct": 0.45302841803258376,
      "actualOrForecast": 99654613.66917199,
      "percentActualOrForecast": 0.44673055938658884,
      "vsProjection": -0.006297858645994914
    },
    "markdown": {
      "projectionValue": -49141338.049688175,
      "projectionPct": -0.21760792678914465,
      "actualOrForecast": -49145963.55095942,
      "percentActualOrForecast": -0.2203109618346226,
      "vsProjection": -0.0027030350454779473
    },
    "shrink": {
      "projectionValue": -3722004.829366595,
      "projectionPct": -0.016481800996112444,
      "actualOrForecast": -3385418.8087754017,
      "percentActualOrForecast": -0.015176116614357655,
      "vsProjection": 0.001305684381754789
    },
    "line5": {
      "actualOrForecast": 47123231.30943716,
      "percentActualOrForecast": 0.21124348093760859,
      "projectionValue": 49441857.878757,
      "projectionPct": 0.21893869024732668,
      "vsProjection": -2318626.5693,
      "percentVsProjection": -0.0077
    },
    "line6": {
      "projection": 0,
      "actualOrForecast": 20102.101399712,
      "vsProjection": 20102.101399712
    },
    "line7": {
      "projection": 27806351.12037953,
      "actualOrForecast": 30113722.46211171,
      "vsProjection": 2307371.3417321816
    },
    "line8": {
      "actualOrForecast": 77257055.87294859,
      "percentActualOrForecast": 0.34632704413724347,
      "projectionValue": 77248208.99913654,
      "projectionPct": 0.34207091779796,
      "vsProjection": 8846.8738,
      "percentVsProjection": 0.0043
    }
  },
  "divisions": [
    {
      "id": "34",
      "name": "Test Division",
      "quarter": {
        "id": "Quarter-202502",
        "quarterNumber": 202502,
        "line1Projection": 225825128.5914994,
        "lastYear": 224884093.76394215,
        "actualOrForecast": 223075434.5662158,
        "idPercentage": -0.008042628393384125,
        "vsLY": {
          "value": -1808659.197726339,
          "percentage": -0.008042628393384125
        },
        "vsProjection": {
          "value": -2749694.025283575,
          "percentage": -0.012176209274998639
        },
        "bookGrossProfit": {
          "projectionValue": 102305200.75781177,
          "projectionPct": 0.45302841803258376,
          "actualOrForecast": 99654613.66917199,
          "percentActualOrForecast": 0.44673055938658884,
          "vsProjection": -0.006297858645994914
        }
      },
      "banners": [
        {
          "id": "25",
          "name": "Test Banner",
          "quarter": {
            "id": "Quarter-202502",
            "quarterNumber": 202502,
            "line1Projection": 129149501.80419451,
            "lastYear": 128723676.5694745,
            "actualOrForecast": 127246343.43085676
          },
          "departments": [
            {
              "id": "301",
              "name": "Grocery",
              "quarter": {
                "id": "Quarter-202502",
                "quarterNumber": 202502,
                "line1Projection": 129149501.80419451,
                "lastYear": 128723676.5694745,
                "actualOrForecast": 127246343.43085676
              },
              "periods": [
                {
                  "id": "Period-202506",
                  "periodNumber": 202506,
                  "line1Projection": 129149501.80419451,
                  "lastYear": 128723676.5694745,
                  "actualOrForecast": 127246343.43085676
                }
              ],
              "weeks": [
                {
                  "id": "Week-202525",
                  "weekNumber": 202525,
                  "periodNumber": 202507,
                  "line1Projection": 32341866.929070704,
                  "lastYear": 32104742.440233648,
                  "actualOrForecast": 64283531.66046807
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

const mockSmicData = [
  { deptId: '301', deptName: 'Grocery' }
];

const mockAppliedFilters = {
  timeframe: { quarter: 2, fiscalYear: 2025 }
};

// Test the helper functions
console.log('Testing helper functions:');
console.log('getDivisionName("34", "Test Division"):', getDivisionName("34", "Test Division"));
console.log('getBannerName("25", "Test Banner"):', getBannerName("25", "Test Banner"));

// Test the main function (this would normally trigger a file download)
console.log('\nTesting handleDownloadExcel with new structure...');
try {
  // Note: This will attempt to create an Excel file, but won't actually download in Node.js
  handleDownloadExcel(mockApiResponse, mockSmicData, mockAppliedFilters, 'Test-Dashboard-Download.xlsx');
  console.log('✅ handleDownloadExcel executed successfully with new divisions structure');
} catch (error) {
  console.error('❌ Error testing handleDownloadExcel:', error.message);
}

console.log('\n✅ Test completed. The new divisions and banners hierarchy should now be supported in Excel downloads.');
