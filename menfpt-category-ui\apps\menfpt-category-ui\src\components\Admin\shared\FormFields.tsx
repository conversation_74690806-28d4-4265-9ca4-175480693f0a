import React from 'react';
import Select from '@albertsons/uds/molecule/Select';
import { Input } from '@albertsons/uds/molecule/Input';
import InfoTooltip from '../../InfoTooltip';
import { Info } from 'lucide-react';
const getStandardizedPlaceholderClasses = (existingClasses: string = ''): string => {
  return `standardized-placeholder ${existingClasses}`.trim();
};
interface FormFieldProps {
  label: string;
  required?: boolean;
  tooltip?: string;
  anchor?: 'top' | 'bottom' | 'left' | 'right';
  error?: string;
  className?: string;
  infoIcon?: boolean;
}

interface SelectFieldProps extends FormFieldProps {
  items: any[];
  placeholder: string;
  itemText: string;
  onChange: (item: any) => void;
  value?: string;
}

interface InputFieldProps extends FormFieldProps {
  name: string;
  placeholder: string;
  type?: 'text' | 'date';
  value: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  disabled?: boolean;
}
export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  required = false,
  tooltip,
  anchor = 'top',
  error,
  items,
  placeholder,
  itemText,
  onChange,
  className = 'w-full h-10',
  infoIcon = true
}) => (
  <div className='col-span-1'>
    <div className="label  text-sm font-bold flex items-center gap-1">
      {label}
      {required && <span className="text-[#bf2912]">*</span>}
      {tooltip && infoIcon && (
        <InfoTooltip
          label={tooltip}
          icon={<Info size={16} color="#1B6EBB" />}
          anchor={anchor}
          variant="dark"
          className="uds-tooltip-top"
        />
      )}
    </div>
    <Select
      className={getStandardizedPlaceholderClasses(className)}
      items={items}
      placeholder={placeholder}
      itemText={itemText}
      onChange={onChange}
      error={!!error}
    />
    {error && (
      <div className="text-red-600 text-xs mt-1">{error}</div>
    )}
  </div>
);
export const InputField: React.FC<InputFieldProps> = ({
  label,
  required = false,
  tooltip,
  anchor = 'top',
  error,
  name,
  placeholder,
  type = 'text',
  value,
  onChange,
  onBlur,
  disabled = false,
  className,
  infoIcon = true
}) => (
  <div className='col-span-1'>
    <div className="label  text-sm font-bold flex items-center gap-1 mb-1">
      {label}
      {required && <span className="text-[#bf2912]">*</span>}
      {tooltip && infoIcon && (
        <InfoTooltip
          label={tooltip}
          icon={<Info size={16} color="#1B6EBB" />}
          anchor={anchor}
          variant="dark"
          className="uds-tooltip-top"
        />
      )}
    </div>
    {type === 'date' ? (
      <input
        name={name}
        placeholder={placeholder}
        type="date"
        onChange={onChange}
        onBlur={onBlur}
        value={value}
        disabled={disabled}
        className={getStandardizedPlaceholderClasses("w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent")}
      />
    ) : (
      <Input
        name={name}
        placeholder={placeholder}
        type={type || 'text'}
        onChange={onChange}
        onBlur={onBlur}
        value={value}
        error={!!error}
        disabled={disabled}
        className={getStandardizedPlaceholderClasses(disabled ? `bg-gray-100 ${className || ''}` : className || '')}
      />
    )}
    {error && (
      <div className="text-red-600 text-xs mt-1">{error}</div>
    )}
  </div>
);