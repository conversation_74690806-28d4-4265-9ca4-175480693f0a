import { useState, useEffect } from 'react';
import { useSelectorWrap } from '../../../rtk/rtk-utilities';
import { getDesksForDivision } from '../../../features/worksheetFilter/deskUtils';
import { DropdownType } from '../../../interfaces/worksheetFilter';

interface UseFormDataProps {
  selectedDivision?: DropdownType | null;
  FiltersList?: any[] | null;
}

export const useFormData = ({ selectedDivision, FiltersList }: UseFormDataProps = {}) => {
  // Get departments from Redux
  const departmentsState = useSelectorWrap('departments_rn');
  const departments = departmentsState.data || [];

  // State for desks (will be updated based on selected division/department)
  const [desks, setDesks] = useState<DropdownType[]>([]);

  // Update desks when division changes
  useEffect(() => {
    if (selectedDivision && FiltersList && Array.isArray(FiltersList) && FiltersList.length > 0) {
      const deskList = getDesksForDivision(FiltersList, selectedDivision);
      setDesks(deskList);
    } else {
      setDesks([]);
    }
  }, [selectedDivision, FiltersList]);

  return {
    departments,
    desks,
    setDesks
  };
};