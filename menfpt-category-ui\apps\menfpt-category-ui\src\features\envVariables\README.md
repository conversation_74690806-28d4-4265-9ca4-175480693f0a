# Environment Variables Feature

This feature provides RTK Query integration with the BFF service to fetch environment variables.

## Files Structure

```
src/features/envVariables/
├── index.ts                    # Main exports
├── envVariables.slice.ts       # RTK slice for state management
├── useEnvVariables.ts          # Custom hook combining RTK Query and slice
├── envVariables.slice.spec.ts  # Slice tests
├── useEnvVariables.spec.ts     # Hook tests
└── README.md                   # This file
```

## Usage

### Basic Usage with Custom Hook

```tsx
import React from 'react';
import { useEnvVariables } from '../features/envVariables';

const MyComponent: React.FC = () => {
  const { data, error, isLoading, refetch } = useEnvVariables();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const variables = data?.GetEnvVariables?.variables;
  
  return (
    <div>
      {variables && Object.entries(variables).map(([key, value]) => (
        <div key={key}>
          <strong>{key}:</strong> {String(value)}
        </div>
      ))}
    </div>
  );
};
```

### Direct RTK Query Usage

```tsx
import React from 'react';
import { useGetEnvVariablesQuery } from '../server/Api/menfptCategoryAPI';

const MyComponent: React.FC = () => {
  const { data, error, isLoading, refetch } = useGetEnvVariablesQuery();

  // Same usage as above
};
```

### Redux State Access

```tsx
import { useSelector } from 'react-redux';
import { RootState } from '../rtk/store';

const MyComponent: React.FC = () => {
  const envVariablesState = useSelector((state: RootState) => state.envVariables_rn);
  
  // Access state.data, state.status
};
```

## API Response Format

The API returns environment variables in the following format:

```json
{
  "data": {
    "GetEnvVariables": {
      "variables": {
        "PHARMA_UPLOAD_DAYS": "THURSDAY,FRIDAY",
        "NODE_ENV": "development",
        "PORT": 4001,
        "CALENDAR_ENDPOINT": "https://menfpt.dev.westus.aks.az.albertsons.com/menfpt/calendar/",
        "CATEGORY_ENDPOINT": "https://menfpt.dev.westus.aks.az.albertsons.com/menfpt/category/"
      }
    }
  }
}
```

## Features

- ✅ RTK Query integration for automatic caching and refetching
- ✅ Redux state management with loading/error states
- ✅ TypeScript support with proper interfaces
- ✅ Comprehensive test coverage
- ✅ Follows existing project patterns
- ✅ Automatic system variable filtering (handled by BFF)

## Testing

Run the tests for this feature:

```bash
npm test -- --testPathPattern="envVariables"
```

## Dependencies

- Redux Toolkit
- RTK Query
- React Redux
- TypeScript 