import { createGenericSlice } from 'apps/menfpt-category-ui/src/rtk/rtk-slice';

// Store the department desk cascade search query for worksheet filter
export const departmentDeskSearchQuerySlice = createGenericSlice({
  name: 'departmentDeskSearchQuery_rn',
  initialState: { status: 'loading', data: { value: '' } },
})({
  setDepartmentDeskSearchQuery(state, { payload }) {
    if (state.data) {
      state.data.value = payload;
    } else {
      state.data = { value: payload };
    }
  },
});

export const { setDepartmentDeskSearchQuery } = departmentDeskSearchQuerySlice.actions; 