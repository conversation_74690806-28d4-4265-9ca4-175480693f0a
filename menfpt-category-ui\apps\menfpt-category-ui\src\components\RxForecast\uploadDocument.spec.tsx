import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import UploadDocument from './uploadDocument';

// Mock dependencies
jest.mock('@albertsons/uds/molecule/Card', () => {
  const MockCard = ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  );

  MockCard.Header = ({ children }: any) => <div data-testid="card-header">{children}</div>;
  MockCard.Content = ({ children }: any) => <div data-testid="card-content">{children}</div>;

  return {
    __esModule: true,
    default: MockCard,
  };
});

jest.mock('@albertsons/uds/molecule/FileUpload', () => ({
  __esModule: true,
  default: ({ onFileUpload, allowedExtensions, width }: any) => (
    <div data-testid="file-upload">
      <input
        data-testid="file-input"
        type="file"
        accept={allowedExtensions?.map((ext: string) => `.${ext}`).join(',')}
        onChange={(e) => onFileUpload && onFileUpload(e.target.files)}
        multiple
      />
      <div>Width: {width}</div>
      <div>Allowed: {allowedExtensions?.join(', ')}</div>
    </div>
  ),
  FileUploadStatus: ({ fileName, fileSize, uploadStatus, showProgress, id }: any) => (
    <div data-testid={`file-status-${id}`}>
      <div>File: {fileName}</div>
      <div>Size: {fileSize}MB</div>
      <div>Status: {uploadStatus}</div>
      {showProgress && <div data-testid="progress-indicator">Loading...</div>}
    </div>
  ),
}));

jest.mock('./uploadDocument.components', () => ({
  AlertComponents: ({ alertOpen, successAlertOpen, alertMessage, onCloseAlert, onCloseSuccessAlert }: any) => (
    <div>
      {alertOpen && (
        <div data-testid="alert-error">
          <div data-testid="alert-title">Document upload failed!</div>
          <div data-testid="alert-message">{alertMessage}</div>
          <button data-testid="alert-close" onClick={onCloseAlert}>
            Close
          </button>
        </div>
      )}
      {successAlertOpen && (
        <div data-testid="alert-success">
          <div data-testid="alert-title">Successfully uploaded the files.</div>
          <div data-testid="alert-message">All files have been uploaded successfully.</div>
          <button data-testid="alert-close" onClick={onCloseSuccessAlert}>
            Close
          </button>
        </div>
      )}
    </div>
  ),
  UploadDayWarning: ({ allowedDaysMessage }: any) => (
    <div data-testid="upload-day-warning">
      You can only upload the RX forecast file on {allowedDaysMessage}, please visit back on these days.
    </div>
  ),
  UploadSection: ({ onFileUpload }: any) => (
    <div data-testid="upload-section">
      <div data-testid="rx-forecast-alert-message">RxForecast Alert Message</div>
      <div className="header text-black text-sm font-bold pt-2">Add document</div>
      <div data-testid="file-upload">
        <input
          data-testid="file-input"
          type="file"
          accept=".xlsx"
          onChange={(e) => onFileUpload && onFileUpload(e.target.files)}
          multiple
        />
        <div>Width: 949</div>
        <div>Allowed: xlsx</div>
      </div>
    </div>
  ),
  FileStatusList: ({ filesUploaded }: any) => (
    <div data-testid="file-status-list">
      {filesUploaded.length > 0 && (
        <div className="mt-4">
          <p>Document{filesUploaded.length > 1 ? 's' : ''} {filesUploaded.length}</p>
        </div>
      )}
      {filesUploaded.map((item: any, index: number) => (
        <div key={index} data-testid={`file-status-${index}`}>
          <div>File: {item.file.name}</div>
          <div>Size: {Math.round((item.file.size / 1000000) * 10) / 10}MB</div>
          <div>Status: {item.status}</div>
          {item.isLoading && <div data-testid="progress-indicator">Loading...</div>}
        </div>
      ))}
    </div>
  ),
}));

jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('../../server/Api/menfptCategoryAPI', () => ({
  useUploadFilePharmaMutation: jest.fn(),
  useDownloadFilePharmaMutation: jest.fn(),
}));

jest.mock('../../features/envVariables', () => ({
  useEnvVariables: jest.fn(),
}));

jest.mock('./uploadDocument.utils', () => ({
  validateFiles: jest.fn(),
  readFileAsArrayBuffer: jest.fn(),
  createUploadRequest: jest.fn(),
}));

jest.mock('./uploadDocument.hooks', () => ({
  useUploadDayValidation: jest.fn(),
}));

const mockStore = configureStore([]);

describe('UploadDocument', () => {
  const mockUseSelectorWrap = require('../../rtk/rtk-utilities').useSelectorWrap;
  const mockUseUploadFilePharmaMutation = require('../../server/Api/menfptCategoryAPI').useUploadFilePharmaMutation;
  const mockUseDownloadFilePharmaMutation = require('../../server/Api/menfptCategoryAPI').useDownloadFilePharmaMutation;
  const mockUseEnvVariables = require('../../features/envVariables').useEnvVariables;
  const mockValidateFiles = require('./uploadDocument.utils').validateFiles;
  const mockReadFileAsArrayBuffer = require('./uploadDocument.utils').readFileAsArrayBuffer;
  const mockCreateUploadRequest = require('./uploadDocument.utils').createUploadRequest;
  const mockUseUploadDayValidation = require('./uploadDocument.hooks').useUploadDayValidation;
  const mockUploadFilePharma = jest.fn();
  const mockDownloadFilePharma = jest.fn();

  const defaultUserInfo = {
    data: {
      userName: 'John Doe',
    },
  };

  const defaultDisplayDate = {
    data: {
      fiscalWeekNumber: 42,
    },
  };

  const defaultEnvVariables = {
    data: {
      GetEnvVariables: {
        variables: {
          PHARMA_UPLOAD_DAYS: 'Monday,Wednesday,Friday'
        }
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockUseEnvVariables.mockReturnValue(defaultEnvVariables);
    mockUseUploadDayValidation.mockReturnValue({
      isUploadAllowed: true,
      allowedDaysMessage: 'Monday, Wednesday and Friday'
    });
    mockUseSelectorWrap.mockImplementation((key: string) => {
      if (key === 'userInfo_rn') return defaultUserInfo;
      if (key === 'displayDate_rn') return defaultDisplayDate;
      return { data: null };
    });
    
    // Setup API mutation mocks
    mockUploadFilePharma.mockResolvedValue({ data: { fileUploadToBlob: { success: true } } });
    mockDownloadFilePharma.mockResolvedValue({ uploadedDocuments: [] });
    
    mockUseUploadFilePharmaMutation.mockReturnValue([mockUploadFilePharma]);
    mockUseDownloadFilePharmaMutation.mockReturnValue([mockDownloadFilePharma, { 
      isLoading: false, 
      isError: false, 
      data: null 
    }]);
    
    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'mocked-url');
    
    // Mock console methods
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const renderComponent = (storeState = {}) => {
    const store = mockStore(storeState);
    return render(
      <Provider store={store}>
        <UploadDocument />
      </Provider>
    );
  };

  describe('Component Rendering', () => {
    it('renders the upload document component correctly', () => {
      renderComponent();

      expect(screen.getAllByTestId('card')).toHaveLength(2); // Upload card + Documents card
      expect(screen.getByText('Upload Rx Documents')).toBeInTheDocument();
      expect(screen.getByText('Documents for Download')).toBeInTheDocument();
      expect(screen.getByText('Add document')).toBeInTheDocument();
      expect(screen.getByTestId('file-upload')).toBeInTheDocument();
      expect(screen.getByTestId('rx-forecast-alert-message')).toBeInTheDocument();
    });

    it('renders upload warning when upload is not allowed', () => {
      mockUseUploadDayValidation.mockReturnValue({
        isUploadAllowed: false,
        allowedDaysMessage: 'Monday, Wednesday and Friday'
      });

      renderComponent();

      expect(screen.getByTestId('upload-day-warning')).toBeInTheDocument();
      expect(screen.getByText(/You can only upload the RX forecast file on Monday, Wednesday and Friday/)).toBeInTheDocument();
      expect(screen.queryByTestId('upload-section')).not.toBeInTheDocument();
    });

    it('renders upload section when upload is allowed', () => {
      renderComponent();

      expect(screen.getByTestId('upload-section')).toBeInTheDocument();
      expect(screen.queryByTestId('upload-day-warning')).not.toBeInTheDocument();
    });

    it('initially shows no file statuses', () => {
      renderComponent();

      // FileStatusList is currently commented out in the component
      expect(screen.queryByTestId('file-status-list')).not.toBeInTheDocument();
      expect(screen.queryByTestId(/file-status-\d/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Document \d/)).not.toBeInTheDocument();
    });
  });

  describe('Upload Day Validation', () => {
    it('prevents upload when not allowed and shows alert', async () => {
      mockUseUploadDayValidation.mockReturnValue({
        isUploadAllowed: false,
        allowedDaysMessage: 'Monday, Wednesday and Friday'
      });

      renderComponent();

      // When upload is not allowed, there should be no file input available
      expect(screen.queryByTestId('file-input')).not.toBeInTheDocument();
      expect(screen.getByTestId('upload-day-warning')).toBeInTheDocument();
      expect(screen.getByText(/You can only upload the RX forecast file on Monday, Wednesday and Friday/)).toBeInTheDocument();

      // Since we can't upload when not allowed, we don't test file upload behavior
      expect(mockValidateFiles).not.toHaveBeenCalled();
      expect(mockUploadFilePharma).not.toHaveBeenCalled();
    });

    it('shows file upload functionality when upload is allowed', async () => {
      // This test ensures that when upload is allowed, the file input is available
      renderComponent();

      expect(screen.getByTestId('file-input')).toBeInTheDocument();
      expect(screen.queryByTestId('upload-day-warning')).not.toBeInTheDocument();
      expect(screen.getByTestId('upload-section')).toBeInTheDocument();
    });
  });

  describe('File Validation', () => {
    const createMockFile = (name: string, size: number, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') => {
      const file = new File(['content'], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('handles valid files successfully', async () => {
      const file = createMockFile('test.xlsx', 1024 * 1024); // 1MB
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockReturnValue({
        query: 'mocked-query',
        variables: { fileUploadToBlob: {} }
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(mockValidateFiles).toHaveBeenCalledWith([file]);
      });

      await waitFor(() => {
        expect(screen.getByTestId('alert-success')).toBeInTheDocument();
        expect(screen.getByText('Successfully uploaded the files.')).toBeInTheDocument();
      });
    });

    it('handles oversized files', async () => {
      const file = createMockFile('large.xlsx', 26 * 1024 * 1024); // 26MB
      mockValidateFiles.mockReturnValue({
        fileArr: [],
        oversized: true,
        oversizedFiles: ['large.xlsx']
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
        expect(screen.getByText('The file exceeds the 25MB limit. Try again with a smaller file.')).toBeInTheDocument();
      });

      expect(mockUploadFilePharma).not.toHaveBeenCalled();
    });

    it('handles empty file list', () => {
      mockValidateFiles.mockReturnValue({
        fileArr: [],
        oversized: false,
        oversizedFiles: []
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [] } });

      expect(mockValidateFiles).toHaveBeenCalledWith([]);
      expect(mockUploadFilePharma).not.toHaveBeenCalled();
    });
  });

  describe('File Upload Process', () => {
    const createMockFile = (name: string, size: number) => {
      const file = new File(['content'], name, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    beforeEach(() => {
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockReturnValue({
        query: 'mocked-query',
        variables: { fileUploadToBlob: {} }
      });
    });

    it('handles single file upload successfully', async () => {
      const file = createMockFile('test.xlsx', 1024 * 1024);
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(mockUploadFilePharma).toHaveBeenCalled();
      });

      await waitFor(() => {
        expect(screen.getByTestId('alert-success')).toBeInTheDocument();
      });
    });

    it('handles multiple file uploads', async () => {
      const files = [
        createMockFile('test1.xlsx', 1024 * 1024),
        createMockFile('test2.xlsx', 2 * 1024 * 1024),
      ];
      mockValidateFiles.mockReturnValue({
        fileArr: files.map(file => ({ file, status: 'inProgress', isLoading: true })),
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files } });

      await waitFor(() => {
        expect(mockUploadFilePharma).toHaveBeenCalledTimes(2);
      });

      await waitFor(() => {
        expect(screen.getByTestId('alert-success')).toBeInTheDocument();
      });
    });

    it('handles mixed success and failure', async () => {
      const files = [
        createMockFile('success.xlsx', 1024 * 1024),
        createMockFile('failure.xlsx', 1024 * 1024),
      ];
      mockValidateFiles.mockReturnValue({
        fileArr: files.map(file => ({ file, status: 'inProgress', isLoading: true })),
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma
        .mockResolvedValueOnce({ data: { fileUploadToBlob: { success: true } } })
        .mockResolvedValueOnce({ errors: ['Upload failed'] });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files } });

      await waitFor(() => {
        expect(mockUploadFilePharma).toHaveBeenCalledTimes(2);
      });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
        expect(screen.getByText('Some files failed to upload. Please check the file status and try again.')).toBeInTheDocument();
      });
    });
  });

  describe('API Response Handling', () => {
    const createMockFile = (name: string) => {
      const file = new File(['content'], name, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      return file;
    };

    beforeEach(() => {
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockReturnValue({
        query: 'mocked-query',
        variables: { fileUploadToBlob: {} }
      });
    });

    it('handles successful response', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-success')).toBeInTheDocument();
      });
    });

    it('handles response with errors', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        errors: ['Upload failed'],
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles response without data', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({});

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles response with success: false', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: false } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles unexpected response structure', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { unexpectedField: 'value' },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles network/upload errors', async () => {
      const file = createMockFile('test.xlsx');
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockUploadFilePharma.mockRejectedValue(new Error('Network error'));

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
        expect(screen.getByText('Some files failed to upload. Please check the file status and try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Alert Interactions', () => {
    it('closes error alert when close button is clicked', async () => {
      // Test with oversized file to trigger error alert while keeping upload allowed
      const file = new File(['content'], 'large.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 26 * 1024 * 1024 }); // 26MB
      
      mockValidateFiles.mockReturnValue({
        fileArr: [],
        oversized: true,
        oversizedFiles: ['large.xlsx']
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('alert-close'));

      await waitFor(() => {
        expect(screen.queryByTestId('alert-error')).not.toBeInTheDocument();
      });
    });

    it('closes success alert when close button is clicked', async () => {
      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockReturnValue({
        query: 'mocked-query',
        variables: { fileUploadToBlob: {} }
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-success')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('alert-close'));

      await waitFor(() => {
        expect(screen.queryByTestId('alert-success')).not.toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles missing user info gracefully', async () => {
      mockUseSelectorWrap.mockImplementation((key: string) => {
        if (key === 'userInfo_rn') return { data: null };
        if (key === 'displayDate_rn') return defaultDisplayDate;
        return { data: null };
      });

      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockImplementation(() => {
        throw new Error('Missing userInfo');
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles missing display date gracefully', async () => {
      mockUseSelectorWrap.mockImplementation((key: string) => {
        if (key === 'userInfo_rn') return defaultUserInfo;
        if (key === 'displayDate_rn') return { data: null };
        return { data: null };
      });

      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockImplementation(() => {
        throw new Error('Missing displayDate');
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles file reading errors', async () => {
      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockRejectedValue(new Error('File reading failed'));

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByTestId('alert-error')).toBeInTheDocument();
      });
    });

    it('handles contractor user name correctly', async () => {
      mockUseSelectorWrap.mockImplementation((key: string) => {
        if (key === 'userInfo_rn') return { data: { userName: 'John Doe (Contractor)' } };
        if (key === 'displayDate_rn') return defaultDisplayDate;
        return { data: null };
      });

      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 });
      
      mockValidateFiles.mockReturnValue({
        fileArr: [{ file, status: 'inProgress', isLoading: true }],
        oversized: false,
        oversizedFiles: []
      });
      mockReadFileAsArrayBuffer.mockResolvedValue([1, 2, 3, 4]);
      mockCreateUploadRequest.mockReturnValue({
        query: 'mocked-query',
        variables: { fileUploadToBlob: {} }
      });
      mockUploadFilePharma.mockResolvedValue({
        data: { fileUploadToBlob: { success: true } },
      });

      renderComponent();

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        // The createUploadRequest should be called with the original username
        // The actual contractor name stripping happens inside the createUploadRequest function
        expect(mockCreateUploadRequest).toHaveBeenCalledWith(
          file,
          [1, 2, 3, 4],
          'John Doe (Contractor)', // This is what gets passed to the function
          42
        );
      });
    });

    it('handles undefined env variables', () => {
      mockUseEnvVariables.mockReturnValue({ data: null });
      mockUseUploadDayValidation.mockReturnValue({
        isUploadAllowed: true,
        allowedDaysMessage: ''
      });

      renderComponent();

      expect(screen.getByTestId('upload-section')).toBeInTheDocument();
    });
  });
});
