import React, { useState } from 'react';
import "./weekSelection.scss";
import { useWeeksToBeDisabledForQuarter } from '../../features/periodClose/periodClose.flags';

interface WeekSelectionProps {
  isSingleWeekSelect: boolean;
  errorWeeks: any;
  selectedWeeks: any;
  touchedWeeks:any;
  weeksInQuarter: number; // Receive this from the parent
  weekData: any;
  onWeekSelect: (weeks: number[]) => void;
  firstWeekNbr?: number;
}

const WeekSelection: React.FC<WeekSelectionProps> = ({
  errorWeeks,
  selectedWeeks,
  touchedWeeks,
  isSingleWeekSelect,
  weeksInQuarter,
  weekData,
  onWeekSelect,
  firstWeekNbr = 1
}) => {
  // const [selectedWeeks, setSelectedWeeks] = useState<number[]>([]);
const weekToDisable=useWeeksToBeDisabledForQuarter()
  const handleWeekClick = (week: number) => {
    let updatedSelectedWeeks;
      if(isSingleWeekSelect) {
        updatedSelectedWeeks = [week]
      } else {
        if (selectedWeeks?.includes(week)) {
          updatedSelectedWeeks = selectedWeeks.filter((w) => w !== week);
        } else {
          updatedSelectedWeeks = [...selectedWeeks || [] , week];
        }
      }
    //  setSelectedWeeks(updatedSelectedWeeks);
      onWeekSelect(updatedSelectedWeeks);
  };
  return (
    <div className='week-container flex flex-wrap gap-1'>
      {Array.from({length: weeksInQuarter}, (_, index) => {
        const weekNumber = firstWeekNbr + index;
        const hasError = (touchedWeeks)?.includes(String(weekNumber)) && errorWeeks.includes(String(weekNumber))
        const isValid = (touchedWeeks)?.includes(String(weekNumber)) && !errorWeeks.includes(String(weekNumber)) && weekData.hasOwnProperty(weekNumber)

        let weekClass = 'py-2 px-0 text-center rounded cursor-pointer font-nunito-sans font-normal text-xs leading-4';
 
        if(hasError && selectedWeeks?.includes(weekNumber)){
          weekClass += " week-error-selected"
        } else  if(isValid && selectedWeeks?.includes(weekNumber)){
          weekClass += " week-valid-selected"
        } else if(hasError) {
          weekClass += " week-error"
        } else if(isValid) {
          weekClass += " week-valid"
        }

        if(selectedWeeks?.includes(weekNumber)) {
           weekClass += " text-white bg-blue-500"
        } else {
          weekClass +=  " text-gray-800 bg-gray-200"
        }
        const year = weekData[weekNumber]?.year || weekData.year || new Date().getFullYear();
        const weekCode = `${year}${String(weekNumber).padStart(2, '0')}`;
        const isDisabled = weekToDisable?.includes(weekCode);
        if (isDisabled) {
          weekClass += ' week-disabled opacity-50 cursor-not-allowed';
        }

        return (
          <span
            key={weekNumber}
            onClick={isDisabled ? undefined : () => handleWeekClick(weekNumber)}
            className={weekClass}
            aria-disabled={isDisabled}
            tabIndex={isDisabled ? -1 : 0}
          >
            {weekNumber}
          </span>
        )
      })}
    </div>
  );
};

export default WeekSelection;
