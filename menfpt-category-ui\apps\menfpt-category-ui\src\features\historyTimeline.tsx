import React from 'react';
import Timeline from '@albertsons/uds/molecule/Timeline';
import { MessageSquare, User } from 'lucide-react';
import { Tooltip } from 'react-tooltip';
import './tooltipStyles.scss';
import './historyTimeline.scss';
import { ForecastChangeLog } from '../interfaces/forecast-adjustments';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { formatToPST } from '../../../../libs/utils/src';
import { CombinedFilterAndQuartersQuery } from '../server/Query/combinedFilterAndQuartersQuery';
import { useGetCombinedFiltersAndQuartersQuery } from '../server/Api/menfptCategoryAPI';
import { getFiscalWeekNumber } from '../util/getFiscalWeekNumber';
const tag = <User />;

// Utility to get fiscal week number from a date and fiscal week ranges

interface HistoryTimelineProps {
    data: ForecastChangeLog[];
}

const fieldNameMapping: { [key: string]: string } = {
    line_7_retail_allowances_nbr: "Allowance",
    line_6_supplies_packaging_nbr: "Supply packaging",
    line_5_markdowns_nbr: "Markdown",
    line_5_shrink_nbr: "Total shrink",
    line_5_book_gross_profit_nbr: "Book Gross Profit",
    line_1_sales_to_public_nbr: "Sales to Public",
    line_7_retail_selling_allowances_nbr: "Selling Allowance",
    line_7_retail_non_selling_allowances_nbr: "Non-selling Allowance",
};

const fieldOrder = [
    "Sales to Public",
    "Book Gross Profit",
    "Markdown",
    "Total shrink",
    "Supply packaging",
    // Removed standalone "Allowance" to avoid duplication
    "Selling Allowance",
    "Non-selling Allowance",
];

const fieldHierarchy: { [key: string]: string[] } = {
    Allowance: ["Selling Allowance", "Non-selling Allowance"],
};
const HistoryTimeline: React.FunctionComponent<HistoryTimelineProps> = ({ data }) => {
    const { data: calendarWeek } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};
    const strikeThroughGroups: { [key: string]: string[] } = {
        salespublic: ["Sales to Public"],
        grossprofit: ["Book Gross Profit"],
        totalshrink: ["Total shrink"],
        marksdown: ["Markdown"],
        suppliespackaging: ["Supply packaging"],
        allowances: ["Allowance"],
        selling: ["Selling Allowance", "Allowance"],
        nonselling: ["Non-selling Allowance", "Allowance"],
    };

    const isStruck = (displayName: string, editedColumns: string): boolean => {
        const normalizedDisplayName = displayName.toLowerCase().replace(/\s+/g, "").replace(/-/g, "");
        const editedColumnsArray = editedColumns?.split("|").map(col => col.trim()?.toLowerCase()) || [];
        return editedColumnsArray.some(col => {
            const group = strikeThroughGroups[col] || [];
            return group.some(groupField => {
                const normalizedGroupField = groupField.toLowerCase().replace(/\s+/g, "").replace(/-/g, "");
                return normalizedGroupField === normalizedDisplayName;
            });
        });
    };

    const getWeekNumber = (fiscalWeekNbr: string | undefined): number => {
        if (!fiscalWeekNbr) return 0;
        return Number(String(fiscalWeekNbr).slice(-2));
    };

    return data?.length > 0 ? (
        <>
            <div className="flex flex-row ml-8 border-b border-gray-300 pb-4 mb-2 mt-4">
                <p className="w-[265px] text-base font-bold">Details</p>
                <p className="w-[140px] font-bold ml-3 text-base">Before</p>
                <p className="w-[160px] font-bold ml-5 text-base">After</p>
            </div>
            <Timeline>
                {data.map((item, itemIndex) => {
                    const isoDate = item.updatedTimestamp?.slice(0, 10);
                    let formattedDate = '';
                    if (isoDate) {
                        const [year, month, day] = isoDate.split('-');
                        formattedDate = `${month}/${day}/${year}`;
                    }
                    const weekNum = calendarWeek && Array.isArray(calendarWeek)
                        ? getFiscalWeekNumber(formattedDate, calendarWeek)
                        : 0;

                    return (
                        <Timeline.Item
                            key={itemIndex}
                            label={
                                <>
                                    <span className="text-base text-sm">{`${item?.updatedBy}`}</span>
                                    <span className="text-base font-light text-gray-400">{` | `}</span>
                                    <span className="font-light text-[12px]">
                                        {calendarWeek && Array.isArray(calendarWeek)
                                            ? `Week ${weekNum > 0 ? weekNum%100 : '-'},`
                                            : '-'}
                                    </span>
                                    <span className="font-light text-[12px]">{` ${formatToPST(item.updatedTimestamp || '')}`}</span>
                                </>
                            }
                            icon={tag}
                            divider={true}
                        >
                            <div className="history-ml-0 mr-3">
                                {item.updatedMetrics?.map((metric, metricIndex) => {
                                    const tooltipId = `tooltip-${itemIndex}-${metricIndex}`;
                                    const allFields = metric.adjustedFields
                                        ?.filter(field => field.fieldName?.endsWith('nbr'))
                                        .map(field => ({
                                            ...field,
                                            displayName: fieldNameMapping[field.fieldName || ""] || field.fieldName,
                                        }))
                                        .sort((a, b) => {
                                            const indexA = fieldOrder.indexOf(a.displayName || "");
                                            const indexB = fieldOrder.indexOf(b.displayName || "");
                                            return indexA - indexB;
                                        }) || [];
                                    const groupedFields: { [key: string]: any[] } = {};
                                    const standaloneFields: any[] = [];

                                    allFields.forEach(field => {
                                        const parent = Object.keys(fieldHierarchy).find(parent =>
                                            field.displayName && fieldHierarchy[parent].includes(field.displayName)
                                        );
                                        if (parent) {
                                            groupedFields[parent] = groupedFields[parent] || [];
                                            groupedFields[parent].push(field);
                                        } else {
                                            if (field.displayName !== "Allowance") {
                                                standaloneFields.push(field);
                                            }
                                        }
                                    });

                                    return (
                                        <div key={metricIndex}>
                                            <div>
                                                <span className="flex items-center example-container mb-2">
                                                    {`Week ${getWeekNumber(metric.fiscalWeekNbrs)}`}
                                                    <MessageSquare
                                                        size={16}
                                                        data-tooltip-id={tooltipId}
                                                        data-testid="message-square-icon"
                                                        className="ml-2"
                                                        data-tooltip-place="right"
                                                    />
                                                    <Tooltip id={tooltipId} className="example-orange">
                                                        <div className="text-center body-data">
                                                            <div>Reason: {metric.reason}</div>
                                                            <div>Comment: {metric.comment}</div>
                                                        </div>
                                                    </Tooltip>
                                                </span>
                                            </div>
                                            {standaloneFields.map((field, fieldIndex) => (
                                                <div className="history-row flex flex-row text-base my-1 font-extralight" key={`standalone-${fieldIndex}`}>
                                                    <div className="details-column">{field.displayName}</div>
                                                    <div className="value-column">
                                                        <span className={field.oldValue && isStruck(field.displayName, item.editedColumns ?? "") ? "line-through" : ""}>
                                                            {field?.oldValue !== undefined && field?.oldValue !== null ? (
                                                                Number(field.oldValue) === 0
                                                                    ? <span>$0</span>
                                                                    : <span title={`${field.oldValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(field.oldValue))}`}</span>
                                                            ) : ("- -")}
                                                        </span>
                                                    </div>
                                                    <div className="value-column">
                                                        {field?.newValue !== undefined && field?.newValue !== null ? (
                                                            Number(field.newValue) === 0
                                                                ? <span>$0</span>
                                                                : <span title={`${field.newValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(field.newValue))}`}</span>
                                                        ) : ("- -")}
                                                    </div>
                                                </div>
                                            ))}
                                            {Object.entries(groupedFields).map(([parent, children], groupIndex) => {
                                                const allowanceField = metric.adjustedFields?.find(field =>
                                                    field.fieldName === 'line_7_retail_allowances_nbr');
                                                return (
                                                    <div key={`group-${groupIndex}`}>
                                                        <div className="history-row flex flex-row text-base my-1 font-extralight">
                                                            <div className="details-column">{parent}</div>
                                                            <div className="value-column">
                                                                <span className={allowanceField?.oldValue && isStruck(parent, item.editedColumns ?? "") ? "line-through" : ""}>
                                                                    {allowanceField?.oldValue !== undefined && allowanceField?.oldValue !== null ? (
                                                                        Number(allowanceField?.oldValue) === 0
                                                                            ? <span>$0</span>
                                                                            : <span title={`${allowanceField.oldValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(allowanceField.oldValue))}`}</span>
                                                                    ) : ("- -")}
                                                                </span>
                                                            </div>
                                                            <div className="value-column">
                                                                {allowanceField?.newValue !== undefined && allowanceField?.newValue !== null ? (
                                                                    Number(allowanceField?.newValue) === 0
                                                                        ? <span>$0</span>
                                                                        : <span title={`${allowanceField.newValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(allowanceField.newValue))}`}</span>
                                                                ) : ("- -")}
                                                            </div>
                                                        </div>
                                                        {children.map((field, childIndex) => (
                                                            <div className="history-row flex flex-row text-base my-1 font-extralight" key={`child-${childIndex}`}>
                                                                <div className="details-column pl-2"><span className="child-symbol">└</span><span className="child-text">{field.displayName}</span></div>
                                                                <div className="value-column">
                                                                    <span className={field.oldValue && isStruck(field.displayName, item.editedColumns ?? "") ? "line-through" : ""}>
                                                                        {field?.oldValue !== undefined && field?.oldValue !== null ? (
                                                                            Number(field.oldValue) === 0
                                                                                ? <span>$0</span>
                                                                                : <span title={`${field.oldValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(field.oldValue))}`}</span>
                                                                        ) : ("- -")}
                                                                    </span>
                                                                </div>
                                                                <div className="value-column">
                                                                    {field?.newValue !== undefined && field?.newValue !== null ? (
                                                                        Number(field.newValue) === 0
                                                                            ? <span>$0</span>
                                                                            : <span title={`${field.newValue}`}>{`$${new Intl.NumberFormat('en-US', { minimumFractionDigits: 4 }).format(Number(field.newValue))}`}</span>
                                                                    ) : ("- -")}
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    );
                                })}
                            </div>
                        </Timeline.Item>
                    );
                })}
            </Timeline>
        </>
    ) : (
        <div className="flex justify-center items-center text-xl py-8 mt-4" data-testid="no-records">
            No Records Found For The Selected Filters
        </div>
    );
};

export default HistoryTimeline;
