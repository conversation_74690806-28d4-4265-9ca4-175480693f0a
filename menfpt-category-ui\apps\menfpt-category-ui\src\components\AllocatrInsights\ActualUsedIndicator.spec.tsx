import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import ActualUsedIndicator from './ActualUsedIndicator';
import { renderCell } from './utils/tableCell';

describe('ActualUsedIndicator', () => {
  it('renders SVG element', () => {
    const { container } = render(<ActualUsedIndicator />);
    expect(container.querySelector('svg')).toBeInTheDocument();
  });

  it('applies default color #000', () => {
    const { container } = render(<ActualUsedIndicator />);
    const path = container.querySelector('path');
    expect(path).toHaveAttribute('fill', '#000');
  });

  it('renders inside a table cell using renderCell', () => {
    const { container } = render(
      <table><tbody><tr>{renderCell(100, { indicator: <ActualUsedIndicator color="#0f0" /> })}</tr></tbody></table>
    );
    const indicator = container.querySelector('svg');
    expect(indicator).toBeInTheDocument();
    const path = indicator ? indicator.querySelector('path') : null;
    expect(path).toHaveAttribute('fill', '#0f0');
  });
});
