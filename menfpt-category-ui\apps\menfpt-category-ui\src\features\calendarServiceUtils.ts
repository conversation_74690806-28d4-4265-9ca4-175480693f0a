import { useSelectorWrap } from '../rtk/rtk-utilities';

import { generatePeriodStatuses } from './periodClose/generatePeriodStatuses';
import { setPeriodStatuses } from './periodClose/periodStatuses.slice';

export function createQtrPayloadForCalendar(
  currentQuarterNbr: string | number | undefined | null
) {

  const qtrNumber = Number(currentQuarterNbr);
  return { qtrNumbersArr: [qtrNumber] };
}

export function useCurrentQuarterNbr(): number | undefined {
  const currentQtrData = useSelectorWrap('displayDate_rn');
  return currentQtrData?.data?.fiscalQuarterNumber;
}

export function handleCalendarApiResp({
  calendarApiResp,
  dispatch
}: {
  calendarApiResp: any;
  dispatch: Function;
}) { 

      const tagStatuses = generatePeriodStatuses(calendarApiResp, dispatch);     
      dispatch(setPeriodStatuses(tagStatuses));
}

