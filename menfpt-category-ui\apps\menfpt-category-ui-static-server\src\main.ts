/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import * as express from 'express';
import * as path from 'path';
import * as cors from 'cors';
import {createProxyMiddleware} from 'http-proxy-middleware';


const SERVER_SERVLET_CONTEXT_PATH = '/menfpt-category-ui';
const TARGET_URL = process.env.NFPT_GRAPHQL_ENDPOINT;
const APIM_SUBSCRIPTION_KEY = process.env.NFPT_APIM_SUBSCRIPTION_KEY || '';

const app = express();

app.use(cors());

app.get(SERVER_SERVLET_CONTEXT_PATH + '/health', (req, res) => {
  res.json({ status: 'up' });
});

app.use(`${SERVER_SERVLET_CONTEXT_PATH}/api`, createProxyMiddleware({
  target: TARGET_URL,
  changeOrigin: true,
  pathRewrite: (path) => {
    return path.replace(`${SERVER_SERVLET_CONTEXT_PATH}/api`, '');
  },
  onProxyReq: (proxyReq) => {
    // Add an extra header to the request
    proxyReq.setHeader('Ocp-Apim-Subscription-Key', APIM_SUBSCRIPTION_KEY);
  },
}));

app.use(
  `${SERVER_SERVLET_CONTEXT_PATH}/`,
  express.static(path.join(__dirname, 'apps/menfpt-category-ui'))
);

app.use(
  `${SERVER_SERVLET_CONTEXT_PATH}/*`,
  express.static(path.join(__dirname, 'apps/menfpt-category-ui'))
);

// app.get('/api', (req, res) => {
//   res.send({ message: 'Welcome to pricing-static-server!' });
// });

const port = process.env.port || 3010;
const server = app.listen(port, () => {
 /* console.log(
    `Port number ${port} | Context Path : ${SERVER_SERVLET_CONTEXT_PATH}`
  );*/
});
server.on('error', console.error);
