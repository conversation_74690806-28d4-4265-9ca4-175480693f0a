import React from 'react';
import Button from '@albertsons/uds/molecule/Button';
import Link from '@albertsons/uds/molecule/Link';
import { RotateCw } from 'lucide-react';

// Styles as variables
export const footerContainerStyles = [
  "flex justify-end space-x-4 rounded-br-[4px] rounded-bl-[4px] border-t-[1px] p-[12px] bg-white z-20 w-full",
  "md:p-[12px] md:space-x-3",
  "sm:p-[8px] sm:space-x-2 sm:rounded-none sm:border-t sm:bg-white"
].join(' ');
export const cancelButtonStyles = "min-w-[120px] h-[40px] rounded-[8px] gap-[8px] py-[8px] border-[2px] px-4 w-auto";
export const applyButtonStyles = "min-w-[120px] h-[40px] rounded-[8px] gap-[8px] py-[8px] px-4 w-auto";

interface ModalFooterProps {
  shouldDisplayTimeFrame: boolean;
  selectedTimeframe?: any;
  selectedPeriods?: any[];
  selectedWeeks?:any[];
  selectedDivision: any[];
  selectedDesk?: any;
  selectedDepartment?: any;
  resetFilters: () => void;
  applyFilters: () => void;
  onCancel: () => void;
  close: () => void;
}

const ModalFooter: React.FC<ModalFooterProps> = ({
  shouldDisplayTimeFrame,
  selectedTimeframe,
  selectedPeriods,
  selectedDivision,
  selectedDesk,
  selectedDepartment,
  resetFilters,
  applyFilters,
  onCancel,
  close
}) => {
  return (
    <div className={footerContainerStyles}>
      <Link onClick={resetFilters} className="no-underline cursor-pointer" before={<RotateCw className="fill-none stroke-current"/>} > Reset Selection </Link>
      <Button
        className={cancelButtonStyles}
        variant="secondary"
        onClick={() => {
          onCancel();
          close();
        }}
      >
        Cancel
      </Button>
      <Button
        className={applyButtonStyles}
        variant="primary"
        onClick={() => {
          applyFilters();
          close();
        }}
        disabled={
          (shouldDisplayTimeFrame && !selectedTimeframe) ||
          (shouldDisplayTimeFrame && (!selectedPeriods || selectedPeriods.length === 0)) ||
          selectedDivision.length === 0 ||
          (!selectedDesk && (!selectedDepartment || (Array.isArray(selectedDepartment) && selectedDepartment.length === 0)))
        }
      >
        Apply
      </Button>
    </div>
  );
};

export default ModalFooter;
