import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FileUploadBox from './FileUploadBox';

// Mock alert to avoid actual alerts during testing
const mockAlert = jest.fn();
global.alert = mockAlert;

describe('FileUploadBox', () => {
  const mockOnFileUpload = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with all required elements', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      // Check if main text elements are present
      expect(screen.getByText('Drag and drop')).toBeInTheDocument();
      expect(screen.getByText('document or')).toBeInTheDocument();
      expect(screen.getByText('browse')).toBeInTheDocument();
      expect(screen.getByText('Only .xlsx files are allowed. 25 mb maximum file size')).toBeInTheDocument();

      // Check if file input is present
      const fileInput = screen.getByLabelText('browse');
      expect(fileInput).toBeInTheDocument();
      expect(fileInput).toHaveAttribute('type', 'file');
    });

    it('should render with correct styling classes', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const container = screen.getByText('Drag and drop').closest('div')?.parentElement?.parentElement;
      expect(container).toHaveClass('flex', 'justify-center', 'items-center', 'border-2', 'border-dashed', 'bg-[#ebf3fa]');
    });

    it('should render the upload icon SVG', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const svg = document.querySelector('svg');
      expect(svg).toBeInTheDocument();
      expect(svg).toHaveAttribute('width', '25');
      expect(svg).toHaveAttribute('height', '24');
    });
  });

  describe('File Selection via Browse', () => {
    it('should handle valid .xlsx file selection', async () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(input);

      await waitFor(() => {
        expect(mockOnFileUpload).toHaveBeenCalledWith([file]);
      });
      
      expect(mockAlert).not.toHaveBeenCalled();
    });

    it('should reject invalid file type', async () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test content'], 'test.pdf', {
        type: 'application/pdf',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(input);

      // Since alert was removed from component, just check onFileUpload wasn't called
      expect(mockOnFileUpload).not.toHaveBeenCalled();
    });

    it('should reject file larger than 25MB', async () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const largeFile = new File(['x'.repeat(26 * 1024 * 1024)], 'large.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [largeFile],
        writable: false,
      });

      fireEvent.change(input);

      // Since alert was removed from component, just check onFileUpload wasn't called
      expect(mockOnFileUpload).not.toHaveBeenCalled();
    });

    it('should handle when no file is selected', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [],
        writable: false,
      });

      fireEvent.change(input);

      expect(mockOnFileUpload).not.toHaveBeenCalled();
      expect(mockAlert).not.toHaveBeenCalled();
    });
  });

  describe('Drag and Drop Functionality', () => {
    const createDataTransfer = (files: File[]) => {
      return {
        files: {
          ...files,
          length: files.length,
          item: (index: number) => files[index],
          [Symbol.iterator]: function* () {
            for (const file of files) {
              yield file;
            }
          }
        },
        types: ['Files']
      };
    };

    it('should handle valid file drop', async () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const dropZone = screen.getByText('Drag and drop').closest('div')?.parentElement?.parentElement;
      
      fireEvent.drop(dropZone!, {
        dataTransfer: createDataTransfer([file])
      });

      await waitFor(() => {
        expect(mockOnFileUpload).toHaveBeenCalledWith([file]);
      });
      
      expect(mockAlert).not.toHaveBeenCalled();
    });

    it('should reject invalid file type on drop', async () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test content'], 'test.txt', {
        type: 'text/plain',
      });

      const dropZone = screen.getByText('Drag and drop').closest('div')?.parentElement?.parentElement;
      
      fireEvent.drop(dropZone!, {
        dataTransfer: createDataTransfer([file])
      });

      // Since alert was removed from component, just check onFileUpload wasn't called
      expect(mockOnFileUpload).not.toHaveBeenCalled();
    });

    it('should prevent default on dragover', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const dropZone = screen.getByText('Drag and drop').closest('div')?.parentElement?.parentElement;
      const dragOverEvent = new Event('dragover', { bubbles: true });
      const preventDefaultSpy = jest.spyOn(dragOverEvent, 'preventDefault');
      
      fireEvent(dropZone!, dragOverEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should prevent default on drop', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const dropZone = screen.getByText('Drag and drop').closest('div')?.parentElement?.parentElement;
      const dropEvent = new Event('drop', { bubbles: true });
      const preventDefaultSpy = jest.spyOn(dropEvent, 'preventDefault');
      
      Object.defineProperty(dropEvent, 'dataTransfer', {
        value: createDataTransfer([file])
      });
      
      fireEvent(dropZone!, dropEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('File Validation', () => {
    it('should validate correct MIME type', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const validFile = new File(['test'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [validFile],
        writable: false,
      });

      fireEvent.change(input);

      expect(mockOnFileUpload).toHaveBeenCalledWith([validFile]);
      expect(mockAlert).not.toHaveBeenCalled();
    });

    it('should validate file size exactly at 25MB limit', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const exactLimitFile = new File(['x'.repeat(25 * 1024 * 1024)], 'limit.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [exactLimitFile],
        writable: false,
      });

      fireEvent.change(input);

      expect(mockOnFileUpload).toHaveBeenCalledWith([exactLimitFile]);
      expect(mockAlert).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper file input attributes', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const input = screen.getByLabelText('browse');
      expect(input).toHaveAttribute('type', 'file');
      expect(input).toHaveAttribute('accept', '.xlsx');
      expect(input).toHaveClass('hidden');
    });
  });

  describe('Component Integration', () => {
    it('should call onFileUpload with correct file array structure', () => {
      render(<FileUploadBox onFileUpload={mockOnFileUpload} />);

      const file = new File(['test'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const input = screen.getByLabelText('browse') as HTMLInputElement;
      
      Object.defineProperty(input, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(input);

      expect(mockOnFileUpload).toHaveBeenCalledTimes(1);
      expect(mockOnFileUpload).toHaveBeenCalledWith([file]);
    });

  });
});
