import * as utils from './periodCloseLastQrtr.utils';
import * as quarterUtils from '../worksheetFilter/utils/quarterUtils';

describe('periodCloseLastQrtr.utils', () => {
  describe('getLastQuarterNbr', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
      jest.restoreAllMocks();
    });

    it('returns null if any input is missing', () => {
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: null, currentQuarterNbr: 1, currentQuarterStartDate: '2024-01-01' })).toBeNull();
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 1, currentQuarterNbr: null, currentQuarterStartDate: '2024-01-01' })).toBeNull();
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 1, currentQuarterNbr: 1, currentQuarterStartDate: null })).toBeNull();
    });

    it('returns null if currentQuarterNbr !== qrtrNbrDisplayedInTable', () => {
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 1, currentQuarterNbr: 2, currentQuarterStartDate: '2024-01-01' })).toBeNull();
    });

    it('returns null if not within allowed window', () => {
      jest.setSystemTime(new Date('2024-01-01'));
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 1, currentQuarterNbr: 1, currentQuarterStartDate: '2000-01-01' })).toBeNull();
    });

    it('returns previous quarter number for valid input (not first quarter)', () => {
      jest.setSystemTime(new Date('2024-04-10'));
      jest.spyOn(quarterUtils, 'generateQuartersForYear').mockReturnValue([
        { fiscalQuarterNumber: 1 }, { fiscalQuarterNumber: 2 }, { fiscalQuarterNumber: 3 }, { fiscalQuarterNumber: 4 }
      ]);
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 2, currentQuarterNbr: 2, currentQuarterStartDate: '2024-04-01' })).toBe(1);
    });

    it('returns last quarter of previous year if first quarter', () => {
      jest.setSystemTime(new Date('2024-01-10'));
      jest.spyOn(quarterUtils, 'generateQuartersForYear')
        .mockImplementation((year) => {
          if (year === 2023) {
            return [{ fiscalQuarterNumber: 1 }, { fiscalQuarterNumber: 2 }, { fiscalQuarterNumber: 3 }, { fiscalQuarterNumber: 4 }];
          }
          return [{ fiscalQuarterNumber: 1 }, { fiscalQuarterNumber: 2 }, { fiscalQuarterNumber: 3 }, { fiscalQuarterNumber: 4 }];
        });
      expect(utils.getLastQuarterNbr({ qrtrNbrDisplayedInTable: 1, currentQuarterNbr: 1, currentQuarterStartDate: '2024-01-01' })).toBe(4);
    });
  });
});