// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import './styles.scss';
import { app_store } from './rtk/store';
import Home from './pages/home';
import AdjustmentWorkSheet from './pages/adjustment-worksheet';
import NFPTContainer from './app-main-container';
import Rxforecast from './components/RxForecast/rxforecast';
import RouteProtection from './components/RouteProtection/RouteProtection';
import AccessDenied from './pages/access-denied';
import Admin from './components/Admin/admin-tabs';
import KeeperFacilities from './components/Facilities/KeeperFacilities';
import ViewMoreIdFacilities from './components/Facilities/ViewMoreIdFacilities';

export function App() {
  return (
    <div className="px-0">
      <Suspense fallback={<div>Loading</div>}>
        <Provider store={app_store}>
          <NFPTContainer>
            <Routes>
              <Route
                index
                path="/dashboard"
                element={
                  <RouteProtection>
                    <Home/>
                  </RouteProtection>
                }
              /> 

              <Route
                index
                path="/adjustment-worksheet"
                element={
                  <RouteProtection>
                    <AdjustmentWorkSheet/>
                  </RouteProtection>
                }  />           
              <Route
                index
                path="/rx-forecast"
                element={
                  <RouteProtection>
                    <Rxforecast/>
                  </RouteProtection>
                }  />           
              <Route
                path="access-denied"
                element={<AccessDenied/>}  />   
              <Route
                index
                path="/admin"
                element={
                  <RouteProtection>
                    <Admin/>
                  </RouteProtection>
                }
              />         
              <Route
                path="/keeperfacilities"
                element={<KeeperFacilities />}
              />
              <Route
                path="/idfacilities"
                element={<ViewMoreIdFacilities />}
              />
            </Routes>
          </NFPTContainer>
        </Provider>
      </Suspense>
    </div>
  );
}

export default App;
