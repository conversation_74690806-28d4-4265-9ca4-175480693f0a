import { Dispatch } from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { setSelectedSm, setSmDataForSelectedDept } from '../roles/rolesFilter.slice';
import { getSmDataByDivisionAndDept } from '../roles/rolesUtils';
import { SmDataType } from '../../types/smTypes';
import { serializeSmData, createEmptySerializedSmData } from '../../utils/serializationUtils';

export function safeOnSmDataChange(
  lastSmDataRef: React.MutableRefObject<SmDataType | null>,
  onSmDataChange: ((smData: SmDataType) => void) | undefined,
  newData: SmDataType
) {
  if (JSON.stringify(lastSmDataRef.current) !== JSON.stringify(newData)) {
    lastSmDataRef.current = newData;
    onSmDataChange?.(newData);
  }
}

export function handleCascadeSmDataChange(
  deptRoleSuggestionsState: any,
  allDepartments: DropdownType[],
  safeOnSmDataChangeFn: (newData: SmDataType) => void
) {
  const { cascadeSearchSelectedItemId, cascadeSearchSelectedItemType } = deptRoleSuggestionsState.data || {};
  if (
    cascadeSearchSelectedItemId &&
    (cascadeSearchSelectedItemType === 'department' || cascadeSearchSelectedItemType === 'sm' || cascadeSearchSelectedItemType === 'ASM')
  ) {
    let deptId = null;
    if (cascadeSearchSelectedItemType === 'department') {
      deptId = cascadeSearchSelectedItemId;
    } else {
      deptId = cascadeSearchSelectedItemId.split('-')[0];
    }
    if (!deptId || !Array.isArray(allDepartments) || allDepartments.length === 0) {
      safeOnSmDataChangeFn(new Map());
      return;
    }
    const smData = getSmDataByDivisionAndDept({
      selectedDeptId: deptId,
      deptListForSelectedDivision: allDepartments,
    }) as SmDataType;
    safeOnSmDataChangeFn(smData);
  } else {
    safeOnSmDataChangeFn(new Map());
  }
}

export function handleNormalSmDataChange(
  selectedDepartment: DropdownType | DropdownType[] | undefined,
  allDepartments: DropdownType[],
  safeOnSmDataChangeFn: (newData: SmDataType) => void
) {
  if (selectedDepartment && !Array.isArray(selectedDepartment)) {
    if (!selectedDepartment.num || !Array.isArray(allDepartments) || allDepartments.length === 0) {
      safeOnSmDataChangeFn(new Map());
      return;
    }
    const smData = getSmDataByDivisionAndDept({
      selectedDeptId: selectedDepartment.num,
      deptListForSelectedDivision: allDepartments,
    }) as SmDataType;
    safeOnSmDataChangeFn(smData);
  } else {
    safeOnSmDataChangeFn(new Map);
  }
}
const getDeptIds = (selectedDepartment: DropdownType | DropdownType[], allDepartments: DropdownType[]) => {
  const ids: string[] = [];
  if (Array.isArray(allDepartments) && allDepartments.length > 0) {
    if (Array.isArray(selectedDepartment)) {
      selectedDepartment.map((dept: DropdownType) => dept.num && ids.push(dept.num.toString()));
    } else if (selectedDepartment && selectedDepartment.num) {
      ids.push(selectedDepartment.num.toString());
    }
  }
  return ids;
}

export function handleDepartmentChange(
  department: DropdownType | DropdownType[],
  selectedDepartment: DropdownType | DropdownType[] | undefined,
  isMultipleSelectionAllowed: boolean,
  allDepartments: DropdownType[],
  onDepartmentChange: (department: DropdownType | DropdownType[]) => void,
  onSmDataChange: ((smData: SmDataType) => void) | undefined,
  dispatch: Dispatch<any>,
  fromCascade = false
) {
  if (!fromCascade) {
    dispatch(setSmDataForSelectedDept(createEmptySerializedSmData()));
    dispatch(setSelectedSm(createEmptySerializedSmData()));
  }
  onDepartmentChange(department);
  if (onSmDataChange) {
    const deptIds = getDeptIds(department, allDepartments);
    const smData = getSmDataByDivisionAndDept({
        selectedDeptId: deptIds,
        deptListForSelectedDivision: allDepartments,
      }) as SmDataType;
    
    
    // Use utility function to serialize for Redux
    const serializedSmData = serializeSmData(smData);
    
    onSmDataChange(smData);
    dispatch(setSmDataForSelectedDept(serializedSmData));
    dispatch(setSelectedSm(createEmptySerializedSmData()));
  }
} 