import React, { useState, ChangeEvent } from "react";
import Input from "@albertsons/uds/molecule/Input";
import Select, { Option } from "@albertsons/uds/molecule/Select";

export interface Filters {
  userName?: string;
  role?: string;
  accessStatus?: string;
  oracleDepartment?: string;
}

interface Props {
  initialValues?: Filters;
  roleOptions: string[];
  statusOptions: string[];
  departmentOptions: string[];
  onApply: (values: Filters) => void;
  onReset: () => void;
}

const AccessControlFilters: React.FC<Props> = ({
  initialValues,
  roleOptions,
  statusOptions,
  departmentOptions,
  onApply,
  onReset,
}) => {
  const [values, setValues] = useState<Filters>(initialValues ?? {});

  const setField = <K extends keyof Filters>(k: K, v?: Filters[K]) =>
    setValues((p) => ({ ...p, [k]: v }));

  const handleReset = () => {
    setValues({});
    onReset();
  };

  const handleApply = () => {
    onApply(values);
  };

  return (
    <div className="w-full max-w-full h-full bg-white flex flex-col">
      <div className="flex-1 overflow-auto">
        <div className="mb-4">
          <Input
            className="w-full"
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              setField("userName", e.target.value || undefined)
            }
            value={values.userName ?? ""}
            name="userName"
            label="User name"
            placeholder="Search by user name"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-bold text-gray-700 mb-1">
            Role
          </label>
          <Select
            className="w-full"
            placeholder="All roles"
            itemText="name"
            onChange={(selected) =>
              setField("role", selected?.name || undefined)
            }
          >
            {roleOptions.map((role, idx) => (
              <Option key={idx} item={{ name: role }} />
            ))}
          </Select>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-bold text-gray-700 mb-2">
            Access status
          </label>
          <div className="flex gap-6">
            <label className="inline-flex items-center gap-2 text-sm">
              <input
                type="radio"
                name="accessStatus"
                value="Active"
                checked={values.accessStatus === "Active"}
                onChange={() => setField("accessStatus", "Active")}
                className="w-4 h-4"
              />
              <span>Active</span>
            </label>
            <label className="inline-flex items-center gap-2 text-sm">
              <input
                type="radio"
                name="accessStatus"
                value="Inactive"
                checked={values.accessStatus === "Inactive"}
                onChange={() => setField("accessStatus", "Inactive")}
                className="w-4 h-4"
              />
              <span>Inactive</span>
            </label>
          </div>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-bold text-gray-700 mb-1">
            Oracle department
          </label>
          <Select
            className="w-full"
            placeholder="All departments"
            itemText="name"
            onChange={(selected) =>
              setField("oracleDepartment", selected?.name || undefined)
            }
          >
            {departmentOptions.map((dept, idx) => (
              <Option key={idx} item={{ name: dept }} />
            ))}
          </Select>
        </div>
      </div>
      <div className="border-t border-gray-100 py-4 flex items-center justify-between">
        <button
          type="button"
          onClick={handleReset}
          className="px-4 py-2 mr-2 w-full rounded-md border border-[#1b6ebb] text-sm text-[#1b6ebb] bg-white hover:bg-gray-50"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleApply}
          className="px-4 py-2 w-full rounded-md bg-[#1b6ebb] text-white text-sm hover:bg-blue-700"
        >
          Apply
        </button>
      </div>
    </div>
  );
};

export default AccessControlFilters;
