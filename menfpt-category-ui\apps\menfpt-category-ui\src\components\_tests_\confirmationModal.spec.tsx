import React from 'react';
import { BrowserRouter as Router, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { app_store } from '../../rtk/store';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import ConfirmationModal from '../confirmationModal';

describe('ConfirmationModal', () => {
    const props = {
        title: 'Test Title',
        message: 'Test Message',
        confirmText: 'Confirm',
        isOpen: true,
        setIsOpen: jest.fn(),
    };

    it('should render the modal with given title and message', () => {
        render(
            <Router>
                <Provider store={app_store}>
                    <ConfirmationModal {...props} />
                </Provider>
            </Router>
        );
        expect(screen.getByText('Test Title')).toBeInTheDocument();
        expect(screen.getByText('Test Message')).toBeInTheDocument();
    });

    it('should call setIsOpen with false when Cancel button is clicked', () => {
        render(<ConfirmationModal {...props} />);
        fireEvent.click(screen.getByText('Cancel'));
        expect(props.setIsOpen).toHaveBeenCalledWith(false);
    });
    
    it('should render the confirm button with given text', () => {
        render(<ConfirmationModal {...props} />);
        expect(screen.getByText('Confirm')).toBeInTheDocument();
    });

    it('should not render the modal when isOpen is false', () => {
        render(<ConfirmationModal {...props} isOpen={false} />);
        expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
        expect(screen.queryByText('Test Message')).not.toBeInTheDocument();
    });


    it('should have the correct classes for title and message', () => {
        render(<ConfirmationModal {...props} />);
        expect(screen.getByText('Test Title')).toHaveClass('text-center select-none font-bold text-[28px] mt-16');
        expect(screen.getByText('Test Message')).toHaveClass('text-center select-none text-[20px] mt-4');
    });

    it('should have the correct classes for buttons', () => {
        render(<ConfirmationModal {...props} />);
        expect(screen.getByText('Cancel')).toHaveClass('mr-2 whitespace-nowrap');
        expect(screen.getByText('Confirm')).toHaveClass('ml-2 whitespace-nowrap');
    });
});