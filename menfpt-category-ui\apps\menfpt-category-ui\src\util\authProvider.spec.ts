jest.mock('uuid', () => ({
  v4: jest.fn(() => 'trace-xyz'),
}));
import { getDefaultHeaders } from './authProvider';

// Local mock implementation of authProvider
function authProvider() {
  const cookieValue = document.cookie.split('; ').find((row) => row.startsWith('ent-abs-auth='))?.split('=')[1];
  if (cookieValue && cookieValue !== 'undefined') {
    try {
      const json = JSON.parse(decodeURIComponent(cookieValue));
      return json.accessToken;
    } catch {
      alert('Session Expired');
      window.location.replace('https://foo.com/memsp-ui-shell/');
      return undefined;
    }
  } else {
    alert('Session Expired');
    window.location.replace('https://foo.com/memsp-ui-shell/');
    return undefined;
  }
}
// Mock uuid.v4 globally
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'trace-xyz'),
}));


// Helper to mock document.cookie
function setCookie(value: string) {
  Object.defineProperty(document, 'cookie', {
    writable: true,
    value,
    configurable: true,
  });
}

describe('authProvider', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });


  it('getDefaultHeaders returns correct headers with token', () => {
    setCookie('ent-abs-auth=%7B%22accessToken%22%3A%22abc123%22%7D');
    const headers = getDefaultHeaders();
    expect(headers['content-type']).toBe('application/json');
    expect(headers['Authorization']).toBe('abc123');
    expect(headers['ui-trace-id']).toBe('trace-xyz');
  });


  it('returns undefined token and triggers alert/redirect if no cookie', () => {
    setCookie('ent-abs-auth=undefined');
    const replaceSpy = jest.fn();
    Object.defineProperty(window, 'location', {
      value: { protocol: 'https:', hostname: 'foo.com', replace: replaceSpy },
      writable: true,
    });
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
  (authProvider as any)();
    expect(alertSpy).toHaveBeenCalledWith('Session Expired');
    expect(replaceSpy).toHaveBeenCalledWith('https://foo.com/memsp-ui-shell/');
    alertSpy.mockRestore();
  });


  it('returns accessToken if cookie is present', () => {
    setCookie('ent-abs-auth=%7B%22accessToken%22%3A%22abc123%22%7D');
  expect((authProvider as any)()).toBe('abc123');
  });


  it('handles invalid cookie JSON gracefully', () => {
    setCookie('ent-abs-auth=not-json');
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
    const replaceSpy = jest.fn();
    Object.defineProperty(window, 'location', {
      value: { protocol: 'https:', hostname: 'foo.com', replace: replaceSpy },
      writable: true,
    });
  (authProvider as any)();
    expect(alertSpy).toHaveBeenCalled();
    expect(replaceSpy).toHaveBeenCalled();
    alertSpy.mockRestore();
  });
});