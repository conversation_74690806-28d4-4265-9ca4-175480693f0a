{"ast": null, "code": "import ExcelJS from 'exceljs';\nimport { saveAs } from 'file-saver';\nimport { toTitleCase } from '@ui/utils';\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\nexport const formatCurrency = value => {\n  if (value === null || value === undefined || value === '') return ''; // Add this return statement\n  const num = Number(value);\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', {\n    maximumFractionDigits: 0\n  })}`;\n};\nexport const getDeptName = (smicData, deptId, fallback) => {\n  const found = smicData.find(item => String(item.deptId).trim() === String(deptId).trim());\n  return toTitleCase((found == null ? void 0 : found.deptName) || fallback || '');\n};\nexport const getDivisionName = (smicData, divisionId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId) === String(divisionId));\n  return toTitleCase((found == null ? void 0 : found.divisionName) || fallback || '');\n};\nexport const getBannerName = (smicData, divisionId, bannerId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId) === String(divisionId) && String(item.bannerId) === String(bannerId));\n  return toTitleCase((found == null ? void 0 : found.bannerName) || fallback || '');\n};\nconst addRows = (rows, dept, smicData, useWeekId = false) => {\n  var _dept$name, _dept$periods;\n  const quarter = dept.quarter || {};\n  const deptName = getDeptName(smicData, dept.id, (_dept$name = dept == null ? void 0 : dept.name) != null ? _dept$name : '');\n  const isTotal = dept.id === 'Total';\n  const baseRow = {\n    departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}`\n  };\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\n  const weeksByPeriod = {};\n  (dept.weeks || []).forEach(week => {\n    var _ref, _week$periodNumber;\n    const periodNum = (_ref = (_week$periodNumber = week.periodNumber) != null ? _week$periodNumber : week.periodNbr) != null ? _ref : '';\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\n    weeksByPeriod[periodNum].push(week);\n  });\n  (_dept$periods = dept.periods) == null || _dept$periods.forEach(period => {\n    var _ref2, _period$periodNumber;\n    const periodNum = (_ref2 = (_period$periodNumber = period.periodNumber) != null ? _period$periodNumber : period.periodNbr) != null ? _ref2 : '';\n    rows.push(mapRow(Object.assign({}, baseRow, {\n      departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period'\n    }), period, formatCurrency, 'Period', periodNum));\n    const weeks = weeksByPeriod[periodNum] || [];\n    const sortedWeeks = weeks.slice().sort((a, b) => {\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n      return aNum - bNum;\n    });\n    sortedWeeks.forEach(week => {\n      let weekNum = '--';\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\n        weekNum = String(parseInt(week.id.slice(-2), 10));\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\n        weekNum = String(week.weekNumber % 100);\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n      }\n      rows.push(mapRow(Object.assign({}, baseRow, {\n        departmentName: `Week ${weekNum} (fiscal wk ${weekNum})`\n      }), week, formatCurrency, 'Week', '', String(weekNum)));\n    });\n  });\n};\nexport const styleWorksheet = worksheet => {\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (colNumber !== 1) {\n      var _cell$value;\n      worksheet.getColumn(colNumber).width = Math.max(String((_cell$value = cell.value) != null ? _cell$value : '').length + 1, 16);\n    }\n  });\n  let maxA = 0;\n  worksheet.eachRow((row, rowNumber) => {\n    var _row$getCell$value;\n    const cellValue = String((_row$getCell$value = row.getCell(1).value) != null ? _row$getCell$value : '');\n    if (cellValue.length > maxA) maxA = cellValue.length;\n  });\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\n  const thinLightBlack = {\n    style: 'thin',\n    color: {\n      argb: 'FF222222'\n    }\n  };\n  const thinLightBlackBorder = {\n    top: thinLightBlack,\n    left: thinLightBlack,\n    bottom: thinLightBlack,\n    right: thinLightBlack\n  };\n  worksheet.eachRow(row => {\n    row.eachCell(cell => {\n      cell.border = thinLightBlackBorder;\n    });\n  });\n  const lightGrayFill = {\n    type: \"pattern\",\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFD3D3D3'\n    }\n  };\n  worksheet.getRow(1).eachCell(cell => {\n    cell.fill = lightGrayFill;\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getRow(2).eachCell(cell => {\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A2').fill = lightGrayFill;\n  worksheet.getCell('A2').font = {\n    bold: true\n  };\n  const lightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFA8F1FF'\n    }\n  };\n  const highlightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FF6FE6FC'\n    }\n  };\n  const divisionFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FF3F97FC'\n    }\n  }; // Darker blue for divisions\n  const bannerFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FF85C1FF'\n    }\n  }; // Medium blue for banners\n\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      const firstCell = row.getCell(1).value;\n      if (typeof firstCell === 'string') {\n        // Check row type based on cell value and apply appropriate styling\n        if (firstCell.trim() === 'Total') {\n          row.eachCell(cell => {\n            cell.fill = divisionFill;\n            cell.font = Object.assign({}, cell.font, {\n              bold: true\n            });\n          });\n        } else if (/^Division:/.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = divisionFill;\n            cell.font = Object.assign({}, cell.font, {\n              bold: true\n            });\n          });\n        } else if (/^Banner:/.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = bannerFill;\n            cell.font = Object.assign({}, cell.font, {\n              bold: true\n            });\n          });\n        } else if (/^[0-9]+ - /.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = highlightBlueFill;\n          });\n        } else if (/^Period\\b/.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = lightBlueFill;\n          });\n        } else if (/^Week\\b/.test(firstCell)) {\n          // Optional: Add specific styling for week rows if needed\n        }\n      }\n    }\n  });\n  worksheet.getCell('A1').alignment = {\n    vertical: 'middle',\n    horizontal: 'center'\n  };\n  ['A1', 'A2'].forEach(cell => {\n    worksheet.getCell(cell).alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A3').font = Object.assign({}, worksheet.getCell('A3').font, {\n    bold: true\n  });\n};\nexport const styleVsProjection = worksheet => {\n  const vsProjectionColIndices = [];\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\n  });\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      vsProjectionColIndices.forEach(colIdx => {\n        const cell = row.getCell(colIdx);\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\n        const num = Number(raw);\n        if (!isNaN(num) && raw !== '') {\n          cell.font = Object.assign({}, cell.font, {\n            color: {\n              argb: num < 0 ? 'FFFF0000' : 'FF008000'\n            }\n          });\n          const header = worksheet.getRow(2).getCell(colIdx).value;\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\n            if (num < 0) {\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })})`;\n            } else {\n              cell.value = `$${num.toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })}`;\n            }\n          }\n        }\n      });\n    }\n  });\n};\nexport const handleDownloadExcel = async (dashboardData, smicData = [], appliedFilters, fileName = 'Dashboard Excel Download.xlsx') => {\n  var _dashboardData, _dashboardData2, _dashboardData3, _dashboardData4, _appliedFilters$timef, _appliedFilters$timef2, _appliedFilters$banne;\n  // Add better debugging information\n  console.log('Excel download initiated with:', {\n    dataLength: (_dashboardData = dashboardData) == null ? void 0 : _dashboardData.length,\n    smicDataLength: smicData == null ? void 0 : smicData.length,\n    dataStructure: ((_dashboardData2 = dashboardData) == null ? void 0 : _dashboardData2.length) > 0 ? Object.keys(dashboardData[0]).join(', ') : 'none',\n    firstItem: ((_dashboardData3 = dashboardData) == null ? void 0 : _dashboardData3.length) > 0 ? JSON.stringify(dashboardData[0]).substring(0, 500) + '...' : 'none'\n  });\n\n  // Ensure we have data\n  if (!((_dashboardData4 = dashboardData) != null && _dashboardData4.length)) {\n    console.warn('No dashboard data available to export!');\n    alert('No data available to export. Please ensure data is loaded in the dashboard first.');\n    return;\n  }\n  const quarterNumber = (appliedFilters == null || (_appliedFilters$timef = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef.quarter) || '';\n  const fiscalYear = (appliedFilters == null || (_appliedFilters$timef2 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef2.fiscalYear) || '';\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\n  const isVariance = fileName.toLowerCase().includes('variance');\n  const isBannerSelected = (appliedFilters == null || (_appliedFilters$banne = appliedFilters.banner) == null ? void 0 : _appliedFilters$banne.length) > 0;\n\n  // Create workbook and worksheet\n  const workbook = new ExcelJS.Workbook();\n  const worksheet = workbook.addWorksheet('Dashboard');\n  worksheet.addRow(parentHeaderRow);\n  worksheet.addRow(COMMON_HEADERS);\n  const rows = [];\n\n  // Process Total first if it exists\n  const totalData = dashboardData.find(item => item.id === 'Total');\n  if (totalData) {\n    addRows(rows, totalData, smicData, isVariance);\n    dashboardData = dashboardData.filter(item => item.id !== 'Total');\n  }\n\n  // Check for nested structure (banners/departments)\n  const isBannersData = dashboardData.some(item => item.banners || item.divisions);\n  if (isBannersData) {\n    console.log('Processing nested banner structure');\n    dashboardData.forEach(mainItem => {\n      if (mainItem.divisions && Array.isArray(mainItem.divisions)) {\n        // Process divisions (may contain banners and departments)\n        mainItem.divisions.forEach(division => {\n          // Add division row with proper formatting\n          const divisionRow = {\n            departmentName: `Division: ${division.id} - ${division.name || getDivisionName(smicData, division.id, 'Division')}`\n          };\n          rows.push(mapRow(divisionRow, division.quarter || {}, formatCurrency, 'Division'));\n          if (isBannerSelected && division.banners && Array.isArray(division.banners)) {\n            division.banners.forEach(banner => {\n              // Add banner row with proper indentation\n              const bannerName = banner.name || getBannerName(smicData, division.id, banner.id, 'Banner');\n              const bannerRow = {\n                departmentName: `  Banner: ${banner.id} - ${bannerName}`\n              };\n              rows.push(mapRow(bannerRow, banner.quarter || {}, formatCurrency, 'Banner'));\n              if (banner.departments && Array.isArray(banner.departments)) {\n                banner.departments.forEach(dept => {\n                  var _dept$name2;\n                  const deptName = getDeptName(smicData, dept.id, (_dept$name2 = dept == null ? void 0 : dept.name) != null ? _dept$name2 : '');\n                  // Add department row with deeper indentation\n                  const deptBaseRow = {\n                    departmentName: `    ${dept.id} - ${deptName}`\n                  };\n                  rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n                  // Process periods and weeks with proper indentation\n                  processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\n                });\n              }\n            });\n          } else if (division.departments && Array.isArray(division.departments)) {\n            division.departments.forEach(dept => {\n              var _dept$name3;\n              const deptName = getDeptName(smicData, dept.id, (_dept$name3 = dept == null ? void 0 : dept.name) != null ? _dept$name3 : '');\n              // Add department directly under division with proper indentation\n              const deptBaseRow = {\n                departmentName: `  ${dept.id} - ${deptName}`\n              };\n              rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n              // Process periods and weeks with proper indentation\n              processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\n            });\n          }\n        });\n      } else if (isBannerSelected && mainItem.banners && Array.isArray(mainItem.banners)) {\n        // Process banners that are not under divisions\n        mainItem.banners.forEach(banner => {\n          const bannerRow = {\n            departmentName: `Banner: ${banner.id} - ${banner.name || 'Banner'}`\n          };\n          rows.push(mapRow(bannerRow, banner.quarter || {}, formatCurrency, 'Banner'));\n          if (banner.departments && Array.isArray(banner.departments)) {\n            banner.departments.forEach(dept => {\n              var _dept$name4;\n              const deptName = getDeptName(smicData, dept.id, (_dept$name4 = dept == null ? void 0 : dept.name) != null ? _dept$name4 : '');\n              // Add department with proper indentation\n              const deptBaseRow = {\n                departmentName: `  ${dept.id} - ${deptName}`\n              };\n              rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n              // Process periods and weeks with proper indentation\n              processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\n            });\n          }\n        });\n      }\n    });\n  } else {\n    var _dashboardData5;\n    console.log('Using SMIC data to organize departments by division/banner');\n\n    // Group by division and banner using SMIC data\n    const divisionBannerMap = new Map();\n    console.log('SMIC data length:', smicData == null ? void 0 : smicData.length);\n    console.log('Department data count:', (_dashboardData5 = dashboardData) == null ? void 0 : _dashboardData5.length);\n\n    // Organize departments by division and banner\n    dashboardData.forEach(dept => {\n      // Find the division and banner for this department from smicData\n      const deptInfo = smicData.find(item => String(item.deptId || '').trim() === String(dept.id || '').trim());\n      if (deptInfo) {\n        console.log(`Found department ${dept.id} in SMIC data with division ${deptInfo.divisionId} and banner ${deptInfo.bannerId}`);\n        const divisionId = deptInfo.divisionId || '00';\n        const bannerId = deptInfo.bannerId || '00';\n        if (!divisionBannerMap.has(divisionId)) {\n          divisionBannerMap.set(divisionId, new Map());\n        }\n        const bannerMap = divisionBannerMap.get(divisionId);\n        if (!bannerMap.has(bannerId)) {\n          bannerMap.set(bannerId, []);\n        }\n        bannerMap.get(bannerId).push(dept);\n      } else {\n        console.warn(`Department ${dept.id} not found in SMIC data`);\n        // If no matching division/banner found, add to default\n        if (!divisionBannerMap.has('00')) {\n          divisionBannerMap.set('00', new Map());\n        }\n        const bannerMap = divisionBannerMap.get('00');\n        if (!bannerMap.has('00')) {\n          bannerMap.set('00', []);\n        }\n        bannerMap.get('00').push(dept);\n      }\n    });\n    console.log('Division count:', divisionBannerMap.size);\n    divisionBannerMap.forEach((bannerMap, divisionId) => {\n      console.log(`Division ${divisionId} has ${bannerMap.size} banners`);\n    });\n\n    // Add division, banner, and department rows\n    divisionBannerMap.forEach((bannerMap, divisionId) => {\n      // Add division row\n      const divisionName = getDivisionName(smicData, divisionId, `Division ${divisionId}`);\n      const divisionRow = {\n        departmentName: `Division: ${divisionId} - ${divisionName}`\n      };\n\n      // Use first department's quarter data for the division\n      const firstBanner = bannerMap.values().next().value;\n      if (firstBanner && firstBanner.length > 0) {\n        rows.push(mapRow(divisionRow, firstBanner[0].quarter || {}, formatCurrency, 'Division'));\n      }\n      bannerMap.forEach((departments, bannerId) => {\n        // Add banner row if not default '00'\n        if (isBannerSelected && bannerId !== '00') {\n          const bannerName = getBannerName(smicData, divisionId, bannerId, `Banner ${bannerId}`);\n          const bannerRow = {\n            departmentName: `  Banner: ${bannerId} - ${bannerName}`\n          };\n          if (departments.length > 0) {\n            rows.push(mapRow(bannerRow, departments[0].quarter || {}, formatCurrency, 'Banner'));\n          }\n        }\n\n        // Add departments with proper hierarchy\n        departments.forEach(dept => {\n          var _dept$name5;\n          const deptName = getDeptName(smicData, dept.id, (_dept$name5 = dept == null ? void 0 : dept.name) != null ? _dept$name5 : '');\n          const indentation = isBannerSelected && bannerId !== '00' ? '    ' : '  '; // Extra indent if under a banner\n          const deptBaseRow = {\n            departmentName: `${indentation}${dept.id} - ${deptName}`\n          };\n          rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n          // Process periods and weeks with proper indentation\n          processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\n        });\n      });\n    });\n  }\n  console.log(`Number of rows to add: ${rows.length}`);\n  if (rows.length > 0) {\n    console.log('Sample row data:', rows[0]);\n  }\n  if (rows.length === 0) {\n    console.warn('Failed to generate any rows, adding dummy row');\n    worksheet.addRow(['No data available']);\n  } else {\n    console.log(`Adding ${rows.length} rows to worksheet`);\n    rows.forEach(row => {\n      if (row) {\n        const rowValues = Object.values(row);\n        // Ensure we're adding values, even if empty\n        worksheet.addRow(rowValues.map(val => val === undefined ? '' : val));\n      }\n    });\n  }\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\n  styleWorksheet(worksheet);\n  styleVsProjection(worksheet);\n  applyPrintSettings(worksheet);\n  let ySplit = 2;\n  const totalRows = worksheet.rowCount;\n  for (let i = 3; i <= totalRows; i++) {\n    var _worksheet$getRow$get;\n    const cellValue = String((_worksheet$getRow$get = worksheet.getRow(i).getCell(1).value) != null ? _worksheet$getRow$get : '');\n    if (/^\\d+ - /.test(cellValue.trim()) && i !== 3) {\n      ySplit = i - 1;\n      break;\n    }\n    if (i === totalRows) {\n      ySplit = totalRows;\n    }\n  }\n  worksheet.views = [{\n    state: 'frozen',\n    ySplit,\n    xSplit: 1\n  }];\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), fileName);\n};\nexport const handleDownloadBothExcel = async (performanceSummaryData, forecastVarianceData, smicData = [], appliedFilters) => {\n  // Commenting out the validation that disables download when no data is present\n  // if (!performanceSummaryData?.length && !forecastVarianceData?.length) {\n  //   return alert('No dashboard data to export!');\n  // }\n\n  const date = new Date().toLocaleDateString('en-CA');\n  const workbook = new ExcelJS.Workbook();\n  if (performanceSummaryData != null && performanceSummaryData.length) {\n    await processWorksheet(workbook, 'Performance Summary', performanceSummaryData, smicData, appliedFilters, false);\n  }\n  if (forecastVarianceData != null && forecastVarianceData.length) {\n    await processWorksheet(workbook, 'Forecast Variance', forecastVarianceData, smicData, appliedFilters, true);\n  }\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), `Allocatr Insights Performance and Variance Excel Download-${date}.xlsx`);\n};\n\n// New helper function to process worksheets with the same logic\nconst processWorksheet = async (workbook, sheetName, data, smicData, appliedFilters, isVariance) => {\n  var _appliedFilters$timef3, _appliedFilters$timef4, _appliedFilters$banne2, _data;\n  const worksheet = workbook.addWorksheet(sheetName);\n\n  // Extract quarter information\n  const quarterNumber = (appliedFilters == null || (_appliedFilters$timef3 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef3.quarter) || '';\n  const fiscalYear = (appliedFilters == null || (_appliedFilters$timef4 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef4.fiscalYear) || '';\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n  const isBannerSelected = (appliedFilters == null || (_appliedFilters$banne2 = appliedFilters.banner) == null ? void 0 : _appliedFilters$banne2.length) > 0;\n  worksheet.addRow(getParentHeaderRow(quarterDisplay));\n  worksheet.addRow(COMMON_HEADERS);\n  const rows = [];\n\n  // Process Total first if it exists\n  const totalData = data.find(item => item.id === 'Total');\n  if (totalData) {\n    addRows(rows, totalData, smicData, isVariance);\n    data = data.filter(item => item.id !== 'Total');\n  }\n\n  // Group by division and banner\n  const divisionBannerMap = new Map();\n\n  // Organize departments by division and banner\n  console.log('SMIC data length:', smicData == null ? void 0 : smicData.length);\n  console.log('Department data count:', (_data = data) == null ? void 0 : _data.length);\n  data.forEach(dept => {\n    // Find the division and banner for this department from smicData\n    const deptInfo = smicData.find(item => String(item.deptId || '').trim() === String(dept.id || '').trim());\n    if (deptInfo) {\n      console.log(`Found department ${dept.id} in SMIC data with division ${deptInfo.divisionId} and banner ${deptInfo.bannerId}`);\n      const divisionId = deptInfo.divisionId || '00';\n      const bannerId = deptInfo.bannerId || '00';\n      if (!divisionBannerMap.has(divisionId)) {\n        divisionBannerMap.set(divisionId, new Map());\n      }\n      const bannerMap = divisionBannerMap.get(divisionId);\n      if (!bannerMap.has(bannerId)) {\n        bannerMap.set(bannerId, []);\n      }\n      bannerMap.get(bannerId).push(dept);\n    } else {\n      console.warn(`Department ${dept.id} not found in SMIC data`);\n      // If no matching division/banner found, add to default\n      if (!divisionBannerMap.has('00')) {\n        divisionBannerMap.set('00', new Map());\n      }\n      const bannerMap = divisionBannerMap.get('00');\n      if (!bannerMap.has('00')) {\n        bannerMap.set('00', []);\n      }\n      bannerMap.get('00').push(dept);\n    }\n  });\n  console.log('Division count:', divisionBannerMap.size);\n  divisionBannerMap.forEach((bannerMap, divisionId) => {\n    console.log(`Division ${divisionId} has ${bannerMap.size} banners`);\n  });\n\n  // Add division, banner, and department rows\n  divisionBannerMap.forEach((bannerMap, divisionId) => {\n    // Add division row\n    const divisionName = getDivisionName(smicData, divisionId, `Division ${divisionId}`);\n    const divisionRow = {\n      departmentName: `Division: ${divisionId} - ${divisionName}`\n    };\n\n    // Use first department's quarter data for the division\n    const firstBanner = bannerMap.values().next().value;\n    if (firstBanner && firstBanner.length > 0) {\n      rows.push(mapRow(divisionRow, firstBanner[0].quarter || {}, formatCurrency, 'Division'));\n    }\n    bannerMap.forEach((departments, bannerId) => {\n      // Add banner row if not default '00'\n      if (isBannerSelected && bannerId !== '00') {\n        const bannerName = getBannerName(smicData, divisionId, bannerId, `Banner ${bannerId}`);\n        const bannerRow = {\n          departmentName: `Banner: ${bannerId} - ${bannerName}`\n        };\n        if (departments.length > 0) {\n          rows.push(mapRow(bannerRow, departments[0].quarter || {}, formatCurrency, 'Banner'));\n        }\n      }\n\n      // Add departments with proper hierarchy\n      departments.forEach(dept => {\n        var _dept$name6;\n        const deptName = getDeptName(smicData, dept.id, (_dept$name6 = dept == null ? void 0 : dept.name) != null ? _dept$name6 : '');\n        const indentation = isBannerSelected && bannerId !== '00' ? '    ' : '  '; // Extra indent if under a banner\n        const deptBaseRow = {\n          departmentName: `${indentation}${dept.id} - ${deptName}`\n        };\n        rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n        // Process periods and weeks with proper indentation\n        processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\n      });\n    });\n  });\n\n  // Add this before adding rows to worksheet in both functions\n  console.log(`Number of rows to add: ${rows.length}`);\n  if (rows.length > 0) {\n    console.log('Sample row data:', rows[0]);\n  }\n\n  // Add rows to worksheet\n  rows.forEach(row => {\n    if (row) {\n      const rowValues = Object.values(row);\n      // Ensure we're adding values, even if empty\n      worksheet.addRow(rowValues.map(val => val === undefined ? '' : val));\n    }\n  });\n\n  // Apply styling\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\n  styleWorksheet(worksheet);\n  styleVsProjection(worksheet);\n  applyPrintSettings(worksheet);\n};\n\n// Add this new helper function to process department details with proper indentation\nfunction processDepartmentDetails(rows, dept, baseRow, smicData, isVariance) {\n  var _dept$periods2;\n  const weeksByPeriod = {};\n\n  // Group weeks by their period\n  (dept.weeks || []).forEach(week => {\n    var _ref3, _week$periodNumber2;\n    const periodNum = (_ref3 = (_week$periodNumber2 = week.periodNumber) != null ? _week$periodNumber2 : week.periodNbr) != null ? _ref3 : '';\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\n    weeksByPeriod[periodNum].push(week);\n  });\n\n  // Process each period\n  (_dept$periods2 = dept.periods) == null || _dept$periods2.forEach(period => {\n    var _ref4, _period$periodNumber2;\n    const periodNum = (_ref4 = (_period$periodNumber2 = period.periodNumber) != null ? _period$periodNumber2 : period.periodNbr) != null ? _ref4 : '';\n    const periodDisplayNum = periodNum ? parseInt(String(periodNum).slice(-2), 10) : '';\n\n    // Add period with indentation (one level deeper than department)\n    const periodIndent = baseRow.departmentName.startsWith('  ') ? '      ' : '    ';\n    rows.push(mapRow(Object.assign({}, baseRow, {\n      departmentName: `${periodIndent}Period ${periodDisplayNum}`\n    }), period, formatCurrency, 'Period', periodNum));\n\n    // Process weeks for this period with additional indentation\n    const weeks = weeksByPeriod[periodNum] || [];\n    const sortedWeeks = weeks.slice().sort((a, b) => {\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n      return aNum - bNum;\n    });\n    sortedWeeks.forEach(week => {\n      let weekNum = '--';\n      if (typeof week.id === 'string' && week.id.startsWith('Week-')) {\n        weekNum = String(parseInt(week.id.slice(-2), 10));\n      } else if (typeof week.weekNumber === 'number') {\n        weekNum = String(week.weekNumber % 100);\n      } else if (typeof week.weekNumber === 'string') {\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n      }\n\n      // Add week with indentation (one level deeper than period)\n      const weekIndent = periodIndent + '  ';\n      rows.push(mapRow(Object.assign({}, baseRow, {\n        departmentName: `${weekIndent}Week ${weekNum} (fiscal wk ${weekNum})`\n      }), week, formatCurrency, 'Week', '', String(weekNum)));\n    });\n  });\n}", "map": {"version": 3, "names": ["ExcelJS", "saveAs", "toTitleCase", "getParentHeaderRow", "COMMON_HEADERS", "VS_PROJECTION_HEADERS", "VS_PROJECTION_DOLLAR_HEADERS", "mapRow", "applyPrintSettings", "formatCurrency", "value", "undefined", "num", "Number", "isNaN", "toLocaleString", "maximumFractionDigits", "getDeptName", "smicData", "deptId", "fallback", "found", "find", "item", "String", "trim", "deptName", "getDivisionName", "divisionId", "divisionName", "getBannerName", "bannerId", "bannerName", "addRows", "rows", "dept", "useWeekId", "_dept$name", "_dept$periods", "quarter", "id", "name", "isTotal", "baseRow", "departmentName", "push", "weeksByPeriod", "weeks", "for<PERSON>ach", "week", "_ref", "_week$periodNumber", "periodNum", "periodNumber", "periodNbr", "periods", "period", "_ref2", "_period$periodNumber", "Object", "assign", "parseInt", "slice", "sortedWeeks", "sort", "a", "b", "aNum", "weekNumber", "bNum", "weekNum", "startsWith", "styleWorksheet", "worksheet", "getRow", "eachCell", "cell", "colNumber", "_cell$value", "getColumn", "width", "Math", "max", "length", "maxA", "eachRow", "row", "rowNumber", "_row$getCell$value", "cellValue", "getCell", "thinLightBlack", "style", "color", "argb", "thinLightBlackBorder", "top", "left", "bottom", "right", "border", "lightGrayFill", "type", "pattern", "fgColor", "fill", "font", "bold", "alignment", "vertical", "horizontal", "lightBlueFill", "highlightBlueFill", "divisionFill", "bannerFill", "firstCell", "test", "styleVsProjection", "vsProjectionColIndices", "includes", "colIdx", "raw", "replace", "header", "abs", "handleDownloadExcel", "dashboardData", "appliedFilters", "fileName", "_dashboardData", "_dashboardData2", "_dashboardData3", "_dashboardData4", "_appliedFilters$timef", "_appliedFilters$timef2", "_appliedFilters$banne", "console", "log", "dataLength", "smicData<PERSON>ength", "dataStructure", "keys", "join", "firstItem", "JSON", "stringify", "substring", "warn", "alert", "quarterNumber", "timeframe", "fiscalYear", "quarterDisplay", "parentHeaderRow", "is<PERSON><PERSON>ce", "toLowerCase", "isBannerSelected", "banner", "workbook", "Workbook", "addWorksheet", "addRow", "totalData", "filter", "isBannersData", "some", "banners", "divisions", "mainItem", "Array", "isArray", "division", "divisionRow", "bannerRow", "departments", "_dept$name2", "deptBaseRow", "processDepartmentDetails", "_dept$name3", "_dept$name4", "_dashboardData5", "divisionBannerMap", "Map", "deptInfo", "has", "set", "bannerMap", "get", "size", "firstBanner", "values", "next", "_dept$name5", "indentation", "rowV<PERSON>ues", "map", "val", "mergeRanges", "range", "mergeCells", "ySplit", "totalRows", "rowCount", "i", "_worksheet$getRow$get", "views", "state", "xSplit", "styledBuffer", "xlsx", "writeBuffer", "Blob", "handleDownloadBothExcel", "performanceSummaryData", "forecastVarianceData", "date", "Date", "toLocaleDateString", "processWorksheet", "sheetName", "data", "_appliedFilters$timef3", "_appliedFilters$timef4", "_appliedFilters$banne2", "_data", "_dept$name6", "_dept$periods2", "_ref3", "_week$periodNumber2", "_ref4", "_period$periodNumber2", "periodDisplayNum", "periodIndent", "weekIndent"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/components/DashboardDownloadExcel/DashboardDownloadExcel.tsx"], "sourcesContent": ["import ExcelJS from 'exceljs';\r\nimport { saveAs } from 'file-saver';\r\nimport { toTitleCase } from '@ui/utils';\r\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\r\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\r\n\r\nexport const formatCurrency = (value: any) => {\r\n  if (value === null || value === undefined || value === '') return ''; // Add this return statement\r\n  const num = Number(value);\r\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n};\r\nexport const getDeptName = (smicData: any[], deptId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.deptId).trim() === String(deptId).trim());\r\n  return toTitleCase(found?.deptName || fallback || '');\r\n};\r\nexport const getDivisionName = (smicData: any[], divisionId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.divisionId) === String(divisionId));\r\n  return toTitleCase(found?.divisionName || fallback || '');\r\n};\r\n\r\nexport const getBannerName = (smicData: any[], divisionId: string, bannerId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => \r\n    String(item.divisionId) === String(divisionId) && \r\n    String(item.bannerId) === String(bannerId)\r\n  );\r\n  return toTitleCase(found?.bannerName || fallback || '');\r\n};\r\n\r\nconst addRows = (rows: any[], dept: any, smicData: any[], useWeekId: boolean = false) => {\r\n  const quarter = dept.quarter || {};\r\n  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n  const isTotal = dept.id === 'Total';\r\n  const baseRow = { departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}` };\r\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\r\n\r\n  const weeksByPeriod: Record<string, any[]> = {};\r\n  (dept.weeks || []).forEach((week: any) => {\r\n    const periodNum = week.periodNumber ?? week.periodNbr ?? '';\r\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\r\n    weeksByPeriod[periodNum].push(week);\r\n  });\r\n\r\n  dept.periods?.forEach((period: any) => {\r\n    const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n    rows.push(\r\n      mapRow(\r\n        { ...baseRow, departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },\r\n        period,\r\n        formatCurrency,\r\n        'Period',\r\n        periodNum\r\n      )\r\n    );\r\n\r\n    const weeks = weeksByPeriod[periodNum] || [];\r\n    const sortedWeeks = weeks.slice().sort((a, b) => {\r\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n      return aNum - bNum;\r\n    });\r\n    sortedWeeks.forEach((week: any) => {\r\n      let weekNum = '--';\r\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n        weekNum = String(parseInt(week.id.slice(-2), 10));\r\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\r\n        weekNum = String(week.weekNumber % 100);\r\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\r\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n      }\r\n      rows.push(\r\n        mapRow(\r\n          { ...baseRow, departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },\r\n          week,\r\n          formatCurrency,\r\n          'Week',\r\n          '',\r\n          String(weekNum)\r\n        )\r\n      );\r\n    });\r\n  });\r\n};\r\nexport const styleWorksheet = worksheet => {\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (colNumber !== 1) {\r\n      worksheet.getColumn(colNumber).width = Math.max(String(cell.value ?? '').length + 1, 16);\r\n    }\r\n  });\r\n  let maxA = 0;\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    const cellValue = String(row.getCell(1).value ?? '');\r\n    if (cellValue.length > maxA) maxA = cellValue.length;\r\n  });\r\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\r\n\r\n  const thinLightBlack = { style: 'thin', color: { argb: 'FF222222' } };\r\n  const thinLightBlackBorder = {\r\n  top: thinLightBlack, left: thinLightBlack,bottom: thinLightBlack,right: thinLightBlack\r\n  };\r\n   worksheet.eachRow(row => {\r\n    row.eachCell(cell => {\r\n      cell.border = thinLightBlackBorder;\r\n    });\r\n  });\r\n  const lightGrayFill = { type: \"pattern\", pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };\r\n  worksheet.getRow(1).eachCell(cell => { \r\n    cell.fill = lightGrayFill; \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });\r\n  worksheet.getRow(2).eachCell(cell => { \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' }; \r\n  });\r\n  worksheet.getCell('A2').fill = lightGrayFill; worksheet.getCell('A2').font = { bold: true };\r\n  const lightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFA8F1FF' } };\r\n  const highlightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6FE6FC' } }; \r\n  const divisionFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF3F97FC' } }; // Darker blue for divisions\r\n  const bannerFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF85C1FF' } }; // Medium blue for banners\r\n  \r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      const firstCell = row.getCell(1).value;\r\n      if (typeof firstCell === 'string') {\r\n        // Check row type based on cell value and apply appropriate styling\r\n        if (firstCell.trim() === 'Total') {\r\n          row.eachCell(cell => { cell.fill = divisionFill; cell.font = { ...cell.font, bold: true }; });\r\n        } else if (/^Division:/.test(firstCell)) {\r\n          row.eachCell(cell => { cell.fill = divisionFill; cell.font = { ...cell.font, bold: true }; });\r\n        } else if (/^Banner:/.test(firstCell)) {\r\n          row.eachCell(cell => { cell.fill = bannerFill; cell.font = { ...cell.font, bold: true }; });\r\n        } else if (/^[0-9]+ - /.test(firstCell)) {\r\n          row.eachCell(cell => { cell.fill = highlightBlueFill; });\r\n        } else if (/^Period\\b/.test(firstCell)) {\r\n          row.eachCell(cell => { cell.fill = lightBlueFill; });\r\n        } else if (/^Week\\b/.test(firstCell)) {\r\n          // Optional: Add specific styling for week rows if needed\r\n        }\r\n      }\r\n    }\r\n  });\r\n  worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };\r\n  ['A1', 'A2'].forEach(cell => {\r\n    worksheet.getCell(cell).alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });worksheet.getCell('A3').font = { ...worksheet.getCell('A3').font, bold: true };\r\n}\r\nexport const styleVsProjection = (worksheet: ExcelJS.Worksheet) => {\r\n  const vsProjectionColIndices: number[] = [];\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\r\n  });\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      vsProjectionColIndices.forEach(colIdx => {\r\n        const cell = row.getCell(colIdx);\r\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\r\n        const num = Number(raw);\r\n        if (!isNaN(num) && raw !== '') {\r\n          cell.font = { ...cell.font, color: { argb: num < 0 ? 'FFFF0000' : 'FF008000' } };\r\n          const header = worksheet.getRow(2).getCell(colIdx).value;\r\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\r\n            if (num < 0) {\r\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;\r\n            } else {\r\n              cell.value = `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  });\r\n};\r\nexport const handleDownloadExcel = async (\r\n  dashboardData: any[],\r\n  smicData: any[] = [],\r\n  appliedFilters?: any,\r\n  fileName: string = 'Dashboard Excel Download.xlsx'\r\n) => {\r\n  // Add better debugging information\r\n  console.log('Excel download initiated with:', {\r\n    dataLength: dashboardData?.length,\r\n    smicDataLength: smicData?.length,\r\n    dataStructure: dashboardData?.length > 0 ? Object.keys(dashboardData[0]).join(', ') : 'none',\r\n    firstItem: dashboardData?.length > 0 ? JSON.stringify(dashboardData[0]).substring(0, 500) + '...' : 'none'\r\n  });\r\n  \r\n  // Ensure we have data\r\n  if (!dashboardData?.length) {\r\n    console.warn('No dashboard data available to export!');\r\n    alert('No data available to export. Please ensure data is loaded in the dashboard first.');\r\n    return;\r\n  }\r\n\r\n  const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n  const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\r\n  const isVariance = fileName.toLowerCase().includes('variance');\r\n  const isBannerSelected = appliedFilters?.banner?.length > 0;\r\n  \r\n  // Create workbook and worksheet\r\n  const workbook = new ExcelJS.Workbook();\r\n  const worksheet = workbook.addWorksheet('Dashboard');\r\n  worksheet.addRow(parentHeaderRow);\r\n  worksheet.addRow(COMMON_HEADERS);\r\n  \r\n  const rows: any[] = [];\r\n  \r\n  // Process Total first if it exists\r\n  const totalData = dashboardData.find(item => item.id === 'Total');\r\n  if (totalData) {\r\n    addRows(rows, totalData, smicData, isVariance);\r\n    dashboardData = dashboardData.filter(item => item.id !== 'Total');\r\n  }\r\n  \r\n  // Check for nested structure (banners/departments)\r\n  const isBannersData = dashboardData.some(item => item.banners || item.divisions);\r\n  \r\n  if (isBannersData) {\r\n    console.log('Processing nested banner structure');\r\n    \r\n    dashboardData.forEach(mainItem => {\r\n      if (mainItem.divisions && Array.isArray(mainItem.divisions)) {\r\n        // Process divisions (may contain banners and departments)\r\n        mainItem.divisions.forEach(division => {\r\n          // Add division row with proper formatting\r\n          const divisionRow = { departmentName: `Division: ${division.id} - ${division.name || getDivisionName(smicData, division.id, 'Division')}` };\r\n          rows.push(mapRow(divisionRow, division.quarter || {}, formatCurrency, 'Division'));\r\n          \r\n          if (isBannerSelected && division.banners && Array.isArray(division.banners)) {\r\n            division.banners.forEach(banner => {\r\n              // Add banner row with proper indentation\r\n              const bannerName = banner.name || getBannerName(smicData, division.id, banner.id, 'Banner');\r\n              const bannerRow = { departmentName: `  Banner: ${banner.id} - ${bannerName}` };\r\n              rows.push(mapRow(bannerRow, banner.quarter || {}, formatCurrency, 'Banner'));\r\n              \r\n              if (banner.departments && Array.isArray(banner.departments)) {\r\n                banner.departments.forEach(dept => {\r\n                  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n                  // Add department row with deeper indentation\r\n                  const deptBaseRow = { departmentName: `    ${dept.id} - ${deptName}` };\r\n                  rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n                  \r\n                  // Process periods and weeks with proper indentation\r\n                  processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\r\n                });\r\n              }\r\n            });\r\n          } else if (division.departments && Array.isArray(division.departments)) {\r\n            division.departments.forEach(dept => {\r\n              const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n              // Add department directly under division with proper indentation\r\n              const deptBaseRow = { departmentName: `  ${dept.id} - ${deptName}` };\r\n              rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n              \r\n              // Process periods and weeks with proper indentation\r\n              processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\r\n            });\r\n          }\r\n        });\r\n      } else if (isBannerSelected && mainItem.banners && Array.isArray(mainItem.banners)) {\r\n        // Process banners that are not under divisions\r\n        mainItem.banners.forEach(banner => {\r\n          const bannerRow = { departmentName: `Banner: ${banner.id} - ${banner.name || 'Banner'}` };\r\n          rows.push(mapRow(bannerRow, banner.quarter || {}, formatCurrency, 'Banner'));\r\n          \r\n          if (banner.departments && Array.isArray(banner.departments)) {\r\n            banner.departments.forEach(dept => {\r\n              const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n              // Add department with proper indentation\r\n              const deptBaseRow = { departmentName: `  ${dept.id} - ${deptName}` };\r\n              rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n              \r\n              // Process periods and weeks with proper indentation\r\n              processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  } else {\r\n    console.log('Using SMIC data to organize departments by division/banner');\r\n    \r\n    // Group by division and banner using SMIC data\r\n    const divisionBannerMap = new Map();\r\n    \r\n    console.log('SMIC data length:', smicData?.length);\r\n    console.log('Department data count:', dashboardData?.length);\r\n    \r\n    // Organize departments by division and banner\r\n    dashboardData.forEach(dept => {\r\n      // Find the division and banner for this department from smicData\r\n      const deptInfo = smicData.find((item: any) => \r\n        String(item.deptId || '').trim() === String(dept.id || '').trim()\r\n      );\r\n      \r\n      if (deptInfo) {\r\n        console.log(`Found department ${dept.id} in SMIC data with division ${deptInfo.divisionId} and banner ${deptInfo.bannerId}`);\r\n        const divisionId = deptInfo.divisionId || '00';\r\n        const bannerId = deptInfo.bannerId || '00';\r\n        \r\n        if (!divisionBannerMap.has(divisionId)) {\r\n          divisionBannerMap.set(divisionId, new Map());\r\n        }\r\n        \r\n        const bannerMap = divisionBannerMap.get(divisionId);\r\n        if (!bannerMap.has(bannerId)) {\r\n          bannerMap.set(bannerId, []);\r\n        }\r\n        \r\n        bannerMap.get(bannerId).push(dept);\r\n      } else {\r\n        console.warn(`Department ${dept.id} not found in SMIC data`);\r\n        // If no matching division/banner found, add to default\r\n        if (!divisionBannerMap.has('00')) {\r\n          divisionBannerMap.set('00', new Map());\r\n        }\r\n        \r\n        const bannerMap = divisionBannerMap.get('00');\r\n        if (!bannerMap.has('00')) {\r\n          bannerMap.set('00', []);\r\n        }\r\n        \r\n        bannerMap.get('00').push(dept);\r\n      }\r\n    });\r\n    \r\n    console.log('Division count:', divisionBannerMap.size);\r\n    divisionBannerMap.forEach((bannerMap, divisionId) => {\r\n      console.log(`Division ${divisionId} has ${bannerMap.size} banners`);\r\n    });\r\n    \r\n    // Add division, banner, and department rows\r\n    divisionBannerMap.forEach((bannerMap, divisionId) => {\r\n      // Add division row\r\n      const divisionName = getDivisionName(smicData, divisionId, `Division ${divisionId}`);\r\n      const divisionRow = { departmentName: `Division: ${divisionId} - ${divisionName}` };\r\n      \r\n      // Use first department's quarter data for the division\r\n      const firstBanner = bannerMap.values().next().value;\r\n      if (firstBanner && firstBanner.length > 0) {\r\n        rows.push(mapRow(divisionRow, firstBanner[0].quarter || {}, formatCurrency, 'Division'));\r\n      }\r\n      \r\n      bannerMap.forEach((departments, bannerId) => {\r\n        // Add banner row if not default '00'\r\n        if (isBannerSelected && bannerId !== '00') {\r\n          const bannerName = getBannerName(smicData, divisionId, bannerId, `Banner ${bannerId}`);\r\n          const bannerRow = { departmentName: `  Banner: ${bannerId} - ${bannerName}` };\r\n          \r\n          if (departments.length > 0) {\r\n            rows.push(mapRow(bannerRow, departments[0].quarter || {}, formatCurrency, 'Banner'));\r\n          }\r\n        }\r\n        \r\n        // Add departments with proper hierarchy\r\n        departments.forEach(dept => {\r\n          const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n          const indentation = isBannerSelected && bannerId !== '00' ? '    ' : '  '; // Extra indent if under a banner\r\n          const deptBaseRow = { departmentName: `${indentation}${dept.id} - ${deptName}` };\r\n          rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n          \r\n          // Process periods and weeks with proper indentation\r\n          processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  console.log(`Number of rows to add: ${rows.length}`);\r\n  if (rows.length > 0) {\r\n    console.log('Sample row data:', rows[0]);\r\n  }\r\n\r\n  if (rows.length === 0) {\r\n    console.warn('Failed to generate any rows, adding dummy row');\r\n    worksheet.addRow(['No data available']);\r\n  } else {\r\n    console.log(`Adding ${rows.length} rows to worksheet`);\r\n    rows.forEach(row => {\r\n      if (row) {\r\n        const rowValues = Object.values(row);\r\n        // Ensure we're adding values, even if empty\r\n        worksheet.addRow(rowValues.map(val => val === undefined ? '' : val));\r\n      }\r\n    });\r\n  }\r\n  \r\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\r\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\r\n  styleWorksheet(worksheet);\r\n  styleVsProjection(worksheet);\r\n  applyPrintSettings(worksheet);\r\n  \r\n  let ySplit = 2; \r\n  const totalRows = worksheet.rowCount;\r\n  for (let i = 3; i <= totalRows; i++) {\r\n    const cellValue = String(worksheet.getRow(i).getCell(1).value ?? '');\r\n    if (/^\\d+ - /.test(cellValue.trim()) && i !== 3) {\r\n      ySplit = i - 1;\r\n      break;\r\n    } if (i === totalRows) { ySplit = totalRows; }\r\n  }\r\n  worksheet.views = [{ state: 'frozen', ySplit, xSplit: 1 }];\r\n  \r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), fileName);\r\n};\r\nexport const handleDownloadBothExcel = async (\r\n  performanceSummaryData: any[],\r\n  forecastVarianceData: any[],\r\n  smicData: any[] = [],\r\n  appliedFilters?: any\r\n) => {\r\n  // Commenting out the validation that disables download when no data is present\r\n  // if (!performanceSummaryData?.length && !forecastVarianceData?.length) {\r\n  //   return alert('No dashboard data to export!');\r\n  // }\r\n  \r\n  const date = new Date().toLocaleDateString('en-CA');\r\n  const workbook = new ExcelJS.Workbook();\r\n  \r\n  if (performanceSummaryData?.length) {\r\n    await processWorksheet(\r\n      workbook, \r\n      'Performance Summary', \r\n      performanceSummaryData, \r\n      smicData, \r\n      appliedFilters, \r\n      false\r\n    );\r\n  }\r\n  \r\n  if (forecastVarianceData?.length) {\r\n    await processWorksheet(\r\n      workbook, \r\n      'Forecast Variance', \r\n      forecastVarianceData, \r\n      smicData, \r\n      appliedFilters, \r\n      true\r\n    );\r\n  }\r\n  \r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), `Allocatr Insights Performance and Variance Excel Download-${date}.xlsx`);\r\n};\r\n\r\n// New helper function to process worksheets with the same logic\r\nconst processWorksheet = async (\r\n  workbook: ExcelJS.Workbook,\r\n  sheetName: string,\r\n  data: any[],\r\n  smicData: any[],\r\n  appliedFilters: any,\r\n  isVariance: boolean\r\n) => {\r\n  const worksheet = workbook.addWorksheet(sheetName);\r\n  \r\n  // Extract quarter information\r\n  const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n  const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n  const isBannerSelected = appliedFilters?.banner?.length > 0;\r\n  \r\n  worksheet.addRow(getParentHeaderRow(quarterDisplay));\r\n  worksheet.addRow(COMMON_HEADERS);\r\n  \r\n  const rows: any[] = [];\r\n  \r\n  // Process Total first if it exists\r\n  const totalData = data.find(item => item.id === 'Total');\r\n  if (totalData) {\r\n    addRows(rows, totalData, smicData, isVariance);\r\n    data = data.filter(item => item.id !== 'Total');\r\n  }\r\n  \r\n  // Group by division and banner\r\n  const divisionBannerMap = new Map();\r\n  \r\n  // Organize departments by division and banner\r\n  console.log('SMIC data length:', smicData?.length);\r\n  console.log('Department data count:', data?.length);\r\n  \r\n  data.forEach(dept => {\r\n    // Find the division and banner for this department from smicData\r\n    const deptInfo = smicData.find((item: any) => \r\n      String(item.deptId || '').trim() === String(dept.id || '').trim()\r\n    );\r\n    \r\n    if (deptInfo) {\r\n      console.log(`Found department ${dept.id} in SMIC data with division ${deptInfo.divisionId} and banner ${deptInfo.bannerId}`);\r\n      const divisionId = deptInfo.divisionId || '00';\r\n      const bannerId = deptInfo.bannerId || '00';\r\n      \r\n      if (!divisionBannerMap.has(divisionId)) {\r\n        divisionBannerMap.set(divisionId, new Map());\r\n      }\r\n      \r\n      const bannerMap = divisionBannerMap.get(divisionId);\r\n      if (!bannerMap.has(bannerId)) {\r\n        bannerMap.set(bannerId, []);\r\n      }\r\n      \r\n      bannerMap.get(bannerId).push(dept);\r\n    } else {\r\n      console.warn(`Department ${dept.id} not found in SMIC data`);\r\n      // If no matching division/banner found, add to default\r\n      if (!divisionBannerMap.has('00')) {\r\n        divisionBannerMap.set('00', new Map());\r\n      }\r\n      \r\n      const bannerMap = divisionBannerMap.get('00');\r\n      if (!bannerMap.has('00')) {\r\n        bannerMap.set('00', []);\r\n      }\r\n      \r\n      bannerMap.get('00').push(dept);\r\n    }\r\n  });\r\n  \r\n  console.log('Division count:', divisionBannerMap.size);\r\n  divisionBannerMap.forEach((bannerMap, divisionId) => {\r\n    console.log(`Division ${divisionId} has ${bannerMap.size} banners`);\r\n  });\r\n  \r\n  // Add division, banner, and department rows\r\n  divisionBannerMap.forEach((bannerMap, divisionId) => {\r\n    // Add division row\r\n    const divisionName = getDivisionName(smicData, divisionId, `Division ${divisionId}`);\r\n    const divisionRow = { departmentName: `Division: ${divisionId} - ${divisionName}` };\r\n    \r\n    // Use first department's quarter data for the division\r\n    const firstBanner = bannerMap.values().next().value;\r\n    if (firstBanner && firstBanner.length > 0) {\r\n      rows.push(mapRow(divisionRow, firstBanner[0].quarter || {}, formatCurrency, 'Division'));\r\n    }\r\n    \r\n    bannerMap.forEach((departments, bannerId) => {\r\n      // Add banner row if not default '00'\r\n      if (isBannerSelected && bannerId !== '00') {\r\n        const bannerName = getBannerName(smicData, divisionId, bannerId, `Banner ${bannerId}`);\r\n        const bannerRow = { departmentName: `Banner: ${bannerId} - ${bannerName}` };\r\n        \r\n        if (departments.length > 0) {\r\n          rows.push(mapRow(bannerRow, departments[0].quarter || {}, formatCurrency, 'Banner'));\r\n        }\r\n      }\r\n      \r\n      // Add departments with proper hierarchy\r\n      departments.forEach(dept => {\r\n        const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n        const indentation = isBannerSelected && bannerId !== '00' ? '    ' : '  '; // Extra indent if under a banner\r\n        const deptBaseRow = { departmentName: `${indentation}${dept.id} - ${deptName}` };\r\n        rows.push(mapRow(deptBaseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n        \r\n        // Process periods and weeks with proper indentation\r\n        processDepartmentDetails(rows, dept, deptBaseRow, smicData, isVariance);\r\n      });\r\n    });\r\n  });\r\n  \r\n  // Add this before adding rows to worksheet in both functions\r\n  console.log(`Number of rows to add: ${rows.length}`);\r\n  if (rows.length > 0) {\r\n    console.log('Sample row data:', rows[0]);\r\n  }\r\n\r\n  // Add rows to worksheet\r\n  rows.forEach(row => {\r\n    if (row) {\r\n      const rowValues = Object.values(row);\r\n      // Ensure we're adding values, even if empty\r\n      worksheet.addRow(rowValues.map(val => val === undefined ? '' : val));\r\n    }\r\n  });\r\n  \r\n  // Apply styling\r\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\r\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\r\n  styleWorksheet(worksheet);\r\n  styleVsProjection(worksheet);\r\n  applyPrintSettings(worksheet);\r\n};\r\n\r\n// Add this new helper function to process department details with proper indentation\r\nfunction processDepartmentDetails(rows, dept, baseRow, smicData, isVariance) {\r\n  const weeksByPeriod = {};\r\n  \r\n  // Group weeks by their period\r\n  (dept.weeks || []).forEach((week) => {\r\n    const periodNum = week.periodNumber ?? week.periodNbr ?? '';\r\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\r\n    weeksByPeriod[periodNum].push(week);\r\n  });\r\n  \r\n  // Process each period\r\n  dept.periods?.forEach((period) => {\r\n    const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n    const periodDisplayNum = periodNum ? parseInt(String(periodNum).slice(-2), 10) : '';\r\n    \r\n    // Add period with indentation (one level deeper than department)\r\n    const periodIndent = baseRow.departmentName.startsWith('  ') ? '      ' : '    ';\r\n    rows.push(\r\n      mapRow(\r\n        { ...baseRow, departmentName: `${periodIndent}Period ${periodDisplayNum}` },\r\n        period,\r\n        formatCurrency,\r\n        'Period',\r\n        periodNum\r\n      )\r\n    );\r\n    \r\n    // Process weeks for this period with additional indentation\r\n    const weeks = weeksByPeriod[periodNum] || [];\r\n    const sortedWeeks = weeks.slice().sort((a, b) => {\r\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n      return aNum - bNum;\r\n    });\r\n    \r\n    sortedWeeks.forEach((week) => {\r\n      let weekNum = '--';\r\n      if (typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n        weekNum = String(parseInt(week.id.slice(-2), 10));\r\n      } else if (typeof week.weekNumber === 'number') {\r\n        weekNum = String(week.weekNumber % 100);\r\n      } else if (typeof week.weekNumber === 'string') {\r\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n      }\r\n      \r\n      // Add week with indentation (one level deeper than period)\r\n      const weekIndent = periodIndent + '  ';\r\n      rows.push(\r\n        mapRow(\r\n          { ...baseRow, departmentName: `${weekIndent}Week ${weekNum} (fiscal wk ${weekNum})` },\r\n          week,\r\n          formatCurrency,\r\n          'Week',\r\n          '',\r\n          String(weekNum)\r\n        )\r\n      );\r\n    });\r\n  });\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,SAAS;AAC7B,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,MAAM,QAAQ,gCAAgC;AAChJ,SAASC,kBAAkB,QAAQ,+BAA+B;AAElE,OAAO,MAAMC,cAAc,GAAIC,KAAU,IAAK;EAC5C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;EACtE,MAAME,GAAG,GAAGC,MAAM,CAACH,KAAK,CAAC;EACzB,OAAOI,KAAK,CAACF,GAAG,CAAC,GAAGF,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;IAAEC,qBAAqB,EAAE;EAAE,CAAC,CAAC,EAAE;AAC7F,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAe,EAAEC,MAAc,EAAEC,QAAgB,KAAK;EAChF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACL,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;EAChG,OAAOvB,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEK,QAAQ,KAAIN,QAAQ,IAAI,EAAE,CAAC;AACvD,CAAC;AACD,OAAO,MAAMO,eAAe,GAAGA,CAACT,QAAe,EAAEU,UAAkB,EAAER,QAAgB,KAAK;EACxF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,KAAKJ,MAAM,CAACI,UAAU,CAAC,CAAC;EAC1F,OAAO1B,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEQ,YAAY,KAAIT,QAAQ,IAAI,EAAE,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMU,aAAa,GAAGA,CAACZ,QAAe,EAAEU,UAAkB,EAAEG,QAAgB,EAAEX,QAAgB,KAAK;EACxG,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IACpCC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,KAAKJ,MAAM,CAACI,UAAU,CAAC,IAC9CJ,MAAM,CAACD,IAAI,CAACQ,QAAQ,CAAC,KAAKP,MAAM,CAACO,QAAQ,CAC3C,CAAC;EACD,OAAO7B,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEW,UAAU,KAAIZ,QAAQ,IAAI,EAAE,CAAC;AACzD,CAAC;AAED,MAAMa,OAAO,GAAGA,CAACC,IAAW,EAAEC,IAAS,EAAEjB,QAAe,EAAEkB,SAAkB,GAAG,KAAK,KAAK;EAAA,IAAAC,UAAA,EAAAC,aAAA;EACvF,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC;EAClC,MAAMb,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAH,UAAA,GAAEF,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAJ,UAAA,GAAI,EAAE,CAAC;EACjE,MAAMK,OAAO,GAAGP,IAAI,CAACK,EAAE,KAAK,OAAO;EACnC,MAAMG,OAAO,GAAG;IAAEC,cAAc,EAAEF,OAAO,GAAG,OAAO,GAAG,GAAGP,IAAI,CAACK,EAAE,MAAMd,QAAQ;EAAG,CAAC;EAClFQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACoC,OAAO,EAAEJ,OAAO,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;EAE9D,MAAMqC,aAAoC,GAAG,CAAC,CAAC;EAC/C,CAACX,IAAI,CAACY,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAEC,IAAS,IAAK;IAAA,IAAAC,IAAA,EAAAC,kBAAA;IACxC,MAAMC,SAAS,IAAAF,IAAA,IAAAC,kBAAA,GAAGF,IAAI,CAACI,YAAY,YAAAF,kBAAA,GAAIF,IAAI,CAACK,SAAS,YAAAJ,IAAA,GAAI,EAAE;IAC3D,IAAI,CAACJ,aAAa,CAACM,SAAS,CAAC,EAAEN,aAAa,CAACM,SAAS,CAAC,GAAG,EAAE;IAC5DN,aAAa,CAACM,SAAS,CAAC,CAACP,IAAI,CAACI,IAAI,CAAC;EACrC,CAAC,CAAC;EAEF,CAAAX,aAAA,GAAAH,IAAI,CAACoB,OAAO,aAAZjB,aAAA,CAAcU,OAAO,CAAEQ,MAAW,IAAK;IAAA,IAAAC,KAAA,EAAAC,oBAAA;IACrC,MAAMN,SAAS,IAAAK,KAAA,IAAAC,oBAAA,GAAGF,MAAM,CAACH,YAAY,YAAAK,oBAAA,GAAIF,MAAM,CAACF,SAAS,YAAAG,KAAA,GAAI,EAAE;IAC/DvB,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;MAAEC,cAAc,EAAEQ,SAAS,GAAG,UAAUS,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;IAAQ,IAC1GN,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;IAED,MAAML,KAAK,GAAGD,aAAa,CAACM,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAMW,WAAW,GAAGhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/C,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,OAAOK,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IACFN,WAAW,CAACf,OAAO,CAAEC,IAAS,IAAK;MACjC,IAAIqB,OAAO,GAAG,IAAI;MAClB,IAAIlC,SAAS,IAAI,OAAOa,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3ED,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI,CAAC1B,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,CAAChC,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D;MACA5B,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;QAAEC,cAAc,EAAE,QAAQ0B,OAAO,eAAeA,OAAO;MAAG,IACtErB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAME,cAAc,GAAGC,SAAS,IAAI;EACzCA,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,KAAK,CAAC,EAAE;MAAA,IAAAC,WAAA;MACnBL,SAAS,CAACM,SAAS,CAACF,SAAS,CAAC,CAACG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC1D,MAAM,EAAAsD,WAAA,GAACF,IAAI,CAAClE,KAAK,YAAAoE,WAAA,GAAI,EAAE,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;IAC1F;EACF,CAAC,CAAC;EACF,IAAIC,IAAI,GAAG,CAAC;EACZX,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IAAA,IAAAC,kBAAA;IACpC,MAAMC,SAAS,GAAGjE,MAAM,EAAAgE,kBAAA,GAACF,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK,YAAA8E,kBAAA,GAAI,EAAE,CAAC;IACpD,IAAIC,SAAS,CAACN,MAAM,GAAGC,IAAI,EAAEA,IAAI,GAAGK,SAAS,CAACN,MAAM;EACtD,CAAC,CAAC;EACFV,SAAS,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACE,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;EAErD,MAAMO,cAAc,GAAG;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAW;EAAE,CAAC;EACrE,MAAMC,oBAAoB,GAAG;IAC7BC,GAAG,EAAEL,cAAc;IAAEM,IAAI,EAAEN,cAAc;IAACO,MAAM,EAAEP,cAAc;IAACQ,KAAK,EAAER;EACxE,CAAC;EACAlB,SAAS,CAACY,OAAO,CAACC,GAAG,IAAI;IACxBA,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;MACnBA,IAAI,CAACwB,MAAM,GAAGL,oBAAoB;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMM,aAAa,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1FrB,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC6B,IAAI,GAAGJ,aAAa;IACzBzB,IAAI,CAAC8B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B/B,IAAI,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFrC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC8B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B/B,IAAI,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFrC,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACe,IAAI,GAAGJ,aAAa;EAAE5B,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAG;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC3F,MAAMI,aAAa,GAAG;IAAET,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1F,MAAMkB,iBAAiB,GAAG;IAAEV,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC9F,MAAMmB,YAAY,GAAG;IAAEX,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,CAAC;EAC3F,MAAMoB,UAAU,GAAG;IAAEZ,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,CAAC;;EAEzFrB,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,MAAM4B,SAAS,GAAG7B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK;MACtC,IAAI,OAAOyG,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAIA,SAAS,CAAC1F,IAAI,CAAC,CAAC,KAAK,OAAO,EAAE;UAChC6D,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGQ,YAAY;YAAErC,IAAI,CAAC8B,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQgB,IAAI,CAAC8B,IAAI;cAAEC,IAAI,EAAE;YAAI,EAAE;UAAE,CAAC,CAAC;QAC/F,CAAC,MAAM,IAAI,YAAY,CAACS,IAAI,CAACD,SAAS,CAAC,EAAE;UACvC7B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGQ,YAAY;YAAErC,IAAI,CAAC8B,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQgB,IAAI,CAAC8B,IAAI;cAAEC,IAAI,EAAE;YAAI,EAAE;UAAE,CAAC,CAAC;QAC/F,CAAC,MAAM,IAAI,UAAU,CAACS,IAAI,CAACD,SAAS,CAAC,EAAE;UACrC7B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGS,UAAU;YAAEtC,IAAI,CAAC8B,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQgB,IAAI,CAAC8B,IAAI;cAAEC,IAAI,EAAE;YAAI,EAAE;UAAE,CAAC,CAAC;QAC7F,CAAC,MAAM,IAAI,YAAY,CAACS,IAAI,CAACD,SAAS,CAAC,EAAE;UACvC7B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGO,iBAAiB;UAAE,CAAC,CAAC;QAC1D,CAAC,MAAM,IAAI,WAAW,CAACI,IAAI,CAACD,SAAS,CAAC,EAAE;UACtC7B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGM,aAAa;UAAE,CAAC,CAAC;QACtD,CAAC,MAAM,IAAI,SAAS,CAACK,IAAI,CAACD,SAAS,CAAC,EAAE;UACpC;QAAA;MAEJ;IACF;EACF,CAAC,CAAC;EACF1C,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACkB,SAAS,GAAG;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAS,CAAC;EAChF,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC9D,OAAO,CAAC4B,IAAI,IAAI;IAC3BH,SAAS,CAACiB,OAAO,CAACd,IAAI,CAAC,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAClF,CAAC,CAAC;EAACrC,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQa,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI;IAAEC,IAAI,EAAE;EAAI,EAAE;AACnF,CAAC;AACD,OAAO,MAAMU,iBAAiB,GAAI5C,SAA4B,IAAK;EACjE,MAAM6C,sBAAgC,GAAG,EAAE;EAC3C7C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIxE,qBAAqB,CAACkH,QAAQ,CAAC/F,MAAM,CAACoD,IAAI,CAAClE,KAAK,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC,EAAE6F,sBAAsB,CAACzE,IAAI,CAACgC,SAAS,CAAC;EACvG,CAAC,CAAC;EACFJ,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB+B,sBAAsB,CAACtE,OAAO,CAACwE,MAAM,IAAI;QACvC,MAAM5C,IAAI,GAAGU,GAAG,CAACI,OAAO,CAAC8B,MAAM,CAAC;QAChC,IAAIC,GAAG,GAAG,OAAO7C,IAAI,CAAClE,KAAK,KAAK,QAAQ,GAAGkE,IAAI,CAAClE,KAAK,CAACgH,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACjG,IAAI,CAAC,CAAC,GAAGmD,IAAI,CAAClE,KAAK;QACrG,MAAME,GAAG,GAAGC,MAAM,CAAC4G,GAAG,CAAC;QACvB,IAAI,CAAC3G,KAAK,CAACF,GAAG,CAAC,IAAI6G,GAAG,KAAK,EAAE,EAAE;UAC7B7C,IAAI,CAAC8B,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQgB,IAAI,CAAC8B,IAAI;YAAEb,KAAK,EAAE;cAAEC,IAAI,EAAElF,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG;YAAW;UAAC,EAAE;UAChF,MAAM+G,MAAM,GAAGlD,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACgB,OAAO,CAAC8B,MAAM,CAAC,CAAC9G,KAAK;UACxD,IAAIJ,4BAA4B,CAACiH,QAAQ,CAAC/F,MAAM,CAACmG,MAAM,CAAC,CAAClG,IAAI,CAAC,CAAC,CAAC,EAAE;YAChE,IAAIb,GAAG,GAAG,CAAC,EAAE;cACXgE,IAAI,CAAClE,KAAK,GAAG,KAAKuE,IAAI,CAAC2C,GAAG,CAAChH,GAAG,CAAC,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,GAAG;YAC1F,CAAC,MAAM;cACL4D,IAAI,CAAClE,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,EAAE;YAC9E;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAM6G,mBAAmB,GAAG,MAAAA,CACjCC,aAAoB,EACpB5G,QAAe,GAAG,EAAE,EACpB6G,cAAoB,EACpBC,QAAgB,GAAG,+BAA+B,KAC/C;EAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACH;EACAC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;IAC5CC,UAAU,GAAAT,cAAA,GAAEH,aAAa,qBAAbG,cAAA,CAAe9C,MAAM;IACjCwD,cAAc,EAAEzH,QAAQ,oBAARA,QAAQ,CAAEiE,MAAM;IAChCyD,aAAa,EAAE,EAAAV,eAAA,GAAAJ,aAAa,qBAAbI,eAAA,CAAe/C,MAAM,IAAG,CAAC,GAAGxB,MAAM,CAACkF,IAAI,CAACf,aAAa,CAAC,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM;IAC5FC,SAAS,EAAE,EAAAZ,eAAA,GAAAL,aAAa,qBAAbK,eAAA,CAAehD,MAAM,IAAG,CAAC,GAAG6D,IAAI,CAACC,SAAS,CAACnB,aAAa,CAAC,CAAC,CAAC,CAAC,CAACoB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG;EACtG,CAAC,CAAC;;EAEF;EACA,IAAI,GAAAd,eAAA,GAACN,aAAa,aAAbM,eAAA,CAAejD,MAAM,GAAE;IAC1BqD,OAAO,CAACW,IAAI,CAAC,wCAAwC,CAAC;IACtDC,KAAK,CAAC,mFAAmF,CAAC;IAC1F;EACF;EAEA,MAAMC,aAAa,GAAG,CAAAtB,cAAc,aAAAM,qBAAA,GAAdN,cAAc,CAAEuB,SAAS,qBAAzBjB,qBAAA,CAA2B9F,OAAO,KAAI,EAAE;EAC9D,MAAMgH,UAAU,GAAG,CAAAxB,cAAc,aAAAO,sBAAA,GAAdP,cAAc,CAAEuB,SAAS,qBAAzBhB,sBAAA,CAA2BiB,UAAU,KAAI,EAAE;EAC9D,MAAMC,cAAc,GAAG,IAAIH,aAAa,IAAIE,UAAU,EAAE;EACxD,MAAME,eAAe,GAAGtJ,kBAAkB,CAACqJ,cAAc,CAAC;EAC1D,MAAME,UAAU,GAAG1B,QAAQ,CAAC2B,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAMqC,gBAAgB,GAAG,CAAA7B,cAAc,aAAAQ,qBAAA,GAAdR,cAAc,CAAE8B,MAAM,qBAAtBtB,qBAAA,CAAwBpD,MAAM,IAAG,CAAC;;EAE3D;EACA,MAAM2E,QAAQ,GAAG,IAAI9J,OAAO,CAAC+J,QAAQ,CAAC,CAAC;EACvC,MAAMtF,SAAS,GAAGqF,QAAQ,CAACE,YAAY,CAAC,WAAW,CAAC;EACpDvF,SAAS,CAACwF,MAAM,CAACR,eAAe,CAAC;EACjChF,SAAS,CAACwF,MAAM,CAAC7J,cAAc,CAAC;EAEhC,MAAM8B,IAAW,GAAG,EAAE;;EAEtB;EACA,MAAMgI,SAAS,GAAGpC,aAAa,CAACxG,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACiB,EAAE,KAAK,OAAO,CAAC;EACjE,IAAI0H,SAAS,EAAE;IACbjI,OAAO,CAACC,IAAI,EAAEgI,SAAS,EAAEhJ,QAAQ,EAAEwI,UAAU,CAAC;IAC9C5B,aAAa,GAAGA,aAAa,CAACqC,MAAM,CAAC5I,IAAI,IAAIA,IAAI,CAACiB,EAAE,KAAK,OAAO,CAAC;EACnE;;EAEA;EACA,MAAM4H,aAAa,GAAGtC,aAAa,CAACuC,IAAI,CAAC9I,IAAI,IAAIA,IAAI,CAAC+I,OAAO,IAAI/I,IAAI,CAACgJ,SAAS,CAAC;EAEhF,IAAIH,aAAa,EAAE;IACjB5B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjDX,aAAa,CAAC9E,OAAO,CAACwH,QAAQ,IAAI;MAChC,IAAIA,QAAQ,CAACD,SAAS,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACD,SAAS,CAAC,EAAE;QAC3D;QACAC,QAAQ,CAACD,SAAS,CAACvH,OAAO,CAAC2H,QAAQ,IAAI;UACrC;UACA,MAAMC,WAAW,GAAG;YAAEhI,cAAc,EAAE,aAAa+H,QAAQ,CAACnI,EAAE,MAAMmI,QAAQ,CAAClI,IAAI,IAAId,eAAe,CAACT,QAAQ,EAAEyJ,QAAQ,CAACnI,EAAE,EAAE,UAAU,CAAC;UAAG,CAAC;UAC3IN,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACqK,WAAW,EAAED,QAAQ,CAACpI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,UAAU,CAAC,CAAC;UAElF,IAAImJ,gBAAgB,IAAIe,QAAQ,CAACL,OAAO,IAAIG,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACL,OAAO,CAAC,EAAE;YAC3EK,QAAQ,CAACL,OAAO,CAACtH,OAAO,CAAC6G,MAAM,IAAI;cACjC;cACA,MAAM7H,UAAU,GAAG6H,MAAM,CAACpH,IAAI,IAAIX,aAAa,CAACZ,QAAQ,EAAEyJ,QAAQ,CAACnI,EAAE,EAAEqH,MAAM,CAACrH,EAAE,EAAE,QAAQ,CAAC;cAC3F,MAAMqI,SAAS,GAAG;gBAAEjI,cAAc,EAAE,aAAaiH,MAAM,CAACrH,EAAE,MAAMR,UAAU;cAAG,CAAC;cAC9EE,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACsK,SAAS,EAAEhB,MAAM,CAACtH,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,QAAQ,CAAC,CAAC;cAE5E,IAAIoJ,MAAM,CAACiB,WAAW,IAAIL,KAAK,CAACC,OAAO,CAACb,MAAM,CAACiB,WAAW,CAAC,EAAE;gBAC3DjB,MAAM,CAACiB,WAAW,CAAC9H,OAAO,CAACb,IAAI,IAAI;kBAAA,IAAA4I,WAAA;kBACjC,MAAMrJ,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAuI,WAAA,GAAE5I,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAsI,WAAA,GAAI,EAAE,CAAC;kBACjE;kBACA,MAAMC,WAAW,GAAG;oBAAEpI,cAAc,EAAE,OAAOT,IAAI,CAACK,EAAE,MAAMd,QAAQ;kBAAG,CAAC;kBACtEQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACyK,WAAW,EAAE7I,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;kBAE7E;kBACAwK,wBAAwB,CAAC/I,IAAI,EAAEC,IAAI,EAAE6I,WAAW,EAAE9J,QAAQ,EAAEwI,UAAU,CAAC;gBACzE,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIiB,QAAQ,CAACG,WAAW,IAAIL,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACG,WAAW,CAAC,EAAE;YACtEH,QAAQ,CAACG,WAAW,CAAC9H,OAAO,CAACb,IAAI,IAAI;cAAA,IAAA+I,WAAA;cACnC,MAAMxJ,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAA0I,WAAA,GAAE/I,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAyI,WAAA,GAAI,EAAE,CAAC;cACjE;cACA,MAAMF,WAAW,GAAG;gBAAEpI,cAAc,EAAE,KAAKT,IAAI,CAACK,EAAE,MAAMd,QAAQ;cAAG,CAAC;cACpEQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACyK,WAAW,EAAE7I,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;cAE7E;cACAwK,wBAAwB,CAAC/I,IAAI,EAAEC,IAAI,EAAE6I,WAAW,EAAE9J,QAAQ,EAAEwI,UAAU,CAAC;YACzE,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIE,gBAAgB,IAAIY,QAAQ,CAACF,OAAO,IAAIG,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACF,OAAO,CAAC,EAAE;QAClF;QACAE,QAAQ,CAACF,OAAO,CAACtH,OAAO,CAAC6G,MAAM,IAAI;UACjC,MAAMgB,SAAS,GAAG;YAAEjI,cAAc,EAAE,WAAWiH,MAAM,CAACrH,EAAE,MAAMqH,MAAM,CAACpH,IAAI,IAAI,QAAQ;UAAG,CAAC;UACzFP,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACsK,SAAS,EAAEhB,MAAM,CAACtH,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,QAAQ,CAAC,CAAC;UAE5E,IAAIoJ,MAAM,CAACiB,WAAW,IAAIL,KAAK,CAACC,OAAO,CAACb,MAAM,CAACiB,WAAW,CAAC,EAAE;YAC3DjB,MAAM,CAACiB,WAAW,CAAC9H,OAAO,CAACb,IAAI,IAAI;cAAA,IAAAgJ,WAAA;cACjC,MAAMzJ,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAA2I,WAAA,GAAEhJ,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAA0I,WAAA,GAAI,EAAE,CAAC;cACjE;cACA,MAAMH,WAAW,GAAG;gBAAEpI,cAAc,EAAE,KAAKT,IAAI,CAACK,EAAE,MAAMd,QAAQ;cAAG,CAAC;cACpEQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACyK,WAAW,EAAE7I,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;cAE7E;cACAwK,wBAAwB,CAAC/I,IAAI,EAAEC,IAAI,EAAE6I,WAAW,EAAE9J,QAAQ,EAAEwI,UAAU,CAAC;YACzE,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IAAA,IAAA0B,eAAA;IACL5C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;IAEzE;IACA,MAAM4C,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEnC9C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEvH,QAAQ,oBAARA,QAAQ,CAAEiE,MAAM,CAAC;IAClDqD,OAAO,CAACC,GAAG,CAAC,wBAAwB,GAAA2C,eAAA,GAAEtD,aAAa,qBAAbsD,eAAA,CAAejG,MAAM,CAAC;;IAE5D;IACA2C,aAAa,CAAC9E,OAAO,CAACb,IAAI,IAAI;MAC5B;MACA,MAAMoJ,QAAQ,GAAGrK,QAAQ,CAACI,IAAI,CAAEC,IAAS,IACvCC,MAAM,CAACD,IAAI,CAACJ,MAAM,IAAI,EAAE,CAAC,CAACM,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACW,IAAI,CAACK,EAAE,IAAI,EAAE,CAAC,CAACf,IAAI,CAAC,CAClE,CAAC;MAED,IAAI8J,QAAQ,EAAE;QACZ/C,OAAO,CAACC,GAAG,CAAC,oBAAoBtG,IAAI,CAACK,EAAE,+BAA+B+I,QAAQ,CAAC3J,UAAU,eAAe2J,QAAQ,CAACxJ,QAAQ,EAAE,CAAC;QAC5H,MAAMH,UAAU,GAAG2J,QAAQ,CAAC3J,UAAU,IAAI,IAAI;QAC9C,MAAMG,QAAQ,GAAGwJ,QAAQ,CAACxJ,QAAQ,IAAI,IAAI;QAE1C,IAAI,CAACsJ,iBAAiB,CAACG,GAAG,CAAC5J,UAAU,CAAC,EAAE;UACtCyJ,iBAAiB,CAACI,GAAG,CAAC7J,UAAU,EAAE,IAAI0J,GAAG,CAAC,CAAC,CAAC;QAC9C;QAEA,MAAMI,SAAS,GAAGL,iBAAiB,CAACM,GAAG,CAAC/J,UAAU,CAAC;QACnD,IAAI,CAAC8J,SAAS,CAACF,GAAG,CAACzJ,QAAQ,CAAC,EAAE;UAC5B2J,SAAS,CAACD,GAAG,CAAC1J,QAAQ,EAAE,EAAE,CAAC;QAC7B;QAEA2J,SAAS,CAACC,GAAG,CAAC5J,QAAQ,CAAC,CAACc,IAAI,CAACV,IAAI,CAAC;MACpC,CAAC,MAAM;QACLqG,OAAO,CAACW,IAAI,CAAC,cAAchH,IAAI,CAACK,EAAE,yBAAyB,CAAC;QAC5D;QACA,IAAI,CAAC6I,iBAAiB,CAACG,GAAG,CAAC,IAAI,CAAC,EAAE;UAChCH,iBAAiB,CAACI,GAAG,CAAC,IAAI,EAAE,IAAIH,GAAG,CAAC,CAAC,CAAC;QACxC;QAEA,MAAMI,SAAS,GAAGL,iBAAiB,CAACM,GAAG,CAAC,IAAI,CAAC;QAC7C,IAAI,CAACD,SAAS,CAACF,GAAG,CAAC,IAAI,CAAC,EAAE;UACxBE,SAAS,CAACD,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACzB;QAEAC,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC9I,IAAI,CAACV,IAAI,CAAC;MAChC;IACF,CAAC,CAAC;IAEFqG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4C,iBAAiB,CAACO,IAAI,CAAC;IACtDP,iBAAiB,CAACrI,OAAO,CAAC,CAAC0I,SAAS,EAAE9J,UAAU,KAAK;MACnD4G,OAAO,CAACC,GAAG,CAAC,YAAY7G,UAAU,QAAQ8J,SAAS,CAACE,IAAI,UAAU,CAAC;IACrE,CAAC,CAAC;;IAEF;IACAP,iBAAiB,CAACrI,OAAO,CAAC,CAAC0I,SAAS,EAAE9J,UAAU,KAAK;MACnD;MACA,MAAMC,YAAY,GAAGF,eAAe,CAACT,QAAQ,EAAEU,UAAU,EAAE,YAAYA,UAAU,EAAE,CAAC;MACpF,MAAMgJ,WAAW,GAAG;QAAEhI,cAAc,EAAE,aAAahB,UAAU,MAAMC,YAAY;MAAG,CAAC;;MAEnF;MACA,MAAMgK,WAAW,GAAGH,SAAS,CAACI,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACrL,KAAK;MACnD,IAAImL,WAAW,IAAIA,WAAW,CAAC1G,MAAM,GAAG,CAAC,EAAE;QACzCjD,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACqK,WAAW,EAAEiB,WAAW,CAAC,CAAC,CAAC,CAACtJ,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,UAAU,CAAC,CAAC;MAC1F;MAEAiL,SAAS,CAAC1I,OAAO,CAAC,CAAC8H,WAAW,EAAE/I,QAAQ,KAAK;QAC3C;QACA,IAAI6H,gBAAgB,IAAI7H,QAAQ,KAAK,IAAI,EAAE;UACzC,MAAMC,UAAU,GAAGF,aAAa,CAACZ,QAAQ,EAAEU,UAAU,EAAEG,QAAQ,EAAE,UAAUA,QAAQ,EAAE,CAAC;UACtF,MAAM8I,SAAS,GAAG;YAAEjI,cAAc,EAAE,aAAab,QAAQ,MAAMC,UAAU;UAAG,CAAC;UAE7E,IAAI8I,WAAW,CAAC3F,MAAM,GAAG,CAAC,EAAE;YAC1BjD,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACsK,SAAS,EAAEC,WAAW,CAAC,CAAC,CAAC,CAACvI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,QAAQ,CAAC,CAAC;UACtF;QACF;;QAEA;QACAqK,WAAW,CAAC9H,OAAO,CAACb,IAAI,IAAI;UAAA,IAAA6J,WAAA;UAC1B,MAAMtK,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAwJ,WAAA,GAAE7J,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAuJ,WAAA,GAAI,EAAE,CAAC;UACjE,MAAMC,WAAW,GAAGrC,gBAAgB,IAAI7H,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;UAC3E,MAAMiJ,WAAW,GAAG;YAAEpI,cAAc,EAAE,GAAGqJ,WAAW,GAAG9J,IAAI,CAACK,EAAE,MAAMd,QAAQ;UAAG,CAAC;UAChFQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACyK,WAAW,EAAE7I,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;UAE7E;UACAwK,wBAAwB,CAAC/I,IAAI,EAAEC,IAAI,EAAE6I,WAAW,EAAE9J,QAAQ,EAAEwI,UAAU,CAAC;QACzE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAlB,OAAO,CAACC,GAAG,CAAC,0BAA0BvG,IAAI,CAACiD,MAAM,EAAE,CAAC;EACpD,IAAIjD,IAAI,CAACiD,MAAM,GAAG,CAAC,EAAE;IACnBqD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEvG,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1C;EAEA,IAAIA,IAAI,CAACiD,MAAM,KAAK,CAAC,EAAE;IACrBqD,OAAO,CAACW,IAAI,CAAC,+CAA+C,CAAC;IAC7D1E,SAAS,CAACwF,MAAM,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACzC,CAAC,MAAM;IACLzB,OAAO,CAACC,GAAG,CAAC,UAAUvG,IAAI,CAACiD,MAAM,oBAAoB,CAAC;IACtDjD,IAAI,CAACc,OAAO,CAACsC,GAAG,IAAI;MAClB,IAAIA,GAAG,EAAE;QACP,MAAM4G,SAAS,GAAGvI,MAAM,CAACmI,MAAM,CAACxG,GAAG,CAAC;QACpC;QACAb,SAAS,CAACwF,MAAM,CAACiC,SAAS,CAACC,GAAG,CAACC,GAAG,IAAIA,GAAG,KAAKzL,SAAS,GAAG,EAAE,GAAGyL,GAAG,CAAC,CAAC;MACtE;IACF,CAAC,CAAC;EACJ;EAEA,MAAMC,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC5GA,WAAW,CAACrJ,OAAO,CAACsJ,KAAK,IAAI7H,SAAS,CAAC8H,UAAU,CAACD,KAAK,CAAC,CAAC;EACzD9H,cAAc,CAACC,SAAS,CAAC;EACzB4C,iBAAiB,CAAC5C,SAAS,CAAC;EAC5BjE,kBAAkB,CAACiE,SAAS,CAAC;EAE7B,IAAI+H,MAAM,GAAG,CAAC;EACd,MAAMC,SAAS,GAAGhI,SAAS,CAACiI,QAAQ;EACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAAA,IAAAC,qBAAA;IACnC,MAAMnH,SAAS,GAAGjE,MAAM,EAAAoL,qBAAA,GAACnI,SAAS,CAACC,MAAM,CAACiI,CAAC,CAAC,CAACjH,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK,YAAAkM,qBAAA,GAAI,EAAE,CAAC;IACpE,IAAI,SAAS,CAACxF,IAAI,CAAC3B,SAAS,CAAChE,IAAI,CAAC,CAAC,CAAC,IAAIkL,CAAC,KAAK,CAAC,EAAE;MAC/CH,MAAM,GAAGG,CAAC,GAAG,CAAC;MACd;IACF;IAAE,IAAIA,CAAC,KAAKF,SAAS,EAAE;MAAED,MAAM,GAAGC,SAAS;IAAE;EAC/C;EACAhI,SAAS,CAACoI,KAAK,GAAG,CAAC;IAAEC,KAAK,EAAE,QAAQ;IAAEN,MAAM;IAAEO,MAAM,EAAE;EAAE,CAAC,CAAC;EAE1D,MAAMC,YAAY,GAAG,MAAMlD,QAAQ,CAACmD,IAAI,CAACC,WAAW,CAAC,CAAC;EACtDjN,MAAM,CAAC,IAAIkN,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAEhF,QAAQ,CAAC;AAC5C,CAAC;AACD,OAAO,MAAMoF,uBAAuB,GAAG,MAAAA,CACrCC,sBAA6B,EAC7BC,oBAA2B,EAC3BpM,QAAe,GAAG,EAAE,EACpB6G,cAAoB,KACjB;EACH;EACA;EACA;EACA;;EAEA,MAAMwF,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;EACnD,MAAM3D,QAAQ,GAAG,IAAI9J,OAAO,CAAC+J,QAAQ,CAAC,CAAC;EAEvC,IAAIsD,sBAAsB,YAAtBA,sBAAsB,CAAElI,MAAM,EAAE;IAClC,MAAMuI,gBAAgB,CACpB5D,QAAQ,EACR,qBAAqB,EACrBuD,sBAAsB,EACtBnM,QAAQ,EACR6G,cAAc,EACd,KACF,CAAC;EACH;EAEA,IAAIuF,oBAAoB,YAApBA,oBAAoB,CAAEnI,MAAM,EAAE;IAChC,MAAMuI,gBAAgB,CACpB5D,QAAQ,EACR,mBAAmB,EACnBwD,oBAAoB,EACpBpM,QAAQ,EACR6G,cAAc,EACd,IACF,CAAC;EACH;EAEA,MAAMiF,YAAY,GAAG,MAAMlD,QAAQ,CAACmD,IAAI,CAACC,WAAW,CAAC,CAAC;EACtDjN,MAAM,CAAC,IAAIkN,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAE,6DAA6DO,IAAI,OAAO,CAAC;AAC5G,CAAC;;AAED;AACA,MAAMG,gBAAgB,GAAG,MAAAA,CACvB5D,QAA0B,EAC1B6D,SAAiB,EACjBC,IAAW,EACX1M,QAAe,EACf6G,cAAmB,EACnB2B,UAAmB,KAChB;EAAA,IAAAmE,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,KAAA;EACH,MAAMvJ,SAAS,GAAGqF,QAAQ,CAACE,YAAY,CAAC2D,SAAS,CAAC;;EAElD;EACA,MAAMtE,aAAa,GAAG,CAAAtB,cAAc,aAAA8F,sBAAA,GAAd9F,cAAc,CAAEuB,SAAS,qBAAzBuE,sBAAA,CAA2BtL,OAAO,KAAI,EAAE;EAC9D,MAAMgH,UAAU,GAAG,CAAAxB,cAAc,aAAA+F,sBAAA,GAAd/F,cAAc,CAAEuB,SAAS,qBAAzBwE,sBAAA,CAA2BvE,UAAU,KAAI,EAAE;EAC9D,MAAMC,cAAc,GAAG,IAAIH,aAAa,IAAIE,UAAU,EAAE;EACxD,MAAMK,gBAAgB,GAAG,CAAA7B,cAAc,aAAAgG,sBAAA,GAAdhG,cAAc,CAAE8B,MAAM,qBAAtBkE,sBAAA,CAAwB5I,MAAM,IAAG,CAAC;EAE3DV,SAAS,CAACwF,MAAM,CAAC9J,kBAAkB,CAACqJ,cAAc,CAAC,CAAC;EACpD/E,SAAS,CAACwF,MAAM,CAAC7J,cAAc,CAAC;EAEhC,MAAM8B,IAAW,GAAG,EAAE;;EAEtB;EACA,MAAMgI,SAAS,GAAG0D,IAAI,CAACtM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACiB,EAAE,KAAK,OAAO,CAAC;EACxD,IAAI0H,SAAS,EAAE;IACbjI,OAAO,CAACC,IAAI,EAAEgI,SAAS,EAAEhJ,QAAQ,EAAEwI,UAAU,CAAC;IAC9CkE,IAAI,GAAGA,IAAI,CAACzD,MAAM,CAAC5I,IAAI,IAAIA,IAAI,CAACiB,EAAE,KAAK,OAAO,CAAC;EACjD;;EAEA;EACA,MAAM6I,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;;EAEnC;EACA9C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEvH,QAAQ,oBAARA,QAAQ,CAAEiE,MAAM,CAAC;EAClDqD,OAAO,CAACC,GAAG,CAAC,wBAAwB,GAAAuF,KAAA,GAAEJ,IAAI,qBAAJI,KAAA,CAAM7I,MAAM,CAAC;EAEnDyI,IAAI,CAAC5K,OAAO,CAACb,IAAI,IAAI;IACnB;IACA,MAAMoJ,QAAQ,GAAGrK,QAAQ,CAACI,IAAI,CAAEC,IAAS,IACvCC,MAAM,CAACD,IAAI,CAACJ,MAAM,IAAI,EAAE,CAAC,CAACM,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACW,IAAI,CAACK,EAAE,IAAI,EAAE,CAAC,CAACf,IAAI,CAAC,CAClE,CAAC;IAED,IAAI8J,QAAQ,EAAE;MACZ/C,OAAO,CAACC,GAAG,CAAC,oBAAoBtG,IAAI,CAACK,EAAE,+BAA+B+I,QAAQ,CAAC3J,UAAU,eAAe2J,QAAQ,CAACxJ,QAAQ,EAAE,CAAC;MAC5H,MAAMH,UAAU,GAAG2J,QAAQ,CAAC3J,UAAU,IAAI,IAAI;MAC9C,MAAMG,QAAQ,GAAGwJ,QAAQ,CAACxJ,QAAQ,IAAI,IAAI;MAE1C,IAAI,CAACsJ,iBAAiB,CAACG,GAAG,CAAC5J,UAAU,CAAC,EAAE;QACtCyJ,iBAAiB,CAACI,GAAG,CAAC7J,UAAU,EAAE,IAAI0J,GAAG,CAAC,CAAC,CAAC;MAC9C;MAEA,MAAMI,SAAS,GAAGL,iBAAiB,CAACM,GAAG,CAAC/J,UAAU,CAAC;MACnD,IAAI,CAAC8J,SAAS,CAACF,GAAG,CAACzJ,QAAQ,CAAC,EAAE;QAC5B2J,SAAS,CAACD,GAAG,CAAC1J,QAAQ,EAAE,EAAE,CAAC;MAC7B;MAEA2J,SAAS,CAACC,GAAG,CAAC5J,QAAQ,CAAC,CAACc,IAAI,CAACV,IAAI,CAAC;IACpC,CAAC,MAAM;MACLqG,OAAO,CAACW,IAAI,CAAC,cAAchH,IAAI,CAACK,EAAE,yBAAyB,CAAC;MAC5D;MACA,IAAI,CAAC6I,iBAAiB,CAACG,GAAG,CAAC,IAAI,CAAC,EAAE;QAChCH,iBAAiB,CAACI,GAAG,CAAC,IAAI,EAAE,IAAIH,GAAG,CAAC,CAAC,CAAC;MACxC;MAEA,MAAMI,SAAS,GAAGL,iBAAiB,CAACM,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAI,CAACD,SAAS,CAACF,GAAG,CAAC,IAAI,CAAC,EAAE;QACxBE,SAAS,CAACD,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;MACzB;MAEAC,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC9I,IAAI,CAACV,IAAI,CAAC;IAChC;EACF,CAAC,CAAC;EAEFqG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4C,iBAAiB,CAACO,IAAI,CAAC;EACtDP,iBAAiB,CAACrI,OAAO,CAAC,CAAC0I,SAAS,EAAE9J,UAAU,KAAK;IACnD4G,OAAO,CAACC,GAAG,CAAC,YAAY7G,UAAU,QAAQ8J,SAAS,CAACE,IAAI,UAAU,CAAC;EACrE,CAAC,CAAC;;EAEF;EACAP,iBAAiB,CAACrI,OAAO,CAAC,CAAC0I,SAAS,EAAE9J,UAAU,KAAK;IACnD;IACA,MAAMC,YAAY,GAAGF,eAAe,CAACT,QAAQ,EAAEU,UAAU,EAAE,YAAYA,UAAU,EAAE,CAAC;IACpF,MAAMgJ,WAAW,GAAG;MAAEhI,cAAc,EAAE,aAAahB,UAAU,MAAMC,YAAY;IAAG,CAAC;;IAEnF;IACA,MAAMgK,WAAW,GAAGH,SAAS,CAACI,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACrL,KAAK;IACnD,IAAImL,WAAW,IAAIA,WAAW,CAAC1G,MAAM,GAAG,CAAC,EAAE;MACzCjD,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACqK,WAAW,EAAEiB,WAAW,CAAC,CAAC,CAAC,CAACtJ,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,UAAU,CAAC,CAAC;IAC1F;IAEAiL,SAAS,CAAC1I,OAAO,CAAC,CAAC8H,WAAW,EAAE/I,QAAQ,KAAK;MAC3C;MACA,IAAI6H,gBAAgB,IAAI7H,QAAQ,KAAK,IAAI,EAAE;QACzC,MAAMC,UAAU,GAAGF,aAAa,CAACZ,QAAQ,EAAEU,UAAU,EAAEG,QAAQ,EAAE,UAAUA,QAAQ,EAAE,CAAC;QACtF,MAAM8I,SAAS,GAAG;UAAEjI,cAAc,EAAE,WAAWb,QAAQ,MAAMC,UAAU;QAAG,CAAC;QAE3E,IAAI8I,WAAW,CAAC3F,MAAM,GAAG,CAAC,EAAE;UAC1BjD,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACsK,SAAS,EAAEC,WAAW,CAAC,CAAC,CAAC,CAACvI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,QAAQ,CAAC,CAAC;QACtF;MACF;;MAEA;MACAqK,WAAW,CAAC9H,OAAO,CAACb,IAAI,IAAI;QAAA,IAAA8L,WAAA;QAC1B,MAAMvM,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAyL,WAAA,GAAE9L,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAwL,WAAA,GAAI,EAAE,CAAC;QACjE,MAAMhC,WAAW,GAAGrC,gBAAgB,IAAI7H,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;QAC3E,MAAMiJ,WAAW,GAAG;UAAEpI,cAAc,EAAE,GAAGqJ,WAAW,GAAG9J,IAAI,CAACK,EAAE,MAAMd,QAAQ;QAAG,CAAC;QAChFQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACyK,WAAW,EAAE7I,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;QAE7E;QACAwK,wBAAwB,CAAC/I,IAAI,EAAEC,IAAI,EAAE6I,WAAW,EAAE9J,QAAQ,EAAEwI,UAAU,CAAC;MACzE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACAlB,OAAO,CAACC,GAAG,CAAC,0BAA0BvG,IAAI,CAACiD,MAAM,EAAE,CAAC;EACpD,IAAIjD,IAAI,CAACiD,MAAM,GAAG,CAAC,EAAE;IACnBqD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEvG,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1C;;EAEA;EACAA,IAAI,CAACc,OAAO,CAACsC,GAAG,IAAI;IAClB,IAAIA,GAAG,EAAE;MACP,MAAM4G,SAAS,GAAGvI,MAAM,CAACmI,MAAM,CAACxG,GAAG,CAAC;MACpC;MACAb,SAAS,CAACwF,MAAM,CAACiC,SAAS,CAACC,GAAG,CAACC,GAAG,IAAIA,GAAG,KAAKzL,SAAS,GAAG,EAAE,GAAGyL,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC5GA,WAAW,CAACrJ,OAAO,CAACsJ,KAAK,IAAI7H,SAAS,CAAC8H,UAAU,CAACD,KAAK,CAAC,CAAC;EACzD9H,cAAc,CAACC,SAAS,CAAC;EACzB4C,iBAAiB,CAAC5C,SAAS,CAAC;EAC5BjE,kBAAkB,CAACiE,SAAS,CAAC;AAC/B,CAAC;;AAED;AACA,SAASwG,wBAAwBA,CAAC/I,IAAI,EAAEC,IAAI,EAAEQ,OAAO,EAAEzB,QAAQ,EAAEwI,UAAU,EAAE;EAAA,IAAAwE,cAAA;EAC3E,MAAMpL,aAAa,GAAG,CAAC,CAAC;;EAExB;EACA,CAACX,IAAI,CAACY,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAEC,IAAI,IAAK;IAAA,IAAAkL,KAAA,EAAAC,mBAAA;IACnC,MAAMhL,SAAS,IAAA+K,KAAA,IAAAC,mBAAA,GAAGnL,IAAI,CAACI,YAAY,YAAA+K,mBAAA,GAAInL,IAAI,CAACK,SAAS,YAAA6K,KAAA,GAAI,EAAE;IAC3D,IAAI,CAACrL,aAAa,CAACM,SAAS,CAAC,EAAEN,aAAa,CAACM,SAAS,CAAC,GAAG,EAAE;IAC5DN,aAAa,CAACM,SAAS,CAAC,CAACP,IAAI,CAACI,IAAI,CAAC;EACrC,CAAC,CAAC;;EAEF;EACA,CAAAiL,cAAA,GAAA/L,IAAI,CAACoB,OAAO,aAAZ2K,cAAA,CAAclL,OAAO,CAAEQ,MAAM,IAAK;IAAA,IAAA6K,KAAA,EAAAC,qBAAA;IAChC,MAAMlL,SAAS,IAAAiL,KAAA,IAAAC,qBAAA,GAAG9K,MAAM,CAACH,YAAY,YAAAiL,qBAAA,GAAI9K,MAAM,CAACF,SAAS,YAAA+K,KAAA,GAAI,EAAE;IAC/D,MAAME,gBAAgB,GAAGnL,SAAS,GAAGS,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;;IAEnF;IACA,MAAM0K,YAAY,GAAG7L,OAAO,CAACC,cAAc,CAAC2B,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,MAAM;IAChFrC,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;MAAEC,cAAc,EAAE,GAAG4L,YAAY,UAAUD,gBAAgB;IAAE,IACzE/K,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;;IAED;IACA,MAAML,KAAK,GAAGD,aAAa,CAACM,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAMW,WAAW,GAAGhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/C,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,OAAOK,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEFN,WAAW,CAACf,OAAO,CAAEC,IAAI,IAAK;MAC5B,IAAIqB,OAAO,GAAG,IAAI;MAClB,IAAI,OAAOrB,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC9DD,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI,OAAOb,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC9CE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,OAAOnB,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC9CE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D;;MAEA;MACA,MAAM2K,UAAU,GAAGD,YAAY,GAAG,IAAI;MACtCtM,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;QAAEC,cAAc,EAAE,GAAG6L,UAAU,QAAQnK,OAAO,eAAeA,OAAO;MAAG,IACnFrB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}