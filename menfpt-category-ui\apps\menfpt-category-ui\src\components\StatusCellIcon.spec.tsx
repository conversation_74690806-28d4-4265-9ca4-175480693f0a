import React from 'react';
import { render, screen } from '@testing-library/react';
import StatusCellIcon from './StatusCellIcon';
import { Provider } from 'react-redux';
import { createStore } from 'redux';

// Minimal mock reducer and store
const mockReducer = (state = {}) => state;
const mockStore = createStore(mockReducer);

// Mock dependencies
jest.mock('../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));
jest.mock('./renderRowCloseStatus', () => ({
  __esModule: true,
  default: ({ periodNbr, weekNbr, className }: any) => (
    <span data-testid="mock-render-row-close-status">{`${periodNbr}-${weekNbr}`}</span>
  ),
  getPeriodStatusInfo: jest.fn(),
  STATUS_BG_CLASS: {
    closed: 'bg-red',
    open: 'bg-green',
  },
}));

import { useSelectorWrap } from '../rtk/rtk-utilities';
import { getPeriodStatusInfo, STATUS_BG_CLASS } from './renderRowCloseStatus';

describe('StatusCellIcon', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockRowData = [{ fiscalPeriodNbr: 1, fiscalWeekNbr: 2 }];

  it('renders with correct test id and class for closed status', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: 'mockStatuses' });
    (getPeriodStatusInfo as jest.Mock).mockReturnValue({ statusKey: 'closed' });
    render(
      <Provider store={mockStore}>
        <StatusCellIcon rowData={mockRowData} />
      </Provider>
    );
    const div = screen.getByTestId('period-status-cell-icon-closed');
    expect(div).toBeInTheDocument();
    expect(div).toHaveClass('bg-red');
    expect(screen.getByTestId('mock-render-row-close-status')).toHaveTextContent('1-2');
  });

  it('renders with correct test id and class for open status', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: 'mockStatuses' });
    (getPeriodStatusInfo as jest.Mock).mockReturnValue({ statusKey: 'open' });
    render(
      <Provider store={mockStore}>
        <StatusCellIcon rowData={mockRowData} />
      </Provider>
    );
    const div = screen.getByTestId('period-status-cell-icon-open');
    expect(div).toBeInTheDocument();
    expect(div).toHaveClass('bg-green');
  });

  it('renders with default test id and no bg class if statusKey is null', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: 'mockStatuses' });
    (getPeriodStatusInfo as jest.Mock).mockReturnValue({ statusKey: null });
    render(
      <Provider store={mockStore}>
        <StatusCellIcon rowData={mockRowData} />
      </Provider>
    );
    const div = screen.getByTestId('period-status-cell-icon');
    expect(div).toBeInTheDocument();
    expect(div.className).not.toMatch(/bg-red|bg-green/);
  });

  it('renders with default test id if rowData is missing', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: 'mockStatuses' });
    (getPeriodStatusInfo as jest.Mock).mockReturnValue({ statusKey: null });
    render(
      <Provider store={mockStore}>
        <StatusCellIcon rowData={null} />
      </Provider>
    );
    const div = screen.getByTestId('period-status-cell-icon');
    expect(div).toBeInTheDocument();
  });

  it('renders nothing if useSelectorWrap returns undefined', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue(undefined);
    (getPeriodStatusInfo as jest.Mock).mockReturnValue({ statusKey: null });
    render(
      <Provider store={mockStore}>
        <StatusCellIcon rowData={mockRowData} />
      </Provider>
    );
    expect(screen.getByTestId('period-status-cell-icon')).toBeInTheDocument();
  });

});