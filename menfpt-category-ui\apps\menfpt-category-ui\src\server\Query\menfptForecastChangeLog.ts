export const menfptGetForecastChangeLogQuery = `
   query GetForecastChangeLog($forecastChangeLogReqest: ForecastChangeLogReq) {
                        getForecastChangeLog(forecastChangeLogReqest: $forecastChangeLogReqest) {
                            updatedTimestamp
                            updatedBy
                            editedColumns
                            updatedMetrics {
                                fiscalWeekNbrs
                                keyAttributeName
                                keyAttributeValue
                                reason
                                comment
                                adjustedFields {
                                    fieldName
                                    oldValue
                                    newValue
                                }
                                bannerId
                            }
                        }
                    }
                `;