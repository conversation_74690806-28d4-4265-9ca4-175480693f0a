import React from 'react';
import RenderRowCloseStatus, {
  getPeriodStatusInfo,
  STATUS_BG_CLASS,
} from './renderRowCloseStatus';
import { useSelectorWrap } from '../rtk/rtk-utilities';

// Helper to get periodStatuses from the correct reducer
function usePeriodStatuses() {
  const activeQuarterTab = useSelectorWrap('activeQuarterTab_rn')?.data;
 /*  const periodStatusesReducerName =
    activeQuarterTab === 'lastQtr'
      ? 'periodStatuses_rn'
      : 'periodStatuses_rn'; */
  return useSelectorWrap("periodStatuses_rn")?.data;
}

// Helper to get the status key for a row
function getStatusKey(periodStatuses: any, rowData: any) {
  if (!rowData || !rowData[0]) return null;
  const { statusKey } = getPeriodStatusInfo(
    periodStatuses,
    String(rowData[0]?.fiscalPeriodNbr),
    String(rowData[0]?.fiscalWeekNbr)
  );
  return statusKey;
}

// Helper to get the container class for a row
function getContainerClass(rowData: any, statusKey: string | null) {
  let containerClass = 'w-[18px] flex items-center mr-1 opacity-80';
  if (
    rowData &&
    rowData[0]?.fiscalWeekNbr &&
    String(rowData[0]?.fiscalWeekNbr) !== '0' &&
    statusKey &&
    STATUS_BG_CLASS[statusKey]
  ) {
    containerClass += ` ${STATUS_BG_CLASS[statusKey]}`;
  }
  return containerClass;
}

// Helper to render the status icon
function StatusIcon({ rowData }: { rowData: any }) {
  return (
    <span className="flex items-center justify-center">
      <RenderRowCloseStatus
        periodNbr={String(rowData[0]?.fiscalPeriodNbr)}
        weekNbr={String(rowData[0]?.fiscalWeekNbr)}
        className="w-[16px] h-[16px]"
      />
    </span>
  );
}

// Main component
const StatusCellIcon = ({ rowData }: { rowData: any }) => {
  const periodStatuses = usePeriodStatuses();
  const statusKey = getStatusKey(periodStatuses, rowData);
  const containerClass = getContainerClass(rowData, statusKey);

  return (
    <div className={containerClass} data-testid={statusKey ? `period-status-cell-icon-${statusKey}` : 'period-status-cell-icon'}>
      {/* Always render the icon, but only week rows get background */}
      {rowData && <StatusIcon rowData={rowData} />}
    </div>
  );
};

export default StatusCellIcon;
