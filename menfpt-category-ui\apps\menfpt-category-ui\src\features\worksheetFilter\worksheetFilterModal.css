/* Minimum heights for modal content sections */
.worksheet-filter-modal-content {
  min-height: 300px;
}

.worksheet-filter-modal-header {
  min-height: 40px;
}

.worksheet-filter-modal-footer {
  min-height: 20px;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
}


.worksheet-filter-modal-container {
  display: flex;
  flex-direction: column;
  min-height: 40vh;
  height: auto;
  max-height: 80vh;
  width: 80vw;
  max-width: 1262px;
  background: #fff;
  box-sizing: border-box;
  overflow: hidden;
}



.worksheet-filter-modal-content {
  padding: 1rem;
  box-sizing: border-box;
  max-height: calc(80vh - 120px);
}


.worksheet-filter-modal-footer {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 50;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04); 
}


@media (max-width: 600px) {
  .worksheet-filter-modal-container {
    min-height: 60vh;
    height: 70vh;
    max-height: 80vh;
    width: 98vw;
    max-width: 98vw;
    /* padding: 0.5rem; */
    border-radius: 8px;
  }
  .worksheet-filter-modal-content {
    padding: 0.5rem;
    max-height: calc(80vh - 100px);
  }
}


@media (min-width: 1600px) {
  .worksheet-filter-modal-container {
    max-width: 1200px;
    width: 60vw;
    height: 80vh;
    max-height: 80vh;
  }
}
