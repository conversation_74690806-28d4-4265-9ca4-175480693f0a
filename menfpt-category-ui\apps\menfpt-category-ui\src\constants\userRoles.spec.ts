import { USER_ROLES, UserRole } from './userRoles';

describe('USER_ROLES', () => {
  it('should have PHARMACY role defined', () => {
    expect(USER_ROLES.PHARMACY).toBeDefined();
    expect(USER_ROLES.PHARMACY).toBe('pharmacyUser');
  });

  it('should have correct role value', () => {
    expect(USER_ROLES.PHARMACY).toBe('pharmacyUser');
  });

  it('should be readonly (as const)', () => {
    // TypeScript should prevent modification, but let's test the structure
    expect(typeof USER_ROLES.PHARMACY).toBe('string');
  });

  it('should export UserRole type', () => {
    // This test ensures the type is exported
    const testRole: UserRole = USER_ROLES.PHARMACY;
    expect(testRole).toBe('pharmacyUser');
  });

  it('should have consistent role naming', () => {
    // Ensure the role name follows the expected pattern
    expect(USER_ROLES.PHARMACY).toMatch(/^[a-zA-Z]+$/);
  });
}); 