import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SelectWeek } from './release-week-select';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useDispatch } from 'react-redux';

// Mock Select and Option for testing
jest.mock('@albertsons/uds/molecule/Select', () => {
  const React = require('react');
  const Select = ({ children, value, onChange, ...props }: any) => {
    // Ensure children is always an array and render at least one option
    let optionChildren = React.Children.toArray(children);
    if (!optionChildren || optionChildren.length === 0) {
      // Render a default option for empty case
      optionChildren = [
        React.createElement('option', {
          'data-testid': 'mock-option',
          value: '',
          key: 'default',
        }, 'No options')
      ];
    } else {
      optionChildren = optionChildren.map((child, idx) =>
        React.cloneElement(child, {
          'data-testid': 'mock-option',
          value: child.props.item?.value ?? '',
          key: idx,
        })
      );
    }
    return (
      <select
        data-testid="mock-select"
        value={value?.value || ''}
        onChange={e => {
          const selectedValue = e.target.value;
          const selected = optionChildren.find(
            child => child.props.value === selectedValue
          )?.props.item;
          onChange && onChange(selected);
        }}
        {...props}
      >
        {optionChildren}
      </select>
    );
  };
  const Option = ({ item, ...props }) => <option {...props}>{item.name}</option>;
  return {
    __esModule: true,
    default: Select,
    Option,
  };
});

jest.mock('../../rtk/rtk-utilities', () => {
  let callCount = 0;
  let values: any[] = [];
  const useSelectorWrap = jest.fn((key) => {
    // Always return { data: ... } for all selectors
    if (values.length > callCount) {
      const val = values[callCount];
      callCount++;
      return { data: val };
    }
    callCount++;
    return { data: undefined };
  });
  (useSelectorWrap as any).setValues = (vals: any[]) => {
    values = vals;
    callCount = 0;
  };
  return { useSelectorWrap };
});
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

const mockDispatch = jest.fn();
(useDispatch as jest.Mock).mockReturnValue(mockDispatch);

const mockCalendarWeek = [
  {
    fiscalWeekEndDate: '08/22/2025',
    fiscalWeekStartDate: '08/16/2025',
    fiscalWeekNumber: 34,
  },
  {
    fiscalWeekEndDate: '08/15/2025',
    fiscalWeekStartDate: '08/09/2025',
    fiscalWeekNumber: 33,
  },
];

const mockAppliedFilters = {
  timeframe: { fiscalQuarterNumber: 3 },
};
const mockDisplayDate = { fiscalQuarterNumber: 3 };

function setupSelectorWrap(
  calendarWeek = mockCalendarWeek,
  appliedFilters = mockAppliedFilters,
  displayDate = mockDisplayDate
) {
  // Always provide 3 values for the selectors
  (useSelectorWrap as any).setValues([
    calendarWeek,
    appliedFilters,
    displayDate,
  ]);
}

describe('SelectWeek', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders Select with options for Performance Summary tab', () => {
    setupSelectorWrap();
    render(<SelectWeek selectedTab="Performance Summary" />);
    expect(screen.getByTestId('mock-select')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-option').length).toBeGreaterThan(0);
  });

  it('does not render Select for other tabs', () => {
    setupSelectorWrap(); // Ensure all selectors are mocked
    render(<SelectWeek selectedTab="Other Tab" />);
    expect(screen.queryByTestId('mock-select')).not.toBeInTheDocument();
  });

  it('calls weekChange and dispatches on option change', () => {
    setupSelectorWrap();
    const weekChange = jest.fn();
    render(<SelectWeek selectedTab="Performance Summary" weekChange={weekChange} />);
    const select = screen.getByTestId('mock-select');
    fireEvent.change(select, { target: { value: mockCalendarWeek[0].fiscalWeekEndDate } });
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('handles empty calendarWeek', () => {
    setupSelectorWrap([], mockAppliedFilters, mockDisplayDate);
    render(<SelectWeek selectedTab="Performance Summary" />);
    expect(screen.getByTestId('mock-select')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-option').length).toBe(1);
  });

  it('handles missing appliedFilters and displayDate', () => {
    setupSelectorWrap(mockCalendarWeek, undefined, undefined);
    render(<SelectWeek selectedTab="Performance Summary" />);
    expect(screen.getByTestId('mock-select')).toBeInTheDocument();
  });

  it('handles non-current quarter', () => {
    setupSelectorWrap(mockCalendarWeek, { timeframe: { fiscalQuarterNumber: 2 } }, { fiscalQuarterNumber: 3 });
    render(<SelectWeek selectedTab="Performance Summary" />);
    expect(screen.getByTestId('mock-select')).toBeInTheDocument();
  });
});