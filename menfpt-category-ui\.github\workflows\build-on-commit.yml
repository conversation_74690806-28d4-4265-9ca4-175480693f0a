name: build-on-commit
on:
  workflow_dispatch:
  push:
    branches:
      - 'dev*'
      - 'fb*'
      - 'hf*'
      - 'master'
      - 'rel*'
    paths-ignore:
      - '.github/**'
      - 'apim/**'
jobs:
  Extract_branch: 
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/extractbranch.yml@v2
  call-ci-workflow:
    needs: [Extract_branch]
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-node-ci-nonprod.yml@v4
    with:
      ACTIONSFILE: "Actionsfile/dev"
      TAG: "${{ needs.Extract_branch.outputs.branch_name }}-boc-${{ github.run_number }}"
      VERACODE_APPNAME: "menfpt-category-ui"
      branch_name: "${{ needs.Extract_branch.outputs.branch_name }}"
      npm_build_command: "npm run build:app"
      # npm_install_command: 'npm cache clean --force && npm install'
      npm_sonar_command: "npm ci && npm run build && npm run test"
      node_version: 18

    secrets:
      SONAR_CONTINUEONERROR_NONPROD: 'true'
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      TL_USERNAME: ${{ secrets.TL_USERNAME }}
      TL_PASSWORD: ${{ secrets.TL_PASSWORD }}
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      VERACODEID: ${{ secrets.VERACODEID }}
      VERACODEKEY: ${{ secrets.VERACODEKEY }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
  #Build_Tag:
   # runs-on: [ AKS ]
    #needs: [Extract_branch]
    #steps:
    #  - name: display deployment tag
     #   run: echo "Build Tag - ${{ needs.Extract_branch.outputs.branch_name }}-boc-${{ github.run_number }}" >> $GITHUB_STEP_SUMMARY

