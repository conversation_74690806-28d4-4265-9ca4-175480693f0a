import { createGenericSlice } from '../../rtk/rtk-slice';
import { EnvVariablesState } from '../../interfaces/envVariables';

/* Store environment variables data */
export const envVariablesSlice = createGenericSlice({
  name: 'envVariables_rn',
  initialState: { status: 'loading', data: undefined } as EnvVariablesState,
})({
  setEnvVariables(state, { payload }) {
    state.data = payload;
    state.status = 'finished';
  },
  setEnvVariablesError(state) {
    state.status = 'error';
    state.data = undefined;
  },
  clearEnvVariables(state) {
    state.status = 'loading';
    state.data = undefined;
  },
});

export const { 
  setEnvVariables, 
  setEnvVariablesError, 
  clearEnvVariables 
} = envVariablesSlice.actions; 