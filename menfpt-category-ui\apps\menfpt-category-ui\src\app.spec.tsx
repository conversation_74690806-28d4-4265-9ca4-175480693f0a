import { render, screen } from '@testing-library/react';
import { Browser<PERSON>outer as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { app_store } from '../src/rtk/store';
import App from './app';

jest.mock('powerbi-client', () => ({
  PowerBIEmbed: jest.fn(() => <div data-testid="powerbi-embed">PowerBI Embed</div>),
}));

// Mock the useSelectorWrap hook
jest.mock('./rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

const { useSelectorWrap } = require('./rtk/rtk-utilities');

describe('App', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders app without crashing', () => {
    useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
    
    render(
      <Router>
        <Provider store={app_store}>
          <App />
        </Provider>
      </Router>
    );
  });

  it('should include access denied route', () => {
    useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
    
    render(
      <Router>
        <Provider store={app_store}>
          <App />
        </Provider>
      </Router>
    );

    // The access denied route should be available in the routing structure
    expect(true).toBe(true); // Placeholder assertion
  });

  it('should have proper route protection structure', () => {
    useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
    
    render(
      <Router>
        <Provider store={app_store}>
          <App />
        </Provider>
      </Router>
    );

    // Test that the app renders without errors
    expect(true).toBe(true);
  });

  it('should handle different user roles correctly', () => {
    useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
    
    render(
      <Router>
        <Provider store={app_store}>
          <App />
        </Provider>
      </Router>
    );

    // Test that the app renders without errors for pharmacy users
    expect(true).toBe(true);
  });
});
