import React from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { useDeskDisplay } from '../../worksheetFilterRouteUtils';
import ModalDivisionSection from './ModalDivisionSection';
import ModalDepartmentDeskSection from './ModalDepartmentDeskSection';
import { useModalContentState } from './useModalContentState';
import ModalReadOnlyList from './ModalCategorySection';
import SmRoleUsersList from '../roles/smRoleUsersList';
import TimeframeSelector from '../timeframe/timeframeSelector';

export const modalContentContainerStyles =
  'bg-white flex flex-row justify-start rounded-lg font-nunito font-semibold text-base leading-4 tracking-normal';

interface ModalContentProps {
  shouldDisplayTimeFrame: boolean;
  selectedTimeframe?: DropdownType;
  selectedPeriods?: DropdownType[];
  selectedWeeks?: { periodNum: number; weekNum: number }[];
  handleTimeframeChange: (timeframe: DropdownType) => void;
  handlePeriodChange: (periods: DropdownType[]) => void;
  handleWeeksChange: (weeks: { periodNum: number; weekNum: number }[]) => void;
  divisions: DropdownType[];
  selectedDivision: DropdownType[];
  desks: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  selectedDesk?: DropdownType;
  categories: DropdownType[];
  isDisplayDeptRoleCascade: boolean;
  activeTabInFilter: any;
  handleDivisionChange: (divisions: DropdownType[]) => void;
  handleDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  handleDeskChange: (desk: DropdownType) => void;
}
const ModalContent: React.FC<ModalContentProps> = ({
  shouldDisplayTimeFrame,
  selectedTimeframe,
  selectedPeriods,
  selectedWeeks,
  handleTimeframeChange,
  handlePeriodChange,
  handleWeeksChange,
  divisions,
  selectedDivision,
  desks,
  selectedDepartment,
  selectedDesk,
  categories,
  isDisplayDeptRoleCascade,
  activeTabInFilter,
  handleDivisionChange,
  handleDepartmentChange,
  handleDeskChange
}) => {
  const isDisplayDesk = useDeskDisplay();

  const {
    filteredDepartments,
    filteredDesks,
    isSearchFocused,
    setIsSearchFocused,
    isSuggestionsVisible,
    setIsSuggestionsVisible,
    searchContainerRef,
    getSearchHandler,
    getSearchValue,
  } = useModalContentState({
   
    desks,
    isDisplayDeptRoleCascade,
    activeTabInFilter,
  });

  const timeframeSection = shouldDisplayTimeFrame ? (
  <div className="min-h-0 flex flex-col flex-1 min-w-0 grow overflow-hidden border-r border-[#c8daeb] pr-4 pl-6">
      <TimeframeSelector
        selectedTimeframe={selectedTimeframe}
        onTimeframeChange={handleTimeframeChange}
        selectedPeriods={selectedPeriods}
        selectedWeeks={selectedWeeks}
        handlePeriodChange={handlePeriodChange}
        handleWeeksChange={handleWeeksChange}
      />
    </div>
  ) : null;

  const divisionSection = (
    <ModalDivisionSection
      divisions={divisions}
      selectedDivisions={selectedDivision}
      onDivisionChange={handleDivisionChange}
    />
  );

  const departmentDeskSection = (
    <ModalDepartmentDeskSection
      filteredDepartments={filteredDepartments}
      filteredDesks={filteredDesks}
      selectedDepartment={selectedDepartment}
      selectedDesk={selectedDesk}
      selectedDivision={selectedDivision}
      handleDepartmentChange={handleDepartmentChange}
      handleDeskChange={handleDeskChange}
    />
  );

  const categorySection = (
    <ModalReadOnlyList
      items={categories}
      isDisplayDesk={isDisplayDesk}
      title={`Following ${categories.length} Categories are included:`}
    />
  );

  return (
    <div className={`${modalContentContainerStyles} min-h-0 flex flex-col`}>
      <div className="flex-1 flex flex-col overflow-hidden min-h-0">
        <div className="flex flex-row w-full border-b border-[#c8daeb] max-h-[50vh]">
          {timeframeSection}
          {divisionSection}
          {departmentDeskSection}
          <SmRoleUsersList/>
        </div>
        <div className="w-full max-h-[15vh] my-2 flex grow flex-col overflow-y-auto transition-all duration-200">
          {categorySection}
        </div>
      </div>
      {/* ModalFooter should be rendered outside this component, or you can add a footer slot here if needed */}
    </div>
  );
};

export default ModalContent;
