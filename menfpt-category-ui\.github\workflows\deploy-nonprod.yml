name: deploy-nonprod
on:
  workflow_dispatch:
    inputs: 
      dev:
        type: boolean
        description: 'deploy dev'    
        required: false
        default: false
      qa1:
        type: boolean
        description: 'deploy qa1'    
        required: false
        default: false
      qa2:
        type: boolean
        description: deploy qa2  
        required: false
        default: false
      qa6:
        type: boolean
        description: deploy qa6  
        required: false
        default: false        
      perf1:
        type: boolean
        description: deploy perf1 
        required: false
        default: false
      stage:
        type: boolean
        description: deploy stage    
        required: false
        default: false        
      Tag:
        type: string
        description: 'Tag'    
        required: true
      rollbackrestart:
        description: 'rollbackrestart'
        required: false
        default: false
        type: boolean  

jobs:
  Extract_branch: 
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/extractbranch.yml@v2
  
  dev-deploy:
    needs: [Extract_branch]
    if: success() && github.event.inputs.dev == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: dev
      ACTIONSFILE: "Actionsfile/dev"
      TAG: "${{ github.event.inputs.Tag }}"
#       rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-dev:
    needs: dev-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/dev" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-dev:
    needs: [Deploy-Status-check-dev]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/dev"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}

  qa1-deploy:
    needs: [Extract_branch]
    if: success() && github.event.inputs.qa1 == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: qa1
      ACTIONSFILE: "Actionsfile/qa1"
      TAG: "${{ github.event.inputs.Tag }}"
#       rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-qa1:
    needs: qa1-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa1" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-qa1:
    needs: [Deploy-Status-check-qa1]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa1"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}

  perf1-deploy:
    needs: [Extract_branch]
    if: success() && github.event.inputs.perf1 == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: perf1
      ACTIONSFILE: "Actionsfile/perf1"
      TAG: "${{ github.event.inputs.Tag }}"
#       rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-perf1:
    needs: perf1-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/perf1" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-perf1:
    needs: [Deploy-Status-check-perf1]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/perf1"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}

  qa2-deploy:
    needs: [Extract_branch]
    if: success() && github.event.inputs.qa2 == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: qa2
      ACTIONSFILE: "Actionsfile/qa2"
      TAG: "${{ github.event.inputs.Tag }}"
#       rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-qa2:
    needs: qa2-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa2" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-qa2:
    needs: [Deploy-Status-check-qa2]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa2"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}

  qa6-deploy:
      needs: [Extract_branch]
      if: success() && github.event.inputs.qa6 == 'true'
      uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
      with:
        ENVIRONMENT: qa6
        ACTIONSFILE: "Actionsfile/qa6"
        TAG: "${{ github.event.inputs.Tag }}"
    #      rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
      secrets:
        REGISTRY_USER: ${{ secrets.ACR_USER }}
        REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
        KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
        PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-qa6:
    needs: qa6-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa6" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-qa6:
    needs: [Deploy-Status-check-qa6]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa6"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}
      
  stage-deploy:
    needs: [Extract_branch]
    if: success() && github.event.inputs.stage == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: stage
      ACTIONSFILE: "Actionsfile/stage"
      TAG: "${{ github.event.inputs.Tag }}"
#       rollbackrestart: ${{ github.event.inputs.rollbackrestart }}
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }} 
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }} 
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }} 
  Deploy-Status-check-stage:
    needs: stage-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/stage" 
    secrets:  
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}  
  rollback-restart-stage:
    needs: [Deploy-Status-check-stage]
    if: success() && github.event.inputs.rollbackrestart == 'true'
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/rollbackrestart.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/stage"
      TAG: "${{ github.event.inputs.Tag }}"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}

