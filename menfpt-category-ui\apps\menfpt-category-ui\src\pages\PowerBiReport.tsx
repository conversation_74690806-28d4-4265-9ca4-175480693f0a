import Spinner from '@albertsons/uds/molecule/Spinner';
import { PowerBIEmbed } from 'powerbi-client-react';
import { useEffect, useState } from 'react';
import { Embed, models } from 'powerbi-client';
// import { getEmbedReportToken, getReportIdFromURL } from './report.utils';

const EMBED_ENDPOINT_URI = '/memsp-ui-shell/api/powerbi-embed-token';

export const getEmbedReportToken = async (forceRefresh?: boolean) => {
  try {
    const response = await fetch(
      `${EMBED_ENDPOINT_URI}/?forceRefresh=${forceRefresh ? 'true' : 'false'}`,
      {
        credentials: 'same-origin',
      }
    );

    if (!response.ok) {
     console.log('unable to fetch powerBI access token URL');
    //   throw new Error('unable to fetch powerBI access token URL');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.log('Embed Report Token API failed', error);
    return null;
  }
};

export const getReportIdFromURL = (urlString: string): string | null => {
  try {
    const url = new URL(urlString);
    const params = new URLSearchParams(url.search);
    const reportId = params.get('reportId');
    return reportId;
  } catch (error) {
    console.error(`Invalid URL: ${urlString}`);
    return null;
  }
};


interface IPowerBiReportProps {
  reportId: string;
}

export const PowerBiReport = ({ reportId }: IPowerBiReportProps) => {
    const [report, setReport] = useState<Embed>();
    // const reportId = '787b8818-f2b3-42a6-8251-8a61ba058f20';
    const [isLoading, setIsLoading] = useState(false);
    const [token, setToken] = useState<string>('');
    const accessToken = window.localStorage.getItem('bi_access_token') || 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayIsImtpZCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ehdfXmtiyd7vCKXBoe3XMYQjUW_9HNcU0ZNMOqIgR_B_7q1jDfzAN-HrNAb2-hPUsQrK_LZxaEsNesMtPsBve3nx0WwU6ZjFLR1yZs3ZIZfAGrdEqBjtHJ03S2UMJ7z86i6EEYtG2szN3JWpwWfRjfp3sWVbwZRTo_rAzTjzYdyBdyF5INgqsxSx0XiGTZJIqkPbI6aptDb899HR_J5LkJl-5yjrA33gGUF2mSuzeOEP8ZWKM3kZA4lZy4znW3ZMR2RQIkelnG9kTURfQpfjnzpoQ2ca2EZKic2A_0C_omyMkXOxRQ6DwyXVZnXpytqykK0bkoEcghkCbDRT49Et3w';
    // '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    const my_url = `https://app.powerbi.com/reportEmbed?reportId=${reportId}&config=${token}`;

    // const [reportConfig, setReportConfig] = useState<models.IReportEmbedConfiguration>({
    //     type: 'report',
    //     id: reportId,
    //     embedUrl: my_url,
    //     accessToken: accessToken,
    //     tokenType: models.TokenType.Embed,
    //     settings: {
    //         panes: {
    //             filters: {
    //                 visible: true,
    //             },
    //         },
    //         background: models.BackgroundType.Default,
    //         }
    //     });

    // async function getReportDetails(
    //     forceRefresh?: boolean
    //   ): Promise<string | null> {
    //     setIsLoading(true);
    //     const reportData = await getEmbedReportToken(forceRefresh);
    
    //     if (reportData) {
    //       setIsLoading(false);
    //       setToken(reportData?.token);
    //       return reportData?.token;
    //     }
    //     setIsLoading(false);
    //     return null;
    //   }
    
      useEffect(() => {
        const fetchEmbedToken = async () => {
            const tokenResponse = await getEmbedReportToken(false);
            if (tokenResponse) {
                setToken(tokenResponse.token);
            }
            else 
            {
                setToken(accessToken);
            }
        };

        fetchEmbedToken();
      }, []);

    const reportFilters: models.ReportLevelFilters[] = [
        {
            // TODO: I'm working on getting the sample "Shoes For crews" power bi report schema. 
            $schema: "http://powerbi.com/product/schema#basic",
            target: {
                table: "",
                column: ""
            },
            filterType: models.FilterType.Basic,
            operator: "In",
            values: ["", ""]
        }
    ];

    const emConf: models.IReportEmbedConfiguration = {
        type: 'report',
        id: reportId,
        embedUrl: my_url,
        accessToken: token,
        filters: reportFilters,
        settings: {
            panes: {
                filters: {
                    visible: true,
                },
            },
            background: models.BackgroundType.Default,
        }
    }

    return (
        <div className="flex w-full h-full">
            { 
            // token &&
                <PowerBIEmbed
                key={reportId}
                cssClassName='w-full h-full'
                embedConfig={{ ...emConf }}
                eventHandlers={new Map([
                    ['dataSelected', function(event, report) {
                        console.log('Report data selected');
                    }],
                    ['loaded', function(event, report) {
                        console.log('Report loaded');
                    }],
                    ['rendered', function() {
                        console.log('Report rendered');
                    }],
                    ['error', async function(event, report) {
                        console.log('Report error');
                    }],
                ])}
                getEmbeddedComponent={(embeddedReport) => setReport(embeddedReport)}
            />}
        </div>
    );
};

export default PowerBiReport;