.categories-scrollbar::-webkit-scrollbar {
    width: thin;
}

.categories-scrollbar::-webkit-scrollbar-track {
    background: #f0f0f0;
}

.categories-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #888 #F1F4F9; /* thumb color, track color */
}

.dateHeader
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    letter-spacing: 0px;
    vertical-align: middle;
}

.AdjustmentHeader
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0px;
}

.ResultFilter
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;
}

.ResultFilterResult
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;

}

.ChangeButton
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0%;
}


/* .BodyDataMRegular
{
    font-family: Nunito Sans;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;

}


.BodyDataSRegular
{
    font-family: Nunito Sans;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 0px;
    vertical-align: middle;
} */
