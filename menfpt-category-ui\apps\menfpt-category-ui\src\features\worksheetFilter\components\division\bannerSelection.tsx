import React from 'react';
import { useDispatch } from 'react-redux';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { formatName } from '../roles/rolesUtils';
import ClearSelection from '../roles/clearSelection';
import { worksheetFilterConfig } from '../../worksheetFilterConfig';
import { extractCurrentRoute } from '../../worksheetFilterRouteUtils';
//import { setSelectedBanners, setBannerDataForSelectedDivision } from './division.slice';
import { SelectableList, SelectableItem } from '../shared';

interface BannerSelectionProps {
  selectedBanners?: any[];
  availableBanners?: any[];
  selectedDivision: number;
  onBannerChange: (item: SelectableItem, selectedDivision: number, checked?: boolean) => void;
}

const BannerSelection = ({ 
  selectedBanners = [],
  availableBanners = [],
  selectedDivision,
  onBannerChange,

}: BannerSelectionProps) => {
  const dispatch = useDispatch();
  
  // Get banner data from Redux store

  const currentRoute = extractCurrentRoute(location.pathname);
  const isMultipleSelectionsAllowed = worksheetFilterConfig.isAllowMultipleDivisonsSelection.includes(currentRoute);

  // Convert banner data to SelectableItem format
  const bannerItems: SelectableItem[] = availableBanners.map(banner => ({
    id: banner.num,
    label: banner.name,
    data: banner
  }));

  const selectedItems: SelectableItem[] = selectedBanners.map(banner => ({
    id: banner.num,
    label: banner.name,
    data: banner
  }));

  return (
    <div className="min-h-0 max-h-[500px] flex flex-col flex-1 grow min-w-0 border-[#c8daeb] pl-6">
      <div className="bg-white flex flex-col flex-1 min-h-0">
        <SelectableList
          items={bannerItems}
          selectedItems={selectedItems}
          isMultipleSelectionAllowed={isMultipleSelectionsAllowed}
          onItemChange={(item, checked) => onBannerChange(item, selectedDivision, checked)}
          showSelectAll={false}
          emptyMessage={'Select a division to view banners'}
          listClassName="pr-[5px] self-stretch relative flex-1 flex flex-col justify-start items-start overflow-y-auto min-h-0 nfpt-scrollbar"
          itemClassName="self-stretch min-h-10 p-2.5 bg-white rounded-lg inline-flex justify-start items-center gap-6 overflow-hidden"
        />
      </div>
    </div>
  );
};

export default BannerSelection;
