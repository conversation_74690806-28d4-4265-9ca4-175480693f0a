name: PRScan checking
on:
  push:
    branches: [ 'master', 'develop' ]
  pull_request:
    branches: [ 'master', 'develop' ]
jobs:
  call-workflow:
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/PRScan_node.yml@v4
    with:
      NodeVersion: 18
      command: 'npm ci && npm run build && npm run test'
      GatePr: true
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}