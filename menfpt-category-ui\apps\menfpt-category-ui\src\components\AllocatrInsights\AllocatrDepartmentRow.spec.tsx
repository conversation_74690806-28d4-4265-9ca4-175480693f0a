import React from 'react';
import { render } from '@testing-library/react';
import AllocatrDepartmentRow from './AllocatrDepartmentRow';

describe('AllocatrDepartmentRow', () => {
  const mockQuarter = {
    id: 'Q1',
    line1Projection: 0,
    lastYear: 0,
    actualOrForecast: 0,
    idPercentage: 0,
    vsLY: { value: 0, percentage: 0 },
    vsProjection: { value: 0, percentage: 0 },
    quarterNumber: 1,
  };

  function renderRow(department, value, className) {
    return render(
      <AllocatrDepartmentRow department={{ ...department, quarter: mockQuarter }} value={value} className={className} />
    );
  }

  it('renders department name cell', () => {
    const { getByText } = renderRow({ id: 'HR', name: 'HR' }, 100);
    expect(getByText((content) => content.trim() === 'HR - HR')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    const { container } = renderRow({ id: 'IT', name: 'IT' }, 200, 'custom');
    expect(container.querySelector('.custom')).toBeInTheDocument();
  });

  it('renders Total department id special case', () => {
    const { getByText } = renderRow({ id: 'Total', name: 'All Departments' }, 999);
    expect(getByText((content) => content.trim() === 'Total')).toBeInTheDocument();
  });

  it('handles empty department name', () => {
    const { getByText } = renderRow({ id: 'X', name: '' }, 0);
    expect(getByText((content) => content.trim() === 'X -')).toBeInTheDocument();
  });

  it('handles missing department id', () => {
    const { getByText } = renderRow({ id: '', name: 'NoId' }, -1);
    expect(getByText((content) => content.trim() === '- NoId')).toBeInTheDocument();
  });

  it('handles missing value (renders placeholders)', () => {
    const { container } = renderRow({ id: 'Finance', name: 'Finance' });
    // Should render at least one placeholder cell
    const placeholders = container.querySelectorAll('b');
    expect(Array.from(placeholders).some((el) => el.textContent === '--')).toBe(true);
  });

  it('handles undefined value (renders placeholders)', () => {
    const { container } = renderRow({ id: 'A', name: 'Alpha' }, undefined);
    const placeholders = container.querySelectorAll('b');
    expect(Array.from(placeholders).some((el) => el.textContent === '--')).toBe(true);
  });
});
