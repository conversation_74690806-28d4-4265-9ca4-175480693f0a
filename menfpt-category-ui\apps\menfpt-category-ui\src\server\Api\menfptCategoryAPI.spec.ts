import { GQLParams } from '../../interfaces/menfpt-category';
import entitlementApi from './menfptCategoryAPI';
import { getEnvParamVal } from '../../util/envVarsManager';
import { getDefaultHeaders } from '../../util/authProvider';
import { GetJobRunsFromDatabricksQuery } from '../Query/EPBCSSyncMonitorQuery';

jest.mock('@reduxjs/toolkit/query/react', () => {
  const originalModule = jest.requireActual('@reduxjs/toolkit/query/react');
  return {
    ...originalModule,
    createApi: jest.fn((options) => {
      const builder = {
        query: jest.fn().mockImplementation((config) => config),
        mutation: jest.fn().mockImplementation((config) => config),
      };
      return {
        ...options,
        endpoints: options.endpoints(builder),
      };
    }),
  };
});

jest.mock('../../util/envVarsManager');
jest.mock('../../util/authProvider');

describe('entitlementApi', () => {
  const { endpoints } = entitlementApi;

  beforeEach(() => {
    (getEnvParamVal as jest.Mock).mockReturnValue('http://localhost:3000');
    (getDefaultHeaders as jest.Mock).mockReturnValue({
      Authorization: 'Bearer test-token',
      'Content-Type': 'application/json',
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getmenfptCategoryTitle endpoint', () => {
    const endpoint = (endpoints as any).getmenfptCategoryTitle;
    const params: GQLParams = { query: 'test', variables: {} };

    it('should be defined', () => {
      expect(endpoint).toBeDefined();
    });

    it('should create a POST request with the correct body', () => {
      const queryConfig = endpoint.query(params);
      expect(queryConfig.method).toBe('POST');
      expect(queryConfig.body).toBe(JSON.stringify(params));
    });

    it('should have the correct headers', () => {
      const queryConfig = endpoint.query(params);
      expect(queryConfig.headers).toEqual({ "content-type": "application/json"});
    });

    it('should transform the response correctly', () => {
      const mockResponse = { data: { title: 'Test Title' } };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toEqual({ title: 'Test Title' });
    });
  });

  describe('getDisplayDate endpoint', () => {
    const endpoint = (endpoints as any).getDisplayDate;
    const payload = { fiscalYear: 2024 };

    it('should be defined', () => {
      expect(endpoint).toBeDefined();
    });

    it('should create a POST request with the correct body and headers', () => {
      const queryConfig = endpoint.query(payload);
      expect(queryConfig.method).toBe('POST');
      expect(queryConfig.headers).toEqual(getDefaultHeaders());
      const body = JSON.parse(queryConfig.body);
      expect(body.variables.displayDateReq).toEqual(payload);
    });

    it('should transform the response to return calendarDetails', () => {
      const mockResponse = { data: { getDisplayDate: { calendarDetails: { year: 2024 } } } };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toEqual({ year: 2024 });
    });

    it('should return an empty array if calendarDetails are missing', () => {
      const mockResponse = { data: { getDisplayDate: {} } };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toEqual([]);
    });
  });

  describe('getWorkSheetFilter endpoint', () => {
    const endpoint = (endpoints as any).getWorkSheetFilter;
    const params: GQLParams = { query: 'worksheet filter', variables: {} };

    it('should be defined', () => {
      expect(endpoint).toBeDefined();
    });

    it('should create a POST request with correct body and headers', () => {
      const queryConfig = endpoint.query(params);
      expect(queryConfig.method).toBe('POST');
      expect(queryConfig.headers).toEqual(getDefaultHeaders());
      expect(queryConfig.body).toBe(JSON.stringify(params));
    });

    it('should transform the response correctly', () => {
      const mockResponse = { data: { filter: 'test' } };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toEqual({ filter: 'test' });
    });
  });
  
  describe('getJobRunsFromDatabricks endpoint', () => {
    const endpoint = (endpoints as any).getJobRunsFromDatabricks;
    const params = { input: { jobName: 'test-job', limit: 10 } };

    it('should be defined', () => {
      expect(endpoint).toBeDefined();
    });

    it('should create a POST request with the correct query and variables', () => {
      const queryConfig = endpoint.query(params);
      expect(queryConfig.method).toBe('POST');
      expect(queryConfig.headers).toEqual(getDefaultHeaders());
      const body = JSON.parse(queryConfig.body);
      expect(body.query).toBe(GetJobRunsFromDatabricksQuery);
      expect(body.variables).toEqual(params);
    });

    it('should transform the response correctly', () => {
      const mockResponse = { data: { jobRuns: [{ id: 1 }] } };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toEqual({ jobRuns: [{ id: 1 }] });
    });

    it('should handle an empty response', () => {
      const mockResponse = { data: null };
      const transformed = endpoint.transformResponse(mockResponse);
      expect(transformed).toBeNull();
    });
  });
});