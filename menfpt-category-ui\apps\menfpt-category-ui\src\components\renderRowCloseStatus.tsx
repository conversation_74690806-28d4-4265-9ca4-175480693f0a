import React from 'react';
import { certifiedIcon, closeIcon } from '../features/periodClose/periodIcons';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import Tooltip from '@albertsons/uds/molecule/Tooltip';
import Button from '@albertsons/uds/molecule/Button';

interface RenderRowCloseStatusProps {
  periodNbr?: string;
  weekNbr?: string;
  className?: string;
  /**
   * If true, only return the status key (string), not the icon.
   */
  onlyStatusKey?: boolean;
  /**
   * Label to display in the tooltip (e.g., 'Period' or 'Week').
   */
  labelType?: 'Period' | 'Week';
}

// Helper to get the status key and icon for a given period/week
function getPeriodStatusInfo(
  periodStatuses: any,
  periodNbr?: string,
  weekNbr?: string
): { statusKey: string | null; icon: JSX.Element | null } {
  if (!Object.keys(periodStatuses).length) return { statusKey: null, icon: null };
  const statusPriority = [
    { key: 'certified', icon: certifiedIcon, showIcon: true },
    { key: 'locked', icon: closeIcon, showIcon: true },
    { key: 'notLocked', icon: null, showIcon: false },
    { key: 'notCertifiedButLocked', icon: null, showIcon: false },
  ];
  if (weekNbr === '0') weekNbr = undefined;

  let foundKey: string | null = null;
  let foundIcon: JSX.Element | null = null;

  if (periodNbr && weekNbr) {
    for (const status of statusPriority) {
      const hasStatus = periodStatuses[status.key]?.[periodNbr]?.includes(weekNbr);
      if (hasStatus) {
        foundKey = status.key;
        foundIcon = status.showIcon && status.icon ? status.icon() : null;
        break;
      }
    }
  } else if (periodNbr && !weekNbr) {
    for (const status of statusPriority) {
      const hasStatus = !!periodStatuses[status.key]?.[periodNbr];
      if (hasStatus) {
        foundKey = status.key;
        foundIcon = status.showIcon && status.icon ? status.icon() : null;
        break;
      }
    }
  } else if (!periodNbr && weekNbr) {
    for (const status of statusPriority) {
      const found = Object.entries(periodStatuses[status.key] || {}).some(
        ([, weeks]) =>
          weekNbr && Array.isArray(weeks) && (weeks as string[]).includes(weekNbr)
      );
      if (found) {
        foundKey = status.key;
        foundIcon = status.showIcon && status.icon ? status.icon() : null;
        break;
      }
    }
  }

  return { statusKey: foundKey, icon: foundIcon };
}

// Maps status keys to background color classes
const STATUS_BG_CLASS: Record<string, string> = {
  certified: 'bg-green-100',
  locked: 'bg-gray-200',
  notLocked: 'bg-[#7ab9f4]',
  notCertifiedButLocked: 'bg-[#B9C0D4]',
};

// Maps status keys to tooltip text for the title attribute
const STATUS_TITLE: Record<string, string> = {
  certified: 'This Period is currently locked for editing. Syncing with EPBCS and in the True-Up Actuals Process will continue independently',
  locked: 'This Period has been Audited and closed, based on Actuals Reconciliation with GL postings.',
  // Add more mappings as needed
};

const RenderRowCloseStatus: React.FC<RenderRowCloseStatusProps> = ({
  periodNbr,
  weekNbr,
  className,
  onlyStatusKey,
  labelType = 'Period',
}) => {

  const { data: periodStatuses } = useSelectorWrap("periodStatuses_rn");

  const { statusKey, icon } = getPeriodStatusInfo(periodStatuses, periodNbr, weekNbr);
  if (onlyStatusKey) return statusKey ? <span data-testid={`period-status-key-${statusKey}`}>{statusKey}</span> : null;
  // Return both icon and statusKey as class if needed
  return (
    <span
      className={className}
      data-testid={statusKey ? `period-status-icon-${statusKey}` : undefined}
    >
     <span className='tool-tip-initilizer'></span>
       <Tooltip zIndex={10} anchor='right' variant='dark' className={'uds-tooltip-right'} label={statusKey ? STATUS_TITLE[statusKey] : undefined}>

      {icon}
    </Tooltip>
    </span>
  );
};

export { getPeriodStatusInfo, STATUS_BG_CLASS };
export default RenderRowCloseStatus;

