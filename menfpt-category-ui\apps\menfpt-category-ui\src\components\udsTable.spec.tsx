import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import thunk from 'redux-thunk';
import UdsTable from './udsTable';

// --- Mock UDS components ---
jest.mock('@albertsons/uds/molecule/Table', () => (props: any) => (
  <table data-testid="uds-table">
    {props.items.map((item: any, idx: number) => (
      <tr key={idx}>
        <td>{item}</td>
      </tr>
    ))}
  </table>
));

jest.mock('@albertsons/uds/molecule/Menu', () => (props: any) => (
  <div data-testid="uds-menu">{props.trigger}</div>
));

jest.mock('@albertsons/uds/molecule/Spinner', () => () => (
  <div data-testid="uds-spinner">Spinner</div>
));

jest.mock('lucide-react', () => ({
  ChevronDown: () => <span>ChevronDown</span>,
  ChevronsDownUp: () => <span>ChevronsDownUp</span>,
  ChevronsUpDown: () => <span>ChevronsUpDown</span>,
  ChevronUp: () => <span>ChevronUp</span>,
  EllipsisVertical: () => <span>EllipsisVertical</span>,
}));

jest.mock('../../../../libs/utils/src', () => ({
  formatToPST: (date: string) => `PST: ${date}`,
}));

// --- ✅ Mock RTK Query hook + middleware ---
const mockQueryData = {
  data: {
    GetEnvVariables: {
      variables: {
        LINE_TOOLTIP_TEXT: JSON.stringify([
          'Sales to Public',
          'Realized Gross Profit',
          'Supplies Packaging',
          'Retail Allowance',
          'Realized Gross Profit Before Other Revenue - Sales',
        ]),
      },
    },
  },
  error: null,
  isLoading: false,
  refetch: jest.fn(),
};

jest.mock('../../src/server/Api/menfptCategoryAPI', () => ({
  useGetEnvVariablesQuery: jest.fn(() => mockQueryData),
  middleware: () => (store: any) => (next: any) => (action: any) => next(action),
}));


describe('UdsTable', () => {
  let store: any;

  const baseForecast = {
    aggregatedLevel: 'Quarter to Date',
    subRow: 'Projection',
    mainRow: 'Main',
    line1PublicToSalesNbr: 1000,
    line1PublicToSalesPct: 10,
    line5BookGrossProfitNbr: 2000,
    line5BookGrossProfitPct: 20,
    line5MarkDownsNbr: 3000,
    line5MarkDownsPct: 30,
    line5ShrinkNbr: 4000,
    line5ShrinkPct: 40,
    line5RealGrossProfitNbr: 5000,
    line5RealGrossProfitPct: 50,
    line6SuppliesPackagingNbr: 6000,
    line7RetailsAllowancesNbr: 7000,
    line7RetailsAllowancesPct: 70,
    line7RetailsSellingAllowancesNbr: 7100,
    line7RetailsSellingAllowancesPct: 71,
    line7RetailsNonSellingAllowancesNbr: 7200,
    line7RetailsNonSellingAllowancesPct: 72,
    line8RealGrossProfitNbr: 8000,
    line8RealGrossProfitPct: 80,
    fiscalYearNbr: 2024,
    fiscalQuarterNbr: 2,
    fiscalPeriodNbr: 5,
    fiscalWeekNbr: 10,
    fiscalWeekEnding: '2024-05-01',
    comment: 'Test',
    forecastType: 'TypeA',
    sourceTs: '2024-05-01T00:00:00Z',
    updatedTs: '2024-05-02T00:00:00Z',
  };

  const dataMap = new Map([
    ['Row1', [baseForecast]],
    [
      'Row2',
      [
        {
          ...baseForecast,
          subRow: 'DS Forecast',
          updatedTs: '2024-05-03T00:00:00Z',
          sourceTs: '2024-05-04T00:00:00Z',
        },
      ],
    ],
    [
      'Row3',
      [
        {
          ...baseForecast,
          subRow: 'Actual to Date',
          updatedTs: '2024-05-05T00:00:00Z',
        },
      ],
    ],
  ]);

  beforeEach(() => {
    store = configureStore({
      reducer: () => ({
        envVariables: {
          data: null,
          error: null,
          isLoading: false,
        },
      }),
      middleware: [thunk],
    });
  });

  it('renders table with rows', () => {
    render(
      <Provider store={store}>
        <UdsTable
          data={dataMap}
          currentWeek={10}
          resetWeek={jest.fn()}
          resetPermission={false}
          onEditForecast={jest.fn()}
        />
      </Provider>
    );
    expect(screen.getByTestId('uds-table')).toBeInTheDocument();
    expect(screen.getAllByRole('row')).toHaveLength(3);
  });

  it('renders footer with formatted dates', () => {
    render(
      <Provider store={store}>
        <UdsTable
          data={dataMap}
          currentWeek={10}
          resetWeek={jest.fn()}
          resetPermission={false}
          onEditForecast={jest.fn()}
        />
      </Provider>
    );

    expect(
      screen.getByText((content) =>
        content.includes('Projection PST: 2024-05-01T00:00:00Z')
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText((content) => content.includes('DS Forecast PST: 2024-05-03T00:00:00Z'))
    ).toBeInTheDocument();
    expect(
      screen.getByText((content) =>
        content.replace(/\s+/g, ' ').includes('Actuals PST: 2024-05-05T00:00:00Z')
      )
    ).toBeInTheDocument();
  });

  it('handles empty data gracefully', () => {
    render(
      <Provider store={store}>
        <UdsTable
          data={new Map()}
          currentWeek={1}
          resetWeek={jest.fn()}
          resetPermission={false}
          onEditForecast={jest.fn()}
        />
      </Provider>
    );
    const rows = screen.getByTestId('uds-table').querySelectorAll('tr');
    expect(rows).toHaveLength(0);
  });

  it('handles edge cases: zero values', () => {
    const edgeData = new Map([
      ['Row1', [{ ...baseForecast, line1PublicToSalesNbr: 0, line1PublicToSalesPct: 0 }]],
    ]);
    render(
      <Provider store={store}>
        <UdsTable
          data={edgeData}
          currentWeek={10}
          resetWeek={jest.fn()}
          resetPermission={false}
          onEditForecast={jest.fn()}
        />
      </Provider>
    );
    expect(screen.getByTestId('uds-table')).toBeInTheDocument();
  });
});

