Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
[1m[33mCould not find a version for "events" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "clsx" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "redux-thunk" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "react-router" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
[1m[33mCould not find a version for "powerbi-client" in the root "package.json" when collecting shared packages for the Module Federation setup. The package will not be shared.[39m[22m
<i> [1m[32m[webpack-dev-server] Project is running at:[39m[22m
<i> [1m[32m[webpack-dev-server] Loopback: [36mhttp://localhost:3010/[39m, [36mhttp://[::1]:3010/[39m[39m[22m
<i> [1m[32m[webpack-dev-server] 404s will fallback to '[36m/index.html[39m'[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mWeb Development Server is listening at http://localhost:3010/[22m


[createGlobPatternsForDependencies] WARNING: There was no ProjectGraph available to read from, returning an empty array of glob patterns

Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB [1m[32m[rendered][39m[22m
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB [1m[32m[rendered][39m[22m
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared) [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes [1m[32m[rendered][39m[22m
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[32m[rendered][39m[22m [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/pages/dashboard-tabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/EPBCSSyncMonitor.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/udsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/quarterTabs.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/bottomdropdowm.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/editForecast.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/tooltipStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/historyTimeline.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/forecastTimestampsFooter.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/ForecastEdit/weekSelection.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/components/AllocatrInsights/AllocatrInsightsTable.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/timeframe/timeframeSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/worksheetFilter/components/deptDesk/departmentDeskSelectorStyles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[5].use[3]!./src/features/searchIcon.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (b0ab7d71d3ca5887)
[ [32mready[39m ] http://localhost:3010
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.css[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /.well-known/appspecific/com.chrome.devtools.json[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /polyfills.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /styles.js[39m[22m
<i> [1m[32m[webpack-dev-middleware] wait until bundle finished: /main.js[39m[22m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 954 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (24647fda4f6c5dc4)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 953 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[32m[rendered][39m[22m [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (09fb53b2b6d42ea4)
[32mNo errors found.[39m
Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mmain.js[39m[22m 1 auxiliary asset
Entrypoint [1mpolyfills[39m[22m [1m[33m[big][39m[22m 1.67 MiB (1.87 MiB) = [1m[32mpolyfills.js[39m[22m 1 auxiliary asset
Entrypoint [1mstyles[39m[22m [1m[33m[big][39m[22m 1.3 MiB (1.44 MiB) = [1m[32mstyles.css[39m[22m 52.3 KiB [1m[32mstyles.js[39m[22m 1.25 MiB 1 auxiliary asset
Entrypoint [1mmenfpt-category-ui[39m[22m [1m[33m[big][39m[22m 1.26 MiB (1.45 MiB) = [1m[32mremoteEntry.js[39m[22m 1 auxiliary asset
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mlibs_utils_src_index_ts.js[39m[22m 2.66 KiB
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 29.4 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: menfpt-category-ui) [1m[32mremoteEntry.js[39m[22m (menfpt-category-ui) 1.2 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 28.9 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_extends_js-node_modules_babel_runtime_helpers_esm_obje-b8c332.js[39m[22m 606 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_babel_runtime_helpers_esm_toPropertyKey_js.js[39m[22m 1000 bytes
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mnode_modules_file-saver_dist_FileSaver_min_js.js[39m[22m 2.68 KiB
chunk (runtime: polyfills) [1m[32mpolyfills.js[39m[22m (polyfills) 1.53 MiB (javascript) 42 bytes (consume-shared) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.css[39m[22m, [1m[32msrc_app_tsx-webpack_sharing_consume_default_lucide-react_lucide-react.js[39m[22m 953 KiB (javascript) 630 bytes (consume-shared) 52.3 KiB (css/mini-extract) [1m[33msplit chunk (cache group: default)[39m[22m
chunk (runtime: main) [1m[32msrc_bootstrap_tsx.js[39m[22m 1.48 KiB (javascript) 42 bytes (consume-shared)
chunk (runtime: menfpt-category-ui) [1m[32msrc_remote-entry_ts.js[39m[22m 32 bytes
chunk (runtime: styles) [1m[32mstyles.css[39m[22m, [1m[32mstyles.js[39m[22m (styles) 1.2 MiB (javascript) 42 bytes (consume-shared) 52.3 KiB (css/mini-extract) 756 bytes (share-init) 19.6 KiB (runtime) [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: main, menfpt-category-ui) [1m[32mvendors-node_modules_albertsons_uds_molecule_Alert_index_js-node_modules_albertsons_uds_molec-1f4601.js[39m[22m (id hint: vendors) 2 MiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_albertsons_uds_node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 400 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_core-js_modules_es_array_includes_js-node_modules_core-js_modules_es_str-71f7a6.js[39m[22m (id hint: vendors) 63.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_date-fns-tz_index_js.js[39m[22m (id hint: vendors) 157 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_exceljs_dist_exceljs_min_js.js[39m[22m (id hint: vendors) 925 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_formik_dist_formik_esm_js.js[39m[22m (id hint: vendors) 162 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_lucide-react_dist_esm_lucide-react_js.js[39m[22m (id hint: vendors) 1.16 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_mui_material_index_js.js[39m[22m (id hint: vendors) 1.82 MiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_popperjs_core_lib_popper_js-node_modules_babel_runtime_helpers_esm_asser-17cc6e.js[39m[22m (id hint: vendors) 70.4 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client-react_dist_powerbi-client-react_js.js[39m[22m (id hint: vendors) 54.7 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_powerbi-client_dist_powerbi_js.js[39m[22m (id hint: vendors) 572 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_prop-types_index_js.js[39m[22m (id hint: vendors) 36.1 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-redux_es_index_js.js[39m[22m (id hint: vendors) 72.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-router-dom_dist_index_js.js[39m[22m (id hint: vendors) 308 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_react-tooltip_dist_react-tooltip_min_mjs.js[39m[22m (id hint: vendors) 92.6 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reduxjs_toolkit_dist_redux-toolkit_esm_js.js[39m[22m (id hint: vendors) 121 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_reselect_es_defaultMemoize_js-node_modules_babel_runtime_helpers_esm_def-54ebe6.js[39m[22m (id hint: vendors) 21.9 KiB [1m[33msplit chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_stagewise_toolbar-react_dist_index_js.js[39m[22m (id hint: vendors) 625 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_uuid_dist_esm-browser_index_js.js[39m[22m (id hint: vendors) 19.4 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) [1m[32mvendors-node_modules_yup_index_esm_js.js[39m[22m (id hint: vendors) 86.1 KiB [1m[33mreused as split chunk (cache group: defaultVendors)[39m[22m
chunk (runtime: main, menfpt-category-ui, polyfills, styles) 42 bytes [1m[33mreused as split chunk (cache group: default)[39m[22m

[1mLOG from ../../node_modules/sass-loader/dist/cjs.js sass-loader ../../node_modules/@nrwl/webpack/src/utils/webpack/plugins/raw-css-loader.js!../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[2]!../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].oneOf[9].use[3]!./src/styles.scss[39m[22m
<w> [1m[33mDeprecation The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mMore info: https://sass-lang.com/d/legacy-js-api[39m[22m
<w> [1m[33m[39m[22m
<w> [1m[33mnull[39m[22m

webpack compiled [1m[32msuccessfully[39m[22m (2f28388f8f3d49d6)
[32mNo errors found.[39m
<i> [1m[32m[webpack-dev-server] Gracefully shutting down. To force exit, press ^C again. Please wait...[39m[22m
