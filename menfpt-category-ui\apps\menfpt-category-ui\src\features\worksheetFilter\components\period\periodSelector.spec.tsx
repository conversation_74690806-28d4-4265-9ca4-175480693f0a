import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import PeriodSelector from './periodSelector';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import * as rtkUtils from '../../../../rtk/rtk-utilities';
import * as api from '../../../../server/Api/menfptCategoryAPI';

const mockPeriods: DropdownType[] = [
  { num: 1, name: 'Period 1' },
  { num: 2, name: 'Period 2' }
];
const mockQuarter: DropdownType = { num: 1, name: 'Q1' };
const mockWeeks = [
  { periodNum: 1, weekNum: 1 },
  { periodNum: 1, weekNum: 2 },
  { periodNum: 2, weekNum: 3 }
];

describe('PeriodSelector', () => {
  it('renders message if no periods available', () => {
    render(<PeriodSelector selectedPeriods={[]} selectedQuarter={undefined} onPeriodChange={jest.fn()} />);
    expect(screen.getByText('Please select Quarter to view')).toBeInTheDocument();
  });

  it('renders periods when available', () => {
    render(<PeriodSelector selectedPeriods={[]} selectedQuarter={mockQuarter} onPeriodChange={jest.fn()} />);
    expect(screen.getByText('Period 1')).toBeInTheDocument();
    expect(screen.getByText('Period 2')).toBeInTheDocument();
  });

  it('calls onPeriodChange when a period is selected', async () => {
    const onPeriodChange = jest.fn();
    render(<PeriodSelector selectedPeriods={[]} selectedQuarter={mockQuarter} onPeriodChange={onPeriodChange} />);
    await userEvent.click(screen.getByText('Period 1'));
    expect(onPeriodChange).toHaveBeenCalled();
  });

  it('calls onWeeksChange when a week is selected', async () => {
    const onPeriodChange = jest.fn();
    const onWeeksChange = jest.fn();
    render(<PeriodSelector selectedPeriods={mockPeriods} selectedQuarter={mockQuarter} onPeriodChange={onPeriodChange} onWeeksChange={onWeeksChange} />);
    // Expand the period to show weeks
    const expandBtn = screen.getAllByLabelText('Expand')[0];
    await userEvent.click(expandBtn);
    // Now interact with week checkbox
    const weekLabel = screen.getByText('Week 1').closest('label');
    expect(weekLabel).not.toBeNull();
    const weekCheckbox = weekLabel ? weekLabel.querySelector('input') : null;
    expect(weekCheckbox).not.toBeNull();
    await userEvent.click(weekCheckbox!);
    expect(onWeeksChange).toHaveBeenCalled();
  });

  it('shows indeterminate state for periods with some weeks selected', async () => {
    render(<PeriodSelector selectedPeriods={mockPeriods} selectedQuarter={mockQuarter} onPeriodChange={jest.fn()} selectedWeeks={[{ periodNum: 1, weekNum: 1 }]} />);
    // Expand the period to show weeks
    const expandBtn = screen.getAllByLabelText('Expand')[0];
    await userEvent.click(expandBtn);
    // Should show Week 1 as checked, Week 2 as unchecked
    const week1Label = screen.getByText('Week 1').closest('label');
    const week2Label = screen.getByText('Week 2').closest('label');
    expect(week1Label).not.toBeNull();
    expect(week2Label).not.toBeNull();
    const week1Checkbox = week1Label ? week1Label.querySelector('input') : null;
    const week2Checkbox = week2Label ? week2Label.querySelector('input') : null;
    expect(week1Checkbox).not.toBeNull();
    expect(week2Checkbox).not.toBeNull();
    expect(week1Checkbox!).toBeChecked();
    expect(week2Checkbox!).not.toBeChecked();
  });
})
// Define DropdownType for test usage
type DropdownType = { num: number; name: string };

jest.mock('../../../../rtk/rtk-utilities');
jest.mock('../../../../server/Api/menfptCategoryAPI');



beforeEach(() => {
 
  (rtkUtils.useSelectorWrap as jest.Mock).mockReturnValue({
    data: [
      { fiscalQuarterNumber: 202401, fiscalPeriodNumber: 1, fiscalWeekNumber: 101 },
      { fiscalQuarterNumber: 202401, fiscalPeriodNumber: 2, fiscalWeekNumber: 201 }
    ]
  });
  (api.useGetDisplayDateQuery as jest.Mock).mockReturnValue({
    data: {
      data: {
        getDisplayDate: {
          weeksForQtr: [
            { fiscalWeekNumber: 101 },
            { fiscalWeekNumber: 201 }
          ]
        }
      }
    },
    isLoading: false
  });
});

// Mock store setup
const mockStore = configureStore([]);
const store = mockStore({});


describe('PeriodSelector', () => {
 
  it('calls onWeeksChange when a week is toggled', async () => {
    function Wrapper() {
      const [selected, setSelected] = React.useState<DropdownType[]>([]);
      const [weeks, setWeeks] = React.useState<{ periodNum: number; weekNum: number }[]>([]);
      return (
        <PeriodSelector
          selectedPeriods={selected}
          selectedQuarter={{ num: 202401, name: 'Q1' }}
          onPeriodChange={setSelected}
          onWeeksChange={setWeeks}
          selectedWeeks={weeks}
        />
      );
    }
    render(
      <Provider store={store}>
        <Wrapper />
      </Provider>
    );
    // Select Period 1 to show weeks
    await userEvent.click(screen.getByText('Period 1'));
    // Wait for Week 1 to appear
    const week1 = await screen.findByLabelText('Week 1');
    // Toggle Week 1 (should uncheck)
    await userEvent.click(week1);
    expect(week1).not.toBeChecked();
    // Toggle Week 1 again (should check)
    await userEvent.click(week1);
    expect(week1).toBeChecked();
  });


  it('calls onPeriodChange and onWeeksChange when select all is clicked', async () => {
    function Wrapper() {
      const [selected, setSelected] = React.useState<DropdownType[]>([]);
      const [weeks, setWeeks] = React.useState<{ periodNum: number; weekNum: number }[]>([]);
      return (
        <PeriodSelector
          selectedPeriods={selected}
          selectedQuarter={{ num: 202401, name: 'Q1' }}
          onPeriodChange={setSelected}
          onWeeksChange={setWeeks}
          selectedWeeks={weeks}
        />
      );
    }
    render(
      <Provider store={store}>
        <Wrapper />
      </Provider>
    );
    const selectAll = screen.getByLabelText(/select/i);
    await userEvent.click(selectAll);
    expect(screen.getByLabelText('Period 1')).toBeChecked();
    expect(screen.getByLabelText('Period 2')).toBeChecked();
    await userEvent.click(selectAll);
    expect(screen.getByLabelText('Period 1')).not.toBeChecked();
    expect(screen.getByLabelText('Period 2')).not.toBeChecked();
  });

});
// Basic render test
describe('PeriodSelector', () => {
  it('renders without crashing', () => {
    render(
      <Provider store={store}>
        <PeriodSelector
          selectedPeriods={[]}
          selectedQuarter={undefined}
          onPeriodChange={jest.fn()}
        />
      </Provider>
    );
  });



it('renders periods and alert', () => {
  render(
    <Provider store={store}>
      <PeriodSelector
        selectedPeriods={[]}
        selectedQuarter={{ num: 202401, name: 'Q1' }}
        onPeriodChange={jest.fn()}
      />
    </Provider>
  );
  expect(screen.getByText('Period 1')).toBeInTheDocument();
  expect(screen.getByText('Period 2')).toBeInTheDocument();
  expect(screen.getByText('Please select period(s)')).toBeInTheDocument();
});

it('calls onPeriodChange when a period is clicked', async () => {
  const onPeriodChange = jest.fn();
  render(
    <Provider store={store}>
      <PeriodSelector
        selectedPeriods={[]}
        selectedQuarter={{ num: 202401, name: 'Q1' }}
        onPeriodChange={onPeriodChange}
      />
    </Provider>
  );
  // Simulate clicking the period label
  await userEvent.click(screen.getByText('Period 1'));
  expect(onPeriodChange).toHaveBeenCalled();
});

it('selects all periods when select all checkbox is clicked', async () => {
  const onPeriodChange = jest.fn();
  render(
    <Provider store={store}>
      <PeriodSelector
        selectedPeriods={[]} // Start with none selected
        selectedQuarter={{ num: 202401, name: 'Q1' }}
        onPeriodChange={onPeriodChange}
      />
    </Provider>
  );
  // Find and click the select all checkbox
  const selectAll = screen.getByLabelText(/select/i);
  await userEvent.click(selectAll);

  // Should call onPeriodChange with all periods
  expect(onPeriodChange).toHaveBeenCalledWith([
    { num: 1, name: 'Period 1' },
    { num: 2, name: 'Period 2' }
  ]);
});


it('shows alert when no quarter is selected', () => {
  render(
    <Provider store={store}>
      <PeriodSelector
        selectedPeriods={[]}
        selectedQuarter={undefined}
        onPeriodChange={jest.fn()}
      />
    </Provider>
  );
  expect(screen.getByText('Please select Quarter to view')).toBeInTheDocument();
});



});