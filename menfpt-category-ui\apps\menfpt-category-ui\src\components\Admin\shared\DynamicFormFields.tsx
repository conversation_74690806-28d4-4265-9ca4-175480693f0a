import React from 'react';
import { SelectField, InputField } from './FormFields';
import { FormConfig } from './formConfig';
import DatePicker from '@albertsons/uds/molecule/DatePicker';
import InfoTooltip from '../../InfoTooltip';
import { Info } from 'lucide-react';

interface DynamicFormFieldsProps {
  formConfig: FormConfig;
  values: any;
  errors: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setFieldValue: (field: string, value: any) => void;
  validateField: (field: string) => void;
}

export const DynamicFormFields: React.FC<DynamicFormFieldsProps> = ({
  formConfig,
  values,
  errors,
  handleChange,
  setFieldValue,
  validateField
}) => {
  const createChangeHandler = (fieldName: string) => (item: any) => {
    const config = formConfig[fieldName];
    const value = config.itemText ? item[config.itemText] : item;
    setFieldValue(fieldName, value);
    validateField(fieldName);
  };

  return (
    <div className='grid grid-cols-2 gap-x-6 gap-y-2'>
      {Object.entries(formConfig).map(([fieldName, config]) => {
        if (config.type === 'display') {
          return (
            <div key={fieldName} className="flex flex-col">
              <label className="text-black text-sm font-bold flex items-center gap-1">
                {config.label}
                {config.required && <span className="text-[#bf2912]">*</span>}
                {config.tooltip && config.infoIcon && (
                  <InfoTooltip
                    label={config.tooltip}
                    icon={<Info size={16} color="#1B6EBB" />}
                    anchor={config.anchor || "top"}
                    variant="dark"
                    className="uds-tooltip-top"
                  />
                )}
              </label>
              <div className="text-base text-gray-900 mt-1">
                {values[fieldName] || '--'}
              </div>
            </div>
          );
        } else if (config.type === 'dateSelector') {
          return (
            <div key={fieldName} className="flex flex-col">
              <label className="text-black  text-sm font-bold flex items-center gap-1">
                {config.label}
                {config.required && <span className="text-[#bf2912]">*</span>}
                {config.tooltip && config.infoIcon && (
                  <InfoTooltip
                    label={config.tooltip}
                    icon={<Info size={16} color="#1B6EBB" />}
                    anchor={config.anchor || "top"}
                    variant="dark"
                    className="uds-tooltip-top"
                  />
                )}
              </label>
              <div className="uds-datepicker-wrapper">
                <DatePicker
                  value={values[fieldName]}
                  dateFormat={'MM/DD/YYYY'}
                  clearable={true}
                  onChange={(date) => {
                    setFieldValue(fieldName, date);
                    validateField(fieldName);
                  }}
                  placeholder={config.placeholder || "Select date"}
                  className="standardized-placeholder"
                />
              </div>
              {errors[fieldName] && (
                <div className="text-red-600 text-xs mt-1">{errors[fieldName]}</div>
              )}
            </div>
          );
        } else if (config.type === 'select') {
          return (
            <SelectField
              key={fieldName}
              label={config.label}
              required={config.required}
              tooltip={config.tooltip}
              anchor={config.anchor}
              error={errors[fieldName]}
              items={config.options || []}
              placeholder={config.placeholder}
              itemText={config.itemText || 'name'}
              onChange={createChangeHandler(fieldName)}
              infoIcon={config.infoIcon}
            />
          );
        } else {
          return (
            <InputField
              key={fieldName}
              label={config.label}
              required={config.required}
              tooltip={config.tooltip}
              anchor={config.anchor}
              error={errors[fieldName]}
              name={fieldName}
              placeholder={config.placeholder}
              type={config.inputType || 'text'}
              value={values[fieldName] || ''}
              onChange={config.disabled ? undefined : handleChange}
              onBlur={config.disabled ? undefined : () => validateField(fieldName)}
              disabled={config.disabled}
              infoIcon={config.infoIcon}
            />
          );
        }
      })}
    </div>
  );
};