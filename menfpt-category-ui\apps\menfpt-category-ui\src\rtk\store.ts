import { combineReducers, configureStore } from '@reduxjs/toolkit';
import menfptCategoryAPI from '../server/Api/menfptCategoryAPI';
import { getStorageKeyByRoute } from '../features/worksheetFilter/worksheetFilterUtils';

//https://redux.js.org/usage/code-splitting#:~:text=However%2C%20Redux%20really%20only%20has,re%2Dgenerate%20the%20root%20reducer.

// Using below to prevent empty reducer error during testing, to be changed.
// const a = createSlice({ name: 'mock', initialState: 1, reducers: {} });
// Define the Reducers that will always be present in the application
const staticReducers = {
  // a: a.reducer, // Temporary
  //[makeApi.reducerPath]: makeApi.reducer,
  [menfptCategoryAPI.reducerPath]: menfptCategoryAPI.reducer,
};


// Save state to localStorage
const saveToLocalStorage = (state: any) => {
  try {
    const filterPg = state['appliedFilter_rn'].data.filterPg;
    const serializedState = JSON.stringify(state['appliedFilter_rn'].data);
    if(filterPg && Object.keys(JSON.parse(serializedState)).length > 0) {      
      localStorage.setItem(filterPg, serializedState);
    }
  } catch (e) {
    console.warn("Could not save state", e);
  }
};


function configureAppStore() {
  const store: any = configureStore({
    reducer: { ...staticReducers },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(
        menfptCategoryAPI.middleware,
      ),
  });
  // Add a dictionary to keep track of the registered async reducers
  store.asyncReducers = {};

  // Create an inject reducer function
  // This function adds the async reducer, and creates a new combined reducer
  store.injectReducer = (key: any, asyncReducer: any) => {
    store.asyncReducers[key] = asyncReducer;
    store.replaceReducer(createReducer(store.asyncReducers));
  };

  store.subscribe(() => saveToLocalStorage(store.getState()));

  // Return the modified store
  return store;
}

export const app_store: any = configureAppStore();

function createReducer(asyncReducers: any) {
  return combineReducers({
    ...staticReducers,
    ...asyncReducers,
  });
}
export type RootState = ReturnType<typeof app_store>;
