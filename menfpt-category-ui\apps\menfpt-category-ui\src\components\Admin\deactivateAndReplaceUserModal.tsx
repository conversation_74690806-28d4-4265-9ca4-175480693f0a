import React, { useState, useRef } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import Button from '@albertsons/uds/molecule/Button';
import Alert from '@albertsons/uds/molecule/Alert';
import { FormActions } from './shared/FormActions';
import DeactivatedUserSection from './shared/DeactivatedUserSection';
import ReplacementUserSection, { ReplacementUserSectionRef } from './shared/ReplacementUserSection';
import { useFormData } from './shared/useFormData';
import { DropdownType } from '../../interfaces/worksheetFilter';
import { AddIcon } from './shared/InfoIcon';
interface UserData {
  role?: string;
  ldap?: string;
  userName?: string;
  manager?: string;
  department?: string;
  desk?: string;
  effectiveStartDate?: string;
  effectiveEndDate?: string;
}
interface DeactivateAndReplaceUserModalProps {
  userData?: UserData;
  isOpen?: boolean;
  onClose?: () => void;
  onBack?: () => void;
  showTriggerButton?: boolean;
}

interface ReplacementUser {
  id: string;
  userName: string;
  role: string;
  ldap: string;
  manager: string;
  department: string;
  desk: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
}
const DeactivateAndReplaceUserModal: React.FC<DeactivateAndReplaceUserModalProps> = ({
  userData = {},
  isOpen: externalIsOpen,
  onClose: externalOnClose,
  onBack,
  showTriggerButton = true
}) => {
  const [internalIsOpen, setInternalOpen] = useState<boolean>(false);
  const [isDeactivatedUserExpanded, setIsDeactivatedUserExpanded] = useState<boolean>(false);
  const [replacementUsers, setReplacementUsers] = useState<ReplacementUser[]>([]);
  const [expandedReplacementUser, setExpandedReplacementUser] = useState<string | null>(null);
  const [FiltersList, setFiltersList] = useState<any[]>([]);
  const [selectedDivision, setSelectedDivision] = useState<DropdownType | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const replacementUserSectionRef = useRef<ReplacementUserSectionRef>(null);
  const { departments, desks } = useFormData({ selectedDivision, FiltersList });
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const handleClose = () => {
    if (externalOnClose) {
      externalOnClose();
    } else {
      setInternalOpen(false);
    }
  };
  const getAvailableDepartments = () => {
    return departments.filter(dept => dept.name !== userData.department);
  };
  const addReplacementUser = () => {
    const newUser: ReplacementUser = {
      id: `temp-user-${Date.now()}`,
      userName: '',
      role: userData.role || '',
      ldap: '',
      manager: userData.manager || '',
      department: '',
      desk: '',
      effectiveStartDate: '',
      effectiveEndDate: ''
    };
    
    setReplacementUsers([...replacementUsers, newUser]);
    setExpandedReplacementUser(newUser.id);
    setIsDeactivatedUserExpanded(false); 
  };
  const toggleReplacementUserExpansion = (userId: string) => {
    if (expandedReplacementUser === userId) {
      setExpandedReplacementUser(null);
    } else {
      setExpandedReplacementUser(userId);
      setIsDeactivatedUserExpanded(false); 
    }
  }
  const toggleDeactivatedUserExpansion = () => {
    setIsDeactivatedUserExpanded(!isDeactivatedUserExpanded);
    if (!isDeactivatedUserExpanded) {
      setExpandedReplacementUser(null); 
    }
  };
  const handleContinue = async () => {
    if (replacementUsers.length === 0) {
      alert('Please add at least one replacement user before continuing.');
      return;
    }
    setIsSubmitting(true);
    try {
      const isValid = await replacementUserSectionRef.current?.validateAllForms();
    } catch (error) {
      console.error('Validation error:', error);
      alert('An error occurred during validation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <>
      {showTriggerButton && (
        <div className='h-10'>
          <Button fixed onClick={() => setInternalOpen(true)}>
            Deactivate and Replace User
          </Button>
        </div>
      )}
      <Modal 
        isOpen={isOpen} 
        onClose={handleClose}
      >
        <div className='select-none font-bold text-[28px] mt-4 ml-6'>
          Deactivate and Replace User
        </div>
        <div className="w-[800px] h-px bg-[#c8daeb]" />
        <div className='p-4 '>
          <Alert isOpen={true} variant='informational' size={"medium"}>
            <div className="San self-stretch text-[#1b6ebb] font-nunito font-bold leading-6">
              You are now deactivating an existing user and replacing with a different temporary user, please enter the details carefully before saving.
            </div>
          </Alert>
          <DeactivatedUserSection 
            userData={userData}
            isExpanded={isDeactivatedUserExpanded}
            onToggle={toggleDeactivatedUserExpansion}
          />
          <ReplacementUserSection 
            ref={replacementUserSectionRef}
            users={replacementUsers}
            expandedUserId={expandedReplacementUser}
            onToggleExpansion={toggleReplacementUserExpansion}
            availableDepartments={getAvailableDepartments()}
          />
          <div className="flex justify-between items-center mt-4">
            <button
              type="button"
              onClick={addReplacementUser}
              className="flex items-center gap-2 text-[#1B6EBB] hover:text-blue-700 font-medium"
            >
              <AddIcon />
              <span>Add Another User</span>
            </button>
            <FormActions 
              onBack={onBack}
              onCancel={handleClose}
              onSubmit={handleContinue}
              showBack={!!onBack}
              submitText="Continue"
              isSubmitting={isSubmitting}
              // disabled={replacementUsers.length === 0}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default DeactivateAndReplaceUserModal;
