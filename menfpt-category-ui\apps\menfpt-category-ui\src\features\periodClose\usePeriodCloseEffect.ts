import { useEffect, useMemo } from 'react';
import { setDataForQrtrDisplayedInTable } from '../../components/quarterDetails.slice';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { handleCalendarApiResp } from '../calendarServiceUtils';
import { getLastQuarterNbr } from './periodCloseLastQrtr.utils';

// Helper to calculate last quarter number
function useLastQuarterNbr(currentQtrData: any, qrtrNbrDisplayedInTable: any) {
  return useMemo(() => {
    if (!currentQtrData?.data?.fiscalQuarterNumber) {
      return null;
    }
    const { fiscalQuarterNumber, fiscalQuarterStartDate } = currentQtrData.data;
    return getLastQuarterNbr({
      qrtrNbrDisplayedInTable,
      currentQuarterNbr: fiscalQuarterNumber,
      currentQuarterStartDate: fiscalQuarterStartDate,
    });
  }, [currentQtrData.data, qrtrNbrDisplayedInTable]);
}

// Helper to handle calendar API response effect
function useHandleCalendarApiResp({
  calendarApiResp,
  calendarApiPayload,
  dispatch,
}: {
  calendarApiResp: any;
  calendarApiPayload: any;
  dispatch: any;
}) {
  useEffect(() => {
    if (
      !calendarApiResp ||
      calendarApiPayload.type !== 'quarterDisplayedInTable_periodClose'
    ) {
      return;
    }
    handleCalendarApiResp({
      calendarApiResp,
      dispatch,
    });
  }, [calendarApiResp, dispatch]);
}

// Custom hook for period close logic in worksheet table container
export function usePeriodCloseEffect({
  calendarApiResp,
  calendarApiPayload,
  dispatch,
  qrtrNbrDisplayedInTable,
}: {
  calendarApiResp: any;
  calendarApiPayload: any;
  dispatch: any;
  qrtrNbrDisplayedInTable: any;
}) {
  const currentQtrData = useSelectorWrap('displayDate_rn');
  const lastQuarterNbr = useLastQuarterNbr(currentQtrData, qrtrNbrDisplayedInTable);

  useHandleCalendarApiResp({ calendarApiResp, calendarApiPayload, dispatch });

 
  return lastQuarterNbr;
}
