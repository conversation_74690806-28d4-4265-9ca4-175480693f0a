import React from 'react';
import { render, screen } from '@testing-library/react';
import ModalTimeframeSection from './ModalTimeframeSection';

jest.mock('../timeframe/timeframeSelector', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="timeframe-selector">
      TimeframeSelector {props.selectedTimeframe ? props.selectedTimeframe.name : 'none'}
      <button onClick={() => props.onTimeframeChange && props.onTimeframeChange({ num: 1, name: 'TF1' })}>Change</button>
    </div>
  )
}));

describe('ModalTimeframeSection', () => {
  it('renders with no selectedTimeframe', () => {
    render(<ModalTimeframeSection onTimeframeChange={jest.fn()} />);
    expect(screen.getByTestId('timeframe-selector')).toHaveTextContent('none');
  });

  it('renders with selectedTimeframe', () => {
    render(
      <ModalTimeframeSection
        selectedTimeframe={{ num: 2, name: 'TF2' }}
        onTimeframeChange={jest.fn()}
      />
    );
    expect(screen.getByTestId('timeframe-selector')).toHaveTextContent('TF2');
  });

  it('calls onTimeframeChange when selector triggers change', () => {
    const onTimeframeChange = jest.fn();
    render(
      <ModalTimeframeSection
        selectedTimeframe={{ num: 2, name: 'TF2' }}
        onTimeframeChange={onTimeframeChange}
      />
    );
    screen.getByText('Change').click();
    expect(onTimeframeChange).toHaveBeenCalledWith({ num: 1, name: 'TF1' });
  });
});
