import { useExtractBannerId } from './filterUtils';
import { worksheetFilterConfig } from '../features/worksheetFilter/worksheetFilterConfig';

jest.mock('../rtk/rtk-utilities', () => {
  return {
    useSelectorWrap: jest.fn((key: string) => {
      if (key === 'appliedFilter_rn') {
        return { data: mockAppliedFilters } as any;
      }
      return { data: undefined } as any;
    }),
  };
});

let mockAppliedFilters: any;

describe('useExtractBannerId - division without banner', () => {
  beforeEach(() => {
    mockAppliedFilters = undefined;
  });

  it('returns division-banner pair with bannerId "00" when a division has no banners (dashboard page)', () => {
    mockAppliedFilters = {
      filterPg: worksheetFilterConfig.lsKeyDashboardPg,
      division: [
        {
          num: 17,
          name: 'Division 17',
          banners: [],
        },
      ],
    };

    const result = useExtractBannerId();

    expect(result).toEqual([
      {
        divisionId: '17',
        bannerId: '00',
      },
    ]);
  });
});


