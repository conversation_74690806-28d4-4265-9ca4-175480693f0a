import * as FileSaver from 'file-saver';
import * as DashboardDownloadExcel from '../DashboardDownloadExcel/DashboardDownloadExcel';
import * as PrintSettings from '../DashboardDownloadExcel/DashboardDownloadExcelPrint';
const { handleDownloadExcel, getDeptName, formatCurrency } = DashboardDownloadExcel;
jest.mock('../DashboardDownloadExcel/DashboardDownloadExcel', () => {
  const actual = jest.requireActual('../DashboardDownloadExcel/DashboardDownloadExcel');
  return {
    ...actual,
    styleWorksheet: jest.fn(),
    styleVsProjection: jest.fn(),
  };
});
jest.mock('file-saver', () => ({ saveAs: jest.fn() }));
jest.spyOn(window, 'alert').mockImplementation(() => {});

// Mock the module to spy on its exports
const mockProcessWorksheet = jest.fn();
jest.mock('../DashboardDownloadExcel/DashboardDownloadExcel', () => ({
  ...jest.requireActual('../DashboardDownloadExcel/DashboardDownloadExcel'),
  processWorksheet: (...args) => mockProcessWorksheet(...args),
}));

describe('handleDownloadExcel', () => {
  const { handleDownloadExcel } = DashboardDownloadExcel;
  const date = new Date().toLocaleDateString('en-CA');

  beforeEach(() => {
    jest.clearAllMocks();
    mockProcessWorksheet.mockClear();
  });



  it('should call saveAs with the correct filename for both summary and variance', async () => {
    await handleDownloadExcel();
    const expectedFileName = `Allocatr Insights Performance and Variance Mock Excel Download-${date}.xlsx`;
    expect(FileSaver.saveAs).toHaveBeenCalledWith(expect.any(Blob), expectedFileName);
  });

  // it('should alert if dashboardData is empty', async () => {
  //   await handleDownloadExcel([], smicData, {});
  //   expect(window.alert).toHaveBeenCalledWith('No dashboard data to export!');
  //   expect(FileSaver.saveAs).not.toHaveBeenCalled();
  // });

  // it('should use appliedFilters for quarter and fiscalYear in header', async () => {
  //   const filters = { timeframe: { quarter: 3, fiscalYear: 2025 } };
  //   await handleDownloadExcel(dashboardData, smicData, filters);
  //   expect(FileSaver.saveAs).toHaveBeenCalled();
  // });

  // it('should fallback to dashboardData[0] for quarter if not in filters', async () => {
  //   await handleDownloadExcel(dashboardData, smicData, {});
  //   expect(FileSaver.saveAs).toHaveBeenCalled();
  // });
  
  // it('should call saveAs with a Blob and correct filename', async () => {
  //   await handleDownloadExcel(dashboardData, smicData, {});
  //   expect(FileSaver.saveAs).toHaveBeenCalledWith(expect.any(Blob), 'Dashboard Excel Download.xlsx');
  // });
});

describe('getDeptName', () => {
  it('should return the department name from smicData if found', () => {
    expect(getDeptName([{ deptId: '1', deptName: 'menswear' }], '1', 'fallback')).toBe('Menswear');
  });
  it('should fallback to provided fallback if not found', () => {
    expect(getDeptName([], '2', 'fallback')).toBe('Fallback');
  });
  it('should return empty string if both missing', () => {
    expect(getDeptName([], '2', '')).toBe('');
  });
  it('should trim deptId and match correctly', () => {
    expect(getDeptName([{ deptId: ' 3 ', deptName: 'shoes' }], '3', 'fallback')).toBe('Shoes');
  });

  it('should handle null deptName in smicData', () => {
    expect(getDeptName([{ deptId: '4', deptName: null }], '4', 'backup')).toBe('Backup');
  });
});

describe('getDivisionName', () => {
  const { getDivisionName } = DashboardDownloadExcel;

  it('should return formatted division name from smicData', () => {
    const smicData = [
      { divisionId: '34', divisionName: 'NOR. CALIFORNIA' }
    ];
    expect(getDivisionName(smicData, '34', 'fallback')).toBe('Nor. California');
  });

  it('should return formatted fallback when division not found in smicData', () => {
    const smicData = [
      { divisionId: '35', divisionName: 'Other Division' }
    ];
    expect(getDivisionName(smicData, '34', 'test division')).toBe('Test Division');
  });

  it('should return divisionId when no fallback and not found in smicData', () => {
    const smicData = [];
    expect(getDivisionName(smicData, '34', '')).toBe('34');
  });
});

describe('getBannerName', () => {
  const { getBannerName } = DashboardDownloadExcel;

  it('should return formatted banner name from smicData', () => {
    const smicData = [
      { divisionId: '34', bannerId: '25', bannerName: 'ACME Stores' }
    ];
    expect(getBannerName(smicData, '34', '25', 'fallback')).toBe('Acme Stores');
  });

  it('should return formatted fallback when banner not found in smicData', () => {
    const smicData = [
      { divisionId: '34', bannerId: '26', bannerName: 'Other Banner' }
    ];
    expect(getBannerName(smicData, '34', '25', 'test banner')).toBe('Test Banner');
  });

  it('should return bannerId when no fallback and not found in smicData', () => {
    const smicData = [];
    expect(getBannerName(smicData, '34', '25', '')).toBe('25');
  });
});

describe('formatCurrency', () => {
  it('should return "" for null, undefined, or empty string', () => {
    expect(formatCurrency(null)).toBe('');
    expect(formatCurrency(undefined)).toBe('');
    expect(formatCurrency('')).toBe('');
  });
  it('should format numbers as $X,XXX', () => {
    expect(formatCurrency(1234)).toBe('$1,234');
    expect(formatCurrency('5678')).toBe('$5,678');
  });
  it('should return original value if not a number', () => {
    expect(formatCurrency('abc')).toBe('abc');
  });
  it('should handle negative numbers', () => {
    expect(formatCurrency(-1234)).toBe('$-1,234');
    expect(formatCurrency('-5678')).toBe('$-5,678');
  });

  it('should handle zero', () => {
    expect(formatCurrency(0)).toBe('$0');
    expect(formatCurrency('0')).toBe('$0');
  });

  it('should handle numbers with decimals', () => {
    expect(formatCurrency(1234.56)).toBe('$1,235');
    expect(formatCurrency('5678.99')).toBe('$5,679');
  });
});

describe('print settings functionality', () => {
  it('getDefaultPrintSettings should return correct default settings', () => {
    const settings = PrintSettings.getDefaultPrintSettings();
    expect(settings).toEqual({
      orientation: 'landscape',
      paperSize: 'A3',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
    });
  });

  it('applyPrintSettings should set worksheet.pageSetup with correct values', () => {
    const worksheet: any = {};
    PrintSettings.applyPrintSettings(worksheet);
    expect(worksheet.pageSetup).toEqual({
      orientation: 'landscape',
      paperSize: 8,
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      printTitlesRow: '1:2',
    });
  });

  it('applyPrintSettings should overwrite existing pageSetup', () => {
    const worksheet = { pageSetup: { orientation: 'portrait', paperSize: 1 } };
    PrintSettings.applyPrintSettings(worksheet);
    expect(worksheet.pageSetup.orientation).toBe('landscape');
    expect(worksheet.pageSetup.paperSize).toBe(8);
  });

  it('applyPrintSettings should work if worksheet.pageSetup is undefined', () => {
    const worksheet: any = { };
    expect(() => PrintSettings.applyPrintSettings(worksheet)).not.toThrow();
    expect(worksheet.pageSetup.orientation).toBe('landscape');
  });

  it('getDefaultPrintSettings returns a new object each time', () => {
    const a = PrintSettings.getDefaultPrintSettings();
    const b = PrintSettings.getDefaultPrintSettings();
    expect(a).not.toBe(b);
    a.orientation = 'portrait';
    expect(b.orientation).toBe('landscape');
  });
});

// describe('handleDownloadExcel print settings integration', () => {
//   it('should call applyPrintSettings when exporting', async () => {
//     const spy = jest.spyOn(PrintSettings, 'applyPrintSettings');
//     await handleDownloadExcel([{ id: '1', name: 'test', quarter: {}, periods: [] }], [{ deptId: '1', deptName: 'test' }], {});
//     expect(spy).toHaveBeenCalled();
//     spy.mockRestore();
//   });

//   it('should not throw if worksheet already has pageSetup', async () => {
//     const spy = jest.spyOn(PrintSettings, 'applyPrintSettings');
//     await handleDownloadExcel([{ id: '1', name: 'test', quarter: {}, periods: [] }], [{ deptId: '1', deptName: 'test' }], {});
//     expect(spy).toHaveBeenCalled();
//     spy.mockRestore();
//   });
// });

describe('handleDownloadExcel with new divisions structure', () => {
  it('should handle new divisions and banners structure with Total row first', async () => {
    const data = {
      id: 'Total',
      name: '',
      quarter: { quarterNumber: 202502 },
      periods: [{ periodNumber: 202506 }],
      weeks: [{ weekNumber: 202525, periodNumber: 202506 }],
      divisions: [{
        id: '34',
        name: 'Test Division',
        quarter: { quarterNumber: 202502 },
        banners: [{
          id: '25',
          name: 'ACME Stores',
          quarter: { quarterNumber: 202502 },
          departments: [{
            id: '301',
            name: 'Grocery',
            quarter: { quarterNumber: 202502 },
            periods: [{ periodNumber: 202506 }],
            weeks: [{ weekNumber: 202525, periodNumber: 202506 }]
          }]
        }]
      }]
    };
    const smicData = [
      { divisionId: '34', divisionName: 'NOR. CALIFORNIA', bannerId: '25', bannerName: 'ACME STORES' }
    ];
    await handleDownloadExcel(data, smicData, {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should handle empty divisions array', async () => {
    const data = {
      id: 'Total',
      name: '',
      quarter: { quarterNumber: 202502 },
      divisions: []
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should fallback to old structure when divisions not present', async () => {
    const data = {
      id: '301',
      name: 'Grocery',
      quarter: { quarterNumber: 202502 },
      periods: [{ periodNumber: 202506 }],
      weeks: [{ weekNumber: 202525, periodNumber: 202506 }]
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should include Total row with dynamic division count', async () => {
    const data = {
      id: 'Total',
      name: '',
      quarter: { quarterNumber: 202502, line1Projection: 1000 },
      periods: [{
        periodNumber: 202506,
        line1Projection: 500
      }],
      weeks: [{
        weekNumber: 202525,
        periodNumber: 202506,
        line1Projection: 100
      }],
      divisions: [
        { id: '34', name: 'Test Division', quarter: { quarterNumber: 202502 } },
        { id: '35', name: 'Another Division', quarter: { quarterNumber: 202502 } }
      ]
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should show singular division count for single division', async () => {
    const data = {
      id: 'Total',
      quarter: { quarterNumber: 202502, line1Projection: 1000 },
      divisions: [
        { id: '34', name: 'Single Division', quarter: { quarterNumber: 202502 } }
      ]
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should not display banner rows when banner data is unavailable', async () => {
    const data = {
      id: 'Total',
      quarter: { quarterNumber: 202502, line1Projection: 1000 },
      divisions: [{
        id: '34',
        name: 'Test Division',
        quarter: { quarterNumber: 202502 },
        banners: [{
          id: '25',
          name: 'Empty Banner'
          // No quarter, departments, periods, or weeks data
        }]
      }]
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should display banner rows when banner has valid data', async () => {
    const data = {
      id: 'Total',
      quarter: { quarterNumber: 202502, line1Projection: 1000 },
      divisions: [{
        id: '34',
        name: 'Test Division',
        quarter: { quarterNumber: 202502 },
        banners: [{
          id: '25',
          name: 'Valid Banner',
          quarter: { quarterNumber: 202502, line1Projection: 500 }
        }]
      }]
    };
    await handleDownloadExcel(data, [], {});
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });
});

// describe('handleDownloadBothExcel', () => {
//   it('should alert if both datasets are empty', async () => {
//     await DashboardDownloadExcel.handleDownloadBothExcel([], [], []);
//     expect(window.alert).toHaveBeenCalledWith('No dashboard data to export!');
//   });

//   it('should export Performance Summary if data exists', async () => {
//     const data = [{ id: '1', name: 'test', quarter: { quarterNumber: 2 }, periods: [] }];
//     await DashboardDownloadExcel.handleDownloadBothExcel(data, [], []);
//     expect(FileSaver.saveAs).toHaveBeenCalled();
//   });

//   it('should export Forecast Variance if data exists', async () => {
//     const data = [{ id: '1', name: 'test', quarter: { quarterNumber: 2 }, periods: [] }];
//     await DashboardDownloadExcel.handleDownloadBothExcel([], data, []);
//     expect(FileSaver.saveAs).toHaveBeenCalled();
//   });

//   it('should export both sheets if both datasets exist', async () => {
//     const data = [{ id: '1', name: 'test', quarter: { quarterNumber: 2 }, periods: [] }];
//     await DashboardDownloadExcel.handleDownloadBothExcel(data, data, []);
//     expect(FileSaver.saveAs).toHaveBeenCalled();
//   });
// });
