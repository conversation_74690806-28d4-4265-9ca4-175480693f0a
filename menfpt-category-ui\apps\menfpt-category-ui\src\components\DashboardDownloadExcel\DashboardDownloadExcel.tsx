import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { toTitleCase } from '@ui/utils';
import { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';
import { applyPrintSettings } from './DashboardDownloadExcelPrint';

export const formatCurrency = (value: any) => {
  if (value === null || value === undefined || value === '') return '';
  const num = Number(value);
  return isNaN(num) ? value : `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
};
export const getDeptName = (smicData: any[], deptId: string, fallback: string) => {
  const found = smicData.find((item: any) => String(item.deptId).trim() === String(deptId).trim());
  return toTitleCase(found?.deptName || fallback || '');
};
const addRows = (rows: any[], dept: any, smicData: any[], useWeekId: boolean = false) => {
  const quarter = dept.quarter || {};
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}` };
  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));

  const weeksByPeriod: Record<string, any[]> = {};
  (dept.weeks || []).forEach((week: any) => {
    const periodNum = week.periodNumber ?? week.periodNbr ?? '';
    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];
    weeksByPeriod[periodNum].push(week);
  });

  dept.periods?.forEach((period: any) => {
    const periodNum = period.periodNumber ?? period.periodNbr ?? '';
    rows.push(
      mapRow(
        { ...baseRow, departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },
        period,
        formatCurrency,
        'Period',
        periodNum
      )
    );

    const weeks = weeksByPeriod[periodNum] || [];
    const sortedWeeks = weeks.slice().sort((a, b) => {
      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);
      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);
      return aNum - bNum;
    });
    sortedWeeks.forEach((week: any) => {
      let weekNum = '--';
      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {
        weekNum = String(parseInt(week.id.slice(-2), 10));
      } else if (!useWeekId && typeof week.weekNumber === 'number') {
        weekNum = String(week.weekNumber % 100);
      } else if (!useWeekId && typeof week.weekNumber === 'string') {
        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));
      }
      rows.push(
        mapRow(
          { ...baseRow, departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },
          week,
          formatCurrency,
          'Week',
          '',
          String(weekNum)
        )
      );
    });
  });
};
export const styleWorksheet = worksheet => {
  worksheet.getRow(2).eachCell((cell, colNumber) => {
    if (colNumber !== 1) {
      worksheet.getColumn(colNumber).width = Math.max(String(cell.value ?? '').length + 1, 16);
    }
  });
  let maxA = 0;
  worksheet.eachRow((row, rowNumber) => {
    const cellValue = String(row.getCell(1).value ?? '');
    if (cellValue.length > maxA) maxA = cellValue.length;
  });
  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);

  const thinLightBlack = { style: 'thin', color: { argb: 'FF222222' } };
  const thinLightBlackBorder = {
  top: thinLightBlack, left: thinLightBlack,bottom: thinLightBlack,right: thinLightBlack
  };
   worksheet.eachRow(row => {
    row.eachCell(cell => {
      cell.border = thinLightBlackBorder;
    });
  });
  const lightGrayFill = { type: "pattern", pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
  worksheet.getRow(1).eachCell(cell => { 
    cell.fill = lightGrayFill; 
    cell.font = { bold: true }; 
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
  });
  worksheet.getRow(2).eachCell(cell => { 
    cell.font = { bold: true }; 
    cell.alignment = { vertical: 'middle', horizontal: 'center' }; 
  });
  worksheet.getCell('A2').fill = lightGrayFill; worksheet.getCell('A2').font = { bold: true };
  const lightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFA8F1FF' } };
  const highlightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6FE6FC' } }; 
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber >= 3) {
      const firstCell = row.getCell(1).value;
      if (typeof firstCell === 'string') {
        if ( firstCell.trim() === 'Total' || /^[0-9]+ - /.test(firstCell)   ) {
          row.eachCell(cell => { cell.fill = highlightBlueFill; });
        } else if (/^Period\b/.test(firstCell)) {
          row.eachCell(cell => { cell.fill = lightBlueFill; });
        }
      }
    }
  });
  worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
  ['A1', 'A2'].forEach(cell => {
    worksheet.getCell(cell).alignment = { vertical: 'middle', horizontal: 'center' };
  });worksheet.getCell('A3').font = { ...worksheet.getCell('A3').font, bold: true };
}
export const styleVsProjection = (worksheet: ExcelJS.Worksheet) => {
  const vsProjectionColIndices: number[] = [];
  worksheet.getRow(2).eachCell((cell, colNumber) => {
    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);
  });
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber >= 3) {
      vsProjectionColIndices.forEach(colIdx => {
        const cell = row.getCell(colIdx);
        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\$, %\(\)]/g, '').trim() : cell.value;
        const num = Number(raw);
        if (!isNaN(num) && raw !== '') {
          cell.font = { ...cell.font, color: { argb: num < 0 ? 'FFFF0000' : 'FF008000' } };
          const header = worksheet.getRow(2).getCell(colIdx).value;
          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {
            if (num < 0) {
              cell.value = `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;
            } else {
              cell.value = `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
            }
          }
        }
      });
    }
  });
};
export const handleDownloadExcel = async (
  dashboardData: any[],
  smicData: any[] = [],
  appliedFilters?: any,
  fileName: string = 'Dashboard Excel Download.xlsx'
) => {
  if (!dashboardData?.length) return alert('No dashboard data to export!');
  let quarterNumber = appliedFilters?.timeframe?.quarter;
  if (!quarterNumber && dashboardData.length) {
    quarterNumber = dashboardData[0]?.quarter?.quarterNumber || dashboardData[0]?.quarterNumber || '';
    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {
      const qStr = String(quarterNumber); if (qStr.length === 6) quarterNumber = Number(qStr.slice(4, 6));
    }
  }
  const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';
  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;
  const parentHeaderRow = getParentHeaderRow(quarterDisplay);
  const isVariance = fileName.toLowerCase().includes('variance');
  const rows: any[] = [];
  dashboardData.forEach(dept => addRows(rows, dept, smicData, isVariance));
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Dashboard');
  worksheet.addRow(parentHeaderRow);
  worksheet.addRow(COMMON_HEADERS);
  rows.forEach(row => worksheet.addRow(Object.values(row)));
  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];
  mergeRanges.forEach(range => worksheet.mergeCells(range));
  styleWorksheet(worksheet);
  styleVsProjection(worksheet);
  applyPrintSettings(worksheet);
  let ySplit = 2; 
  const totalRows = worksheet.rowCount;
  for (let i = 3; i <= totalRows; i++) {
    const cellValue = String(worksheet.getRow(i).getCell(1).value ?? '');
    if (/^\d+ - /.test(cellValue.trim()) && i !== 3) {
      ySplit = i - 1;
      break;
    } if (i === totalRows) { ySplit = totalRows; } }
  worksheet.views = [{ state: 'frozen', ySplit, xSplit: 1 }];
  const styledBuffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([styledBuffer]), fileName);
};
export const handleDownloadBothExcel = async (
  performanceSummaryData: any[],
  forecastVarianceData: any[],
  smicData: any[] = [],
  appliedFilters?: any
) => {
  // if (!performanceSummaryData?.length && !forecastVarianceData?.length) {
  //   return alert('No dashboard data to export!');
  // }
  const workbook = new ExcelJS.Workbook();
  if (performanceSummaryData?.length) {
    const worksheet1 = workbook.addWorksheet('Performance Summary');
    const quarterNumber = appliedFilters?.timeframe?.quarter || '';
    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';
    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;
    worksheet1.addRow(getParentHeaderRow(quarterDisplay));
    worksheet1.addRow(COMMON_HEADERS);
    performanceSummaryData.forEach(dept => {
      const rows: any[] = [];
      addRows(rows, dept, smicData);
      rows.forEach(row => worksheet1.addRow(Object.values(row)));
    });
    styleWorksheet(worksheet1);
    styleVsProjection(worksheet1);
    applyPrintSettings(worksheet1);
  }
  if (forecastVarianceData?.length) {
    const worksheet2 = workbook.addWorksheet('Forecast Variance');
    const quarterNumber = appliedFilters?.timeframe?.quarter || '';
    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';
    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;
    worksheet2.addRow(getParentHeaderRow(quarterDisplay));
    worksheet2.addRow(COMMON_HEADERS);
    forecastVarianceData.forEach(dept => {
      const rows: any[] = [];
      addRows(rows, dept, smicData, true);
      rows.forEach(row => worksheet2.addRow(Object.values(row)));
    });
    styleWorksheet(worksheet2);
    styleVsProjection(worksheet2);
    applyPrintSettings(worksheet2);
  }
  const styledBuffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([styledBuffer]), 'Dashboard Excel Download.xlsx');
};
