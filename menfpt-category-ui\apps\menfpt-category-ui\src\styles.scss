/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Nunito Sans', sans-serif;
    font-size: small;
  }
}

.BodyParagraphMSemiBold
{
  font-family: "Nunito Sans", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0px;
  vertical-align: middle;

}

.BodyParagraphSSemiBold
{
  font-family: "Nunito Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0px;
  vertical-align: middle;

}

.BodyDataXSRegular
{
  font-family: "Nunito Sans", sans-serif;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: 0px;
  vertical-align: middle;
}

.BodyDataMRegular
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0px;
    vertical-align: middle;

}


.BodyDataSRegular
{
    font-family: "Nunito Sans", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 0px;
    vertical-align: middle;
}
/*UDS Tooltip Styles*/
.tool-tip-initilizer + div:not(.truncate) {
    position: fixed !important;
}
.tool-tip-initilizer-top + div:not(.truncate) {
    position: fixed !important;
    bottom: 9px !important;
}
.tabs-tooltip-initilizer + div:not(.truncate) {
    position: fixed !important;
}
 .uds-tooltip-right .border-transparent{
    border-right-color: #033b69;
  }
 .uds-tooltip-left .border-transparent{
    border-left-color: #033b69;
  }
.uds-tooltip-top .border-transparent{
  border-top-color: #033b69;
  position: absolute;
  bottom: -8px;
}
.uds-tooltip-bottom .border-transparent {
  border-bottom-color: #033b69;
}

.nfpt-scrollbar {
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #888 transparent; /* thumb color, track color */

  /* Hide the scrollbar track (background) */
  &::-webkit-scrollbar {
    width: thin; /* Set the width of the scrollbar */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* Make the track transparent */
  }

  /* Style the scrollbar thumb (scroller) */
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3); /* Set the color of the scroller to match design */
    border-radius: 99px; /* Add rounded corners */
    height: 112px; /* Set the height of the scrollbar thumb */
  }
}

.ellipsis-label {
  display: inline-block;
  max-width: 22vw;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
  vertical-align: bottom;
}

.data-popper-bottom-margin [data-popper-placement="top"] {
    margin-bottom: 3px !important;
}
