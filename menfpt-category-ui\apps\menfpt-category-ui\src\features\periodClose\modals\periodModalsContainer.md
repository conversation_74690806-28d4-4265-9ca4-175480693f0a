# periodModalsContainer Requirements and Behavior

## Purpose
This component (and its hook) manages the display of informational modals related to period close and period lock status for the previous quarter in the worksheet UI. It ensures users are notified about important period status changes, but only once per quarter.

---

## Core Requirements

1. **Period Close Modal**
   - **When to show:**
     - If there are periods in the previous quarter that are *closed but not yet locked*.
     - The modal should be shown *after worksheet data has loaded*.
   - **User experience:**
     - The modal is shown only once per quarter per user (tracked via localStorage key with the quarter number).
     - Once displayed, the localStorage key is set to `true` for that quarter.

2. **Period Locked Modal**
   - **When to show:**
     - If there are periods in the previous quarter that are *locked but not yet certified*.
     - The modal should be shown *after worksheet data has loaded*.
   - **User experience:**
     - The modal is shown only once per quarter per user (tracked via localStorage key with the quarter number).
     - Once displayed, the localStorage key is set to `true` for that quarter.

3. **Modal Display Logic**
   - Only one modal is shown at a time, with period locked taking precedence if both conditions are met.
   - The modal is not shown if the corresponding localStorage key is already set to `true` for the current quarter.

4. **LocalStorage Keys**
   - Keys are of the form:
     - `periodCloseAlert_IsDisplayed_<quarterNumber>`
     - `periodLockedAlert_IsDisplayed_<quarterNumber>`
   - These are set to `'true'` when the modal is displayed.

---



5. **Data Dependencies**
   - The logic depends on the `lastQrtrPeriodStatuses` object, which is derived from the previous quarter's calendar API response and processed by `generatePeriodStatuses`.
   - The worksheet data must be loaded before modal logic is evaluated.




8. **Extensibility**
   - To add new modal types, extend the `getInitialModalState` logic and ensure the new modal is returned and rendered by the hook and parent component.

---

## Example Usage
```tsx
const { periodCloseModal, periodLockedModal } = usePeriodModalsContainer();
...
{workSheetData && workSheetData.size > 0 && periodCloseModal}
{workSheetData && workSheetData.size > 0 && periodLockedModal}
```

---

## Related Files
- `periodModalsContainer.tsx` (this logic)
- `generatePeriodStatuses.ts` (status calculation)
- `periodClose.flags.ts` (API and state management) 