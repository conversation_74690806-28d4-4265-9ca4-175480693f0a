import '@testing-library/jest-dom';
// BDD-style tests for adjustment-worksheet.tsx
import AdjustmentWorkSheet from './adjustment-worksheet';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { MemoryRouter } from 'react-router-dom';

jest.mock('../features/WorksheetHeaderControls', () => ({
  WorksheetHeaderControls: () => <div>WorksheetHeaderControls</div>,
}));
jest.mock('./worksheet-table-container', () => () => <div>TableContainer</div>);
jest.mock('../features/worksheetFilter/useCombinedFiltersAndQuarters', () => ({
  useCombinedFiltersAndQuarters: () => ({ filterLoading: false, filterData: ['filter1', 'filter2'] }),
}));
jest.mock('../components/AlertMessage', () => (props: { message: string }) => <div>AlertMessage: {props.message}</div>);
jest.mock('../rtk/rtk-utilities', () => ({
  useSelectorWrap: () => ({ data: { success: true, error: false } }),
}));
jest.mock('react-redux', () => ({
  useDispatch: () => jest.fn(),
}));
jest.mock('../server/Reducer/menfpt-category.slice', () => ({
  setAlertState: jest.fn(),
}));

describe('AdjustmentWorkSheet', () => {
  it('renders all main components when not loading', () => {
    render(
      <MemoryRouter>
        <AdjustmentWorkSheet />
      </MemoryRouter>
    );
    expect(screen.getByText('WorksheetHeaderControls')).toBeInTheDocument();
    expect(screen.getByText('TableContainer')).toBeInTheDocument();
    expect(screen.getByText(/AlertMessage/)).toBeInTheDocument();
  });

  it('shows success message in AlertMessage when alertState.success is true', () => {
    render(
      <MemoryRouter>
        <AdjustmentWorkSheet />
      </MemoryRouter>
    );
    expect(screen.getByText(/Changes are saved successfully/)).toBeInTheDocument();
  });

 
});
