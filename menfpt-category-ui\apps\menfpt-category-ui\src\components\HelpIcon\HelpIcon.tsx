import React from 'react';
import HelpIconSvg from '../../assets/help-icon.svg';
import HelpIconDashboard from '../../assets/help-icon-dashboard.svg';
import './HelpIcon.css';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { CircleHelp } from 'lucide-react';

interface HelpIconProps {
  variant?: 'default' | 'dashboard' | 'dashboard-title';
  className?: string;
}


const getIconProps = (variant: string) => {
  let iconClass = 'help-icon';
  let iconSrc = HelpIconSvg;

  if (variant === 'dashboard') {
    iconClass = 'help-icon-dashboard';
    iconSrc = HelpIconDashboard;
  } else if (variant === 'dashboard-title') {
    iconClass = 'help-icon-dashboard-title';
  }

  return { iconClass, iconSrc };
};






const HelpIcon: React.FC<HelpIconProps> = ({ variant = 'default', className = '' }) => {
  const { data: helpPdfUrl } = useSelectorWrap('helpPdfUrl_rn');
  

  const { iconClass, iconSrc } = getIconProps(variant);
  return (
    <div className="help-icon-container">
    <a 
      href={helpPdfUrl} 
      target="_blank" 
      rel="noopener noreferrer"
      title="Open help document"
    >
      <img
        src={iconSrc}
        className={`${iconClass} ${className}`}
        alt="Help Icon"
        title="Click to view and download help document"
      />
    </a>
    </div>
  );
};

export default HelpIcon;
