import { formatCurrency } from './insightsFormatters';

describe('formatCurrency', () => {
  it('formats numbers with commas and currency', () => {
    expect(formatCurrency(1000)).toBe('$1,000');
    expect(formatCurrency(1234567)).toBe('$1,234,567');
  });

  it('handles negative numbers (parentheses style)', () => {
    expect(formatCurrency(-500)).toBe('($500)');
    expect(formatCurrency(-1234567)).toBe('($1,234,567)');
  });

  it('handles zero', () => {
    expect(formatCurrency(0)).toBe('$0');
  });

  it('handles large numbers', () => {
    expect(formatCurrency(9876543210)).toBe('$9,876,543,210');
  });
});
