// @ts-check

/**
 * @type {import('@nrwl/devkit').ModuleFederationConfig}
 **/
const moduleFederationConfig = {
  name: 'menfpt-category-ui',
  exposes: {
    './Module': './src/remote-entry.ts',
  },
  shared: (libraryName, defaultConfig) => {
    if (libraryName === 'react' || libraryName === 'react-dom') {
      return {
        ...defaultConfig,
        singleton: true,
        eager: true,
        requiredVersion: false
      };
    }
    // Return default config for other libraries
    return defaultConfig;
  }
};

module.exports = moduleFederationConfig;
