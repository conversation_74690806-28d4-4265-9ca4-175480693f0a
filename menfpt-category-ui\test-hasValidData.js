// Simple test to verify the hasValidData function logic

// Helper function to check if data is valid for download
const hasValidData = (data) => {
  if (!data) return false;
  // Handle both old array format and new object format
  if (Array.isArray(data)) {
    return data.length > 0;
  }
  // For object format, check if it has divisions or id
  return data.divisions?.length > 0 || data.id;
};

// Test cases
console.log('Testing hasValidData function:');

// Test null/undefined
console.log('hasValidData(null):', hasValidData(null)); // false
console.log('hasValidData(undefined):', hasValidData(undefined)); // false

// Test empty array
console.log('hasValidData([]):', hasValidData([])); // false

// Test array with data
console.log('hasValidData([{id: 1}]):', hasValidData([{id: 1}])); // true

// Test empty object
console.log('hasValidData({}):', hasValidData({})); // false

// Test object with id only
console.log('hasValidData({id: "Total"}):', hasValidData({id: "Total"})); // true

// Test object with empty divisions
console.log('hasValidData({divisions: []}):', hasValidData({divisions: []})); // false

// Test object with divisions
console.log('hasValidData({divisions: [{id: "34"}]}):', hasValidData({divisions: [{id: "34"}]})); // true

// Test object with both id and divisions
console.log('hasValidData({id: "Total", divisions: [{id: "34"}]}):', hasValidData({id: "Total", divisions: [{id: "34"}]})); // true

console.log('\n✅ All tests completed. The hasValidData function should now correctly identify valid data for both old and new formats.');
