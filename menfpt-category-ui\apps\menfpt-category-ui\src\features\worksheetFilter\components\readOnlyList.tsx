import React from 'react';
import { DropdownType } from '../../../interfaces/worksheetFilter';
import '../../WorksheetHeaderControls.css';

const DEFUALT_TITLE = 'All categories for the department and the users will be listed here based on the above selection';
interface ReadOnlyListProps {
  items: DropdownType[];
  title: string;
}
const getName = (item: DropdownType): string => {
  // pad 0 if category num is less than 4 characters
  return item.num < 1000 ? `0${item.name}` : item.name;
};
const ReadOnlyList: React.FC<ReadOnlyListProps> = ({ items, title }) => {
  const additionalClass = items.length === 0 ? 'font-bold self-center' : 'BodyParagraphSSemiBold';
  return (
    <div className="h-full rounded-md p-2 border-rounded bg-[#F1F4F9] flex flex-col flex-1 min-h-0">
      <div className={`p-1 rounded text-sm/[1.5rem] tracking-normal text-[#5A697B] ${additionalClass}`}>
        {items.length > 0 ? title : DEFUALT_TITLE}
      </div>
      <div
        className="flex-1 min-h-0 categories-scrollbar pl-2 overflow-y-auto overflow-x-hidden"
      >
        <div className="grid grid-cols-3 gap-x-2">
          {items.map((item) => (
            <div
              key={item.num}
              title={item.name}
              className="my-1 text-sm/[1rem] font-normal tracking-normal text-[#5A697B] truncate min-h-4 BodyDataSRegular"
            >
              {getName(item)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReadOnlyList;
