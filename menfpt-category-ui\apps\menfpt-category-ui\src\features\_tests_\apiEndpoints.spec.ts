import { getEndpoints, isLocalEnvironment, API_CONFIG } from '../../util/apiEndpoints';
import * as envVarsManager from '../../util/envVarsManager';

jest.mock('../../util/envVarsManager', () => ({
  getEnvParamVal: jest.fn(),
}));

describe('apiEndpoints', () => {
  const originalLocation = window.location;

  beforeEach(() => {
    jest.clearAllMocks();
    
    Object.defineProperty(window, 'location', {
      value: {
        ...originalLocation,
        hostname: 'example.com', 
      },
      writable: true,
    });
  });

  afterAll(() => {
    Object.defineProperty(window, 'location', {
      value: originalLocation,
      writable: true,
    });
  });

  describe('isLocalEnvironment', () => {
    it('should return true when hostname is localhost', () => {
      window.location.hostname = 'localhost';
      expect(isLocalEnvironment()).toBe(true);
    });

    it('should return true when hostname is 127.0.0.1', () => {
      window.location.hostname = '127.0.0.1';
      expect(isLocalEnvironment()).toBe(true);
    });

    it('should return false for other hostnames', () => {
      window.location.hostname = 'example.com';
      expect(isLocalEnvironment()).toBe(false);
    });
  });

  describe('getEndpoints', () => {
    it('should return correct endpoints when URL does not include context path', () => {
      const mockBaseUrl = 'https://api.example.com/menfpt-category-bff';
      (envVarsManager.getEnvParamVal as jest.Mock).mockReturnValue(mockBaseUrl);
      
      const endpoints = getEndpoints();
      
      expect(endpoints).toEqual({
        view: `${mockBaseUrl}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.VIEW}`,
        download: `${mockBaseUrl}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.DOWNLOAD}`,
      });
      expect(envVarsManager.getEnvParamVal).toHaveBeenCalledWith(API_CONFIG.ENV_PARAM_KEY);
    });

    it('should replace context path with service path when URL includes context path', () => {
      const mockBaseUrl = `https://api.example.com${API_CONFIG.CONTEXT_PATH}`;
      const expectedBaseUrl = `https://api.example.com${API_CONFIG.SERVICE_PATH}`;
      (envVarsManager.getEnvParamVal as jest.Mock).mockReturnValue(mockBaseUrl);
      
      const endpoints = getEndpoints();
      
      expect(endpoints).toEqual({
        view: `${expectedBaseUrl}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.VIEW}`,
        download: `${expectedBaseUrl}/helpIcon/${API_CONFIG.HELP_ICON.ENDPOINTS.DOWNLOAD}`,
      });
      expect(envVarsManager.getEnvParamVal).toHaveBeenCalledWith(API_CONFIG.ENV_PARAM_KEY);
    });
  });
});




