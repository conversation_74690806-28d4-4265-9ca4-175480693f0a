import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import { app_store } from '../../../rtk/store';
import { usePeriodModalsContainer } from './periodModalsContainer';
import * as rtkUtilities from '../../../rtk/rtk-utilities';
import * as calendarUtils from '../../calendarServiceUtils';
import { appConstants } from '@ui/utils';

// Mock the modules
jest.mock('../../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('../../calendarServiceUtils', () => ({
  useCurrentQuarterNbr: jest.fn(),
}));

jest.mock('../../worksheetFilter/utils/quarterUtils', () => ({
  quarterNumberToLabel: jest.fn((qtr) => `Q${qtr % 100}`),
}));

// Test component to use the hook
const TestComponent: React.FC<{ worksheetDataLoaded: boolean }> = ({ worksheetDataLoaded }) => {
  const { periodCloseModal, periodLockedModal, isModalOpen } = usePeriodModalsContainer({ worksheetDataLoaded });
  
  return (
    <div>
      <div data-testid="modal-open">{isModalOpen ? 'true' : 'false'}</div>
      {periodCloseModal}
      {periodLockedModal}
    </div>
  );
};

describe('usePeriodModalsContainer', () => {
  const mockUseSelectorWrap = jest.mocked(rtkUtilities.useSelectorWrap);
  const mockUseCurrentQuarterNbr = jest.mocked(calendarUtils.useCurrentQuarterNbr);

  beforeEach(() => {
    // Mock localStorage
    Storage.prototype.getItem = jest.fn(() => null);
    Storage.prototype.setItem = jest.fn();
    
    // Set up default mocks
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });
    
    mockUseCurrentQuarterNbr.mockReturnValue(202403);
    
    jest.clearAllMocks();
  });

  test('renders without modal when worksheetDataLoaded is false', () => {
    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={false} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('false');
  });

  test('opens period close modal when quarter is open and not dismissed', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('true');
    expect(screen.getByTestId('period-close-modal')).toBeInTheDocument();
  });

  test('opens period locked modal when quarter is locked and not dismissed', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'locked',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('true');
    expect(screen.getByTestId('period-locked-modal')).toBeInTheDocument();
  });

  test('does not open modal when close alert is already dismissed', () => {
    const mockLocalStorageData = JSON.stringify({
      202403: {
        [appConstants.lsKeys.periodClose.closeAlertKey]: true,
      },
    });
    
    Storage.prototype.getItem = jest.fn(() => mockLocalStorageData);

    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('false');
  });

  test('does not open modal when locked alert is already dismissed', () => {
    const mockLocalStorageData = JSON.stringify({
      202403: {
        [appConstants.lsKeys.periodClose.lockedAlertKey]: true,
      },
    });
    
    Storage.prototype.getItem = jest.fn(() => mockLocalStorageData);

    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'locked',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('false');
  });

  test('handles corrupted localStorage data', () => {
    Storage.prototype.getItem = jest.fn(() => 'invalid json');
    
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('true');
  });

  test('saves dismissal to localStorage when modal is opened', () => {
    const setItemSpy = jest.spyOn(Storage.prototype, 'setItem');
    
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    // The hook should save to localStorage when modal opens
    expect(setItemSpy).toHaveBeenCalledWith(
      appConstants.lsKeys.periodClose.periodClose_LsKey,
      expect.stringContaining('202403')
    );
  });

  test('handles missing prevQuarterTabData', () => {
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return { data: null };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('false');
  });

  test('handles undefined currentQuarterNbr', () => {
    mockUseCurrentQuarterNbr.mockReturnValue(undefined as any);
    
    mockUseSelectorWrap.mockImplementation((key) => {
      switch (key) {
        case 'periodStatuses_rn':
          return { data: {} };
        case 'prevQuarterTab_rn':
          return {
            data: {
              lastQtrNbr: 202402,
              quarterStatus: 'open',
            },
          };
        default:
          return { data: null };
      }
    });

    render(
      <Router>
        <Provider store={app_store}>
          <TestComponent worksheetDataLoaded={true} />
        </Provider>
      </Router>
    );
    
    expect(screen.getByTestId('modal-open')).toHaveTextContent('false');
  });
});
