import React from 'react';
import { render, screen } from '@testing-library/react';
import NoDocumentsMessage from './NoDocumentsMessage';

describe('NoDocumentsMessage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders the component without crashing', () => {
    render(<NoDocumentsMessage />);
    expect(screen.getByText(/There are no documents available for download/i)).toBeInTheDocument();
  });

  it('displays the correct message text', () => {
    render(<NoDocumentsMessage />);
    const messageElement = screen.getByText('There are no documents available for download. Please upload a document to view.');
    expect(messageElement).toBeInTheDocument();
  });

  it('applies the correct CSS classes', () => {
    render(<NoDocumentsMessage />);
    const messageElement = screen.getByText('There are no documents available for download. Please upload a document to view.');
    expect(messageElement).toHaveClass('Sans', 'text-black', 'font-nunito', 'leading-6');
  });

  it('renders as a div element', () => {
    render(<NoDocumentsMessage />);
    const messageElement = screen.getByText('There are no documents available for download. Please upload a document to view.');
    expect(messageElement.tagName).toBe('DIV');
  });

  it('has the complete message text in a single element', () => {
    render(<NoDocumentsMessage />);
    const messageElements = screen.getAllByText(/documents available/i);
    expect(messageElements).toHaveLength(1);
  });
});
