import React, { useState, useCallback } from 'react';
import Card from '@albertsons/uds/molecule/Card';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useUploadFilePharmaMutation } from '../../server/Api/menfptCategoryAPI';
import { useEnvVariables } from '../../features/envVariables';
import { FileItem } from './uploadDocument.types';
import { validateFiles, readFileAsArrayBuffer, createUploadRequest } from './uploadDocument.utils';
import { useUploadDayValidation } from './uploadDocument.hooks';
import { 
  AlertComponents, 
  UploadDayWarning, 
  UploadSection, 
  // FileStatusList 
} from './uploadDocument.components';
import Documents from './rxforecastDocuments';

const UploadDocument = () => {
  const [filesUploaded, setFilesUploaded] = useState<FileItem[]>([]);
  const [alertOpen, setAlertOpen] = useState(false);
  const [successAlertOpen, setSuccessAlertOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [refreshDocuments, setRefreshDocuments] = useState(0);
  
  const { data: userInfo } = useSelectorWrap('userInfo_rn');
  const { data: displayDate } = useSelectorWrap('displayDate_rn');
  const [uploadFilePharma] = useUploadFilePharmaMutation();
  const { data: envVariables } = useEnvVariables();
  
  const { isUploadAllowed, allowedDaysMessage } = useUploadDayValidation(envVariables);

  // Callback to trigger documents refresh
  const triggerDocumentsRefresh = useCallback(() => {
    setRefreshDocuments(prev => prev + 1);
  }, []);

  const handleUploadResponse = (response: any, fileName: string): boolean => {
    if (response?.errors || !response?.data) {
      setFilesUploaded(prev =>
        prev.map(item =>
          item.file.name === fileName ? { ...item, status: 'inProgress', isLoading: false } : item
        )
      );
      return false;
    } else if (response?.data?.fileUploadToBlob?.success === false) {
      setFilesUploaded(prev =>
        prev.filter(item => item.file.name !== fileName)
      );
      return false;
    } else if (response?.data?.fileUploadToBlob?.success === true) {
      setFilesUploaded(prev =>
        prev.map(item =>
          item.file.name === fileName ? { ...item, status: 'success', isLoading: false } : item
        )
      );
      return true;
    } else {
      setFilesUploaded(prev =>
        prev.map(item =>
          item.file.name === fileName ? { ...item, status: 'inProgress', isLoading: false } : item
        )
      );
      return false;
    }
  };

  const setUploadAlerts = (allUploadsSuccessful: boolean): void => {
    if (allUploadsSuccessful) {
      setSuccessAlertOpen(true);
      setAlertOpen(false);
      // Trigger documents refresh after successful upload
      triggerDocumentsRefresh();
    } else {
      setAlertMessage('Some files failed to upload. Please check the file status and try again.');
      setAlertOpen(true);
      setSuccessAlertOpen(false);
    }
  };

  const handleUploadError = (error: any): void => {
    setFilesUploaded(prev =>
      prev.map(item => ({ ...item, status: 'inProgress', isLoading: false }))
    );
    setAlertMessage('Some files failed to upload. Please check the file status and try again.');
    setAlertOpen(true);
    setSuccessAlertOpen(false);
  };

  const handleValidationError = (message: string): void => {
    setAlertMessage(message);
    setAlertOpen(true);
    setSuccessAlertOpen(false);
  };

  const handleFileUpload = async (files: File[] | FileList): Promise<void> => {
    if (!isUploadAllowed) {
      setAlertMessage('Upload is not allowed on the current day. Please check the upload schedule.');
      setAlertOpen(true);
      setSuccessAlertOpen(false);
      return;
    }
    
    const { fileArr, oversized } = validateFiles(files);
    setFilesUploaded(fileArr);

    if (oversized) {
      setAlertMessage('The file exceeds the 25MB limit. Try again with a smaller file.');
      setAlertOpen(true);
      setSuccessAlertOpen(false);
      return;
    }

    if (fileArr.length === 0) return;

    setAlertOpen(false);
    setIsUploading(true);
    let allUploadsSuccessful = true;

    try {
      for (let i = 0; i < fileArr.length; i++) {
        const file = fileArr[i].file;
        const fileContent = await readFileAsArrayBuffer(file);
        const filterArgs = createUploadRequest(file, fileContent, userInfo.userName, displayDate.fiscalWeekNumber);
        const response = await uploadFilePharma(filterArgs);
        const uploadSuccess = handleUploadResponse(response, file.name);
        
        if (!uploadSuccess) {
          allUploadsSuccessful = false;
        }
      }

      setUploadAlerts(allUploadsSuccessful);
    } catch (error) {
      handleUploadError(error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <>
      <AlertComponents
        alertOpen={alertOpen}
        successAlertOpen={successAlertOpen}
        alertMessage={alertMessage}
        onCloseAlert={() => setAlertOpen(false)}
        onCloseSuccessAlert={() => setSuccessAlertOpen(false)}
      />

      <div className="pt-5 ml-5">
        <Card className="w-[997px]">
          <Card.Header>
            <div className="header text-[#2b303c] font-nunito text-lg font-extrabold leading-6">
              Upload Rx Documents
            </div>
          </Card.Header>
          <Card.Content>
            {!isUploadAllowed ? (
              <UploadDayWarning allowedDaysMessage={allowedDaysMessage} />
            ) : (
              <>
                <UploadSection onFileUpload={handleFileUpload} isUploading={isUploading} onValidationError={handleValidationError} />
                {/* <FileStatusList filesUploaded={filesUploaded} /> */}
              </>
            )}
          </Card.Content>
        </Card>
         <Documents refreshTrigger={refreshDocuments} />
      </div>
    </>
  );
};

export default UploadDocument;
