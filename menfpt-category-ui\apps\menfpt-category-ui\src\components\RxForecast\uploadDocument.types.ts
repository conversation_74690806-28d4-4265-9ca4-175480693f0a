export type UploadStatusType = 'success' | 'inProgress' | 'replace' | 'uploaded';

export interface FileItem {
  file: File;
  status: UploadStatusType;
  isLoading?: boolean;
}

export interface UploadValidationResult {
  fileArr: FileItem[];
  oversized: boolean;
  oversizedFiles: string[];
}

export interface UploadRequest {
  query: any;
  variables: {
    fileUploadToBlob: {
      fileName: string;
      fileContent?: number[];
      uploadDataPersistenceEnabled: boolean;
      user: string;
      fiscalWeekNbr: number;
    };
  };
} 