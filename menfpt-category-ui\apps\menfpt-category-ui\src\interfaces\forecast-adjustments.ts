interface ForecastChangeLogReq {
    keyAttributeName: string;
    keyAttributeValue: string[];
    divisionId: string[];
    bannerId?: string[];
}

interface AdjustedFields {
    fieldName?: string;
    oldValue?: string;
    newValue?: string;
}


interface UpdatedMetrics {
    fiscalWeekNbrs?: string; 
    keyAttributeName?: string; 
    keyAttributeValue?: string; 
    reason?: string; 
    comment?: string; 
    adjustedFields?: AdjustedFields[]; 
    bannerId?: string[];
}

interface ForecastChangeLog {
    updatedTimestamp?: string;
    updatedBy?: string;
    editedColumns?: string;
    updatedMetrics?: UpdatedMetrics[];
}


export {
    ForecastChangeLogReq,
    AdjustedFields,
    UpdatedMetrics,
    ForecastChangeLog
} 