import '@testing-library/jest-dom';
import { certifiedIcon, closeIcon } from './periodIcons';
import React from 'react';
import { render } from '@testing-library/react';

describe('periodIcons', () => {
  it('renders certifiedIcon as a valid SVG', () => {
    const { container } = render(certifiedIcon());
    expect(container.querySelector('svg')).toBeInTheDocument();
    expect(container.querySelector('path')).toBeInTheDocument();
  });

  it('renders closeIcon as a valid SVG', () => {
    const { container } = render(closeIcon());
    expect(container.querySelector('svg')).toBeInTheDocument();
    expect(container.querySelector('path')).toBeInTheDocument();
  });
}); 