import Spinner from '@albertsons/uds/molecule/Spinner';
import { useEffect, useState } from 'react';

import { useDispatch } from 'react-redux';
import { setSaveAdjustmentApiStatus } from '../components/ForecastEdit/editForecast.slice';
import { createAdjustmentApiBody, saveAdjustment } from '../components/ForecastEdit/editForecastHelper';
import UdsTable from '../components/udsTable';
import { createQtrPayloadForCalendar } from '../features/calendarServiceUtils';
import { worksheetFilterConfig } from '../features/worksheetFilter/worksheetFilterConfig';
import { useExtractBannerId } from '../util/filterUtils';

import { handlePrevQuarterPeriodClose } from '../features/periodClose/periodClose.flags';
import { usePeriodCloseEffect } from '../features/periodClose/usePeriodCloseEffect';
import { Adjustment, Week } from '../interfaces/edit-forecast-adjustments';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { useGetDisplayDateQuery, useGetWorksheetTableDataMutation, useSaveAdjustmentEditsMutation } from '../server/Api/menfptCategoryAPI';
import { adjustmentWorksheetQuery } from '../server/Query/adjustmentWorksheetQuery';
import { setAdjustmentWorkSheetFilter } from '../server/Reducer/menfpt-category.slice';
import { usePeriodModalsContainer } from '../features/periodClose/modals/periodModalsContainer';
import { setDataForQrtrDisplayedInTable } from '../components/quarterDetails.slice';
import ForecastEdit from '../components/ForecastEdit/editForecastAdjustment';



const RESET_REASON = "Week Reset";
const RESET_COMMENT = "Week Reset";

const TableContainer: React.FC<any> = ({selectedQuarter }) => {
  const dispatch = useDispatch();
  const [getWorkSheetTableData] = useGetWorksheetTableDataMutation();
  const [saveAdjustmentEdits] = useSaveAdjustmentEditsMutation();
  const [workSheetData, setWorkSheetData] = useState<Map<string, any>>(new Map());

  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: displayDate } = useSelectorWrap('displayDate_rn');
  const { data: saveAdjustmentApiStatus } = useSelectorWrap('saveAdjustmentApiStatus_rn');
  const { data: userInfo } = useSelectorWrap('userInfo_rn');
  const { data: editAdjustmentPermission} = useSelectorWrap('editAdjustmentPermission_rn');
  const { data:previousTabs } = useSelectorWrap('prevQuarterTab_rn');
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [alert, setAlert] = useState({ success: false, error: false });
  const [selectedEditWeek, setSelectedEditWeek] = useState<number | null>(null);
  // Track if initial data has been loaded
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  // Track the last applied filters to prevent duplicate API calls
  const [lastAppliedFilters, setLastAppliedFilters] = useState<any>(null);
  // Track if we're currently processing a save adjustment
  const [processingSaveAdjustment, setProcessingSaveAdjustment] = useState(false);
  // Track if we're currently fetching data to prevent duplicate calls
  const [isFetching, setIsFetching] = useState(false);

  const [calendarApiPayload, setCalendarApiPayload] = useState<any | null>(null);

  const bannerId = useExtractBannerId();
  const { data: workSheetFilterList } = useSelectorWrap('workSheetFilterList_rn');

  const { data: calendarApiResp } = useGetDisplayDateQuery(calendarApiPayload?.payload, { skip: !calendarApiPayload });



//Returns the quarter number to be displayed in the table
  const getQrtrNbrDisplayedInTable = () => selectedQuarter||appliedFilters.timeframe?.num || displayDate?.fiscalQuarterNumber;
  const worksheetDataLoaded = workSheetData && workSheetData.size > 0;
  const { periodCloseModal, periodLockedModal } = usePeriodModalsContainer({ worksheetDataLoaded });
  
  dispatch(setDataForQrtrDisplayedInTable(calendarApiResp));
  const lastQuarterNbr = usePeriodCloseEffect({
    calendarApiResp,
    calendarApiPayload,
    dispatch,
    qrtrNbrDisplayedInTable: getQrtrNbrDisplayedInTable(),    
  });

  useEffect(() => {
    if (lastQuarterNbr) {
      //Period close:  Trigger calender API to fetch detailed calendar data for prev quarter
      setCalendarApiPayload({
        payload: createQtrPayloadForCalendar(lastQuarterNbr),
        type: 'lastQrtr_periodClose'          
      });
    }
  }, [lastQuarterNbr]);

  useEffect(() => {
    handlePrevQuarterPeriodClose(calendarApiResp, calendarApiPayload, dispatch);
  }, [calendarApiPayload, calendarApiResp, dispatch]);


  // Single effect to handle all data fetching scenarios
  useEffect(() => {

    // Case 1: Save Adjustment was clicked
    if (saveAdjustmentApiStatus === true && !processingSaveAdjustment) {
      console.log('Processing save adjustment');
      setProcessingSaveAdjustment(true);
      dispatch(setSaveAdjustmentApiStatus(false));
      setIsLoading(true);
      fetchWorksheetData();
      return;
    }

    // Reset processing flag when saveAdjustmentApiStatus is false
    if (saveAdjustmentApiStatus === false && processingSaveAdjustment) {
      
      setProcessingSaveAdjustment(false);
    }

    // Case 2: Initial load or filter change
    if (appliedFilters?.filterPg === worksheetFilterConfig.lsKeyAdjustmentPg &&
      (appliedFilters.department || appliedFilters.desk)) {
      // Check if filters have changed to prevent duplicate calls
      const currentFiltersString = JSON.stringify({
        department: appliedFilters.department,
        desk: appliedFilters.desk,
        division: appliedFilters.division,
        category: appliedFilters.category,
        timeframe: appliedFilters.timeframe,
        selectedSm: appliedFilters.selectedSm,
      });

      const lastFiltersString = lastAppliedFilters ? JSON.stringify(lastAppliedFilters) : null;

      // console.log('Filter comparison', {
      //   filtersChanged: currentFiltersString !== lastFiltersString,
      //   initialLoad: !initialDataLoaded
      // });

      // Only fetch if filters have changed or this is the initial load
      if (currentFiltersString !== lastFiltersString) {
       
        setLastAppliedFilters({
          department: appliedFilters.department,
          desk: appliedFilters.desk,
          division: appliedFilters.division,
          category: appliedFilters.category,
          timeframe: appliedFilters.timeframe,
          selectedSm: appliedFilters.selectedSm
        });

        // If this is the first load, mark as initialized
        if (!initialDataLoaded) {
          console.log('Initial data load');
          setInitialDataLoaded(true);
        }

        setIsLoading(true);
        fetchWorksheetData();
      }
      if(selectedQuarter){
        console.log(selectedQuarter,"IN USEEFFECT")
         setIsLoading(true);
        fetchWorksheetData();
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appliedFilters, saveAdjustmentApiStatus, processingSaveAdjustment, selectedQuarter]);



  // Function to fetch worksheet data
  const fetchWorksheetData = () => {
    // Prevent duplicate API calls
    if (isFetching) {
      console.log('Already fetching data, skipping duplicate call');
      return;
    }

    console.log('Fetching worksheet data', new Date().toISOString());
    setIsFetching(true);

    let deptIds: string[] = [];
    if (Array.isArray(appliedFilters?.department)) {
      deptIds = appliedFilters.department.map((dept: { num: string | number }) => dept.num.toString());
    } else if (appliedFilters?.department?.num) {
      deptIds = [appliedFilters.department.num.toString()];
    } else if (appliedFilters?.desk?.num) {
      const filterList = workSheetFilterList?.smicData || [];
      deptIds = filterList
        .filter((item: { deskId: number }) => item.deskId === appliedFilters.desk.num)
        .map((i: { deptId: string }) => i.deptId);
    }

    const worksheetTableData = getWorksheetTable(deptIds, selectedQuarter);

    // Determine the quarter number being requested for worksheet data
     const qrtrNbrDisplayedInTable = getQrtrNbrDisplayedInTable();
    // (No longer needed here, handled in getWorksheetTable)
    worksheetTableData.then((response) => {
      setIsLoading(false);
      setIsFetching(false);
      if ('data' in response) {
       
        const formattedTableData = formatTableData(response.data?.getAdjustmentWorksheetData?.adjustmentWorksheetData);
        setWorkSheetData(formattedTableData);
        const plainObjectData = Object.fromEntries(formattedTableData);
        const quarterToSend = selectedQuarter || displayDate?.fiscalQuarterNumber;
        dispatch(setAdjustmentWorkSheetFilter({ ...plainObjectData, quarterNbr: quarterToSend })); // convert map to serializable data

        //Period close:  Trigger calender API to fetch detailed calendar data for a quarter
        setCalendarApiPayload({
          payload: createQtrPayloadForCalendar(qrtrNbrDisplayedInTable),
          type: 'quarterDisplayedInTable_periodClose'          
        });
    

      }
    }).catch(error => {
      console.error('Error fetching worksheet data:', error);
      setIsLoading(false);
      setIsFetching(false);
    });
  };

  const getYearFromQuarter = (quarterNbr) => parseInt(String(quarterNbr).slice(0, 4), 10);

  const getFiscalParameters = () => {
    if (selectedQuarter===previousTabs.lastQtrNbr) {
      return { 
        currentFiscalWeekNbr: previousTabs.lastQtrWeekNbr,
        currentFiscalYearNbr: getYearFromQuarter(selectedQuarter),
        quarterEndingDate:previousTabs.fiscalQuarterEndDate
      };
    }
    // If timeframe is defined and has a num property, return empty object
    if (appliedFilters.timeframe?.num) {
      return {};
    }
    return {
      currentFiscalPeriodNbr: displayDate?.fiscalPeriodNumber,
      currentFiscalWeekNbr: displayDate?.fiscalWeekNumber,
      currentFiscalYearNbr: displayDate?.fiscalYearNumber,
    };
  };

  const getWorksheetTable = async (deptIds: string[], selectedQuarterArg) => {
    // Use the selectedQuarterArg directly
    // const quarterNbr = selectedQuarterArg || appliedFilters.timeframe?.num || displayDate?.fiscalQuarterNumber;
    // console.log(quarterNbr, "Quarter used for API call");
    const quarterNbr = getQrtrNbrDisplayedInTable();

    const filterArgs = {
      query: adjustmentWorksheetQuery,
      variables: {
        adjustmentWorksheetReq: {
          ...getFiscalParameters(),
          quarterNbr: quarterNbr,
          deptIds: deptIds,
          divisionIds: appliedFilters.division.map((item: { num: number | string }) =>
            item.num.toString()
          ),
          smicCategoryIds: appliedFilters?.category.map((item: { num: number | string }) =>
            item.num.toString()
          ),
          ...(bannerId && { bannerId })
        },
      },
    };
    const getFilterList = await getWorkSheetTableData(filterArgs);
    return getFilterList;
  };

  const formatTableData = (workSheetData: any[] = []) => {
    const tableData: Map<string, any[]> = new Map();
    if (workSheetData && workSheetData.length > 0) {
        workSheetData.forEach((item: any) => {
          const subItem: any[] = tableData.get(item.mainRow) || [];
          subItem.push(item);
          tableData.set(item.mainRow, subItem);
        });
        tableData.forEach(item => {
            if(item[0].subRow === 'Last year actual') item.push(item.shift());
        });
      }
      return tableData;
  }

  const getBaseForecastObjectForWeek = (weekNumber: string) => {
    const baseForecast = workSheetData.get(weekNumber).find((item: any) => item.subRow === 'Base');
    const weekData = {
      line1PublicToSalesNbr: baseForecast.line1PublicToSalesNbr,
      line1PublicToSalesPct: baseForecast.line1PublicToSalesPct,
      line5BookGrossProfitNbr: baseForecast.line5BookGrossProfitNbr,
      line5BookGrossProfitPct: baseForecast.line5BookGrossProfitPct,
      line5MarkDownsNbr: baseForecast.line5MarkDownsNbr,
      line5MarkDownsPct:  baseForecast.line5MarkDownsPct,
      line5ShrinkNbr: baseForecast.line5ShrinkNbr,
      line5ShrinkPct: baseForecast.line5ShrinkPct,
      line6SuppliesPackagingNbr: baseForecast.line6SuppliesPackagingNbr,
      line7RetailsAllowancesNbr: baseForecast.line7RetailsAllowancesNbr,
      line7RetailsSellingAllowancesNbr: baseForecast.line7RetailsSellingAllowancesNbr,
      line7RetailsNonSellingAllowancesNbr: baseForecast.line7RetailsNonSellingAllowancesNbr,
    }
    const resetWeekObject: Week =
      {
        fiscalWeekNbr: baseForecast.fiscalWeekNbr,
        editedColumns: 'reset',
        previousAggregatedData: weekData,
        newAggregatedData: weekData,
        reason: RESET_REASON,
        comment: RESET_COMMENT
    }
    return resetWeekObject;
  }

  const resetWeek = async (fiscalWeekNbr) => {
    console.log("fiscalWeekNbr", fiscalWeekNbr);
    const weekData = getBaseForecastObjectForWeek(fiscalWeekNbr);
    const apiBody: Adjustment = createAdjustmentApiBody(appliedFilters, userInfo, [weekData], bannerId);
    apiBody.isReset = true;
    await saveAdjustment(apiBody, dispatch, saveAdjustmentEdits);
  }
  return (
    <div className="bg-gray-206">
      {/* Only show modals after worksheet data is loaded */}
      {(workSheetData && workSheetData.size > 0) && (
        <>
          {periodLockedModal}
          {periodCloseModal}
        </>
      )}
      
      { isLoading ? <Spinner /> : <UdsTable 
      data={workSheetData} 
      currentWeek={displayDate?.fiscalWeekNumber} 
      resetWeek={resetWeek} 
      resetPermission={editAdjustmentPermission?.disabled}
      onEditForecast={(weekNumber) => { 
        console.log(weekNumber, "weekNumber");
        setSelectedEditWeek(weekNumber); 
        setIsOpen(true); 
        setAlert({success: false, error: false});
      }}
      />}
      <ForecastEdit
    isOpen={isOpen}
    setOpen={setIsOpen}
    historyModalOpen={() => undefined}
    selectedEditWeek={selectedEditWeek}
  />
    </div>
  );
};

export default TableContainer;