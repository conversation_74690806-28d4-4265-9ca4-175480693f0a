{"name": "menfpt-category-ui-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/menfpt-category-ui-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nrwl/cypress:cypress", "options": {"cypressConfig": "apps/menfpt-category-ui-e2e/cypress.config.ts", "devServerTarget": "menfpt-category-ui:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "menfpt-category-ui:serve:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/menfpt-category-ui-e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["menfpt-category-ui"]}