import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import ModalContent from './ModalContent';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

// Mock all child components
jest.mock('./ModalTimeframeSection', () => (props: any) => (
  <div data-testid="modal-timeframe-section" data-selected-timeframe={props.selectedTimeframe?.name || ''}>
    Timeframe Section
  </div>
));

jest.mock('./ModalDivisionSection', () => (props: any) => (
  <div data-testid="modal-division-section" data-divisions-count={props.divisions?.length || 0}>
    Division Section
  </div>
));

jest.mock('./ModalDepartmentDeskSection', () => (props: any) => (
  <div 
    data-testid="modal-department-desk-section" 
    data-departments-count={props.filteredDepartments?.length || 0}
    data-desks-count={props.filteredDesks?.length || 0}
  >
    Department Desk Section
  </div>
));

jest.mock('./ModalDeptRoleCascadeSection', () => (props: any) => (
  <div data-testid="modal-dept-role-cascade-section">
    Dept Role Cascade Section
  </div>
));

jest.mock('./ModalCategorySection', () => (props: any) => (
  <div 
    data-testid="modal-category-section" 
    data-categories-count={props.categories?.length || 0}
    data-display-desk={props.isDisplayDesk ? 'true' : 'false'}
  >
    Category Section
  </div>
));

jest.mock('../period/periodSelector', () => (props: any) => (
  <div 
    data-testid="period-selector"
    data-selected-periods={props.selectedPeriods?.length || 0}
  >
    Period Selector
  </div>
));

// Update hook mocks to return expected shapes
jest.mock('../../worksheetFilterRouteUtils', () => {
  const actual = jest.requireActual('../../worksheetFilterRouteUtils');
  return {
    ...actual,
    useDeskDisplay: jest.fn(() => true),
    extractCurrentRoute: jest.fn(() => 'department'),
    default: {
      ...actual,
      useDeskDisplay: jest.fn(() => true),
      extractCurrentRoute: jest.fn(() => 'department'),
    }
  };
});

jest.mock('./useModalContentState', () => {
  const { mockCategories } = require('./ModalContent.spec');
  return {
    useModalContentState: jest.fn(() => ({
      filteredDepartments: [{ name: 'Filtered Dept 1', value: 'fdept1', num: 1 }],
      filteredDesks: [{ name: 'Filtered Desk 1', value: 'fdesk1', num: 1 }],
      categories: mockCategories, // Ensure categories is set to mockCategories
      isSearchFocused: false,
      setIsSearchFocused: jest.fn(),
      isSuggestionsVisible: false,
      setIsSuggestionsVisible: jest.fn(),
      searchContainerRef: { current: null },
      getSearchHandler: jest.fn(),
      getSearchValue: jest.fn(),
      searchQueryDepartment: '',
      searchQueryDeptRole: '',
      searchQueryDesk: '',
      handleDepartmentSearch: jest.fn(),
      handleDeskSearch: jest.fn(),
    }))
  };
});

jest.mock('../../../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(() => ({ data: [{ name: 'Department 1', value: 'dept1', num: 1 }] }))
}));

import { useDeskDisplay } from '../../worksheetFilterRouteUtils';
import { useModalContentState } from './useModalContentState';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';

const mockUseDeskDisplay = useDeskDisplay as jest.MockedFunction<typeof useDeskDisplay>;
const mockUseModalContentState = useModalContentState as jest.MockedFunction<typeof useModalContentState>;
const mockUseSelectorWrap = useSelectorWrap as jest.MockedFunction<typeof useSelectorWrap>;

// Create a mock store
const mockStore = configureStore({
  reducer: {
    test: (state = {}) => state
  }
});

// Mock data
const mockDivisions: DropdownType[] = [
  { name: 'Division 1', num: 1 },
  { name: 'Division 2', num: 2 }
];

const mockDesks: DropdownType[] = [
  { name: 'Desk 1', num: 1 },
  { name: 'Desk 2', num: 2 }
];

const mockCategories: DropdownType[] = [
  { name: 'Category 1', num: 1 },
  { name: 'Category 2', num: 2 }
];

const mockSelectedDivision: DropdownType[] = [
  { name: 'Division 1', num: 1 }
];

const mockSelectedDepartment: DropdownType = { name: 'Department 1', num: 1 };
const mockSelectedDesk: DropdownType = { name: 'Desk 1', num: 1 };
const mockSelectedTimeframe: DropdownType = { name: 'Q1 2024', num: 1 };
const mockSelectedPeriods: DropdownType[] = [{ name: 'Period 1', num: 1 }];
const mockSelectedWeeks = [{ periodNum: 1, weekNum: 1 }];

const defaultProps = {
  shouldDisplayTimeFrame: false,
  divisions: mockDivisions,
  selectedDivision: mockSelectedDivision,
  desks: mockDesks,
  selectedDepartment: mockSelectedDepartment,
  selectedDesk: mockSelectedDesk,
  selectedTimeframe: mockSelectedTimeframe,
  selectedPeriods: mockSelectedPeriods,
  selectedWeeks: mockSelectedWeeks,
  categories: mockCategories,
  isDisplayDeptRoleCascade: false,
  activeTabInFilter: ['department'],
  handleDivisionChange: jest.fn(),
  handleDepartmentChange: jest.fn(),
  handleDeskChange: jest.fn(),
  handleTimeframeChange: jest.fn(),
  handlePeriodChange: jest.fn(),
  handleWeeksChange: jest.fn()
};

const mockModalContentState = {
  filteredDepartments: [{ name: 'Filtered Dept 1', value: 'fdept1', num: 1 }],
  filteredDesks: [{ name: 'Filtered Desk 1', value: 'fdesk1', num: 1 }],
  isSearchFocused: false,
  setIsSearchFocused: jest.fn(),
  isSuggestionsVisible: false,
  setIsSuggestionsVisible: jest.fn(),
  searchContainerRef: { current: null },
  getSearchHandler: jest.fn(),
  getSearchValue: jest.fn()
};

const renderWithProviders = (ui: React.ReactElement, props = {}) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        {React.cloneElement(ui, { ...defaultProps, ...props })}
      </BrowserRouter>
    </Provider>
  );
};

describe('ModalContent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDeskDisplay.mockReturnValue(true);
    mockUseSelectorWrap.mockReturnValue({
      data: [{ name: 'Department 1', value: 'dept1', num: 1 }]
    });
  });

  it('renders division, department desk, and category sections', () => {
    renderWithProviders(<ModalContent {...defaultProps} />);
    expect(screen.getByTestId('modal-division-section')).toBeInTheDocument();
    expect(screen.getByTestId('modal-department-desk-section')).toBeInTheDocument();
    expect(screen.getByTestId('modal-category-section')).toBeInTheDocument();
  });

  it('passes correct props to ModalDivisionSection', () => {
    renderWithProviders(<ModalContent {...defaultProps} />);
    const divisionSection = screen.getByTestId('modal-division-section');
    expect(divisionSection).toHaveAttribute('data-divisions-count', '2');
  });

  it('passes correct props to ModalDepartmentDeskSection', () => {
    renderWithProviders(<ModalContent {...defaultProps} />);
    const deptDeskSection = screen.getByTestId('modal-department-desk-section');
    expect(deptDeskSection).toHaveAttribute('data-departments-count', '1');
    expect(deptDeskSection).toHaveAttribute('data-desks-count', '1');
  });
  it('updates desk display flag when useDeskDisplay returns false', () => {
    mockUseDeskDisplay.mockReturnValue(false);
    renderWithProviders(<ModalContent {...defaultProps} />);
    const categorySection = screen.getByTestId('modal-category-section');
    expect(categorySection).toHaveAttribute('data-display-desk', 'false');
  });
});
