import Button from '@albertsons/uds/molecule/Button';

export const PeriodLockedModal = ({
  onClose,
}: {
  onClose: () => void;
}) => (
  <div className="right-[15.375rem] inline-flex flex-col absolute items-center gap-6 p-4 rounded-2xl top-[18.75rem] bg-[#f7f8fa]">
    <div className="flex flex-col items-center gap-4">
      <div className="w-[688px] text-[#2b303c] text-center font-['Nunito'] text-[1.75rem] font-bold leading-9">
        Attention, please read this carefully!
      </div>
      <div className="w-[688px] text-[#2b303c] text-center font-['Nunito'] text-xl leading-7">
        Q1 FY2025 has been closed for any edits and the metrics are available
        only to view
      </div>
    </div>
    <div className="flex justify-center items-start gap-4 py-2 px-0 h-[7.5rem]">
      <Button
        className="flex flex-col justify-center items-center gap-2.5 self-stretch py-2 px-4 h-12 rounded-lg bg-[#1b6ebb] label Sans text-white text-center font-['Nunito'] font-semibold leading-5"
        variant="primary"
        onClick={onClose}
      >
        OK, I understand
      </Button>
    </div>
  </div>
);
