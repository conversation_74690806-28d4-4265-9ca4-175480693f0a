import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock FileUploadBox before importing the component that uses it
jest.mock('./FileUploadBox', () => {
  return function FileUploadBox({ onFileUpload }: any) {
    return (
      <div data-testid="file-upload-box">
        <input
          data-testid="file-input"
          type="file"
          accept=".xlsx"
          onChange={(e) => onFileUpload && onFileUpload(e.target.files)}
        />
        <div data-testid="upload-text">Drag and drop document or browse</div>
        <div data-testid="helper-text">Only .xlsx files are allowed. 25 mb maximum file size</div>
      </div>
    );
  };
});

import {
  AlertComponents,
  UploadDayWarning,
  UploadSection,
} from './uploadDocument.components';
jest.mock('@albertsons/uds/molecule/Alert', () => {
  return ({ isOpen, variant, size, children }: any) => (
    isOpen ? (
      <div data-testid={`alert-${variant}`} data-size={size}>
        {children}
      </div>
    ) : null
  );
});

jest.mock('@albertsons/uds/molecule/FileUpload', () => ({
  FileUploadStatus: ({ 
    id, 
    showProgress, 
    fileName, 
    imageUrl, 
    fileSize, 
    uploadStatus, 
    fileType 
  }: any) => (
    <div data-testid={`file-upload-status-${id}`}>
      <div data-testid="file-name">{fileName}</div>
      <div data-testid="file-size">{fileSize}</div>
      <div data-testid="upload-status">{uploadStatus}</div>
      <div data-testid="file-type">{fileType}</div>
      <div data-testid="image-url">{imageUrl}</div>
      {showProgress && <div data-testid="progress-indicator">Loading...</div>}
    </div>
  ),
}));

jest.mock('@albertsons/uds/molecule/Spinner', () => {
  return function Spinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

jest.mock('./rxforecastAlertMessage', () => {
  return function RxForecastAlertMessage() {
    return <div data-testid="rx-forecast-alert-message">RxForecast Alert Message</div>;
  };
});

jest.mock('./rxforecastAlerts', () => {
  return function RxForecastAlerts({ title, message, type, open, onClose }: any) {
    return open ? (
      <div data-testid={`rx-forecast-alert-${type}`}>
        <div data-testid="alert-title">{title}</div>
        <div data-testid="alert-message">{message}</div>
        <button data-testid="alert-close" onClick={onClose}>
          Close
        </button>
      </div>
    ) : null;
  };
});

// Mock URL.createObjectURL for FileStatusList tests
global.URL.createObjectURL = jest.fn(() => 'mocked-blob-url');

describe('uploadDocument.components', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AlertComponents', () => {
    const defaultProps = {
      alertOpen: false,
      successAlertOpen: false,
      alertMessage: '',
      onCloseAlert: jest.fn(),
      onCloseSuccessAlert: jest.fn(),
    };

    it('renders nothing when no alerts are open', () => {
      const { container } = render(<AlertComponents {...defaultProps} />);
      expect(container.firstChild).toBeInTheDocument();
      expect(screen.queryByTestId(/rx-forecast-alert-/)).not.toBeInTheDocument();
    });

    it('renders error alert when alertOpen is true', () => {
      const props = {
        ...defaultProps,
        alertOpen: true,
        alertMessage: 'Upload failed message',
      };

      render(<AlertComponents {...props} />);

      expect(screen.getByTestId('rx-forecast-alert-error')).toBeInTheDocument();
      expect(screen.getByTestId('alert-title')).toHaveTextContent('Document upload fail!');
      expect(screen.getByTestId('alert-message')).toHaveTextContent('Upload failed message');
      expect(screen.getByTestId('alert-close')).toBeInTheDocument();
    });

    it('renders success alert when successAlertOpen is true', () => {
      const props = {
        ...defaultProps,
        successAlertOpen: true,
      };

      render(<AlertComponents {...props} />);

      expect(screen.getByTestId('rx-forecast-alert-success')).toBeInTheDocument();
      expect(screen.getByTestId('alert-title')).toHaveTextContent('Document successfuly uploaded!');
      expect(screen.getByTestId('alert-message')).toHaveTextContent('The uploaded file is available for download below.');
      expect(screen.getByTestId('alert-close')).toBeInTheDocument();
    });

    it('renders both alerts when both are open', () => {
      const props = {
        ...defaultProps,
        alertOpen: true,
        successAlertOpen: true,
        alertMessage: 'Error message',
      };

      render(<AlertComponents {...props} />);

      expect(screen.getByTestId('rx-forecast-alert-error')).toBeInTheDocument();
      expect(screen.getByTestId('rx-forecast-alert-success')).toBeInTheDocument();
    });

    it('calls onCloseAlert when error alert close button is clicked', () => {
      const onCloseAlert = jest.fn();
      const props = {
        ...defaultProps,
        alertOpen: true,
        alertMessage: 'Error message',
        onCloseAlert,
      };

      render(<AlertComponents {...props} />);

      fireEvent.click(screen.getByTestId('alert-close'));
      expect(onCloseAlert).toHaveBeenCalledTimes(1);
    });

    it('calls onCloseSuccessAlert when success alert close button is clicked', () => {
      const onCloseSuccessAlert = jest.fn();
      const props = {
        ...defaultProps,
        successAlertOpen: true,
        onCloseSuccessAlert,
      };

      render(<AlertComponents {...props} />);

      fireEvent.click(screen.getByTestId('alert-close'));
      expect(onCloseSuccessAlert).toHaveBeenCalledTimes(1);
    });

    it('handles empty alert message', () => {
      const props = {
        ...defaultProps,
        alertOpen: true,
        alertMessage: '',
      };

      render(<AlertComponents {...props} />);

      expect(screen.getByTestId('alert-message')).toHaveTextContent('');
    });

    it('handles long alert messages', () => {
      const longMessage = 'This is a very long error message that should be displayed properly in the alert component without breaking the layout or functionality';
      const props = {
        ...defaultProps,
        alertOpen: true,
        alertMessage: longMessage,
      };

      render(<AlertComponents {...props} />);

      expect(screen.getByTestId('alert-message')).toHaveTextContent(longMessage);
    });
  });

  describe('UploadDayWarning', () => {
    it('renders warning message with allowed days', () => {
      const allowedDaysMessage = 'Monday, Wednesday and Friday';
      
      render(<UploadDayWarning allowedDaysMessage={allowedDaysMessage} />);

      expect(screen.getByTestId('alert-informational')).toBeInTheDocument();
      expect(screen.getByText(
        `You can only upload the RX forecast file on ${allowedDaysMessage}, please visit back on these days.`
      )).toBeInTheDocument();
    });

    it('renders with empty allowed days message', () => {
      render(<UploadDayWarning allowedDaysMessage="" />);

      expect(screen.getByTestId('alert-informational')).toBeInTheDocument();
      expect(screen.getByText(
        'You can only upload the RX forecast file on , please visit back on these days.'
      )).toBeInTheDocument();
    });

    it('renders with single day message', () => {
      const allowedDaysMessage = 'Monday';
      
      render(<UploadDayWarning allowedDaysMessage={allowedDaysMessage} />);

      expect(screen.getByText(
        `You can only upload the RX forecast file on ${allowedDaysMessage}, please visit back on these days.`
      )).toBeInTheDocument();
    });

    it('renders with multiple days message', () => {
      const allowedDaysMessage = 'Monday, Tuesday, Wednesday, Thursday and Friday';
      
      render(<UploadDayWarning allowedDaysMessage={allowedDaysMessage} />);

      expect(screen.getByText(
        `You can only upload the RX forecast file on ${allowedDaysMessage}, please visit back on these days.`
      )).toBeInTheDocument();
    });

    it('has correct alert properties', () => {
      render(<UploadDayWarning allowedDaysMessage="Monday" />);

      const alert = screen.getByTestId('alert-informational');
      expect(alert).toHaveAttribute('data-size', 'medium');
    });
  });

  describe('UploadSection', () => {
    const mockOnFileUpload = jest.fn();

    beforeEach(() => {
      mockOnFileUpload.mockClear();
    });

    it('renders all components correctly', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      expect(screen.getByTestId('rx-forecast-alert-message')).toBeInTheDocument();
      expect(screen.getByText('Add document')).toBeInTheDocument();
      expect(screen.getByTestId('file-upload-box')).toBeInTheDocument();
    });

    it('renders header with correct styling', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      const header = screen.getByText('Add document');
      expect(header).toHaveClass('header', 'text-black', 'text-sm', 'font-bold', 'pt-2');
    });

    it('renders FileUploadBox with correct props', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      const fileUploadBox = screen.getByTestId('file-upload-box');
      expect(fileUploadBox).toBeInTheDocument();
      
      const fileInput = screen.getByTestId('file-input');
      expect(fileInput).toHaveAttribute('accept', '.xlsx');
      expect(fileInput).toHaveAttribute('type', 'file');
    });

    it('calls onFileUpload when files are selected', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const fileInput = screen.getByTestId('file-input');

      fireEvent.change(fileInput, { target: { files: [file] } });

      expect(mockOnFileUpload).toHaveBeenCalledTimes(1);
      expect(mockOnFileUpload).toHaveBeenCalledWith([file]);
    });

    it('handles multiple file selection', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      const files = [
        new File(['content1'], 'test1.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
        new File(['content2'], 'test2.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
      ];
      const fileInput = screen.getByTestId('file-input');

      fireEvent.change(fileInput, { target: { files } });

      expect(mockOnFileUpload).toHaveBeenCalledTimes(1);
      expect(mockOnFileUpload).toHaveBeenCalledWith(files);
    });

    it('handles empty file selection', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [] } });

      expect(mockOnFileUpload).toHaveBeenCalledTimes(1);
      expect(mockOnFileUpload).toHaveBeenCalledWith([]);
    });

    it('handles null onFileUpload gracefully', () => {
      render(<UploadSection onFileUpload={undefined as any} />);

      const file = new File(['content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const fileInput = screen.getByTestId('file-input');

      expect(() => {
        fireEvent.change(fileInput, { target: { files: [file] } });
      }).not.toThrow();
    });

    it('shows spinner when isUploading is true', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} isUploading={true} />);

      expect(screen.getByTestId('spinner')).toBeInTheDocument();
      expect(screen.queryByTestId('rx-forecast-alert-message')).not.toBeInTheDocument();
      expect(screen.queryByText('Add document')).not.toBeInTheDocument();
      expect(screen.queryByTestId('file-upload-box')).not.toBeInTheDocument();
    });

    it('shows upload interface when isUploading is false', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} isUploading={false} />);

      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
      expect(screen.getByTestId('rx-forecast-alert-message')).toBeInTheDocument();
      expect(screen.getByText('Add document')).toBeInTheDocument();
      expect(screen.getByTestId('file-upload-box')).toBeInTheDocument();
    });

    it('shows upload interface when isUploading is not provided (defaults to false)', () => {
      render(<UploadSection onFileUpload={mockOnFileUpload} />);

      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
      expect(screen.getByTestId('rx-forecast-alert-message')).toBeInTheDocument();
      expect(screen.getByText('Add document')).toBeInTheDocument();
      expect(screen.getByTestId('file-upload-box')).toBeInTheDocument();
    });
  });

});
