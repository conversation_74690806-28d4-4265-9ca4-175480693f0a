// Utility to get fiscal week number from a date and fiscal week ranges
export function getFiscalWeekNumber(
    dateString: string,
    fiscalWeeks: Array<{ fiscalWeekStartDate: string; fiscalWeekEndDate: string; fiscalWeekNumber: number }>
): number {
    const date = new Date(dateString);
    for (let i = 0; i < fiscalWeeks.length; i++) {
        const start = new Date(fiscalWeeks[i].fiscalWeekStartDate);
        const end = new Date(fiscalWeeks[i].fiscalWeekEndDate);
        if (date >= start && date <= end) {
            return fiscalWeeks[i].fiscalWeekNumber || 0;
        }
    }
    return 0; // not found
}
