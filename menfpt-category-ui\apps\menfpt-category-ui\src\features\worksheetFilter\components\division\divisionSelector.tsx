import React, { useState, useEffect, useRef } from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { filterBySearch } from '../../searchUtils';
import { worksheetFilterConfig } from '../../worksheetFilterConfig';
import { useLocation } from 'react-router-dom';
import { extractCurrentRoute, useDisplayDeptRoleCascade } from '../../worksheetFilterRouteUtils';
import { useDispatch } from 'react-redux';
import { setSelectedSm, setSmDataForSelectedDept } from '../roles/rolesFilter.slice';
import { formatName } from '../roles/rolesUtils';
import AlertBox from '../alert-box';
import { usePersistentSearchQuery } from '../../hooks/usePersistentSearchQuery';
import { createEmptySerializedSmData } from '../../utils/serializationUtils';
import { SelectableList, SelectableItem } from '../shared';
import BannerSelection from './bannerSelection';

const DEFAULT_BANNER = { num: '00', name: 'DEFAULT' };

interface DivisionSelectorProps {
  divisions: DropdownType[];
  selectedDivisions: DropdownType[];
  onDivisionChange: (divisions: DropdownType[]) => void;
}

const DivisionSelector: React.FC<DivisionSelectorProps> = ({
  divisions,
  selectedDivisions,
  onDivisionChange,
}) => {
  const dispatch = useDispatch();
  const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();
  const [searchQuery, handleSearchQuery, handleBlur] = usePersistentSearchQuery('');
  const location = useLocation();
  const filteredDivisions = filterBySearch(divisions, searchQuery);
  const currentRoute = extractCurrentRoute(location.pathname);
  const isMultipleSelectionsAllowed = worksheetFilterConfig.isAllowMultipleDivisonsSelection.includes(currentRoute);
  const hasAutoSelected = useRef(false);

  useEffect(() => {
    hasAutoSelected.current = false;
  }, [divisions]);

  useEffect(() => {
    if (
      !isMultipleSelectionsAllowed &&
      divisions.length === 1 &&
      selectedDivisions.length === 0 &&
      !hasAutoSelected.current
    ) {
      onDivisionChange([divisions[0]]);
      hasAutoSelected.current = true;
    }
  }, [divisions, selectedDivisions, isMultipleSelectionsAllowed, onDivisionChange]);

  // Convert DropdownType to SelectableItem format
  const divisionItems: SelectableItem[] = filteredDivisions.map(div => ({
    id: div.num,
    label: formatName(div.name),
    data: { ...div, banners: div?.banners || [] }
  }));

  const selectedDivisionItems: SelectableItem[] = selectedDivisions?.length > 0 ? selectedDivisions.map(div => ({
    id: div.num,
    label: formatName(div.name),
    data: { ...div, banners: div?.banners || [] }
  })) : [];

  const handleDivisionChange = (item: any, checked?: boolean) => {
    const division = item.data ? item.data as DropdownType : item;
    if (isMultipleSelectionsAllowed) {
      const selectedIndex = selectedDivisions.findIndex(div => div.num === division.num);
      const updatedDivisions = [...selectedDivisions];
      if (selectedIndex > -1) {
        checked ?  updatedDivisions[selectedIndex] = division : updatedDivisions.splice(selectedIndex, 1);
      } else {
        updatedDivisions.push(division);
      }
      onDivisionChange(updatedDivisions);
    } else {
      onDivisionChange([{ ...division, banners: division.banners ? [division.banners[0]] : [] }]);
    }
  };

  const handleSelectAll = () => {
    // Clear SM and ASM selections when division changes
    if (isDisplayDeptRoleCascade) {
      dispatch(setSmDataForSelectedDept(createEmptySerializedSmData()));
      dispatch(setSelectedSm(createEmptySerializedSmData()));
    }

    if (selectedDivisions.length === divisions.length) {
      onDivisionChange([]);
    } else {
      onDivisionChange(divisions);
    }
  };

  const divisionHasValidBanners = (banners: any) => {
    return banners.filter((banner: any ) => banner.num !== '00').length > 0;
  }

  const getIndeterminateState = (item: SelectableItem) => {
    const division = item.data as DropdownType;
    const availableBanners = division.banners || [];
    const selectedBanners = selectedDivisions.find(div => div.num === division.num)?.banners || [];
    
    // Filter out default banners (num === '00') for valid banner count
    const validAvailableBanners = availableBanners.filter(banner => String(banner.num) !== '00');
    const validSelectedBanners = selectedBanners.filter(banner => String(banner.num) !== '00');
    
    // Return indeterminate if some but not all valid banners are selected
    return validAvailableBanners.length > 0 && 
           validSelectedBanners.length > 0 && 
           validSelectedBanners.length < validAvailableBanners.length;
  };

  const handleBannerChange = (item: SelectableItem, selectedDivision: number, checked?: boolean) => {
    const banner = item.data as DropdownType;
    const division = selectedDivisions.find(div => div.num === selectedDivision);
    
    // If division is not selected, we need to select it first
    if (!division) {
      const divisionToSelect = divisions.find(div => div.num === selectedDivision);
      if (divisionToSelect) {
        const newDivision = {
          ...divisionToSelect,
          banners: [banner]
        };
        
        if (isMultipleSelectionsAllowed) {
          // In multiple selection mode, add to existing selections
          const updatedDivisions = [...selectedDivisions, newDivision];
          onDivisionChange(updatedDivisions);
        } else {
          // In single selection mode, replace all previously selected divisions
          const updatedDivisions = [newDivision];
          onDivisionChange(updatedDivisions);
        }
      }
      return;
    }
    
    let updatedBanners = [...(division.banners ?? [])];
    if(isMultipleSelectionsAllowed) {
      checked ? updatedBanners.push(banner) : 
      updatedBanners = updatedBanners.filter(b => b.num !== banner.num);
    } else {
      updatedBanners = [banner];
    }
    
    // Update the division with new banners
    const updatedDivision = {...division, banners: updatedBanners};
    
    // If no banners are selected and it's multiple selection mode, remove the division
    if (updatedBanners.length === 0 && isMultipleSelectionsAllowed) {
      const filteredDivisions = selectedDivisions.filter(div => div.num !== selectedDivision);
      onDivisionChange(filteredDivisions);
    } else {
      if (isMultipleSelectionsAllowed) {
        // In multiple selection mode, update only the specific division
        const updatedDivisions = selectedDivisions.map(div => 
          div.num === selectedDivision ? updatedDivision : div
        );
        onDivisionChange(updatedDivisions);
      } else {
        // In single selection mode, replace all previously selected divisions
        const updatedDivisions = [updatedDivision];
        onDivisionChange(updatedDivisions);
      }
    }
  }

  const renderItemContent = (item: SelectableItem, isSelected: boolean) => {
    //if (!isSelected) return null;
    const banners = divisions.find(div => div.num === item.id)?.banners || []
    const selectedBanners = selectedDivisions.find(div => div.num === item.id)?.banners || [];
    if (banners && !divisionHasValidBanners(banners)) return null;

    // Sort available banners alphabetically by name
    const sortedBanners = [...banners].sort((a, b) => a.name.localeCompare(b.name));

    return (
      <BannerSelection
          selectedBanners={selectedBanners}
          availableBanners={sortedBanners}
          selectedDivision= {item.data.num}
          onBannerChange={handleBannerChange}
        />
    );
  };

  return (
    <div className="bg-white flex flex-col flex-1 min-h-0 overflow-hidden">
      <div className="flex items-center justify-start text-black text-base font-semibold font-['Nunito_Sans'] leading-normal">
        <span>Division</span>
        {selectedDivisions.length === 0 && (
          <AlertBox message={'Please select division(s)'} />
        )}
      </div>
      
  <section className="flex flex-col flex-1 overflow-hidden transition-all duration-200">
        <SelectableList
          items={divisionItems}
          selectedItems={selectedDivisionItems}
          isMultipleSelectionAllowed={isMultipleSelectionsAllowed}
          onItemChange={handleDivisionChange}
          onSelectAll={handleSelectAll}
          showSelectAll={isMultipleSelectionsAllowed}
          selectAllLabel={`${selectedDivisions.length} Selected`}
          emptyMessage="No divisions available"
          className="flex-1"
          listClassName="pr-[5px] self-stretch relative flex-1 flex flex-col justify-start items-start overflow-y-auto nfpt-scrollbar transition-all duration-200"
          itemClassName="self-stretch min-h-10 p-2.5 bg-white rounded-lg inline-flex justify-start items-center gap-6 overflow-hidden"
          renderItemContent={renderItemContent}
          enableExpandCollapse={true}
          getIndeterminateState={getIndeterminateState}
        />
      </section>
    </div>
  );
};

export default DivisionSelector;
