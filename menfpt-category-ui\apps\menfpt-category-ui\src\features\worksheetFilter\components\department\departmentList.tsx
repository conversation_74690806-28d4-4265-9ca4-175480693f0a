// EXAMPLE: How to refactor departmentList.tsx to use the new reusable components

import React from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { useDisplayDeptRoleCascade } from '../../worksheetFilterRouteUtils';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { toTitleCase } from '@ui/utils';
import { SelectableList, SelectableItem } from '../shared';

interface DepartmentListProps {
  departments: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  isMultipleSelectionAllowed: boolean;
  onDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  itemClassName?: string;
}

const DepartmentList: React.FC<DepartmentListProps> = ({
  departments,
  selectedDepartment,
  isMultipleSelectionAllowed,
  onDepartmentChange,
  itemClassName
}) => {
  const { data: allDepartments = [] } = useSelectorWrap('departments_rn') || {};
  const isDisplayDeptRoleCascade = useDisplayDeptRoleCascade();

  const getSelectedDepartments = () => {
    return Array.isArray(selectedDepartment)
      ? selectedDepartment
      : selectedDepartment
      ? [selectedDepartment]
      : [];
  };

  // Convert DropdownType to SelectableItem format
  const departmentItems: SelectableItem[] = departments.map(dept => ({
    id: dept.num,
    label: toTitleCase(dept.name),
    data: dept
  }));

  const selectedDepartmentItems: SelectableItem[] = getSelectedDepartments().map(dept => ({
    id: dept.num,
    label: toTitleCase(dept.name),
    data: dept
  }));

  const handleDepartmentChange = (item: SelectableItem, checked?: boolean) => {
    const department = item.data as DropdownType;
    
    if (isMultipleSelectionAllowed) {
      const selectedDepts = getSelectedDepartments();
      const isDeptSelected = selectedDepts.some(dept => dept.num === department.num);
      
      const updatedDepts = checked && !isDeptSelected
        ? [...selectedDepts, department]
        : selectedDepts.filter(dept => dept.num !== department.num);
        
      onDepartmentChange(updatedDepts);
    } else {
      onDepartmentChange(department);
    }
  };

  const handleSelectAll = () => {
    if (getSelectedDepartments().length === allDepartments.length) {
      onDepartmentChange([]);
    } else {
      onDepartmentChange(allDepartments);
    }
  };

  const getSelectAllLabel = () => {
    if (departments.length === 0) {
      return "Select";
    }
    return `${getSelectedDepartments().length} Selected`;
  };

  return (
    <div className="bg-white flex flex-col flex-1 min-h-0">
      <section 
        className={'flex-1 flex flex-col min-h-0'} 
      > 
        <SelectableList
          items={departmentItems}
          selectedItems={selectedDepartmentItems}
          isMultipleSelectionAllowed={isMultipleSelectionAllowed}
          onItemChange={handleDepartmentChange}
          onSelectAll={handleSelectAll}
          showSelectAll={isMultipleSelectionAllowed}
          selectAllLabel={getSelectAllLabel()}
          emptyMessage="No departments available"
          listClassName="pr-[5px] self-stretch relative flex-1 flex flex-col justify-start items-start overflow-y-auto min-h-0 nfpt-scrollbar"
          itemClassName="self-stretch min-h-10 p-2.5 bg-white rounded-lg inline-flex justify-start items-center gap-6 overflow-hidden"
          disabled={allDepartments.length === 0}
        />
      </section>
    </div>
  );
};

export default DepartmentList;
