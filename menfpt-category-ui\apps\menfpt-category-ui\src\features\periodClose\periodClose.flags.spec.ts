import '@testing-library/jest-dom';
jest.mock('react-redux', () => ({
  useSelector: jest.fn(() => ({ data: {} })),
}));
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));
jest.mock('./generatePeriodStatuses', () => ({
  generatePeriodStatuses: jest.fn(),
}));
beforeAll(() => {
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});
import * as periodCloseFlags from './periodClose.flags';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { generatePeriodStatuses } from './generatePeriodStatuses';

describe('periodClose.flags', () => {
  describe('useShouldDisableEditForecastButton', () => {
    it('returns true if locked or notCertifiedButLocked has keys', () => {
      useSelectorWrap.mockReturnValue({ data: { locked: { a: [1] }, notCertifiedButLocked: {} } });
      expect(periodCloseFlags.useShouldDisableEditForecastButton()).toBe(true);
    });
    it('returns true if notCertifiedButLocked has keys', () => {
      useSelectorWrap.mockReturnValue({ data: { locked: {}, notCertifiedButLocked: { a: [1] } } });
      expect(periodCloseFlags.useShouldDisableEditForecastButton()).toBe(true);
    });
    it('returns true if both are empty', () => {
      useSelectorWrap.mockReturnValue({ data: { locked: {}, notCertifiedButLocked: {} } });
      expect(periodCloseFlags.useShouldDisableEditForecastButton()).toBe(true);
    });
    it('returns false if notLocked has keys', () => {
      useSelectorWrap.mockReturnValue({ data: { notLocked: { a: [1] }, locked: {}, notCertifiedButLocked: {} } });
      expect(periodCloseFlags.useShouldDisableEditForecastButton()).toBe(false);
    });
  });

  describe('handlePrevQuarterPeriodClose', () => {
    it('does nothing if calendarApiResp is falsy', () => {
      const dispatch = jest.fn();
      expect(periodCloseFlags.handlePrevQuarterPeriodClose(null, { type: 'lastQrtr_periodClose' }, dispatch)).toBeUndefined();
      expect(dispatch).not.toHaveBeenCalled();
    });
    it('does nothing if type is not lastQrtr_periodClose', () => {
      const dispatch = jest.fn();
      expect(periodCloseFlags.handlePrevQuarterPeriodClose({}, { type: 'other' }, dispatch)).toBeUndefined();
      expect(dispatch).not.toHaveBeenCalled();
    });
    it('dispatches correct actions for valid input', () => {
      const dispatch = jest.fn();
      const calendarApiResp = { foo: 'bar' };
      const calendarApiPayload = { type: 'lastQrtr_periodClose', payload: { qtrNumbersArr: [1] } };
      generatePeriodStatuses.mockReturnValue({ locked: { 1: [1] }, notLocked: {}, notCertifiedButLocked: {}, certified: {} });
      periodCloseFlags.handlePrevQuarterPeriodClose(calendarApiResp, calendarApiPayload, dispatch);
      expect(dispatch).toHaveBeenCalled();
    });
  });

  describe('useWeeksToBeDisabledForQuarter', () => {
    it('returns combined locked and notCertifiedButLocked weeks', () => {
      useSelectorWrap.mockReturnValue({ data: { locked: { a: [1,2] }, notCertifiedButLocked: { b: [3] } } });
      expect(periodCloseFlags.useWeeksToBeDisabledForQuarter()).toEqual([1,2,3]);
    });
    it('returns empty array if periodStatuses is falsy', () => {
      useSelectorWrap.mockReturnValue({ data: null });
      expect(periodCloseFlags.useWeeksToBeDisabledForQuarter()).toEqual([]);
    });
  });
}); 