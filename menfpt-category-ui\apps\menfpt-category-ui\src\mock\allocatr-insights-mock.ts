import { AllocatrInsightsResponse, DepartmentConfig, DivisionData, BannerData, DepartmentData, PeriodData, WeekData, QuarterData, BaseData } from '../interfaces/allocatr-insights';

export const mockDepartmentConfig: DepartmentConfig[] = [
  {id:'Total', name: ''},
  { id: '3010000', name: 'Grocery Food' },
  { id: '3030000', name: 'Alcohol Beverage' },
  { id: '3070000', name: 'Tobacco' },
  { id: '3090000', name: 'Deli/Prepared Foods' },
  { id: '3110000', name: 'General Merchandise/HBC' },
  { id: '3140000', name: '<PERSON><PERSON>' },
  { id: '3150000', name: '<PERSON>l and Gift Candy' },
  { id: '3160000', name: 'In-Store Bakery' },
  { id: '3170000', name: 'Frozen Grocery' },
  { id: '3270000', name: 'Grocery Non-Foods' },
  { id: '3280000', name: 'Coffee Kiosk' },
  { id: '3290000', name: 'Produce' },
  { id: '3300000', name: 'Seafood' },
  { id: '3330000', name: 'Meat' },
  { id: '3360000', name: 'Bakery Pkgd Outside' },
  { id: '3430000', name: 'Bar' },
  { id: 'FrontEnd', name: 'Front End' },
  { id: 'Pharmacy', name: 'Pharmacy' },
  { id: 'CarWash', name: 'Car Wash' },
];

const createBaseData = (id: string): BaseData => ({
  id,
  line1Projection: Math.random() * 1000000,
  lastYear: Math.random() * 1000000,
  actualOrForecast: Math.random() * 1000000,
  idPercentage: Math.random(),
  vsLY: {
    value: Math.random() * 100000,
    percentage: Math.random(),
  },
  vsProjection: {
    value: Math.random() * 100000,
    percentage: Math.random(),
  },
  bookGrossProfit: {
    projectionValue: Math.random() * 100000,
    projectionPct: Math.random(),
    actualOrForecast: Math.random() * 100000,
    percentActualOrForecast: Math.random(),
    vsProjection: Math.random(),
  },
  markdown: {
    projectionValue: Math.random() * 100000,
    projectionPct: Math.random(),
    actualOrForecast: Math.random() * 100000,
    percentActualOrForecast: Math.random(),
    vsProjection: Math.random(),
  },
  shrink: {
    projectionValue: Math.random() * 100000,
    projectionPct: Math.random(),
    actualOrForecast: Math.random() * 100000,
    percentActualOrForecast: Math.random(),
    vsProjection: Math.random(),
  },
  line5: {
    actualOrForecast: Math.random() * 1000000,
    percentActualOrForecast: Math.random(),
    projectionValue: Math.random() * 1000000,
    projectionPct: Math.random(),
    vsProjection: Math.random() * 100000,
    percentVsProjection: Math.random(),
  },
  line6: {
    projection: Math.random() * 1000000,
    actualOrForecast: Math.random() * 1000000,
    vsProjection: Math.random() * 100000,
  },
  line7: {
    projection: Math.random() * 1000000,
    actualOrForecast: Math.random() * 1000000,
    vsProjection: Math.random() * 100000,
  },
  line8: {
    actualOrForecast: Math.random() * 1000000,
    percentActualOrForecast: Math.random(),
    projectionValue: Math.random() * 1000000,
    projectionPct: Math.random(),
    vsProjection: Math.random() * 100000,
    percentVsProjection: Math.random(),
  },
});

const createWeekData = (weekNumber: number, periodNumber: number): WeekData => ({
  ...createBaseData(`Week-${weekNumber}`),
  weekNumber,
  periodNumber,
  fiscalWeekLabel: `Week ${weekNumber} (fiscal wk ${weekNumber})`,
  isActualUsed: Math.random() > 0.5,
});

const createPeriodData = (periodNumber: number): PeriodData => ({
  ...createBaseData(`Period-${periodNumber}`),
  periodNumber,
});

const createQuarterData = (quarterNumber: number): QuarterData => ({
  ...createBaseData(`Quarter-${quarterNumber}`),
  quarterNumber,
});

const createDepartmentData = (id: string, name: string): DepartmentData => ({
  id,
  name,
  quarter: createQuarterData(202502),
  periods: [createPeriodData(202505), createPeriodData(202506)],
  weeks: [createWeekData(202517, 202505), createWeekData(202518, 202505)],
});

const createBannerData = (id: string, name: string): BannerData => ({
  id,
  name,
  quarter: createQuarterData(202502),
  departments: mockDepartmentConfig
    .filter((dept) => dept.id !== 'Total')
    .map((dept) => createDepartmentData(dept.id, dept.name)),
});

const createDivisionData = (id: string, name: string): DivisionData => ({
  id,
  name,
  quarter: createQuarterData(202502),
  banners: [createBannerData('25-ACME', 'ACME')],
});

export const mockAllocatrInsightsData: AllocatrInsightsResponse = {
  id: 'Total',
  name: ' ',
  quarter: createQuarterData(202502),
  periods: [createPeriodData(202505), createPeriodData(202506)],
  weeks: [createWeekData(202517, 202505), createWeekData(202518, 202505)],
  divisions: [
    createDivisionData('25', 'Southern'),
    createDivisionData('34', 'Mid Atlantic'),
  ],
};

export const mockFiscalWeeks = [
  { id: 'week-4', label: 'Fiscal Wk 4 (2/3 - 2/9)' },
  { id: 'week-5', label: 'Fiscal Wk 5 (2/10 - 2/16)' },
  { id: 'week-6', label: 'Fiscal Wk 6 (2/17 - 2/23)' },
];
