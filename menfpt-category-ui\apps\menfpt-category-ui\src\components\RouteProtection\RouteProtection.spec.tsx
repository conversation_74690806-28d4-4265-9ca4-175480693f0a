import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import RouteProtection from './RouteProtection';

// Mock the useSelectorWrap hook
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));

const { useSelectorWrap } = require('../../rtk/rtk-utilities');

// Mock window.location
const mockLocation = {
  pathname: '/menfpt/dashboard',
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

const createMockStore = (userInfo: any) => {
  return configureStore({
    reducer: {
      userInfo_rn: (state = { data: userInfo }, action: any) => state,
    },
  });
};

const renderWithProviders = (component: React.ReactElement, userInfo: any) => {
  const store = createMockStore(userInfo);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('RouteProtection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Page Name Extraction', () => {
    it('should extract correct page name from dashboard path', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/dashboard';
      
      const { container } = renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should extract correct page name from adjustment-worksheet path', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/adjustment-worksheet';
      
      const { container } = renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should extract correct page name from rx-forecast path', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
      mockLocation.pathname = '/menfpt/rx-forecast';
      
      const { container } = renderWithProviders(
        <RouteProtection pharmacyOnlyRoutes={['/rx-forecast']}>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'admin' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle unknown page names gracefully', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/unknown-page';
      
      const { container } = renderWithProviders(
        <RouteProtection restrictedRoutes={['/unknown-page']}>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle nested paths correctly', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/nested/path/dashboard';
      
      const { container } = renderWithProviders(
        <RouteProtection restrictedRoutes={['/dashboard']}>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('Pharmacy User Access Control', () => {
    it('should redirect pharmacy user from dashboard route', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/dashboard';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should redirect pharmacy user from adjustment-worksheet route', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/adjustment-worksheet';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow pharmacy user to access rx-forecast route', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/rx-forecast';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('Non-Pharmacy User Access Control', () => {
    it('should allow non-pharmacy user to access dashboard route', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
      mockLocation.pathname = '/menfpt/dashboard';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'admin' }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should allow non-pharmacy user to access adjustment-worksheet route', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'FPA' } });
      mockLocation.pathname = '/menfpt/adjustment-worksheet';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'FPA' }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    // Add new test to verify non-pharmacy users can now access rx-forecast route
    // it('should allow non-pharmacy user to access rx-forecast route (temporary change)', () => {
    //   useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
    //   mockLocation.pathname = '/menfpt/rx-forecast';
      
    //   renderWithProviders(
    //     <RouteProtection>
    //       <div data-testid="protected-content">Protected Content</div>
    //     </RouteProtection>,
    //     { userRole: 'admin' }
    //   );

    //   expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    // });

    // it('should allow user with empty role to access rx-forecast route (temporary change)', () => {
    //   useSelectorWrap.mockReturnValue({ data: { userRole: '' } });
    //   mockLocation.pathname = '/menfpt/rx-forecast';
      
    //   renderWithProviders(
    //     <RouteProtection>
    //       <div data-testid="protected-content">Protected Content</div>
    //     </RouteProtection>,
    //     { userRole: '' }
    //   );

    //   expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    // });
  });

  describe('Edge Cases', () => {
    it('should handle undefined userInfo gracefully', () => {
      useSelectorWrap.mockReturnValue({ data: undefined });
      mockLocation.pathname = '/menfpt/dashboard';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        undefined
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should handle undefined userRole gracefully', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: undefined } });
      mockLocation.pathname = '/menfpt/dashboard';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: undefined }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should allow access to non-restricted routes for any user', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/some-other-route';
      
      renderWithProviders(
        <RouteProtection>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('Custom Route Configuration', () => {
    it('should work with custom restricted routes', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'pharmacyUser' } });
      mockLocation.pathname = '/menfpt/custom-restricted';
      
      renderWithProviders(
        <RouteProtection restrictedRoutes={['/custom-restricted']}>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'pharmacyUser' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should work with custom pharmacy-only routes', () => {
      useSelectorWrap.mockReturnValue({ data: { userRole: 'admin' } });
      mockLocation.pathname = '/menfpt/custom-pharmacy-only';
      
      renderWithProviders(
        <RouteProtection pharmacyOnlyRoutes={['/custom-pharmacy-only']}>
          <div data-testid="protected-content">Protected Content</div>
        </RouteProtection>,
        { userRole: 'admin' }
      );

      // The component should redirect, so protected content should not be visible
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });
}); 