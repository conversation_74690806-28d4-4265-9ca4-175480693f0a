import { isFeatureEnabled, killSwitchConfig } from './ks.config';

describe('isFeatureEnabled', () => {
  afterEach(() => {
    // Restore window if it was mocked
    jest.restoreAllMocks();
  });

  it('returns true for globally enabled feature', () => {
    expect(isFeatureEnabled('featureA')).toBe(true);
  });

  it('returns false for globally disabled feature', () => {
    expect(isFeatureEnabled('featureB')).toBe(false);
  });

  it('returns default for object config if not in browser', () => {
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;
    expect(isFeatureEnabled('periodClose')).toBe(false);
    global.window = originalWindow;
  });

  it('returns location-based value if pattern matches href', () => {
    // @ts-ignore
    global.window = { location: { href: 'http://localhost:4200/' } };
    expect(isFeatureEnabled('periodClose')).toBe(true);
  });

  it('returns default if no location pattern matches', () => {
    // @ts-ignore
    global.window = { location: { href: 'https://prod.example.com/' } };
    expect(isFeatureEnabled('periodClose')).toBe(false);
  });

  it('returns correct value for another location pattern', () => {
    // @ts-ignore
    global.window = { location: { href: 'https://dev.example.com/' } };
    expect(isFeatureEnabled('periodClose')).toBe(true);
  });

  it('returns false for unknown feature', () => {
    expect(isFeatureEnabled('unknownFeature')).toBe(false);
  });
});
