import React from 'react';
import HeatMap from 'react-heatmap-grid';

const xLabels = new Array(21).fill(0).map((_, i) => 300 + i);
const yLabels = new Array(13).fill(0).map((_, i) => i + 1);
const data = new Array(13).fill(0).map(() => new Array(21).fill(0).map(() => Math.floor(Math.random() * 100)));

const LaggingIndicatorPage: React.FunctionComponent<any> = () => {
    return (
        <div className='bg-white p-4'>
            <div>Department-Division breakdown</div>
            <div className="flex space-x-4">
                <span className="flex items-center font-nunito-sans font-semibold text-base leading-6 tracking-normal">
                    <span className="inline-block w-2 h-4 bg-red-500 mr-2"></span>
                    49 over +/-20% deviation
                </span>
                <span className="flex items-center font-nunito-sans font-semibold text-base leading-6 tracking-normal">
                    <span className="inline-block w-2 h-4 bg-yellow-500 mr-2"></span>
                    49 over +/-5% deviation
                </span>
                <span className="flex items-center font-nunito-sans font-semibold text-base leading-6 tracking-normal">
                    <span className="inline-block w-2 h-4 bg-green-500 mr-2"></span>
                    49 below +/-5% deviation
                </span>
            </div>
            
            <HeatMap
                xLabels={xLabels}
                yLabels={yLabels}
                data={data}
                height={30}
                squares
                cellStyle={(background, value, min, max, data, x, y) => {
                    const mid = (max + min) / 2;
                    let red, green;
                    if (value < mid) {
                        red = 200; // Darker red
                        green = Math.floor((200 * (value - min)) / (mid - min));
                    } else {
                        green = 200; // Darker green
                        red = Math.floor((200 * (max - value)) / (max - mid));
                    }
                    return {
                        background: `rgb(${red}, ${green}, 0)`,
                        fontSize: "11px",
                    };
                }}
                // cellRender={(value) => value && `${value}%`}
                // title={(value) => `${value}`}
            />
        </div>
    );
};

export default LaggingIndicatorPage;