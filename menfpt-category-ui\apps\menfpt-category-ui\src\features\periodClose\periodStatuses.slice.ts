import { createGenericSlice } from '../../rtk/rtk-slice';

// Slice for managing the  data returned by generatePeriodStatuses utility. Used to track the period statuses for the quarterDisplayedInTable. It is used to save the current quarter or previous quarter statuses. 

export const periodStatusesSlice = createGenericSlice({
  name: 'periodStatuses_rn',
  initialState: { status: 'loading', data: {} },
})({
  setPeriodStatuses(state, { payload }) {
    state.data = payload;
  },
});

export const { setPeriodStatuses } = periodStatusesSlice.actions;

