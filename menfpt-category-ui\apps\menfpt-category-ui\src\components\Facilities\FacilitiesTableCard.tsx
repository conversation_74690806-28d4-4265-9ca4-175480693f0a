import DynamicCardTop from './DynamicCardTop';
import DynamicCardSpacer from './DynamicCardSpacer';
import Table from '@albertsons/uds/molecule/Table';

type FacilitiesTableCardProps<T> = {
  title: string;
  updatedText: string;
  columns: any[];
  items: T[];
  itemKey: keyof T | ((item: T) => string | number);
  footer?: React.ReactNode;
  noPagination?: boolean;
  pageSize?: number;
  extraHeaderContent?: React.ReactNode; 
};

const FacilitiesTableCard = <T,>({
  title,
  updatedText,
  columns,
  items,
  itemKey,
  footer,
  noPagination = true,
  pageSize = 10,
  extraHeaderContent,
}: FacilitiesTableCardProps<T>) => (
  <div className="flex justify-center items-start w-full min-h-[304px]">
    <div
      className="relative mt-[50px] bg-[#fdfdff] rounded-b-lg border-b border-b-[#c8daeb] border-r border-r-[#c8daeb] border-l border-l-[#c8daeb] shadow overflow-hidden w-full min-h-[304px] opacity-100"
    >
      <DynamicCardTop />
      <div className="absolute top-0 left-0 right-0 mt-4 mx-6 text-sm text-gray-700 font-bold z-10 flex items-center justify-between">
        <span>{title}</span>
        {extraHeaderContent}
      </div>
      <div className="absolute top-0 right-0 mt-4 mr-6 text-sm text-gray-500 font-medium z-10">
        {updatedText}
      </div>
      <DynamicCardSpacer />
      <div className="w-full overflow-x-auto bg-[#f1f4f9] scrollbar-none max-w-full m-0 p-0">
        <div className="facilities-table m-0 p-0">
          <Table
            key={
              items.length +
              '-' +
              JSON.stringify(
                items.map(item =>
                  typeof itemKey === 'function'
                    ? itemKey(item)
                    : (item as any)[itemKey]
                )
              )
            } 
            id="facilities-table"
            itemKey={itemKey}
            items={items}
            columns={columns}
            className="facilities-table-custom overflow-x-auto"
            noPagination={noPagination}
            pageSize={pageSize}
            noHeader={true}
            dividers="horizontal"
          />
        </div>
      </div>
      {footer}
    </div>
  </div>
);

export default FacilitiesTableCard;