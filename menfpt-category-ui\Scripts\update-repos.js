const os = require('os');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

const currentDir = process.cwd();
const parentDir = path.dirname(currentDir);
const uiDir = currentDir;
const bffDir = path.join(parentDir, 'menfpt-category-bff');

// Check if BFF directory exists
if (!fs.existsSync(bffDir)) {
    console.error('\x1b[31mError: BFF directory not found at', bffDir, '\x1b[0m');
    console.log('\x1b[33mMake sure menfpt-category-bff is located parallel to menfpt-category-ui\x1b[0m');
    process.exit(1);
}

console.log('\x1b[32mUpdating both codebases...\x1b[0m');
console.log('\x1b[36mUI Directory:', uiDir, '\x1b[0m');
console.log('\x1b[36mBFF Directory:', bffDir, '\x1b[0m');

try {
    // Update both repositories
    execSync('git pull origin master', { cwd: uiDir, stdio: 'inherit' });
    execSync('git pull origin master', { cwd: bffDir, stdio: 'inherit' });
    
    console.log('\x1b[32mBoth codebases updated successfully!\x1b[0m');
} catch (error) {
    console.error('\x1b[31mError:', error.message, '\x1b[0m');
    process.exit(1);
}
