# Route Protection Component

## Overview
The RouteProtection component provides role-based access control for different routes in the application. It restricts access based on user roles and redirects unauthorized users to an access denied page.

## Features
- **Pharmacy User Restrictions**: Pharmacy users cannot access dashboard and adjustment-worksheet routes
- **Pharmacy-Only Routes**: Only pharmacy users can access rx-forecast route
- **Automatic Redirects**: Unauthorized users are redirected to access-denied page
- **Configurable Routes**: Custom restricted and pharmacy-only routes can be configured

## Usage

```tsx
import RouteProtection from './components/RouteProtection';

// Basic usage with default routes
<RouteProtection>
  <YourComponent />
</RouteProtection>

// Custom route configuration
<RouteProtection 
  restrictedRoutes={['/custom-restricted']}
  pharmacyOnlyRoutes={['/custom-pharmacy-only']}
>
  <YourComponent />
</RouteProtection>
```

## Route Access Matrix

| User Role | Dashboard | Adjustment Worksheet | Rx Forecast |
|-----------|-----------|---------------------|-------------|
| pharmacyUser | ❌ Redirected | ❌ Redirected | ✅ Allowed |
| admin | ✅ Allowed | ✅ Allowed | ❌ Redirected |
| FPA | ✅ Allowed | ✅ Allowed | ❌ Redirected |
| Empty/Undefined | ✅ Allowed | ✅ Allowed | ❌ Redirected |

## Test Coverage

### RouteProtection Component Tests
- ✅ Pharmacy user access control (dashboard, adjustment-worksheet, rx-forecast)
- ✅ Non-pharmacy user access control
- ✅ Edge cases (undefined userInfo, undefined userRole)
- ✅ Custom route configuration
- ✅ Non-restricted route access

### AccessDenied Component Tests
- ✅ Component rendering
- ✅ Proper styling classes
- ✅ Semantic structure
- ✅ Accessibility features
- ✅ Text content verification

### Constants Tests
- ✅ USER_ROLES constant definition
- ✅ Type exports
- ✅ Value consistency
- ✅ Index file exports

### App Integration Tests
- ✅ App rendering with different user roles
- ✅ Route protection structure
- ✅ Access denied route inclusion

## Files Created/Modified

### New Files
- `src/components/RouteProtection/RouteProtection.tsx`
- `src/components/RouteProtection/RouteProtection.spec.tsx`
- `src/components/RouteProtection/index.ts`
- `src/components/RouteProtection/index.spec.ts`
- `src/pages/access-denied.tsx`
- `src/pages/access-denied.spec.tsx`
- `src/constants/userRoles.ts`
- `src/constants/userRoles.spec.ts`
- `src/constants/index.spec.ts`

### Modified Files
- `src/app.tsx` - Added route protection and access-denied route
- `src/app.spec.tsx` - Updated tests for new routing structure
- `src/constants/index.ts` - Added user roles export

## Running Tests

```bash
# Run all tests
npm test

# Run specific test files
npm test -- --testPathPattern="RouteProtection"
npm test -- --testPathPattern="access-denied"
npm test -- --testPathPattern="userRoles"
``` 