import Spinner from '@albertsons/uds/molecule/Spinner';
import React, { useState } from 'react';
import { useDispatch } from "react-redux";
import AlertMessage from "../components/AlertMessage";
import { usePeriodModalsContainer } from '../features/periodClose/modals/periodModalsContainer';
import { useCombinedFiltersAndQuarters } from "../features/worksheetFilter/useCombinedFiltersAndQuarters";
import { WorksheetHeaderControls } from "../features/WorksheetHeaderControls";
import { useSelectorWrap } from "../rtk/rtk-utilities";
import { setAlertState } from "../server/Reducer/menfpt-category.slice";
import TableContainer from "./worksheet-table-container";
import { Button } from '@albertsons/uds/molecule/Button';
import { useNavigate } from 'react-router-dom';

const SUCCESS_MSG = "Changes are saved successfully";
const ERROR_MSG = "Couldn't Save. Try Again.";

const AdjustmentWorkSheet: React.FC = () => {
    // Use the combined hook to fetch both filter and quarters data in a single API call
    const { filterLoading, filterData } = useCombinedFiltersAndQuarters();
    const { data: alertState } = useSelectorWrap('alertState_rn');
    const dispatch = useDispatch();
    const navigate = useNavigate();
 
   
    const [selectedQuarter, setSelectedQuarter] = useState<number | undefined>(undefined);

    return (
        <div>

            { filterLoading ? <Spinner /> : <WorksheetHeaderControls FiltersList={filterData} onQuarterChange={setSelectedQuarter} /> }
            {/* Only render TableContainer when filter data is loaded to prevent duplicate API calls */}
            { !filterLoading && <TableContainer selectedQuarter={selectedQuarter}/> }
            <AlertMessage
                message={alertState?.success ? SUCCESS_MSG : ERROR_MSG}
                type={alertState?.success ? 'success' : 'error'}
                open={alertState?.success || alertState?.error}
                onClose={() => {dispatch(setAlertState({ success: false, error: false }))}}
            />
        </div>
    );
}
export default AdjustmentWorkSheet;