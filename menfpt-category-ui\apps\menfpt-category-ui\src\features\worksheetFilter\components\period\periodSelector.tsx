import React, { useEffect, useMemo, useRef } from 'react';
import Checkbox from '@albertsons/uds/molecule/Checkbox';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { toTitleCase } from '@ui/utils';
import AlertBox from '../alert-box';
import { useDispatch } from 'react-redux';
import { setDisplayDate } from '../../../../server/Reducer/menfpt-category.slice';
import { useGetDisplayDateQuery } from '../../../../server/Api/menfptCategoryAPI';
import { skipToken } from '@reduxjs/toolkit/query';
import SelectableList from '../shared/SelectableList';
import { SelectableItem } from '../shared/SelectableListItem';

interface PeriodSelectorProps {
  selectedPeriods: DropdownType[] | undefined;
  selectedQuarter: DropdownType | undefined;
  onPeriodChange: (periodsSelected: DropdownType[]) => void;
  onWeeksChange?: (weeks: { periodNum: number; weekNum: number }[]) => void;
  selectedWeeks?: { periodNum: number; weekNum: number }[];
}

const getQuarterPeriodCountMap = (quarters: any[]) => {
  const map = new Map<number, Map<number, DropdownType>>();
  quarters.forEach((q) => {
    const quarterNum = q.fiscalQuarterNumber;
    const periodNum = q.fiscalPeriodNumber;
    const periodSuffix = String(periodNum).slice(-2); // Get last 2 digits
    if (!map.has(quarterNum)) {
      map.set(quarterNum, new Map());
    }  
    // Use periodNum as key to ensure uniqueness
    map.get(quarterNum)!.set(periodNum, {
      num: periodNum,
      name: `Period ${parseInt(periodSuffix, 10)}` // e.g., Period 1, Period 12
    });
  });
  return map;
};

const getPeriodWeekCountMap = (quarters: any[]) => {
  const map = new Map<number, Set<number>>();
  quarters.forEach((q) => {
    const periodNum = q.fiscalPeriodNumber;
    const weekNum = q.fiscalWeekNumber;
    if (!map.has(periodNum)) {
      map.set(periodNum, new Set());
    }
      // Add week number to the set to ensure uniqueness
    map.get(periodNum)!.add(weekNum);
  });
  return map;
};


interface WeeksSelectedMap {
  [periodNum: number]: number[]; // week numbers selected for each period
}

interface WeekSelection {
  periodNum: number;
  weekNum: number;
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  selectedPeriods = [],
  selectedQuarter,
  onPeriodChange,
  onWeeksChange,
  selectedWeeks = []
}) => {
  const quartersState = useSelectorWrap('quartersInYr_rn');
  const [weeksSelected, setWeeksSelected] = React.useState<WeekSelection[]>(selectedWeeks);

  const selectedQuarterNum = selectedQuarter?.num || null;

  const quarterToPeriodsMap = useMemo(() => {
    return getQuarterPeriodCountMap(quartersState.data || []);
  }, [quartersState.data]);

  const periodToWeekMap = useMemo(() => {
    return getPeriodWeekCountMap(quartersState.data || []);
  }, [quartersState.data]);

  const allPeriods = useMemo(() => {
    return selectedQuarterNum
      ? Array.from(quarterToPeriodsMap.get(selectedQuarterNum)?.values() || [])
      : [];
  }, [selectedQuarterNum, quarterToPeriodsMap]);

  const getWeeksForPeriod = (periodNum: number): number[] => {
    return Array.from(periodToWeekMap.get(periodNum) || []);
  };

  // Convert periods to SelectableItem format
  const periodSelectableItems: SelectableItem[] = allPeriods.map(period => ({
    id: period.num,
    label: period.name,
    data: period
  }));

  // Get selected period items
  const selectedPeriodItems: SelectableItem[] = selectedPeriods.map(period => ({
    id: period.num,
    label: period.name,
    data: period
  }));

  const handlePeriodItemChange = (item: SelectableItem, checked?: boolean) => {
    const period = item.data as DropdownType;
    handlePeriodChange(period);
  };

  const renderPeriodContent = (item: SelectableItem, isSelected: boolean) => {
    const period = item.data as DropdownType;
    const weeks = getWeeksForPeriod(period.num);
    
    const weeksSelectedForPeriod = weeksSelected
      .filter(ws => ws.periodNum === period.num)
      .map(ws => ws.weekNum);

    return (
      <div className="pl-6 flex flex-col">
        {weeks.map(weekNum => (
          <Checkbox
            key={weekNum}
            label={`Week ${parseInt(String(weekNum).slice(-2), 10)}`}
            checked={weeksSelectedForPeriod.includes(weekNum)}
            onChange={() => handleWeekChange(period.num, weekNum)}
            className="p-2.5"
          />
        ))}
      </div>
    );
  };

  const handlePeriodChange = (period: DropdownType) => {
    const isSelected = selectedPeriods.some(p => p.num === period.num);
    let updatedPeriods: DropdownType[];
    let updatedWeeks: WeekSelection[];

    if (isSelected) {
      updatedPeriods = selectedPeriods.filter(p => p.num !== period.num);
      updatedWeeks = weeksSelected.filter(ws => ws.periodNum !== period.num);
    } else {
      updatedPeriods = [...selectedPeriods, period];
      const weeks = getWeeksForPeriod(period.num);
      updatedWeeks = [...weeksSelected, ...weeks.map(w => ({ periodNum: period.num, weekNum: w }))];
    }

    onPeriodChange(updatedPeriods);
    setWeeksSelected(updatedWeeks);
    onWeeksChange?.(updatedWeeks);
  };

  const handleWeekChange = (periodNum: number, weekNum: number) => {
    setWeeksSelected(prev => {
      const exists = prev.some(ws => ws.periodNum === periodNum && ws.weekNum === weekNum);
      let updatedWeeks: WeekSelection[];

      if (exists) {
        updatedWeeks = prev.filter(ws => !(ws.periodNum === periodNum && ws.weekNum === weekNum));
      } else {
        updatedWeeks = [...prev, { periodNum, weekNum }];
      }
      // Find all periods that have at least one week selected
      const selectedPeriodNums = Array.from(new Set(updatedWeeks.map(ws => ws.periodNum)));
      const updatedPeriods = allPeriods.filter(p => selectedPeriodNums.includes(p.num));
      onPeriodChange(updatedPeriods);
      onWeeksChange?.(updatedWeeks);
      return updatedWeeks;
    });
  };

  const getIndeterminateState = (item: SelectableItem) => {
    const period = item.data as DropdownType;
    const weeks = getWeeksForPeriod(period.num);
    const weeksSelectedForPeriod = weeksSelected
      .filter(ws => ws.periodNum === period.num)
      .map(ws => ws.weekNum);

    return weeksSelectedForPeriod.length > 0 && weeksSelectedForPeriod.length < weeks.length;
  };

  useEffect(() => {
    if (selectedQuarterNum && selectedPeriods.length === 0) {
      // Only preselect periods & weeks if nothing is selected yet
      onPeriodChange(allPeriods);
      const weeksArr = allPeriods.flatMap(period =>
        getWeeksForPeriod(period.num).map(weekNum => ({ periodNum: period.num, weekNum }))
      );
      setWeeksSelected(weeksArr);
      onWeeksChange?.(weeksArr);
    }
  }, [selectedQuarterNum]);

  useEffect(() => {
    if (
      selectedWeeks &&
      selectedWeeks.length > 0 &&
      JSON.stringify(selectedWeeks) !== JSON.stringify(weeksSelected)
    ) {
      setWeeksSelected(selectedWeeks);
    }
     // Only sync periods when selectedWeeks is not provided
    if (
      (!selectedWeeks || selectedWeeks.length === 0) &&
      selectedPeriods &&
      selectedPeriods.length > 0 &&
      selectedPeriods.some(
        p => !weeksSelected.some(ws => ws.periodNum === p.num)
      )
    ) {
      const weeksArr = selectedPeriods.flatMap(period =>
        getWeeksForPeriod(period.num).map(weekNum => ({ periodNum: period.num, weekNum }))
      );
      setWeeksSelected(weeksArr);
    }
  }, [selectedPeriods, selectedWeeks]);

  return (
    <div className="bg-white flex flex-col w-full">
      {periodSelectableItems.length > 0 ? (
        <SelectableList
          items={periodSelectableItems}
          selectedItems={selectedPeriodItems}
          isMultipleSelectionAllowed={true}
          onItemChange={handlePeriodItemChange}
          enableExpandCollapse={true}
          renderItemContent={renderPeriodContent}
          emptyMessage="No periods available"
          listClassName="self-stretch flex flex-col justify-start items-start"
          itemClassName="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
          getIndeterminateState={getIndeterminateState}
        />
      ) : (
        <div className="flex items-center justify-center py-4 w-full text-gray-500">
          Please select Quarter to view
        </div>
      )}
    </div>
  );
};

export default PeriodSelector;