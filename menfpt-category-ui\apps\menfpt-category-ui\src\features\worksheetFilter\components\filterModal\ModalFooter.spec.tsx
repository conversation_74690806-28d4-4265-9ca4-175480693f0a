import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import ModalFooter from './ModalFooter';

describe('ModalFooter', () => {
  const defaultProps = {
    shouldDisplayTimeFrame: false,
    selectedTimeframe: undefined,
    selectedDivision: [],
    selectedDesk: undefined,
    selectedDepartment: undefined,
    resetFilters: jest.fn(),
    applyFilters: jest.fn(),
    close: jest.fn(),
  };

  describe('Apply button disabled state', () => {
    it('should be disabled when no division is selected', () => {
      render(<ModalFooter {...defaultProps} />);
      const applyButton = screen.getByText('Apply');
      expect(applyButton).toBeDisabled();
    });

    it('should be disabled when no department or desk is selected', () => {
      render(
        <ModalFooter
          {...defaultProps}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).toBeDisabled();
    });

    it('should be disabled when department is an empty array', () => {
      render(
        <ModalFooter
          {...defaultProps}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDepartment={[]}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).toBeDisabled();
    });

    it('should be enabled when division and department are selected', () => {
      render(
        <ModalFooter
          {...defaultProps}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDepartment={[{ name: 'Test Department', num: 1 }]}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).not.toBeDisabled();
    });

    it('should be enabled when division and desk are selected', () => {
      render(
        <ModalFooter
          {...defaultProps}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDesk={{ name: 'Test Desk', num: 1 }}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).not.toBeDisabled();
    });

    it('should be disabled when timeframe is required but not selected', () => {
      render(
        <ModalFooter
          {...defaultProps}
          shouldDisplayTimeFrame={true}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDepartment={[{ name: 'Test Department', num: 1 }]}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).toBeDisabled();
    });

    it('should be enabled when all required fields are selected', () => {
      render(
        <ModalFooter
          {...defaultProps}
          shouldDisplayTimeFrame={true}
          selectedTimeframe={{ name: 'Q1 FY2025', num: 1 }}
          selectedPeriods={[{ name: 'Period 1', num: 1 }]}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDepartment={[{ name: 'Test Department', num: 1 }]}
        />
      );
      const applyButton = screen.getByText('Apply');
      expect(applyButton).not.toBeDisabled();
    });
  });

  describe('Button actions', () => {
    it('should call resetFilters and close when Cancel is clicked', () => {
      render(<ModalFooter {...defaultProps} />);
      const cancelButton = screen.getByText('Cancel');
      cancelButton.click();
      expect(defaultProps.resetFilters).toHaveBeenCalled();
      expect(defaultProps.close).toHaveBeenCalled();
    });

    it('should call applyFilters and close when Apply is clicked', () => {
      render(
        <ModalFooter
          {...defaultProps}
          selectedDivision={[{ name: 'Test Division', num: 1 }]}
          selectedDepartment={[{ name: 'Test Department', num: 1 }]}
        />
      );
      const applyButton = screen.getByText('Apply');
      applyButton.click();
      expect(defaultProps.applyFilters).toHaveBeenCalled();
      expect(defaultProps.close).toHaveBeenCalled();
    });
  });
}); 