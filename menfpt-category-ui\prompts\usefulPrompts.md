
## Break Large Files into Smaller Modules

- Break this file into multiple files.
- Limit the number of code lines to 150 per file.
- Break the render HTML part into multiple variables.
- Modularize the functions, each not exceeding more than 15 lines.
- Name the files and function names appropriately.

---

## Create a Redux Toolkit (RTK) Slice

- Create a RTK slice in parallel to the existing corresponding component files.
- The slice should have a comment describing its purpose.
- The slice name should always be suffixed with `_rn`.
- The initial status should be `'loading'`.
- Example code:

```js
import { createGenericSlice } from 'apps/menfpt-category-ui/src/rtk/rtk-slice';

// Store the selected tab in the filter
export const activeTabInFilterSlice = createGenericSlice({
  name: 'activeTabInFilter_rn',
  initialState: { status: 'loading', data: ['department'] },
})({
  setActiveTabInFilter(state, { payload }) {
    state.data = payload;
  },
});

export const { setActiveTabInFilter } = activeTabInFilterSlice.actions;
```



- To read data from the slice:

```js
const {data: [activeTab]} = useSelectorWrap('activeTabInFilter_rn');
```

- To dispatch an action:

```js
const dispatch = useDispatch();
dispatch(setDeptRoleSuggestions(suggestions));
```

==================
# Improve code

Analyze the code, comments  and find the below:
Any missed/unhandled usecases
Any edge cases
Any performance improvements possible
Any refactoring possible
Any codesmells

==================
# Testcases


- Fix, rewrite, or create new specs for this file.
- Check if there is an existing `.spec.*` file; if not, create a new one parallel to the code file.
- Use BDD style, covering all branches, positive, negative, and edge cases.
- Once test cases are ready, run them and fix any failed test cases.
- Repeat until all test cases pass.

=======
# Regression and Test Execution

Execute npm run test .  Fix all the failing testcases. Run the testcases on the fixed spec files.  Repeat untill all testcases across the failed spec files were passed. 
Update the testcases as per the code implementation and dont change the actual code.




=================

  Make the changes less invasive and make sure there are no regression issues

=================

Review the changes done till now and refine the chnages to make the fix simple

=================

Add logs for debugging in the below format:
console.info("irk-", "");
Suffix  "irk-" with relevant string

 Stringify the objects while consoling to  see the full structure and values.

