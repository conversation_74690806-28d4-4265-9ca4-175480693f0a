import React, { useState } from 'react';
import './departmentDeskTabs.scss';

import Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';
import { useDispatch } from 'react-redux';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { setActiveTabInFilter } from '../../worksheetFilter.slice';
import { DepartmentSelection } from '../department/departmentSelection';
import { DeskSelection } from '../deskSelection';
import SmRoleUsersList from '../roles/smRoleUsersList';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { SmDataType } from '../../types/smTypes';
interface DepartmentDeskTabsProps {
  departments: DropdownType[];
  desks: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  selectedDesk?: DropdownType;
  isMultipleDeptsSelectionAllowed: boolean;
  searchQueryDepartment: string;
  searchQueryDesk: string;
  onDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  onDeskChange: (desk: DropdownType) => void;
  onDepartmentSearch: (query: string) => void;
  onDeskSearch: (query: string) => void;
  smDataForSelectedDept: SmDataType;
  isDisplayDeptRoleCascade: boolean;
}

export const DepartmentDeskTabs: React.FC<DepartmentDeskTabsProps> = ({
  departments,
  desks,
  selectedDepartment,
  selectedDesk,
  isMultipleDeptsSelectionAllowed,
  searchQueryDepartment,
  searchQueryDesk,
  onDepartmentChange,
  onDeskChange,
  onDepartmentSearch,
  onDeskSearch,
  smDataForSelectedDept: initialSmData,
  isDisplayDeptRoleCascade,
}) => {
  const dispatch = useDispatch();
  const [smDataForSelectedDept, setSmDataForSelectedDept] = useState<SmDataType>(initialSmData || []);
  const { data: { focused: isRoleCascadeSearchFocused } = { focused: false } } = useSelectorWrap("roleCascadeSearchFocus_rn");

  const setActiveTab = (selectedTab) => {
    dispatch(setActiveTabInFilter([selectedTab]));
  };
  return (
    <div className="FilterTab">
      <Tabs
        className={`FilterTabHierarchy${isRoleCascadeSearchFocused ? ' roleCascadeSearchFocused' : ''}`}
        variant="light"
        onChange={(tab) => setActiveTab(tab === 0 ? 'department' : 'desk')}
      >
        <Tab className="rounded-4xl">
          <Tab.Header>
            <span className="BodyParagraphMSemiBold">Department</span>
          </Tab.Header>
          <Tab.Content>
            <div className="flex gap-4 w-full overflow-x-auto pt-[8px]">
              <DepartmentSelection
                departments={departments}
                selectedDepartment={selectedDepartment}
                isMultipleSelectionAllowed={isMultipleDeptsSelectionAllowed}
                searchQuery={searchQueryDepartment}
                onSearchChange={onDepartmentSearch}
                onDepartmentChange={onDepartmentChange}
                onSmDataChange={setSmDataForSelectedDept}
                showSearch={false}
              />
              {isDisplayDeptRoleCascade && (
                <SmRoleUsersList />
              )}
            </div>
          </Tab.Content>
        </Tab>
        <Tab>
          <Tab.Header>
            <span className="BodyParagraphMSemiBold">Desk</span>
          </Tab.Header>
          <Tab.Content>
            <div className="pt-4">
              <DeskSelection
                desks={desks}
                selectedDesk={selectedDesk}
                onDeskChange={onDeskChange}
              />
            </div>
          </Tab.Content>
        </Tab>
      </Tabs>
    </div>
  );
};
