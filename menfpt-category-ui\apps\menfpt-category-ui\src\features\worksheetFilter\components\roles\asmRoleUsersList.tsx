import React from 'react';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { setSelectedSm } from './rolesFilter.slice';
import { useDispatch } from 'react-redux';

import { formatName } from './rolesUtils';
import ClearSelection from './clearSelection';
import { deserializeSmData } from '../../utils/serializationUtils';
import { extractCurrentRoute } from '../../worksheetFilterRouteUtils';
import { worksheetFilterConfig } from '../../worksheetFilterConfig';
import { SelectableList, SelectableItem } from '../shared';

const AsmRoleUsersList = ({ activeSearchQuery, selectedSm, handleAsmChange }: {
    activeSearchQuery?: string | null,
    selectedSm: string | undefined,
    handleAsmChange: (checked: boolean, asm: string) => void
}) => {
  
  const { data: serializedSelectedSmData } = useSelectorWrap('selectedSm_rn') || {};
  const selectedSmData = deserializeSmData(serializedSelectedSmData);
  const { data: serializedSmDataForSelectedDept } = useSelectorWrap('smDataForSelectedDept_rn') || {};
  const smDataForSelectedDept = deserializeSmData(serializedSmDataForSelectedDept);

  const asmData = selectedSm ? smDataForSelectedDept.get(selectedSm) : new Set<string>();
  const selectedAsm = selectedSm ? selectedSmData.get(selectedSm) : new Set<string>();

  const { data: deptSuggestionsData } = useSelectorWrap('deptRoleSuggestions_rn');
  const currentRoute = extractCurrentRoute(location.pathname);
  const isMultipleSelectionsAllowed = worksheetFilterConfig.isAllowMultipleDivisonsSelection.includes(currentRoute);
  const { cascadeSearchSelectedItemId, cascadeSearchSelectedItemType } = deptSuggestionsData || {};
  const dispatch = useDispatch();

  const onAsmChange = (item: SelectableItem, checked?: boolean) => {
    const asm = item.data as string;
    handleAsmChange(!!checked, asm);
  };

  const hasItems = selectedSm && asmData && asmData.size > 0;

  // Convert ASM data to SelectableItem format
  const asmItems: SelectableItem[] = hasItems ? Array.from(asmData).map((asm) => ({
    id: asm,
    label: formatName(asm),
    data: asm
  })) : [];

  // Convert selected ASM data to SelectableItem format
  const selectedAsmItems: SelectableItem[] = selectedAsm ? Array.from(selectedAsm).map((asm) => ({
    id: asm,
    label: formatName(asm),
    data: asm
  })) : [];

  const emptyMessage = selectedSm ? 'No ASM available' : 'Select a SM to view';

  return (
    <div className="filtered-list-container min-h-0 bg-white flex flex-col">
      <SelectableList
        items={asmItems}
        selectedItems={selectedAsmItems}
        isMultipleSelectionAllowed={isMultipleSelectionsAllowed}
        onItemChange={onAsmChange}
        emptyMessage={emptyMessage}
        className="flex-1"
        listClassName="self-stretch flex-1 relative flex flex-col justify-start items-start overflow-y-auto h-[303px] pl-6"
        itemClassName="self-stretch min-h-10 p-2.5 bg-white rounded-lg inline-flex justify-start items-center gap-6 overflow-hidden"
      />
    </div>
  );
};

export default AsmRoleUsersList;
