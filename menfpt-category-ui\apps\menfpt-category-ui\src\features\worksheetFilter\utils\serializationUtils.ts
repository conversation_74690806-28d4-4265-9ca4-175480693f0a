import { SmDataType } from '../types/smTypes';

/**
 * Converts a Map<string, Set<string>> to a plain object for Redux serialization
 */
export const serializeSmData = (smData: SmDataType): Record<string, string[]> => {
  if (smData instanceof Map) {
    return Object.fromEntries(
      Array.from(smData.entries()).map(([key, value]) => [
        key, 
        value instanceof Set ? Array.from(value) : value
      ])
    );
  }
  return smData as Record<string, string[]>;
};

/**
 * Converts a plain object back to Map<string, Set<string>> for business logic use
 */
export const deserializeSmData = (serializedData: Record<string, string[]> | SmDataType): SmDataType => {
  if (serializedData instanceof Map) {
    return serializedData;
  }
  
  if (!serializedData || typeof serializedData !== 'object') {
    return new Map();
  }
  
  return new Map(
    Object.entries(serializedData).map(([key, value]) => [
      key,
      Array.isArray(value) ? new Set(value) : new Set()
    ])
  );
};

/**
 * Creates an empty serialized SM data object
 */
export const createEmptySerializedSmData = (): Record<string, string[]> => ({});

/**
 * Creates an empty SM data Map
 */
export const createEmptySmData = (): SmDataType => new Map();
