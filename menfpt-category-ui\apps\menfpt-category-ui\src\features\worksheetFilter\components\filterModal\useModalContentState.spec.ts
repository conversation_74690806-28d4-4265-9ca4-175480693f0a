
// Mocks must be set before importing the hook
const mockDepartments = [
  { label: 'Dept1', value: 'dept1', name: 'Dept1', num: 1 },
  { label: 'Dept2', value: 'dept2', name: 'Dept2', num: 2 }
];
const mockDesks = [
  { label: 'Desk1', value: 'desk1', name: 'Desk1', num: 1 },
  { label: 'Desk2', value: 'desk2', name: 'Desk2', num: 2 }
];

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));
jest.mock('../../../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn((key) => {
    if (key === 'departments_rn') return { data: mockDepartments };
    if (key === 'departmentDeskSearchQuery_rn') return { data: { value: '' } };
    return { data: [] };
  })
}));
jest.mock('../../searchUtils', () => ({
  filterBySearch: jest.fn((arr, query) => arr.filter((item) => item.label.includes(query))),
  filterByDeptRolesSearch: jest.fn(() => [{ label: 'Role1', value: 'role1' }])
}));
jest.mock('../suggestions/deptRoleSuggestions.slice', () => ({
  setDeptRoleSuggestions: jest.fn(() => ({ type: 'setDeptRoleSuggestions' })),
  clearCascadeSearchSelectedItem: jest.fn(() => ({ type: 'clearCascadeSearchSelectedItem' }))
}));
jest.mock('../deptDesk/departmentDeskSearchQuery.slice', () => ({
  setDepartmentDeskSearchQuery: jest.fn(() => ({ type: 'setDepartmentDeskSearchQuery' }))
}));

import { renderHook, act } from '@testing-library/react-hooks';
import { useModalContentState } from './useModalContentState';
import { useDispatch } from 'react-redux';

describe('useModalContentState', () => {
  let dispatchMock: jest.Mock;

  beforeEach(() => {
    dispatchMock = jest.fn();
    (useDispatch as jest.Mock).mockReturnValue(dispatchMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize and filter departments', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: false,
      activeTabInFilter: ['department'],
    }));
    expect(result.current.filteredDepartments).toEqual(mockDepartments);
    act(() => {
      result.current.handleDepartmentSearch('Dept1');
    });
    expect(result.current.filteredDepartments).toEqual([{ label: 'Dept1', value: 'dept1', name: 'Dept1', num: 1 }]);
  });

  it('should filter desks', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: false,
      activeTabInFilter: ['desk'],
    }));
    act(() => {
      result.current.handleDeskSearch('Desk2');
    });
    expect(result.current.filteredDesks).toEqual([{ label: 'Desk2', value: 'desk2', name: 'Desk2', num: 2 }]);
  });

  it('should handle dept roles search and dispatch actions', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: true,
      activeTabInFilter: ['department'],
    }));
    act(() => {
      result.current.handleDeptRolesSearch('');
    });
    expect(dispatchMock).toHaveBeenCalledWith({ type: 'clearCascadeSearchSelectedItem' });
    act(() => {
      result.current.handleDeptRolesSearch('Role1');
    });
    expect(dispatchMock).toHaveBeenCalledWith({ type: 'setDepartmentDeskSearchQuery' });
  });

  it('should set and get search focus and suggestions visibility', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: false,
      activeTabInFilter: ['department'],
    }));
    act(() => {
      result.current.setIsSearchFocused(true);
      result.current.setIsSuggestionsVisible(true);
    });
    expect(result.current.isSearchFocused).toBe(true);
    expect(result.current.isSuggestionsVisible).toBe(true);
  });

  it('should return correct search handler and value', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: true,
      activeTabInFilter: ['department'],
    }));
    expect(result.current.getSearchHandler()).toBe(result.current.handleDeptRolesSearch);
    expect(result.current.getSearchValue()).toBe('');
  });

  it('should handle click outside and hide suggestions', () => {
    const { result } = renderHook(() => useModalContentState({
      desks: mockDesks,
      isDisplayDeptRoleCascade: false,
      activeTabInFilter: ['department'],
    }));
    // Attach a real DOM node to the ref
    const div = document.createElement('div');
    document.body.appendChild(div);
    // Mock the ref so 'current' is writable
    (result.current.searchContainerRef as { current: HTMLElement | null }).current = div;
    act(() => {
      result.current.setIsSuggestionsVisible(true);
    });
    // Simulate click outside
    act(() => {
      document.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
    });
    expect(result.current.isSuggestionsVisible).toBe(false);
    document.body.removeChild(div);
  });
});
