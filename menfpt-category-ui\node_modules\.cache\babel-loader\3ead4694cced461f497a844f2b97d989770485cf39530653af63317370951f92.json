{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\components\\\\AllocatrInsights\\\\AllocatrInsights.tsx\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useSelectorWrap } from '../../rtk/rtk-utilities';\nimport { useGetAllocatrTableDataMutation, useGetDisplayDateQuery } from '../../server/Api/menfptCategoryAPI';\nimport Spinner from '@albertsons/uds/molecule/Spinner';\nimport { allocatrDashboardTableQuery } from '../../server/Query/allocatrDashboardTableQuery';\nimport AllocatrInsightsTable from './AllocatrInsightsTable';\nimport { worksheetFilterConfig } from '../../features/worksheetFilter/worksheetFilterConfig';\nimport { getAllCategoriesFromFilter } from './AllocatrInsightsHelper';\nimport { createQtrPayloadForCalendar, handleCalendarApiResp, useCurrentQuarterNbr } from '../../features/calendarServiceUtils';\nimport { useDispatch } from 'react-redux';\nimport { setDataForQrtrDisplayedInTable } from '../quarterDetails.slice';\nimport { addDaysToDate } from '../../util/dateUtils';\nimport { subtractForecastVarianceData } from './Forecastvariance';\nimport { getPerformanceVarianceSnapshotTimestamps, formatSnapshotTimestamps } from './utils/performanceVarianceUtils';\nimport { useExtractBannerId } from '../../util/filterUtils';\n// import { mockObjA, mockObjB } from './__mockForecastVarianceTest';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DAY_END_TS = 'T23:59:59.000';\nconst PERFORMANCE_SUMMARY = 'Performance Summary';\nconst FORECAST_VARIANCE = 'Performance Variance';\nconst LATEST_DATA = 'Latest data';\nconst QUARTER_CHANGE = 'quarterchange';\nconst DROPDOWN = 'dropdown';\nconst AllocatrInsights = ({\n  onDataLoaded,\n  selectedTab\n}) => {\n  const dispatch = useDispatch();\n  const [departments, setDepartments] = useState([]);\n  const [insightsData, setInsightsData] = useState({\n    id: '',\n    name: '',\n    divisions: []\n  });\n  const [lastFridayDepartments, setLastFridayDepartments] = useState([]);\n  console.log(lastFridayDepartments, \"last friday full response\");\n  console.log(lastFridayDepartments[0], \"Last friday[0] response\");\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n  const {\n    data: worksheetFilters\n  } = useSelectorWrap('workSheetFilterList_rn') || {};\n  const {\n    data: selectedWeek\n  } = useSelectorWrap('saveWeekSelection_rn');\n  const smicData = (worksheetFilters == null ? void 0 : worksheetFilters.smicData) || [];\n  const [getAllocatrTableData] = useGetAllocatrTableDataMutation();\n  const [calendarApiPayload, setCalendarApiPayload] = useState(null);\n  const {\n    data: calendarApiResp\n  } = useGetDisplayDateQuery(calendarApiPayload, {\n    skip: !calendarApiPayload\n  });\n  const {\n    filterPg,\n    department,\n    timeframe,\n    periods,\n    selectedSm\n  } = appliedFilters || {};\n  const prevSelectedWeek = useRef(null);\n  const {\n    data: CalendarWeek\n  } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};\n  const prevTimeframe = useRef(null);\n  const currentQuarterNbr = useCurrentQuarterNbr();\n  const {\n    data: displayDate\n  } = useSelectorWrap('displayDate_rn');\n  const currentWeekFiscalNumber = displayDate == null ? void 0 : displayDate.fiscalWeekNumber;\n  const timeZone = 'America/Los_Angeles';\n  const weekNumbersInQuarter = Array.isArray(CalendarWeek) ? CalendarWeek.map(w => w.fiscalWeekNumber) : [];\n  const currentWeekIndexInQuarter = currentWeekFiscalNumber ? weekNumbersInQuarter.findIndex(num => num === currentWeekFiscalNumber) + 1 : 0;\n  const totalWeeksInQuarter = weekNumbersInQuarter.length;\n  const divisionBannerPairs = useExtractBannerId();\n  const getRequestedQuarterNbr = React.useCallback(() => {\n    var _appliedFilters$timef;\n    return ((_appliedFilters$timef = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef.num) || 1;\n  }, [appliedFilters]);\n  const getAllPreviousweeks = selectedWeek => {\n    if (!Array.isArray(CalendarWeek) || !selectedWeek) return [];\n    const weekNum = parseInt(selectedWeek.value.split('/')[2] + '' + selectedWeek.name.split(' ')[2]);\n    if (isNaN(weekNum)) return [];\n    return CalendarWeek.filter(week => week.fiscalWeekNumber <= weekNum).map(week => week.fiscalWeekNumber);\n  };\n  const getWorksheetTable = async deptIds => {\n    var _appliedFilters$timef2, _appliedFilters$perio, _appliedFilters$selec;\n    const currentFiscalYearNbr = appliedFilters == null || (_appliedFilters$timef2 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef2.fiscalYear;\n    const quarterNbr = getRequestedQuarterNbr();\n    const selectedPeriods = (appliedFilters == null || (_appliedFilters$perio = appliedFilters.periods) == null ? void 0 : _appliedFilters$perio.map(item => item.num)) || [];\n    const selectedWeeks = (appliedFilters == null || (_appliedFilters$selec = appliedFilters.selectedWeeks) == null ? void 0 : _appliedFilters$selec.map(item => item.weekNum)) || [];\n    // const divisionIds =\n    //   appliedFilters?.division?.map((item: { num: number | string }) =>\n    //     item.num ? item.num.toString() : 0\n    //   ) || [];\n\n    const getLatestReleaseWeek = () => {\n      if (!selectedWeek || selectedWeek.name === LATEST_DATA) return {};\n      return {\n        latestReleaseWeek: {\n          value: addDaysToDate(selectedWeek.value, 2),\n          name: selectedWeek.name\n        },\n        weekNumbers: getAllPreviousweeks(selectedWeek)\n      };\n    };\n    if (selectedTab === PERFORMANCE_SUMMARY) {\n      const filterArgs = {\n        query: allocatrDashboardTableQuery,\n        variables: {\n          allocatrDashboardReq: Object.assign({\n            currentFiscalYearNbr,\n            quarterNbr,\n            deptIds,\n            divisionBannerPairs,\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\n            type: '',\n            periodNumbers: selectedPeriods,\n            filteredWeekNumbers: selectedWeeks\n          }, getLatestReleaseWeek())\n        }\n      };\n      return await getAllocatrTableData(filterArgs);\n    }\n    if (selectedTab === FORECAST_VARIANCE) {\n      var _appliedFilters$timef3, _lastFridayData$data, _currentData$data;\n      const appliedFiscalQtrNbr = appliedFilters == null || (_appliedFilters$timef3 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef3.fiscalQuarterNumber;\n\n      // Get snapshot timestamps using modular utility function\n      const snapshotTimestamps = getPerformanceVarianceSnapshotTimestamps({\n        selectedWeek,\n        currentQuarterNbr,\n        appliedFiscalQtrNbr,\n        appliedFilters,\n        weekNumbersInQuarter,\n        currentWeekIndexInQuarter,\n        totalWeeksInQuarter,\n        getAllPreviousweeks,\n        timeZone\n      });\n\n      // Format timestamps for API payload\n      const {\n        snapshotFridayPT,\n        snapshotPT\n      } = formatSnapshotTimestamps(snapshotTimestamps, timeZone);\n      const {\n        weekNumbersPayload\n      } = snapshotTimestamps;\n      const filterArgsWithSnapshot = {\n        query: allocatrDashboardTableQuery,\n        variables: {\n          allocatrDashboardReq: {\n            currentFiscalYearNbr,\n            quarterNbr,\n            deptIds,\n            divisionBannerPairs,\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\n            type: '',\n            weekNumbers: weekNumbersPayload,\n            filteredWeekNumbers: selectedWeeks,\n            periodNumbers: selectedPeriods,\n            latestReleaseWeek: {\n              name: 'Last Friday',\n              value: snapshotFridayPT\n            }\n          }\n        }\n      };\n      const filterArgsWithoutSnapshot = {\n        query: allocatrDashboardTableQuery,\n        variables: {\n          allocatrDashboardReq: {\n            currentFiscalYearNbr,\n            quarterNbr,\n            deptIds,\n            divisionBannerPairs,\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\n            type: '',\n            weekNumbers: [],\n            filteredWeekNumbers: selectedWeeks,\n            periodNumbers: selectedPeriods,\n            latestReleaseWeek: {\n              name: 'Current/Monday',\n              value: snapshotPT\n            }\n          }\n        }\n      };\n      console.log('Last Friday payload:', filterArgsWithSnapshot);\n      console.log('Current/Monday payload:', filterArgsWithoutSnapshot);\n      const [lastFridayData, currentData] = await Promise.all([getAllocatrTableData(filterArgsWithSnapshot), getAllocatrTableData(filterArgsWithoutSnapshot)]);\n      const lastFridayArray = 'data' in lastFridayData && (_lastFridayData$data = lastFridayData.data) != null && (_lastFridayData$data = _lastFridayData$data.getAllocatrDashboardTableData) != null && _lastFridayData$data.allocatrDashboardTableData ? lastFridayData.data.getAllocatrDashboardTableData.allocatrDashboardTableData : [];\n      const currentArray = 'data' in currentData && (_currentData$data = currentData.data) != null && (_currentData$data = _currentData$data.getAllocatrDashboardTableData) != null && _currentData$data.allocatrDashboardTableData ? currentData.data.getAllocatrDashboardTableData.allocatrDashboardTableData : [];\n      console.log(lastFridayArray, currentArray, \"Variance response api\");\n      const variance = subtractForecastVarianceData([currentArray], [lastFridayArray], currentQuarterNbr === appliedFiscalQtrNbr);\n      // const variance=subtractForecastVarianceData([mockObjA],[mockObjB],true)\n      console.log(variance, \"Varianceeeeeeeee\");\n      return variance;\n    }\n  };\n  const fetchAllocatrDashboardTableData = () => {\n    var _appliedFilters$depar;\n    setIsLoading(true);\n    let deptIds = [];\n    if (Array.isArray(appliedFilters == null ? void 0 : appliedFilters.department)) {\n      deptIds = appliedFilters.department.map(dept => dept.num.toString());\n    } else if (appliedFilters != null && (_appliedFilters$depar = appliedFilters.department) != null && _appliedFilters$depar.num) {\n      deptIds = [appliedFilters.department.num.toString()];\n    }\n    if (deptIds.length !== 0) {\n      getWorksheetTable(deptIds).then(response => {\n        setIsLoading(false);\n        if (selectedTab === PERFORMANCE_SUMMARY) {\n          console.log('API response:', response);\n          if (response && 'data' in response) {\n            var _response$data;\n            const formattedTableData = (_response$data = response.data) == null || (_response$data = _response$data.getAllocatrDashboardTableData) == null ? void 0 : _response$data.allocatrDashboardTableData;\n            console.log('Formatted Table Data from API:', formattedTableData);\n            if (formattedTableData) {\n              if (Array.isArray(formattedTableData)) {\n                if (formattedTableData.length > 0) {\n                  setInsightsData(formattedTableData[0]);\n                  console.log('Set insightsData from array:', formattedTableData[0]);\n                }\n              } else if (typeof formattedTableData === 'object') {\n                setInsightsData(formattedTableData);\n                console.log('Set insightsData from object:', formattedTableData);\n              }\n            }\n          }\n          handleCalendarApiResp({\n            calendarApiResp,\n            dispatch\n          });\n        }\n        if (selectedTab === FORECAST_VARIANCE) {\n          if (Array.isArray(response)) {\n            setLastFridayDepartments(response.map(item => Object.assign({}, item)));\n          } else {\n            setLastFridayDepartments([]);\n          }\n        }\n        handleCalendarApiResp({\n          calendarApiResp,\n          dispatch\n        });\n      });\n    }\n  };\n  const isDashboardPage = () => {\n    return filterPg === worksheetFilterConfig.lsKeyDashboardPg;\n  };\n  useEffect(() => {\n    if (isDashboardPage()) {\n      var _appliedFilters$timef4, _appliedFilters$timef5;\n      if ((appliedFilters == null || (_appliedFilters$timef4 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef4.num) === prevTimeframe.current) {\n        fetchAllocatrDashboardTableData();\n      } else {\n        setCalendarApiPayload(createQtrPayloadForCalendar(getRequestedQuarterNbr()));\n      }\n      prevTimeframe.current = (appliedFilters == null || (_appliedFilters$timef5 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef5.num) || null;\n    }\n  }, [filterPg, department, timeframe, periods, selectedTab, selectedSm]);\n  useEffect(() => {\n    if (isDashboardPage()) {\n      dispatch(setDataForQrtrDisplayedInTable(calendarApiResp));\n    }\n  }, [calendarApiResp]);\n  useEffect(() => {\n    if (isDashboardPage() && (selectedWeek == null ? void 0 : selectedWeek.from) === QUARTER_CHANGE && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {\n      fetchAllocatrDashboardTableData();\n      prevSelectedWeek.current = selectedWeek;\n    }\n  }, [CalendarWeek, selectedWeek]);\n  useEffect(() => {\n    if (isDashboardPage() && (selectedWeek == null ? void 0 : selectedWeek.from) === DROPDOWN && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {\n      fetchAllocatrDashboardTableData();\n      prevSelectedWeek.current = selectedWeek;\n    }\n  }, [selectedWeek]);\n  useEffect(() => {\n    if (onDataLoaded) {\n      if (selectedTab === PERFORMANCE_SUMMARY) {\n        onDataLoaded(departments);\n      } else if (selectedTab === FORECAST_VARIANCE) {\n        onDataLoaded(lastFridayDepartments);\n      }\n    }\n  }, [departments, lastFridayDepartments, onDataLoaded, selectedTab]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 w-full h-full relative\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container overflow-hidden\",\n      children: isLoading ? /*#__PURE__*/_jsxDEV(Spinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(AllocatrInsightsTable, {\n        insightsData: selectedTab === PERFORMANCE_SUMMARY ? insightsData : selectedTab === FORECAST_VARIANCE ? lastFridayDepartments[0] || {\n          id: '',\n          name: '',\n          divisions: [],\n          periods: [],\n          weeks: []\n        } : {\n          id: '',\n          name: '',\n          divisions: [],\n          periods: [],\n          weeks: []\n        },\n        selectedTab: selectedTab\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n};\nexport default AllocatrInsights;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useSelectorWrap", "useGetAllocatrTableDataMutation", "useGetDisplayDateQuery", "Spinner", "allocatrDashboardTableQuery", "AllocatrInsightsTable", "worksheetFilterConfig", "getAllCategoriesFromFilter", "createQtrPayloadForCalendar", "handleCalendarApiResp", "useCurrentQuarterNbr", "useDispatch", "setDataForQrtrDisplayedInTable", "addDaysToDate", "subtractForecastVarianceData", "getPerformanceVarianceSnapshotTimestamps", "formatSnapshotTimestamps", "useExtractBannerId", "jsxDEV", "_jsxDEV", "DAY_END_TS", "PERFORMANCE_SUMMARY", "FORECAST_VARIANCE", "LATEST_DATA", "QUARTER_CHANGE", "DROPDOWN", "AllocatrInsights", "onDataLoaded", "selectedTab", "dispatch", "departments", "setDepartments", "insightsData", "setInsightsData", "id", "name", "divisions", "lastFridayDepartments", "setLastFridayDepartments", "console", "log", "isLoading", "setIsLoading", "data", "appliedFilters", "worksheetFilters", "selectedWeek", "smicData", "getAllocatrTableData", "calendarApiPayload", "setCalendarApiPayload", "calendarApiResp", "skip", "filterPg", "department", "timeframe", "periods", "selectedSm", "prevSelectedWeek", "CalendarWeek", "prevTimeframe", "currentQuarterNbr", "displayDate", "currentWeekFiscalNumber", "fiscalWeekNumber", "timeZone", "weekNumbersInQuarter", "Array", "isArray", "map", "w", "currentWeekIndexInQuarter", "findIndex", "num", "totalWeeksInQuarter", "length", "divisionBannerPairs", "getRequestedQuarterNbr", "useCallback", "_appliedFilters$timef", "getAllPreviousweeks", "weekNum", "parseInt", "value", "split", "isNaN", "filter", "week", "getWorksheetTable", "deptIds", "_appliedFilters$timef2", "_appliedFilters$perio", "_appliedFilters$selec", "currentFiscalYearNbr", "fiscalYear", "quarterNbr", "selectedPeriods", "item", "selectedWeeks", "getLatestReleaseWeek", "latestReleaseWeek", "weekNumbers", "filterArgs", "query", "variables", "allocatrDashboardReq", "Object", "assign", "smicCategoryIds", "type", "periodNumbers", "filteredWeekNumbers", "_appliedFilters$timef3", "_lastFridayData$data", "_currentData$data", "appliedFiscalQtrNbr", "fiscalQuarterNumber", "snapshotTimestamps", "snapshotFridayPT", "snapshotPT", "weekNumbersPayload", "filterArgsWithSnapshot", "filterArgsWithoutSnapshot", "lastFridayData", "currentData", "Promise", "all", "lastFridayArray", "getAllocatrDashboardTableData", "allocatrDashboardTableData", "currentArray", "variance", "fetchAllocatrDashboardTableData", "_appliedFilters$depar", "dept", "toString", "then", "response", "_response$data", "formattedTableData", "isDashboardPage", "lsKeyDashboardPg", "_appliedFilters$timef4", "_appliedFilters$timef5", "current", "from", "JSON", "stringify", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "weeks"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/components/AllocatrInsights/AllocatrInsights.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useMemo } from 'react';\r\nimport { useSelectorWrap } from '../../rtk/rtk-utilities';\r\nimport { useGetAllocatrTableDataMutation, useGetDisplayDateQuery } from '../../server/Api/menfptCategoryAPI';\r\nimport Spinner from '@albertsons/uds/molecule/Spinner';\r\nimport { allocatrDashboardTableQuery } from '../../server/Query/allocatrDashboardTableQuery';\r\nimport { AllocatrDashboardReq, DepartmentData, AllocatrInsightsResponse } from '../../interfaces/allocatr-insights';\r\nimport AllocatrInsightsTable from './AllocatrInsightsTable';\r\nimport { worksheetFilterConfig } from '../../features/worksheetFilter/worksheetFilterConfig';\r\nimport { getAllCategoriesFromFilter, getSmicsForDeptsAndDivisions } from './AllocatrInsightsHelper';\r\nimport {\r\n  createQtrPayloadForCalendar,\r\n  handleCalendarApiResp,\r\n  useCurrentQuarterNbr,\r\n} from '../../features/calendarServiceUtils';\r\nimport { useDispatch } from 'react-redux';\r\nimport { setDataForQrtrDisplayedInTable } from '../quarterDetails.slice';\r\nimport { addDaysToDate } from '../../util/dateUtils';\r\nimport {\r\n  getLastFriday,\r\n  getLastFridayInQuarter,\r\n  getNextMondayAfterQuarter,\r\n  getFridayOfSelectedWeek,\r\n} from './utils/getLastFriday';\r\nimport { subtractForecastVarianceData } from './Forecastvariance';\r\nimport { utcToZonedTime, format } from 'date-fns-tz';\r\nimport { mockAllocatrInsightsData } from '../../mock/allocatr-insights-mock';\r\nimport {\r\n  getPerformanceVarianceSnapshotTimestamps,\r\n  formatSnapshotTimestamps\r\n} from './utils/performanceVarianceUtils';\r\nimport { useExtractBannerId } from '../../util/filterUtils';\r\n// import { mockObjA, mockObjB } from './__mockForecastVarianceTest';\r\nconst DAY_END_TS = 'T23:59:59.000';\r\nconst PERFORMANCE_SUMMARY = 'Performance Summary';\r\nconst FORECAST_VARIANCE = 'Performance Variance';\r\nconst LATEST_DATA = 'Latest data';\r\nconst QUARTER_CHANGE = 'quarterchange';\r\nconst DROPDOWN = 'dropdown';\r\n\r\ninterface AllocatrInsightsProps {\r\n  selectedTab: string;\r\n  onDataLoaded?: (data: any[]) => void;\r\n}\r\n\r\nconst AllocatrInsights: React.FC<AllocatrInsightsProps> = ({ onDataLoaded, selectedTab }) => {\r\n  const dispatch = useDispatch();\r\n  const [departments, setDepartments] = useState<DepartmentData[]>([]);\r\n  const [insightsData, setInsightsData] = useState<AllocatrInsightsResponse>(\r\n    { id: '', name: '', divisions: [] }\r\n  );\r\n  const [lastFridayDepartments, setLastFridayDepartments] = useState<AllocatrInsightsResponse[]>([]);\r\n  console.log(lastFridayDepartments,\"last friday full response\")\r\n  console.log(lastFridayDepartments[0],\"Last friday[0] response\")\r\n\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n  const { data: worksheetFilters } = useSelectorWrap('workSheetFilterList_rn') || {};\r\n  const { data: selectedWeek } = useSelectorWrap('saveWeekSelection_rn');\r\n  const smicData = worksheetFilters?.smicData || [];\r\n  const [getAllocatrTableData] = useGetAllocatrTableDataMutation();\r\n  const [calendarApiPayload, setCalendarApiPayload] = useState<any | null>(null);\r\n  const { data: calendarApiResp } = useGetDisplayDateQuery(calendarApiPayload, {\r\n    skip: !calendarApiPayload,\r\n  });\r\n  const { filterPg, department, timeframe, periods, selectedSm } = appliedFilters || {};\r\n  const prevSelectedWeek = useRef<typeof selectedWeek | null>(null);\r\n  const { data: CalendarWeek } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};\r\n  const prevTimeframe = useRef<typeof Number | null>(null);\r\n  const currentQuarterNbr = useCurrentQuarterNbr();\r\n  const { data: displayDate } = useSelectorWrap('displayDate_rn');\r\n  const currentWeekFiscalNumber = displayDate?.fiscalWeekNumber;\r\n  const timeZone = 'America/Los_Angeles';\r\n  const weekNumbersInQuarter = Array.isArray(CalendarWeek)\r\n    ? CalendarWeek.map((w: any) => w.fiscalWeekNumber)\r\n    : [];\r\n  const currentWeekIndexInQuarter = currentWeekFiscalNumber\r\n    ? weekNumbersInQuarter.findIndex((num: number) => num === currentWeekFiscalNumber) + 1\r\n    : 0;\r\n  const totalWeeksInQuarter = weekNumbersInQuarter.length;\r\n  const divisionBannerPairs = useExtractBannerId();\r\n\r\n  const getRequestedQuarterNbr = React.useCallback(() => appliedFilters.timeframe?.num || 1, [appliedFilters]);\r\n\r\n  const getAllPreviousweeks = (selectedWeek: any) => {\r\n    if (!Array.isArray(CalendarWeek) || !selectedWeek) return [];\r\n    const weekNum = parseInt(selectedWeek.value.split('/')[2] + '' + selectedWeek.name.split(' ')[2]);\r\n    if (isNaN(weekNum)) return [];\r\n    return CalendarWeek.filter((week: { fiscalWeekNumber: number }) => week.fiscalWeekNumber <= weekNum).map(\r\n      (week: { fiscalWeekNumber: number }) => week.fiscalWeekNumber\r\n    );\r\n  };\r\n  const getWorksheetTable = async (deptIds: string[]) => {\r\n    const currentFiscalYearNbr = appliedFilters?.timeframe?.fiscalYear;\r\n    const quarterNbr = getRequestedQuarterNbr();\r\n    const selectedPeriods = appliedFilters?.periods?.map(item => item.num) || [];\r\n    const selectedWeeks = appliedFilters?.selectedWeeks?.map(item => item.weekNum) || [];\r\n    // const divisionIds =\r\n    //   appliedFilters?.division?.map((item: { num: number | string }) =>\r\n    //     item.num ? item.num.toString() : 0\r\n    //   ) || [];\r\n\r\n    const getLatestReleaseWeek = () => {\r\n      if (!selectedWeek || selectedWeek.name === LATEST_DATA) return {};\r\n      return {\r\n        latestReleaseWeek: {\r\n          value: addDaysToDate(selectedWeek.value, 2),\r\n          name: selectedWeek.name,\r\n        },\r\n        weekNumbers: getAllPreviousweeks(selectedWeek),\r\n      };\r\n    };\r\n\r\n    if (selectedTab === PERFORMANCE_SUMMARY) {\r\n      const filterArgs = {\r\n        query: allocatrDashboardTableQuery,\r\n        variables: {\r\n          allocatrDashboardReq: {\r\n            currentFiscalYearNbr,\r\n            quarterNbr,\r\n            deptIds,\r\n            divisionBannerPairs,\r\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\r\n            type: '',\r\n            periodNumbers: selectedPeriods,\r\n            filteredWeekNumbers: selectedWeeks,\r\n             ...getLatestReleaseWeek(),\r\n          },\r\n        },\r\n      };\r\n      return await getAllocatrTableData(filterArgs);\r\n    }\r\n\r\n    if (selectedTab === FORECAST_VARIANCE) {\r\n      const appliedFiscalQtrNbr = appliedFilters?.timeframe?.fiscalQuarterNumber;\r\n\r\n      // Get snapshot timestamps using modular utility function\r\n      const snapshotTimestamps = getPerformanceVarianceSnapshotTimestamps({\r\n        selectedWeek,\r\n        currentQuarterNbr,\r\n        appliedFiscalQtrNbr,\r\n        appliedFilters,\r\n        weekNumbersInQuarter,\r\n        currentWeekIndexInQuarter,\r\n        totalWeeksInQuarter,\r\n        getAllPreviousweeks,\r\n        timeZone\r\n      });\r\n\r\n      // Format timestamps for API payload\r\n      const { snapshotFridayPT, snapshotPT } = formatSnapshotTimestamps(snapshotTimestamps, timeZone);\r\n      const { weekNumbersPayload } = snapshotTimestamps;\r\n      const filterArgsWithSnapshot = {\r\n        query: allocatrDashboardTableQuery,\r\n        variables: {\r\n          allocatrDashboardReq: {\r\n            currentFiscalYearNbr,\r\n            quarterNbr,\r\n            deptIds,\r\n            divisionBannerPairs,\r\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\r\n            type: '',\r\n            weekNumbers: weekNumbersPayload,\r\n            filteredWeekNumbers: selectedWeeks,\r\n            periodNumbers: selectedPeriods,\r\n             latestReleaseWeek: {\r\n             name: 'Last Friday',\r\n             value: snapshotFridayPT,\r\n        },\r\n          },\r\n        },\r\n      };\r\n\r\n      const filterArgsWithoutSnapshot = {\r\n        query: allocatrDashboardTableQuery,\r\n        variables: {\r\n          allocatrDashboardReq: {\r\n            currentFiscalYearNbr,\r\n            quarterNbr,\r\n            deptIds,\r\n            divisionBannerPairs,\r\n            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),\r\n            type: '',\r\n            weekNumbers: [],\r\n            filteredWeekNumbers: selectedWeeks,\r\n            periodNumbers: selectedPeriods,\r\n            latestReleaseWeek: {\r\n            name: 'Current/Monday',\r\n             value: snapshotPT,\r\n        },\r\n          },\r\n        },\r\n      };\r\n\r\n      console.log('Last Friday payload:', filterArgsWithSnapshot);\r\n      console.log('Current/Monday payload:', filterArgsWithoutSnapshot);\r\n      const [lastFridayData, currentData] = await Promise.all([\r\n        getAllocatrTableData(filterArgsWithSnapshot),\r\n        getAllocatrTableData(filterArgsWithoutSnapshot),\r\n      ]);\r\n      const lastFridayArray =\r\n        'data' in lastFridayData && lastFridayData.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData\r\n          ? lastFridayData.data.getAllocatrDashboardTableData.allocatrDashboardTableData\r\n          : [];\r\n\r\n      const currentArray =\r\n        'data' in currentData && currentData.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData\r\n          ? currentData.data.getAllocatrDashboardTableData.allocatrDashboardTableData\r\n          : [];\r\n          console.log(lastFridayArray,currentArray,\"Variance response api\")\r\n     const variance = subtractForecastVarianceData([currentArray], [lastFridayArray], currentQuarterNbr === appliedFiscalQtrNbr);\r\n    // const variance=subtractForecastVarianceData([mockObjA],[mockObjB],true)\r\n      console.log(variance,\"Varianceeeeeeeee\")\r\n      return variance;\r\n    }\r\n  };\r\n\r\n  const fetchAllocatrDashboardTableData = () => {\r\n    setIsLoading(true);\r\n    let deptIds: string[] = [];\r\n\r\n    if (Array.isArray(appliedFilters?.department)) {\r\n      deptIds = appliedFilters.department.map((dept: { num: string | number }) => dept.num.toString());\r\n    } else if (appliedFilters?.department?.num) {\r\n      deptIds = [appliedFilters.department.num.toString()];\r\n    }\r\n    if (deptIds.length !== 0) {\r\n        getWorksheetTable(deptIds).then(response => {\r\n          setIsLoading(false);\r\n          if (selectedTab === PERFORMANCE_SUMMARY) {\r\n          console.log('API response:', response);\r\n          if (response && 'data' in response) {\r\n            const formattedTableData = response.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData;\r\n            console.log('Formatted Table Data from API:', formattedTableData);\r\n            if (formattedTableData) {\r\n              if (Array.isArray(formattedTableData)) {\r\n                if (formattedTableData.length > 0) {\r\n                  setInsightsData(formattedTableData[0]);\r\n                  console.log('Set insightsData from array:', formattedTableData[0]);\r\n                } \r\n              }\r\n               else if (typeof formattedTableData === 'object') {\r\n                setInsightsData(formattedTableData);\r\n                console.log('Set insightsData from object:', formattedTableData);\r\n              } \r\n            } \r\n          } \r\n          handleCalendarApiResp({ calendarApiResp, dispatch });\r\n        }\r\n           if (selectedTab === FORECAST_VARIANCE) {\r\n          if (Array.isArray(response)) {\r\n            setLastFridayDepartments(\r\n              response.map((item: any) => ({ ...item }))\r\n            );\r\n          } \r\n          else {\r\n            setLastFridayDepartments([]);\r\n          }\r\n        }\r\n        handleCalendarApiResp({ calendarApiResp, dispatch });\r\n      });\r\n      }\r\n    };\r\n  const isDashboardPage = () => {\r\n    return filterPg === worksheetFilterConfig.lsKeyDashboardPg;\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (isDashboardPage()) {\r\n      if (appliedFilters?.timeframe?.num === prevTimeframe.current) {\r\n        fetchAllocatrDashboardTableData();\r\n      } else {\r\n        setCalendarApiPayload(createQtrPayloadForCalendar(getRequestedQuarterNbr()));\r\n      }\r\n      prevTimeframe.current = appliedFilters?.timeframe?.num || null;\r\n    }\r\n  }, [filterPg, department, timeframe, periods, selectedTab, selectedSm]);\r\n\r\n  useEffect(() => {\r\n    if(isDashboardPage()) {\r\n      dispatch(setDataForQrtrDisplayedInTable(calendarApiResp));\r\n    }\r\n  }, [calendarApiResp]);\r\n\r\n  useEffect(() => {\r\n    if (isDashboardPage() && selectedWeek?.from === QUARTER_CHANGE && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {\r\n      fetchAllocatrDashboardTableData();\r\n      prevSelectedWeek.current = selectedWeek;\r\n    }\r\n  }, [CalendarWeek, selectedWeek]);\r\n\r\n  useEffect(() => {\r\n    if (isDashboardPage() && selectedWeek?.from === DROPDOWN && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {\r\n      fetchAllocatrDashboardTableData();\r\n      prevSelectedWeek.current = selectedWeek;\r\n    }\r\n  }, [selectedWeek]);\r\n\r\nuseEffect(() => {\r\n    if (onDataLoaded) {\r\n      if (selectedTab === PERFORMANCE_SUMMARY) {\r\n        onDataLoaded(departments);\r\n      } else if (selectedTab === FORECAST_VARIANCE) {\r\n        onDataLoaded(lastFridayDepartments);\r\n      }\r\n    }\r\n  }, [departments, lastFridayDepartments, onDataLoaded, selectedTab]);\r\n\r\n  return (\r\n    <div className=\"flex-1 w-full h-full relative\">\r\n      <div className=\"table-container overflow-hidden\">\r\n        {isLoading ? (\r\n          <Spinner />\r\n        ) : (\r\n          <AllocatrInsightsTable\r\n            insightsData={\r\n              selectedTab === PERFORMANCE_SUMMARY\r\n                ? insightsData\r\n                : selectedTab === FORECAST_VARIANCE\r\n                ? lastFridayDepartments[0] || { id: '', name: '', divisions: [], periods: [], weeks: [] }\r\n                : { id: '', name: '', divisions: [], periods: [], weeks: [] }\r\n            }\r\n            selectedTab={selectedTab}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AllocatrInsights;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAiB,OAAO;AACnE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,+BAA+B,EAAEC,sBAAsB,QAAQ,oCAAoC;AAC5G,OAAOC,OAAO,MAAM,kCAAkC;AACtD,SAASC,2BAA2B,QAAQ,gDAAgD;AAE5F,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,qBAAqB,QAAQ,sDAAsD;AAC5F,SAASC,0BAA0B,QAAsC,0BAA0B;AACnG,SACEC,2BAA2B,EAC3BC,qBAAqB,EACrBC,oBAAoB,QACf,qCAAqC;AAC5C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,8BAA8B,QAAQ,yBAAyB;AACxE,SAASC,aAAa,QAAQ,sBAAsB;AAOpD,SAASC,4BAA4B,QAAQ,oBAAoB;AAGjE,SACEC,wCAAwC,EACxCC,wBAAwB,QACnB,kCAAkC;AACzC,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAG,eAAe;AAClC,MAAMC,mBAAmB,GAAG,qBAAqB;AACjD,MAAMC,iBAAiB,GAAG,sBAAsB;AAChD,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,cAAc,GAAG,eAAe;AACtC,MAAMC,QAAQ,GAAG,UAAU;AAO3B,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAC3F,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAmB,EAAE,CAAC;EACpE,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAC9C;IAAEqC,EAAE,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAG,CACpC,CAAC;EACD,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzC,QAAQ,CAA6B,EAAE,CAAC;EAClG0C,OAAO,CAACC,GAAG,CAACH,qBAAqB,EAAC,2BAA2B,CAAC;EAC9DE,OAAO,CAACC,GAAG,CAACH,qBAAqB,CAAC,CAAC,CAAC,EAAC,yBAAyB,CAAC;EAE/D,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAE8C,IAAI,EAAEC;EAAe,CAAC,GAAG5C,eAAe,CAAC,kBAAkB,CAAC;EACpE,MAAM;IAAE2C,IAAI,EAAEE;EAAiB,CAAC,GAAG7C,eAAe,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;EAClF,MAAM;IAAE2C,IAAI,EAAEG;EAAa,CAAC,GAAG9C,eAAe,CAAC,sBAAsB,CAAC;EACtE,MAAM+C,QAAQ,GAAG,CAAAF,gBAAgB,oBAAhBA,gBAAgB,CAAEE,QAAQ,KAAI,EAAE;EACjD,MAAM,CAACC,oBAAoB,CAAC,GAAG/C,+BAA+B,CAAC,CAAC;EAChE,MAAM,CAACgD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrD,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM;IAAE8C,IAAI,EAAEQ;EAAgB,CAAC,GAAGjD,sBAAsB,CAAC+C,kBAAkB,EAAE;IAC3EG,IAAI,EAAE,CAACH;EACT,CAAC,CAAC;EACF,MAAM;IAAEI,QAAQ;IAAEC,UAAU;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGb,cAAc,IAAI,CAAC,CAAC;EACrF,MAAMc,gBAAgB,GAAG3D,MAAM,CAA6B,IAAI,CAAC;EACjE,MAAM;IAAE4C,IAAI,EAAEgB;EAAa,CAAC,GAAG3D,eAAe,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;EACtF,MAAM4D,aAAa,GAAG7D,MAAM,CAAuB,IAAI,CAAC;EACxD,MAAM8D,iBAAiB,GAAGnD,oBAAoB,CAAC,CAAC;EAChD,MAAM;IAAEiC,IAAI,EAAEmB;EAAY,CAAC,GAAG9D,eAAe,CAAC,gBAAgB,CAAC;EAC/D,MAAM+D,uBAAuB,GAAGD,WAAW,oBAAXA,WAAW,CAAEE,gBAAgB;EAC7D,MAAMC,QAAQ,GAAG,qBAAqB;EACtC,MAAMC,oBAAoB,GAAGC,KAAK,CAACC,OAAO,CAACT,YAAY,CAAC,GACpDA,YAAY,CAACU,GAAG,CAAEC,CAAM,IAAKA,CAAC,CAACN,gBAAgB,CAAC,GAChD,EAAE;EACN,MAAMO,yBAAyB,GAAGR,uBAAuB,GACrDG,oBAAoB,CAACM,SAAS,CAAEC,GAAW,IAAKA,GAAG,KAAKV,uBAAuB,CAAC,GAAG,CAAC,GACpF,CAAC;EACL,MAAMW,mBAAmB,GAAGR,oBAAoB,CAACS,MAAM;EACvD,MAAMC,mBAAmB,GAAG3D,kBAAkB,CAAC,CAAC;EAEhD,MAAM4D,sBAAsB,GAAGjF,KAAK,CAACkF,WAAW,CAAC;IAAA,IAAAC,qBAAA;IAAA,OAAM,EAAAA,qBAAA,GAAAnC,cAAc,CAACW,SAAS,qBAAxBwB,qBAAA,CAA0BN,GAAG,KAAI,CAAC;EAAA,GAAE,CAAC7B,cAAc,CAAC,CAAC;EAE5G,MAAMoC,mBAAmB,GAAIlC,YAAiB,IAAK;IACjD,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACT,YAAY,CAAC,IAAI,CAACb,YAAY,EAAE,OAAO,EAAE;IAC5D,MAAMmC,OAAO,GAAGC,QAAQ,CAACpC,YAAY,CAACqC,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGtC,YAAY,CAACX,IAAI,CAACiD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,IAAIC,KAAK,CAACJ,OAAO,CAAC,EAAE,OAAO,EAAE;IAC7B,OAAOtB,YAAY,CAAC2B,MAAM,CAAEC,IAAkC,IAAKA,IAAI,CAACvB,gBAAgB,IAAIiB,OAAO,CAAC,CAACZ,GAAG,CACrGkB,IAAkC,IAAKA,IAAI,CAACvB,gBAC/C,CAAC;EACH,CAAC;EACD,MAAMwB,iBAAiB,GAAG,MAAOC,OAAiB,IAAK;IAAA,IAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACrD,MAAMC,oBAAoB,GAAGjD,cAAc,aAAA8C,sBAAA,GAAd9C,cAAc,CAAEW,SAAS,qBAAzBmC,sBAAA,CAA2BI,UAAU;IAClE,MAAMC,UAAU,GAAGlB,sBAAsB,CAAC,CAAC;IAC3C,MAAMmB,eAAe,GAAG,CAAApD,cAAc,aAAA+C,qBAAA,GAAd/C,cAAc,CAAEY,OAAO,qBAAvBmC,qBAAA,CAAyBtB,GAAG,CAAC4B,IAAI,IAAIA,IAAI,CAACxB,GAAG,CAAC,KAAI,EAAE;IAC5E,MAAMyB,aAAa,GAAG,CAAAtD,cAAc,aAAAgD,qBAAA,GAAdhD,cAAc,CAAEsD,aAAa,qBAA7BN,qBAAA,CAA+BvB,GAAG,CAAC4B,IAAI,IAAIA,IAAI,CAAChB,OAAO,CAAC,KAAI,EAAE;IACpF;IACA;IACA;IACA;;IAEA,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAACrD,YAAY,IAAIA,YAAY,CAACX,IAAI,KAAKZ,WAAW,EAAE,OAAO,CAAC,CAAC;MACjE,OAAO;QACL6E,iBAAiB,EAAE;UACjBjB,KAAK,EAAEtE,aAAa,CAACiC,YAAY,CAACqC,KAAK,EAAE,CAAC,CAAC;UAC3ChD,IAAI,EAAEW,YAAY,CAACX;QACrB,CAAC;QACDkE,WAAW,EAAErB,mBAAmB,CAAClC,YAAY;MAC/C,CAAC;IACH,CAAC;IAED,IAAIlB,WAAW,KAAKP,mBAAmB,EAAE;MACvC,MAAMiF,UAAU,GAAG;QACjBC,KAAK,EAAEnG,2BAA2B;QAClCoG,SAAS,EAAE;UACTC,oBAAoB,EAAAC,MAAA,CAAAC,MAAA;YAClBd,oBAAoB;YACpBE,UAAU;YACVN,OAAO;YACPb,mBAAmB;YACnBgC,eAAe,EAAErG,0BAA0B,CAACqC,cAAc,CAAC;YAC3DiE,IAAI,EAAE,EAAE;YACRC,aAAa,EAAEd,eAAe;YAC9Be,mBAAmB,EAAEb;UAAa,GAC9BC,oBAAoB,CAAC,CAAC;QAE9B;MACF,CAAC;MACD,OAAO,MAAMnD,oBAAoB,CAACsD,UAAU,CAAC;IAC/C;IAEA,IAAI1E,WAAW,KAAKN,iBAAiB,EAAE;MAAA,IAAA0F,sBAAA,EAAAC,oBAAA,EAAAC,iBAAA;MACrC,MAAMC,mBAAmB,GAAGvE,cAAc,aAAAoE,sBAAA,GAAdpE,cAAc,CAAEW,SAAS,qBAAzByD,sBAAA,CAA2BI,mBAAmB;;MAE1E;MACA,MAAMC,kBAAkB,GAAGtG,wCAAwC,CAAC;QAClE+B,YAAY;QACZe,iBAAiB;QACjBsD,mBAAmB;QACnBvE,cAAc;QACdsB,oBAAoB;QACpBK,yBAAyB;QACzBG,mBAAmB;QACnBM,mBAAmB;QACnBf;MACF,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEqD,gBAAgB;QAAEC;MAAW,CAAC,GAAGvG,wBAAwB,CAACqG,kBAAkB,EAAEpD,QAAQ,CAAC;MAC/F,MAAM;QAAEuD;MAAmB,CAAC,GAAGH,kBAAkB;MACjD,MAAMI,sBAAsB,GAAG;QAC7BlB,KAAK,EAAEnG,2BAA2B;QAClCoG,SAAS,EAAE;UACTC,oBAAoB,EAAE;YACpBZ,oBAAoB;YACpBE,UAAU;YACVN,OAAO;YACPb,mBAAmB;YACnBgC,eAAe,EAAErG,0BAA0B,CAACqC,cAAc,CAAC;YAC3DiE,IAAI,EAAE,EAAE;YACRR,WAAW,EAAEmB,kBAAkB;YAC/BT,mBAAmB,EAAEb,aAAa;YAClCY,aAAa,EAAEd,eAAe;YAC7BI,iBAAiB,EAAE;cACnBjE,IAAI,EAAE,aAAa;cACnBgD,KAAK,EAAEmC;YACZ;UACE;QACF;MACF,CAAC;MAED,MAAMI,yBAAyB,GAAG;QAChCnB,KAAK,EAAEnG,2BAA2B;QAClCoG,SAAS,EAAE;UACTC,oBAAoB,EAAE;YACpBZ,oBAAoB;YACpBE,UAAU;YACVN,OAAO;YACPb,mBAAmB;YACnBgC,eAAe,EAAErG,0BAA0B,CAACqC,cAAc,CAAC;YAC3DiE,IAAI,EAAE,EAAE;YACRR,WAAW,EAAE,EAAE;YACfU,mBAAmB,EAAEb,aAAa;YAClCY,aAAa,EAAEd,eAAe;YAC9BI,iBAAiB,EAAE;cACnBjE,IAAI,EAAE,gBAAgB;cACrBgD,KAAK,EAAEoC;YACZ;UACE;QACF;MACF,CAAC;MAEDhF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiF,sBAAsB,CAAC;MAC3DlF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkF,yBAAyB,CAAC;MACjE,MAAM,CAACC,cAAc,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtD9E,oBAAoB,CAACyE,sBAAsB,CAAC,EAC5CzE,oBAAoB,CAAC0E,yBAAyB,CAAC,CAChD,CAAC;MACF,MAAMK,eAAe,GACnB,MAAM,IAAIJ,cAAc,KAAAV,oBAAA,GAAIU,cAAc,CAAChF,IAAI,cAAAsE,oBAAA,GAAnBA,oBAAA,CAAqBe,6BAA6B,aAAlDf,oBAAA,CAAoDgB,0BAA0B,GACtGN,cAAc,CAAChF,IAAI,CAACqF,6BAA6B,CAACC,0BAA0B,GAC5E,EAAE;MAER,MAAMC,YAAY,GAChB,MAAM,IAAIN,WAAW,KAAAV,iBAAA,GAAIU,WAAW,CAACjF,IAAI,cAAAuE,iBAAA,GAAhBA,iBAAA,CAAkBc,6BAA6B,aAA/Cd,iBAAA,CAAiDe,0BAA0B,GAChGL,WAAW,CAACjF,IAAI,CAACqF,6BAA6B,CAACC,0BAA0B,GACzE,EAAE;MACJ1F,OAAO,CAACC,GAAG,CAACuF,eAAe,EAACG,YAAY,EAAC,uBAAuB,CAAC;MACtE,MAAMC,QAAQ,GAAGrH,4BAA4B,CAAC,CAACoH,YAAY,CAAC,EAAE,CAACH,eAAe,CAAC,EAAElE,iBAAiB,KAAKsD,mBAAmB,CAAC;MAC5H;MACE5E,OAAO,CAACC,GAAG,CAAC2F,QAAQ,EAAC,kBAAkB,CAAC;MACxC,OAAOA,QAAQ;IACjB;EACF,CAAC;EAED,MAAMC,+BAA+B,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC5C3F,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI+C,OAAiB,GAAG,EAAE;IAE1B,IAAItB,KAAK,CAACC,OAAO,CAACxB,cAAc,oBAAdA,cAAc,CAAEU,UAAU,CAAC,EAAE;MAC7CmC,OAAO,GAAG7C,cAAc,CAACU,UAAU,CAACe,GAAG,CAAEiE,IAA8B,IAAKA,IAAI,CAAC7D,GAAG,CAAC8D,QAAQ,CAAC,CAAC,CAAC;IAClG,CAAC,MAAM,IAAI3F,cAAc,aAAAyF,qBAAA,GAAdzF,cAAc,CAAEU,UAAU,aAA1B+E,qBAAA,CAA4B5D,GAAG,EAAE;MAC1CgB,OAAO,GAAG,CAAC7C,cAAc,CAACU,UAAU,CAACmB,GAAG,CAAC8D,QAAQ,CAAC,CAAC,CAAC;IACtD;IACA,IAAI9C,OAAO,CAACd,MAAM,KAAK,CAAC,EAAE;MACtBa,iBAAiB,CAACC,OAAO,CAAC,CAAC+C,IAAI,CAACC,QAAQ,IAAI;QAC1C/F,YAAY,CAAC,KAAK,CAAC;QACnB,IAAId,WAAW,KAAKP,mBAAmB,EAAE;UACzCkB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiG,QAAQ,CAAC;UACtC,IAAIA,QAAQ,IAAI,MAAM,IAAIA,QAAQ,EAAE;YAAA,IAAAC,cAAA;YAClC,MAAMC,kBAAkB,IAAAD,cAAA,GAAGD,QAAQ,CAAC9F,IAAI,cAAA+F,cAAA,GAAbA,cAAA,CAAeV,6BAA6B,qBAA5CU,cAAA,CAA8CT,0BAA0B;YACnG1F,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmG,kBAAkB,CAAC;YACjE,IAAIA,kBAAkB,EAAE;cACtB,IAAIxE,KAAK,CAACC,OAAO,CAACuE,kBAAkB,CAAC,EAAE;gBACrC,IAAIA,kBAAkB,CAAChE,MAAM,GAAG,CAAC,EAAE;kBACjC1C,eAAe,CAAC0G,kBAAkB,CAAC,CAAC,CAAC,CAAC;kBACtCpG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmG,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBACpE;cACF,CAAC,MACK,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;gBAChD1G,eAAe,CAAC0G,kBAAkB,CAAC;gBACnCpG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmG,kBAAkB,CAAC;cAClE;YACF;UACF;UACAlI,qBAAqB,CAAC;YAAE0C,eAAe;YAAEtB;UAAS,CAAC,CAAC;QACtD;QACG,IAAID,WAAW,KAAKN,iBAAiB,EAAE;UACxC,IAAI6C,KAAK,CAACC,OAAO,CAACqE,QAAQ,CAAC,EAAE;YAC3BnG,wBAAwB,CACtBmG,QAAQ,CAACpE,GAAG,CAAE4B,IAAS,IAAAS,MAAA,CAAAC,MAAA,KAAWV,IAAI,CAAG,CAC3C,CAAC;UACH,CAAC,MACI;YACH3D,wBAAwB,CAAC,EAAE,CAAC;UAC9B;QACF;QACA7B,qBAAqB,CAAC;UAAE0C,eAAe;UAAEtB;QAAS,CAAC,CAAC;MACtD,CAAC,CAAC;IACF;EACF,CAAC;EACH,MAAM+G,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOvF,QAAQ,KAAK/C,qBAAqB,CAACuI,gBAAgB;EAC5D,CAAC;EAED/I,SAAS,CAAC,MAAM;IACd,IAAI8I,eAAe,CAAC,CAAC,EAAE;MAAA,IAAAE,sBAAA,EAAAC,sBAAA;MACrB,IAAI,CAAAnG,cAAc,aAAAkG,sBAAA,GAAdlG,cAAc,CAAEW,SAAS,qBAAzBuF,sBAAA,CAA2BrE,GAAG,MAAKb,aAAa,CAACoF,OAAO,EAAE;QAC5DZ,+BAA+B,CAAC,CAAC;MACnC,CAAC,MAAM;QACLlF,qBAAqB,CAAC1C,2BAA2B,CAACqE,sBAAsB,CAAC,CAAC,CAAC,CAAC;MAC9E;MACAjB,aAAa,CAACoF,OAAO,GAAG,CAAApG,cAAc,aAAAmG,sBAAA,GAAdnG,cAAc,CAAEW,SAAS,qBAAzBwF,sBAAA,CAA2BtE,GAAG,KAAI,IAAI;IAChE;EACF,CAAC,EAAE,CAACpB,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAE5B,WAAW,EAAE6B,UAAU,CAAC,CAAC;EAEvE3D,SAAS,CAAC,MAAM;IACd,IAAG8I,eAAe,CAAC,CAAC,EAAE;MACpB/G,QAAQ,CAACjB,8BAA8B,CAACuC,eAAe,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErBrD,SAAS,CAAC,MAAM;IACd,IAAI8I,eAAe,CAAC,CAAC,IAAI,CAAA9F,YAAY,oBAAZA,YAAY,CAAEmG,IAAI,MAAKzH,cAAc,IAAI0H,IAAI,CAACC,SAAS,CAACrG,YAAY,CAAC,KAAKoG,IAAI,CAACC,SAAS,CAACzF,gBAAgB,CAACsF,OAAO,CAAC,EAAE;MAC3IZ,+BAA+B,CAAC,CAAC;MACjC1E,gBAAgB,CAACsF,OAAO,GAAGlG,YAAY;IACzC;EACF,CAAC,EAAE,CAACa,YAAY,EAAEb,YAAY,CAAC,CAAC;EAEhChD,SAAS,CAAC,MAAM;IACd,IAAI8I,eAAe,CAAC,CAAC,IAAI,CAAA9F,YAAY,oBAAZA,YAAY,CAAEmG,IAAI,MAAKxH,QAAQ,IAAIyH,IAAI,CAACC,SAAS,CAACrG,YAAY,CAAC,KAAKoG,IAAI,CAACC,SAAS,CAACzF,gBAAgB,CAACsF,OAAO,CAAC,EAAE;MACrIZ,+BAA+B,CAAC,CAAC;MACjC1E,gBAAgB,CAACsF,OAAO,GAAGlG,YAAY;IACzC;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAEpBhD,SAAS,CAAC,MAAM;IACZ,IAAI6B,YAAY,EAAE;MAChB,IAAIC,WAAW,KAAKP,mBAAmB,EAAE;QACvCM,YAAY,CAACG,WAAW,CAAC;MAC3B,CAAC,MAAM,IAAIF,WAAW,KAAKN,iBAAiB,EAAE;QAC5CK,YAAY,CAACU,qBAAqB,CAAC;MACrC;IACF;EACF,CAAC,EAAE,CAACP,WAAW,EAAEO,qBAAqB,EAAEV,YAAY,EAAEC,WAAW,CAAC,CAAC;EAEnE,oBACET,OAAA;IAAKiI,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5ClI,OAAA;MAAKiI,SAAS,EAAC,iCAAiC;MAAAC,QAAA,EAC7C5G,SAAS,gBACRtB,OAAA,CAAChB,OAAO;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEXtI,OAAA,CAACd,qBAAqB;QACpB2B,YAAY,EACVJ,WAAW,KAAKP,mBAAmB,GAC/BW,YAAY,GACZJ,WAAW,KAAKN,iBAAiB,GACjCe,qBAAqB,CAAC,CAAC,CAAC,IAAI;UAAEH,EAAE,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEoB,OAAO,EAAE,EAAE;UAAEkG,KAAK,EAAE;QAAG,CAAC,GACvF;UAAExH,EAAE,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEoB,OAAO,EAAE,EAAE;UAAEkG,KAAK,EAAE;QAAG,CAC/D;QACD9H,WAAW,EAAEA;MAAY;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAe/H,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}