export interface SyncSession {
  time: string;
  status: string;
  day: string;
  date: string;
}

export interface SyncHistoryWeek {
  lastRun: {
    date: string;
    time: string;
    weekNumber: number;
  };
}

export interface GetJobRunsFromDatabricksResponse {
  syncSessions: SyncSession[];
  nextSync: { sync_day: string; sync_time: string };
  lastSync: { sync_day: string; sync_time: string };
  syncHistory: { weeks: SyncHistoryWeek[] };
}