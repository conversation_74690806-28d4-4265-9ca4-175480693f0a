import { generatePeriodStatuses } from './generatePeriodStatuses';

describe('generatePeriodStatuses', () => {
  const baseWeek = (overrides = {}) => ({
    fiscalPeriodNumber: '202401',
    fiscalWeekNumber: '202401',
    fiscalPeriodEndDate: '2024-01-31',
    fiscalPeriodLockoutDate: '2024-02-05',
    fiscalPeriodCertificationDate: '2024-02-10',
    ...overrides,
  });

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('returns empty status object for non-array input', () => {
    expect(generatePeriodStatuses(null)).toEqual({
      certified: {},
      locked: {},
      notLocked: {},
      notCertifiedButLocked: {},
    });
    expect(generatePeriodStatuses(undefined)).toEqual({
      certified: {},
      locked: {},
      notLocked: {},
      notCertifiedButLocked: {},
    });
  });

  it('classifies certified periods correctly', () => {
    jest.setSystemTime(new Date('2024-02-15T00:00:00Z'));
    const week = baseWeek();
    const result = generatePeriodStatuses([week]);
    expect(result.certified['202401']).toContain('202401');
  });

  it('classifies locked periods correctly', () => {
    jest.setSystemTime(new Date('2024-02-06T00:00:00Z'));
    const week = baseWeek();
    const result = generatePeriodStatuses([week]);
    expect(result.locked['202401']).toContain('202401');
  });

  it('classifies notLocked periods correctly', () => {
    jest.setSystemTime(new Date('2024-01-30T00:00:00Z'));
    const week = baseWeek();
    const result = generatePeriodStatuses([week]);
    expect(result.notLocked['202401']).toContain('202401');
  });

  it('classifies notCertifiedButLocked periods correctly', () => {
    jest.setSystemTime(new Date('2024-02-06T00:00:00Z'));
    const week = baseWeek({ fiscalPeriodCertificationDate: null });
    const result = generatePeriodStatuses([week]);
    expect(result.notCertifiedButLocked['202401']).toContain('202401');
  });

  it('handles multiple periods and weeks', () => {
    jest.setSystemTime(new Date('2024-02-15T00:00:00Z'));
    const week1 = baseWeek({ fiscalPeriodNumber: '202401', fiscalWeekNumber: '202401' });
    const week2 = baseWeek({ fiscalPeriodNumber: '202401', fiscalWeekNumber: '202402' });
    const week3 = baseWeek({ fiscalPeriodNumber: '202402', fiscalWeekNumber: '202403' });
    const result = generatePeriodStatuses([week1, week2, week3]);
    expect(result.certified['202401']).toEqual(['202401', '202402']);
    expect(result.certified['202402']).toEqual(['202403']);
  });

  it('returns empty for periods with no weeks', () => {
    jest.setSystemTime(new Date('2024-02-15T00:00:00Z'));
    const result = generatePeriodStatuses([]);
    expect(result.certified).toEqual({});
  });
});