export const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    currencySign: 'accounting',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatPercentage = (value: number) => {
  const newValue = value * 100;
  const sign = newValue < 0 ? '-' : '';
  return `${sign}${Math.abs(newValue).toFixed(2)}%`;
};

export const formatWeekNumber = (weekString: string) => {
  // Expecting input like 'Week-202401', 'Week-202402', etc.
  const match = weekString.match(/Week-(\d{4})(\d{2})?/);
  if (match) {
    // If weekString is 'Week-202401', weekNumStr will be '01'
    // If weekString is 'Week-202401', weekNum will be 1
    const weekNumStr = weekString.slice(-2);
    const weekNum = parseInt(weekNumStr, 10);
    if (!isNaN(weekNum)) {
      return `Week ${weekNum} (fiscal wk ${weekNum})`;
    }
  }
  // Fallback if input is not as expected
  return weekString;
};
