import { createGenericSlice } from '../../rtk/rtk-slice';

/* Store the focus state of the role cascade search box */
export const roleCascadeSearchFocusSlice = createGenericSlice({
  name: 'roleCascadeSearchFocus_rn',
  initialState: { status: 'loading', data: { focused: false } },
})({
  setRoleCascadeSearchFocus(state, { payload }) {
    if (!state.data) state.data = { focused: false };
    state.data.focused = payload;
  },
});

export const { setRoleCascadeSearchFocus } = roleCascadeSearchFocusSlice.actions; 