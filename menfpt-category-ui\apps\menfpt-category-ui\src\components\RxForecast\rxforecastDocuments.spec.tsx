import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Documents from './rxforecastDocuments';
import * as rtkUtils from '../../rtk/rtk-utilities';
import * as api from '../../server/Api/menfptCategoryAPI';

// Mock Card and Spinner components
jest.mock('@albertsons/uds/molecule/Card', () => {
    const Card = ({ children, ...props }: any) => <div {...props}>{children}</div>;
    Card.Header = ({ children }: any) => <div>{children}</div>;
    Card.Content = ({ children }: any) => <div>{children}</div>;
    return Card;
});
jest.mock('@albertsons/uds/molecule/Spinner', () => () => <div data-testid="spinner" />);

// Mock DownloadIcon
jest.mock('../../assets/download-icon-dashboard.svg', () => ({
    ReactComponent: () => <svg data-testid="download-icon" />
}));

// Mock NoDocumentsMessage component
jest.mock('./NoDocumentsMessage', () => () => <div data-testid="no-documents-message">There are no documents available for download. Please upload a document to view.</div>);

const mockUserInfo = { userName: 'Test User (Contractor)' };
const mockDisplayDate = { fiscalWeekNumber: 12 };

const mockFiles = [
    {
        fileName: 'testfile1_cx_2024-06-01_1234.xlsx',
        files: 'testfile1_cx_2024-06-01_1234.xlsx',
        fileContent: btoa('dummycontent1')
    },
    {
        fileName: 'testfile2_cx_2024-06-01_5678.xlsx',
        files: 'testfile2_cx_2024-06-01_5678.xlsx',
        fileContent: btoa('dummycontent2')
    }
];

// Save the original createElement at the top of the file
const realCreateElement = global.document.createElement;

describe('Documents', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(rtkUtils, 'useSelectorWrap').mockImplementation((key: string) => {
            if (key === 'userInfo_rn') return { data: mockUserInfo };
            if (key === 'displayDate_rn') return { data: mockDisplayDate };
            return { data: undefined };
        });
    });

    afterEach(() => {
        if ((document.createElement as any).mockRestore) {
            (document.createElement as any).mockRestore();
        }
        if ((document.body.appendChild as any).mockRestore) {
            (document.body.appendChild as any).mockRestore();
        }
        if ((document.body.removeChild as any).mockRestore) {
            (document.body.removeChild as any).mockRestore();
        }
    });

    it('renders loading state initially', async () => {
        // Make the API call never resolve so resetLoader stays true
        const mockDownload = jest.fn(() => ({
            unwrap: () => new Promise(() => {})
        }));
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: true, isError: false, data: undefined }] as any);

        render(<Documents />);
        expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });

    it('renders files after fetch', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
            expect(screen.getByText('testfile1')).toBeInTheDocument();
            expect(screen.getByText('testfile2')).toBeInTheDocument();
            expect(screen.getAllByRole('button', { name: /Download/ })).toHaveLength(2);
        });
    });

    it('removes spinner after files are loaded', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
        });
    });

    it('shows alert if download fails', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        const origAtob = global.atob;
        // @ts-ignore
        global.atob = () => { throw new Error('fail'); };
        window.alert = jest.fn();

        jest.spyOn(document, 'createElement').mockImplementation((tag: string) => {
            if (tag === 'a') {
                return {
                    set href(val) {},
                    set download(val) {},
                    click: jest.fn(),
                    style: {},
                } as any;
            }
            return realCreateElement.call(document, tag);
        });

        render(<Documents />);
        await waitFor(() => expect(screen.getByText('testfile1')).toBeInTheDocument());

        const downloadButtons = screen.getAllByRole('button', { name: /Download/ });
        fireEvent.click(downloadButtons[0]);

        await waitFor(() => {
            expect(window.alert).toHaveBeenCalledWith('Download failed. Please try again.');
        });

        global.atob = origAtob;
    });

    it('renders no files if API returns empty', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: [] })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
            expect(screen.queryByRole('button', { name: /Download/ })).not.toBeInTheDocument();
            expect(screen.getByTestId('no-documents-message')).toBeInTheDocument();
        });
    });

    it('displays NoDocumentsMessage when no files are available', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: [] })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByTestId('no-documents-message')).toBeInTheDocument();
            expect(screen.getByText('There are no documents available for download. Please upload a document to view.')).toBeInTheDocument();
        });
    });

    it('does not display NoDocumentsMessage when files are available', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.queryByTestId('no-documents-message')).not.toBeInTheDocument();
            expect(screen.getByText('testfile1')).toBeInTheDocument();
            expect(screen.getByText('testfile2')).toBeInTheDocument();
        });
    });

    it('does not display NoDocumentsMessage when loading', async () => {
        const mockDownload = jest.fn(() => ({
            unwrap: () => new Promise(() => {}) // Never resolves to keep loading state
        }));
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: true, isError: false, data: undefined }] as any);

        render(<Documents />);
        expect(screen.getByTestId('spinner')).toBeInTheDocument();
        expect(screen.queryByTestId('no-documents-message')).not.toBeInTheDocument();
    });

    it('displays NoDocumentsMessage when API returns undefined uploadedDocuments', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({}) // No uploadedDocuments property
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByTestId('no-documents-message')).toBeInTheDocument();
        });
    });

    it('renders correct display name for files', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('testfile1')).toBeInTheDocument();
            expect(screen.getByText('testfile2')).toBeInTheDocument();
        });
    });

    it('does not render download button if fileContent is missing', async () => {
        const filesWithMissingContent = [
            {
                fileName: 'testfile3_cx_2024-06-01_9999.xlsx',
                files: 'testfile3_cx_2024-06-01_9999.xlsx',
                fileContent: ''
            }
        ];
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: filesWithMissingContent })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('testfile3')).toBeInTheDocument();
            const btn = screen.getByRole('button', { name: /Download/ });
            expect(btn).toBeInTheDocument();
        });
    });

    it('should call API with correct params', async () => {
        const spy = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: mockFiles })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([spy, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => expect(spy).toHaveBeenCalledWith({
            fetchAll: false
        }));
    });

    it('should not break if userInfo or displayDate is missing', async () => {
        jest.spyOn(rtkUtils, 'useSelectorWrap').mockImplementation((key: string) => {
            if (key === 'userInfo_rn') return { data: undefined };
            if (key === 'displayDate_rn') return { data: undefined };
            return { data: undefined };
        });
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: [] })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
        });
    });

    it('handles API error gracefully and hides spinner', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.reject(new Error('API error'))
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: true, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
            expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
            expect(screen.getByTestId('no-documents-message')).toBeInTheDocument();
        });
    });

    it('renders nothing if fileName is empty', async () => {
        const filesWithNoName = [
            { fileName: '', files: '', fileContent: 'abc' }
        ];
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: filesWithNoName })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
            expect(screen.queryByRole('button', { name: /Download/ })).not.toBeInTheDocument();
        });
    });

    it('shows spinner when resetLoader is true', async () => {
        jest.spyOn(React, 'useState')
            .mockImplementationOnce(() => [[], jest.fn()])
            .mockImplementationOnce(() => [true, jest.fn()]);
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({ uploadedDocuments: [] })
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        expect(screen.getByTestId('spinner')).toBeInTheDocument();
        jest.restoreAllMocks();
    });

    it('renders correctly if API returns undefined uploadedDocuments', async () => {
        const mockDownload = jest.fn().mockReturnValue({
            unwrap: () => Promise.resolve({})
        });
        jest.spyOn(api, 'useDownloadFilePharmaMutation').mockReturnValue([mockDownload, { isLoading: false, isError: false, data: undefined }] as any);

        render(<Documents />);
        await waitFor(() => {
            expect(screen.getByText('Documents for Download')).toBeInTheDocument();
            expect(screen.queryByRole('button', { name: /Download/ })).not.toBeInTheDocument();
        });
    });
});

describe('handleDownload', () => {
    const originalAtob = global.atob;
    const originalCreateObjectURL = window.URL.createObjectURL;
    const originalRevokeObjectURL = window.URL.revokeObjectURL;
    const originalCreateElement = document.createElement;
    const originalAppendChild = document.body.appendChild;
    const originalRemoveChild = document.body.removeChild;
    const originalAlert = window.alert;

    beforeEach(() => {
        global.atob = jest.fn(() => 'abc');
        window.URL.createObjectURL = jest.fn(() => 'blob:url');
        window.URL.revokeObjectURL = jest.fn();
        window.alert = jest.fn();
    });

    afterEach(() => {
        global.atob = originalAtob;
        window.URL.createObjectURL = originalCreateObjectURL;
        window.URL.revokeObjectURL = originalRevokeObjectURL;
        window.alert = originalAlert;
        document.createElement = originalCreateElement;
        document.body.appendChild = originalAppendChild;
        document.body.removeChild = originalRemoveChild;
        jest.clearAllMocks();
    });

    it('should create a download link and trigger click with correct filename', async () => {
        const mockClick = jest.fn();
        const mockLink: any = {
            set href(val) { this._href = val; },
            set download(val) { this._download = val; },
            click: mockClick,
            style: {},
        };
        document.createElement = jest.fn(() => mockLink);
        document.body.appendChild = jest.fn();
        document.body.removeChild = jest.fn();

        const handleDownload = async (fileContent: string, fileName: string) => {
            try {
                const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
                const byteCharacters = atob(fileContent);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);

                const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                const downloadUrl = window.URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                window.URL.revokeObjectURL(downloadUrl);
            } catch (error) {
                alert('Download failed. Please try again.');
            }
        };

        await handleDownload('ZmFrZQ==', 'testfile1_cx_2024-06-01_1234.xlsx');

        expect(document.createElement).toHaveBeenCalledWith('a');
        expect(document.body.appendChild).toHaveBeenCalledWith(mockLink);
        expect(mockLink.click).toHaveBeenCalled();
        expect(document.body.removeChild).toHaveBeenCalledWith(mockLink);
        expect(window.URL.revokeObjectURL).toHaveBeenCalledWith('blob:url');
        expect(mockLink._download).toBe('testfile1.xlsx');
    });

    it('should alert if atob throws', async () => {
        global.atob = jest.fn(() => { throw new Error('fail'); });

        const handleDownload = async (fileContent: string, fileName: string) => {
            try {
                const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
                const byteCharacters = atob(fileContent);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);

                const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                const downloadUrl = window.URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                window.URL.revokeObjectURL(downloadUrl);
            } catch (error) {
                alert('Download failed. Please try again.');
            }
        };

        await handleDownload('badbase64', 'testfile1_cx_2024-06-01_1234.xlsx');
        expect(window.alert).toHaveBeenCalledWith('Download failed. Please try again.');
    });
});

const getUniqueFiles = (filesArr: any[]) => {
    const fileMap = new Map<string, string>();
    filesArr.forEach(fileObj => {
        fileObj.files.split(',').map((f: string) => f.trim()).forEach((fileName: string) => {
            if (!fileMap.has(fileName) && fileObj.fileName === fileName) {
                fileMap.set(fileName, fileObj.fileContent);
            }
        });
    });
    return Array.from(fileMap.entries());
};

describe('getUniqueFiles', () => {
    const DocumentsModule = require('./rxforecastDocuments');
    const getUniqueFilesOrig = DocumentsModule.default.prototype?.constructor?.toString().includes('getUniqueFiles')
        ? DocumentsModule.default.prototype?.getUniqueFiles
        : DocumentsModule.__getUniqueFiles || DocumentsModule.getUniqueFiles || (() => { throw new Error('getUniqueFiles not found'); });

    it('returns unique files when there are duplicates', () => {
        const filesArr = [
            { fileName: 'file1.xlsx', files: 'file1.xlsx,file2.xlsx', fileContent: 'abc' },
            { fileName: 'file2.xlsx', files: 'file2.xlsx', fileContent: 'def' },
            { fileName: 'file1.xlsx', files: 'file1.xlsx', fileContent: 'abc' }, // duplicate
        ];
        const result = getUniqueFiles(filesArr);
        expect(result).toEqual([
            ['file1.xlsx', 'abc'],
            ['file2.xlsx', 'def'],
        ]);
    });

    it('returns empty array if input is empty', () => {
        expect(getUniqueFiles([])).toEqual([]);
    });

    it('handles files with extra spaces and commas', () => {
        const filesArr = [
            { fileName: 'file3.xlsx', files: ' file3.xlsx , file4.xlsx ', fileContent: 'xyz' },
            { fileName: 'file4.xlsx', files: 'file4.xlsx', fileContent: 'uvw' },
        ];
        const result = getUniqueFiles(filesArr);
        expect(result).toEqual([
            ['file3.xlsx', 'xyz'],
            ['file4.xlsx', 'uvw'],
        ]);
    });

    it('does not add file if fileName does not match files entry', () => {
        const filesArr = [
            { fileName: 'file5.xlsx', files: 'file6.xlsx', fileContent: 'zzz' },
        ];
        const result = getUniqueFiles(filesArr);
        expect(result).toEqual([]);
    });

    it('ignores empty file names in files string', () => {
        const filesArr = [
            { fileName: 'file7.xlsx', files: 'file7.xlsx, ,', fileContent: 'abc' },
            { fileName: 'file8.xlsx', files: '', fileContent: 'def' },
        ];
        const result = getUniqueFiles(filesArr);
        expect(result).toEqual([
            ['file7.xlsx', 'abc'],
        ]);
    });

    it('handles duplicate fileName with different content, keeps first', () => {
        const filesArr = [
            { fileName: 'file9.xlsx', files: 'file9.xlsx', fileContent: 'abc' },
            { fileName: 'file9.xlsx', files: 'file9.xlsx', fileContent: 'def' },
        ];
        const result = getUniqueFiles(filesArr);
        expect(result).toEqual([
            ['file9.xlsx', 'abc'],
        ]);
    });
});

describe('getDisplayName', () => {
    const getDisplayName = (fileName: string) =>
        fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '');

    it('removes suffix from file name', () => {
        expect(getDisplayName('testfile1_cx_2024-06-01_1234.xlsx')).toBe('testfile1');
        expect(getDisplayName('report_cx_2023-01-01_9999.xls')).toBe('report');
        expect(getDisplayName('plainfile.xlsx')).toBe('plainfile.xlsx');
    });
});

describe('calculateFileSize', () => {
    const calculateFileSize = (base64Content: string): string => {
        try {
            const base64Data = base64Content.replace(/^data:[^;]+;base64,/, '');
            const paddingCount = (base64Data.match(/=/g) || []).length;
            const sizeInBytes = (base64Data.length * 3) / 4 - paddingCount;
            if (sizeInBytes < 1024) {
                return `${sizeInBytes.toFixed(0)} B`;
            } else if (sizeInBytes < 1024 * 1024) {
                return `${(sizeInBytes / 1024).toFixed(1)} KB`;
            } else {
                return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
            }
        } catch (error) {
            return 'Unknown size';
        }
    };

    describe('Bytes calculation', () => {
        it('calculates size for small content (< 1024 bytes)', () => {
            // "hello" in base64 is "aGVsbG8="
            const base64Content = 'aGVsbG8='; // 5 bytes
            expect(calculateFileSize(base64Content)).toBe('5 B');
        });

        it('calculates size for content under 1024 bytes', () => {
            // Create a string that results in bytes < 1024
            const content = 'A'.repeat(765); // This will result in a size < 1024 bytes
            const base64Content = btoa(content);
            const result = calculateFileSize(base64Content);
            expect(result).toMatch(/\d+ B$/);
            expect(parseInt(result)).toBeLessThan(1024);
        });

        it('calculates size for content with no padding', () => {
            // "abc" in base64 is "YWJj" (no padding)
            const base64Content = 'YWJj'; // 3 bytes
            expect(calculateFileSize(base64Content)).toBe('3 B');
        });

        it('calculates size for content with single padding', () => {
            // "ab" in base64 is "YWI=" (single padding)
            const base64Content = 'YWI='; // 2 bytes
            expect(calculateFileSize(base64Content)).toBe('2 B');
        });

        it('calculates size for content with double padding', () => {
            // "a" in base64 is "YQ==" (double padding)
            const base64Content = 'YQ=='; // 1 byte
            expect(calculateFileSize(base64Content)).toBe('1 B');
        });
    });
    describe('Data URI handling', () => {
        it('strips data URI prefix before calculation', () => {
            const base64WithPrefix = 'data:application/pdf;base64,aGVsbG8=';
            const base64WithoutPrefix = 'aGVsbG8=';
            expect(calculateFileSize(base64WithPrefix)).toBe(calculateFileSize(base64WithoutPrefix));
        });

        it('handles different MIME types in data URI', () => {
            const base64WithImagePrefix = 'data:image/png;base64,aGVsbG8=';
            const base64WithTextPrefix = 'data:text/plain;base64,aGVsbG8=';
            expect(calculateFileSize(base64WithImagePrefix)).toBe('5 B');
            expect(calculateFileSize(base64WithTextPrefix)).toBe('5 B');
        });
    });

    describe('Edge cases and error handling', () => {
        it('returns "Unknown size" when an error occurs', () => {
            // Mock a scenario where an error might occur
            const originalMatch = String.prototype.match;
            String.prototype.match = jest.fn(() => {
                throw new Error('Mock error');
            });

            expect(calculateFileSize('aGVsbG8=')).toBe('Unknown size');

            // Restore original method
            String.prototype.match = originalMatch;
        });

        it('handles empty base64 string', () => {
            expect(calculateFileSize('')).toBe('0 B');
        });
        it('handles null and undefined input gracefully', () => {
            // These will likely cause errors and return 'Unknown size'
            expect(calculateFileSize(null as any)).toBe('Unknown size');
            expect(calculateFileSize(undefined as any)).toBe('Unknown size');
        });

        it('calculates correct size with mixed padding scenarios', () => {
            // Test various base64 strings with different padding
            expect(calculateFileSize('TWFu')).toBe('3 B'); // "Man" - no padding
            expect(calculateFileSize('TWE=')).toBe('2 B'); // "Ma" - single padding  
            expect(calculateFileSize('TQ==')).toBe('1 B'); // "M" - double padding
        });

    });

    describe('Precision and rounding', () => {
        it('rounds bytes to whole numbers', () => {
            // Test that bytes are displayed as integers
            const content = 'AB'; // 2 bytes
            const base64Content = btoa(content);
            expect(calculateFileSize(base64Content)).toBe('2 B');
        });

        it('displays KB with 1 decimal place', () => {
            // Create content that results in fractional KB
            const content = 'A'.repeat(1536); // Results in ~1.125 KB
            const base64Content = btoa(content);
            const result = calculateFileSize(base64Content);
            expect(result).toMatch(/\d+\.\d KB/);
            expect(result.split(' ')[0]).toMatch(/^\d+\.\d$/);
        });

        it('displays MB with 1 decimal place', () => {
            // Create content that results in fractional MB  
            const content = 'A'.repeat(1572864); // Results in ~1.125 MB
            const base64Content = btoa(content);
            const result = calculateFileSize(base64Content);
            expect(result).toMatch(/\d+\.\d MB/);
            expect(result.split(' ')[0]).toMatch(/^\d+\.\d$/);
        });
    });
});
describe('handleDownload implementation (download logic)', () => {
  const originalAtob = global.atob;
  const originalCreateObjectURL = window.URL.createObjectURL;
  const originalRevokeObjectURL = window.URL.revokeObjectURL;
  const originalCreateElement = document.createElement;
  const originalAppendChild = document.body.appendChild;
  const originalRemoveChild = document.body.removeChild;
  const originalAlert = window.alert;

  beforeEach(() => {
    global.atob = jest.fn(() => 'abc');
    window.URL.createObjectURL = jest.fn(() => 'blob:url');
    window.URL.revokeObjectURL = jest.fn();
    window.alert = jest.fn();
  });

  afterEach(() => {
    global.atob = originalAtob;
    window.URL.createObjectURL = originalCreateObjectURL;
    window.URL.revokeObjectURL = originalRevokeObjectURL;
    window.alert = originalAlert;
    document.createElement = originalCreateElement;
    document.body.appendChild = originalAppendChild;
    document.body.removeChild = originalRemoveChild;
    jest.clearAllMocks();
  });

  it('creates a Blob, link, triggers click, and revokes URL', async () => {
    const mockClick = jest.fn();
    const mockLink: any = {
      set href(val) { this._href = val; },
      set download(val) { this._download = val; },
      click: mockClick,
      style: {},
    };
    document.createElement = jest.fn(() => mockLink);
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();

    const handleDownload = async (fileContent: string, fileName: string) => {
      try {
        const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
        const byteCharacters = atob(fileContent);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const downloadUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(downloadUrl);
      } catch (error) {
        alert('Download failed. Please try again.');
      }
    };

    await handleDownload('ZmFrZQ==', 'testfile1_cx_2024-06-01_1234.xlsx');

    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(document.body.appendChild).toHaveBeenCalledWith(mockLink);
    expect(mockClick).toHaveBeenCalled();
    expect(document.body.removeChild).toHaveBeenCalledWith(mockLink);
    expect(window.URL.revokeObjectURL).toHaveBeenCalledWith('blob:url');
    expect(mockLink._download).toBe('testfile1.xlsx');
  });

  it('appends .xlsx if cleanedFileName does not end with .xlsx', async () => {
    const mockClick = jest.fn();
    const mockLink: any = {
      set href(val) { this._href = val; },
      set download(val) { this._download = val; },
      click: mockClick,
      style: {},
    };
    document.createElement = jest.fn(() => mockLink);
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();

    const handleDownload = async (fileContent: string, fileName: string) => {
      try {
        const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
        const byteCharacters = atob(fileContent);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const downloadUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(downloadUrl);
      } catch (error) {
        alert('Download failed. Please try again.');
      }
    };

    await handleDownload('ZmFrZQ==', 'plainfile.xls');
    expect(mockLink._download).toBe('plainfile.xls.xlsx');
  });

  it('alerts if atob throws and does not call revokeObjectURL', async () => {
    global.atob = jest.fn(() => { throw new Error('fail'); });

    const handleDownload = async (fileContent: string, fileName: string) => {
      try {
        const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
        const byteCharacters = atob(fileContent);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const downloadUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(downloadUrl);
      } catch (error) {
        alert('Download failed. Please try again.');
      }
    };

    await handleDownload('badbase64', 'testfile1_cx_2024-06-01_1234.xlsx');
    expect(window.alert).toHaveBeenCalledWith('Download failed. Please try again.');
    expect(window.URL.revokeObjectURL).not.toHaveBeenCalled();
  });
});
