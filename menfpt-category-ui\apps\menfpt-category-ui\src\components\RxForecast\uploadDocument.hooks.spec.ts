import { renderHook } from '@testing-library/react';
import { useUploadDayValidation } from './uploadDocument.hooks';

// Mock the dependencies
jest.mock('../../util/dateUtils', () => ({
  getNowInPST: jest.fn(),
}));

jest.mock('date-fns-tz', () => ({
  format: jest.fn(),
}));

describe('uploadDocument.hooks', () => {
  const mockGetNowInPST = require('../../util/dateUtils').getNowInPST;
  const mockFormat = require('date-fns-tz').format;

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock current date to be a Wednesday for consistent testing
    mockGetNowInPST.mockReturnValue(new Date('2025-08-06')); // This is a Wednesday
    mockFormat.mockReturnValue('WEDNESDAY');
  });

  describe('useUploadDayValidation', () => {
    it('returns true for upload allowed when no env variables provided', () => {
      const envVariables = null;

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('returns true for upload allowed when env variables are undefined', () => {
      const envVariables = undefined;

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('returns true for upload allowed when PHARMA_UPLOAD_DAYS is not defined', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {}
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('returns true for upload allowed when current day is in allowed days', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Monday,Wednesday,Friday'
          }
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      // Note: The hook always returns true due to the TODO comment in the implementation
      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('Monday and Wednesday and Friday');
    });

    it('returns allowed days message correctly formatted', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Monday, Tuesday, Wednesday, Thursday, Friday'
          }
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.allowedDaysMessage).toBe('Monday and Tuesday and Wednesday and Thursday and Friday');
    });

    it('handles single allowed day', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Monday'
          }
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.allowedDaysMessage).toBe('Monday');
    });

    it('handles days with extra spaces', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: ' Monday , Tuesday , Wednesday '
          }
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.allowedDaysMessage).toBe('Monday and Tuesday and Wednesday');
    });

    it('handles empty PHARMA_UPLOAD_DAYS string', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: ''
          }
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('handles malformed env variables structure', () => {
      const envVariables = {
        GetEnvVariables: null
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('handles missing GetEnvVariables property', () => {
      const envVariables = {
        someOtherProperty: 'value'
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('handles missing variables property', () => {
      const envVariables = {
        GetEnvVariables: {
          someOtherProperty: 'value'
        }
      };

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      expect(result.current.isUploadAllowed).toBe(true);
      expect(result.current.allowedDaysMessage).toBe('');
    });

    it('updates when envVariables change', () => {
      const initialEnvVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Monday,Wednesday'
          }
        }
      };

      const { result, rerender } = renderHook(
        ({ envVars }) => useUploadDayValidation(envVars),
        { initialProps: { envVars: initialEnvVariables } }
      );

      expect(result.current.allowedDaysMessage).toBe('Monday and Wednesday');

      const updatedEnvVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Tuesday,Thursday,Friday'
          }
        }
      };

      rerender({ envVars: updatedEnvVariables });

      expect(result.current.allowedDaysMessage).toBe('Tuesday and Thursday and Friday');
    });

    it('calls date utilities correctly', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'Monday,Wednesday,Friday'
          }
        }
      };

      renderHook(() => useUploadDayValidation(envVariables));

      // Verify that date utilities are called
      expect(mockGetNowInPST).toHaveBeenCalled();
      expect(mockFormat).toHaveBeenCalledWith(
        expect.any(Date),
        'EEEE',
        { timeZone: 'America/Los_Angeles' }
      );
    });

    it('handles different day formats from date utility', () => {
      const envVariables = {
        GetEnvVariables: {
          variables: {
            PHARMA_UPLOAD_DAYS: 'monday,WEDNESDAY,Friday'
          }
        }
      };

      // Test with different day cases
      mockFormat.mockReturnValue('MONDAY');

      const { result } = renderHook(() => useUploadDayValidation(envVariables));

      // The hook always returns true regardless of day matching
      expect(result.current.isUploadAllowed).toBe(true);
    });
  });
});
