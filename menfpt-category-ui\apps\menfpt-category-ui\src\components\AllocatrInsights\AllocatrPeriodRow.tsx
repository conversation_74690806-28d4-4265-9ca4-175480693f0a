import React ,{ forwardRef } from 'react';
import { PeriodData } from '../../interfaces/allocatr-insights';
import { formatCurrency, formatPercentage } from './utils/insightsFormatters';
import RenderRowCloseStatus from '../renderRowCloseStatus';
import { borderClass } from './AllocatrInsightsHelper';
import { renderAllRows } from './utils/tableCell';

interface AllocatrPeriodRowProps {
  period: PeriodData;
  alwaysSticky?: boolean;
  stickyTop?: number;
  isLastTotal?: boolean; // New prop to indicate last period in Total
  isPeriodActualUsed?: boolean; // Visual indicator for period actual used
}

const AllocatrPeriodRow = forwardRef<HTMLTableRowElement, AllocatrPeriodRowProps>(({ period, alwaysSticky, stickyTop, isLastTotal, isPeriodActualUsed }, ref) => {
  const periodSeq = period.periodNumber != null && !isNaN(Number(period.periodNumber))
    ? Number(String(period.periodNumber).slice(-2))
    : 0;

  // Check if this period belongs to the Total department
  const isTotal = period.id?.toLowerCase().includes('total');
  return (
    <tr
      ref={ref}
      key={`period-${period.id}`}
      className={`period-row${(isTotal || alwaysSticky) ? ' sticky-row' : ''}`}
      data-period-number={period.id}
      style={
        (isTotal || alwaysSticky) && typeof stickyTop === 'number'
          ? {
              position: 'sticky',
              top: stickyTop,
              background: '#F9FFF0',
              zIndex: 8 // Lower than department row, higher than normal rows
            }
          : undefined
      }
    >
      <td></td>
      <td className="period-cell  bg-[#F9FFF0]">
        <div className="flex items-center h-full">
          <RenderRowCloseStatus periodNbr={String(period.periodNumber)} className="pl-[2px] flex items-center w-[16px]  h-[16px]"  />
          <div  className="periodText">
            <span className="font-medium truncated-text-period" title={`Period ${periodSeq}`}>
              Period {periodSeq}
            </span>
          </div>
        </div>
      </td>
      {renderAllRows(period,isPeriodActualUsed)}
    </tr>
  );
});

export default AllocatrPeriodRow;