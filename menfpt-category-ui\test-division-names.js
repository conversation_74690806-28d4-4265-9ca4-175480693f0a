// Test script to verify division name lookup functionality

// Mock toTitleCase function
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

// Updated getDivisionName function (same as in the Excel component)
const getDivisionName = (smicData, divisionId, fallback) => {
  const found = smicData.find((item) => String(item.divisionId) === String(divisionId));
  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);
};

// Test data based on your API structure
const mockSmicData = [
  {
    divisionId: '34',
    divisionName: 'NOR. CALIFORNIA',
    bannerId: '25',
    bannerName: 'ACME STORES',
    deptId: '301',
    deptName: 'Grocery'
  },
  {
    divisionId: '35',
    divisionName: 'SOUTHWEST',
    bannerId: '26',
    bannerName: 'SAFEWAY STORES',
    deptId: '301',
    deptName: 'Grocery'
  },
  {
    divisionId: '17',
    divisionName: 'DENVER',
    bannerId: '27',
    bannerName: 'KINGS FOOD MARKETS',
    deptId: '301',
    deptName: 'Grocery'
  }
];

console.log('Testing getDivisionName function with smicData lookup:');
console.log('=====================================================');

// Test 1: Division found in smicData
console.log('Test 1 - Division found in smicData:');
const result1 = getDivisionName(mockSmicData, '34', 'Fallback Division');
console.log(`getDivisionName(smicData, '34', 'Fallback Division'): "${result1}"`);
console.log('Expected: "Nor. California" ✅');

// Test 2: Division not found, use fallback
console.log('\nTest 2 - Division not found, use fallback:');
const result2 = getDivisionName(mockSmicData, '99', 'Custom Division Name');
console.log(`getDivisionName(smicData, '99', 'Custom Division Name'): "${result2}"`);
console.log('Expected: "Custom Division Name" ✅');

// Test 3: Division not found, no fallback, use divisionId
console.log('\nTest 3 - Division not found, no fallback, use divisionId:');
const result3 = getDivisionName(mockSmicData, '99', '');
console.log(`getDivisionName(smicData, '99', ''): "${result3}"`);
console.log('Expected: "99" ✅');

// Test 4: Different divisions
console.log('\nTest 4 - Different divisions:');
const result4a = getDivisionName(mockSmicData, '35', 'Fallback');
const result4b = getDivisionName(mockSmicData, '17', 'Fallback');
console.log(`getDivisionName(smicData, '35', 'Fallback'): "${result4a}"`);
console.log(`getDivisionName(smicData, '17', 'Fallback'): "${result4b}"`);
console.log('Expected: "Southwest" and "Denver" ✅');

// Test 5: Empty smicData
console.log('\nTest 5 - Empty smicData:');
const result5 = getDivisionName([], '34', 'Fallback Division');
console.log(`getDivisionName([], '34', 'Fallback Division'): "${result5}"`);
console.log('Expected: "Fallback Division" ✅');

console.log('\n✅ All tests completed!');
console.log('\nNow in your Excel file, instead of "34 - Division 34", you should see:');
console.log('- "34 - Nor. California" (if division 34 exists in smicData)');
console.log('- "35 - Southwest" (if division 35 exists in smicData)');
console.log('- "17 - Denver" (if division 17 exists in smicData)');
console.log('- "99 - Custom Name" (if division 99 has a name in the API response)');
console.log('- "99 - 99" (if no name found anywhere)');

console.log('\n📋 Complete Excel Structure Example:');
console.log('1. Total of 2 divisions (Quarter)');
console.log('2. Period 202506 (Period)');
console.log('3. Week 202525 (Week)');
console.log('4. 34 - Nor. California (Quarter)    ← Real division name');
console.log('5.   Acme Stores (Quarter)           ← Clean banner name');
console.log('6.   301 - Grocery (Quarter)');
console.log('7. 35 - Southwest (Quarter)          ← Real division name');
console.log('8.   Safeway Stores (Quarter)        ← Clean banner name');
