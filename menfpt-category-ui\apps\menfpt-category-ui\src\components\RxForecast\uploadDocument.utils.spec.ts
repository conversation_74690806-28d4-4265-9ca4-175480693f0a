import { validateFiles, readFileAsArrayBuffer, createUploadRequest, generateFileName } from './uploadDocument.utils';
import { uploadFilePharmaQuery } from '../../server/Query/uploadFilePharmaQuery';

// Mock dependencies
jest.mock('date-fns-tz', () => ({
  format: jest.fn(() => '2025-08-06'),
  utcToZonedTime: jest.fn((date) => date),
}));

jest.mock('../../server/Query/uploadFilePharmaQuery', () => ({
  uploadFilePharmaQuery: 'mocked-query',
}));

// Mock Date.now() for consistent fileName generation
const mockNow = 1691308800000; // Fixed timestamp
jest.spyOn(Date, 'now').mockReturnValue(mockNow);

describe('uploadDocument.utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateFiles', () => {
    const createMockFile = (name: string, size: number, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') => {
      const file = new File(['content'], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('validates files within size limit', () => {
      const files = [
        createMockFile('file1.xlsx', 1024 * 1024), // 1MB
        createMockFile('file2.xlsx', 10 * 1024 * 1024), // 10MB
      ];

      const result = validateFiles(files);

      expect(result.fileArr).toHaveLength(2);
      expect(result.oversized).toBe(false);
      expect(result.oversizedFiles).toHaveLength(0);
      
      result.fileArr.forEach((item, index) => {
        expect(item.file).toBe(files[index]);
        expect(item.status).toBe('inProgress');
        expect(item.isLoading).toBe(true);
      });
    });

    it('rejects files exceeding size limit', () => {
      const files = [
        createMockFile('small.xlsx', 1024 * 1024), // 1MB
        createMockFile('large.xlsx', 26 * 1024 * 1024), // 26MB
        createMockFile('huge.xlsx', 30 * 1024 * 1024), // 30MB
      ];

      const result = validateFiles(files);

      expect(result.fileArr).toHaveLength(1);
      expect(result.fileArr[0].file.name).toBe('small.xlsx');
      expect(result.oversized).toBe(true);
      expect(result.oversizedFiles).toEqual(['large.xlsx', 'huge.xlsx']);
    });

    it('handles all files exceeding size limit', () => {
      const files = [
        createMockFile('large1.xlsx', 26 * 1024 * 1024), // 26MB
        createMockFile('large2.xlsx', 28 * 1024 * 1024), // 28MB
      ];

      const result = validateFiles(files);

      expect(result.fileArr).toHaveLength(0);
      expect(result.oversized).toBe(true);
      expect(result.oversizedFiles).toEqual(['large1.xlsx', 'large2.xlsx']);
    });

    it('handles empty file list', () => {
      const result = validateFiles([]);

      expect(result.fileArr).toHaveLength(0);
      expect(result.oversized).toBe(false);
      expect(result.oversizedFiles).toHaveLength(0);
    });

    it('handles file exactly at size limit', () => {
      const files = [
        createMockFile('exact.xlsx', 25 * 1024 * 1024), // Exactly 25MB
      ];

      const result = validateFiles(files);

      // Files exactly at 25MB are considered oversized (>= comparison in implementation)
      expect(result.fileArr).toHaveLength(0);
      expect(result.oversized).toBe(true);
      expect(result.oversizedFiles).toEqual(['exact.xlsx']);
    });

    it('handles file just under size limit', () => {
      const files = [
        createMockFile('justUnder.xlsx', (25 * 1024 * 1024) - 1), // Just under 25MB
      ];

      const result = validateFiles(files);

      expect(result.fileArr).toHaveLength(1);
      expect(result.oversized).toBe(false);
      expect(result.oversizedFiles).toHaveLength(0);
    });

    it('handles FileList input', () => {
      const file = createMockFile('test.xlsx', 1024 * 1024);
      const fileList = {
        0: file,
        length: 1,
        item: (index: number) => index === 0 ? file : null,
      } as FileList;

      const result = validateFiles(fileList);

      expect(result.fileArr).toHaveLength(1);
      expect(result.fileArr[0].file).toBe(file);
    });
  });

  describe('readFileAsArrayBuffer', () => {
    it('reads file as array buffer successfully', async () => {
      const mockFileContent = new ArrayBuffer(8);
      const mockUint8Array = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      
      // Mock FileReader
      const mockFileReader = {
        result: mockFileContent,
        onload: null as any,
        onerror: null as any,
        readAsArrayBuffer: jest.fn(),
      };

      jest.spyOn(global, 'FileReader').mockImplementation(() => mockFileReader as any);
      jest.spyOn(global, 'Uint8Array').mockImplementation(() => mockUint8Array as any);

      const file = new File(['content'], 'test.xlsx');
      const promise = readFileAsArrayBuffer(file);

      // Simulate successful file read
      mockFileReader.onload({ target: { result: mockFileContent } } as any);

      const result = await promise;
      expect(result).toEqual([1, 2, 3, 4, 5, 6, 7, 8]);
      expect(mockFileReader.readAsArrayBuffer).toHaveBeenCalledWith(file);
    });

    it('handles file read error', async () => {
      const mockFileReader = {
        onload: null as any,
        onerror: null as any,
        readAsArrayBuffer: jest.fn(),
      };

      jest.spyOn(global, 'FileReader').mockImplementation(() => mockFileReader as any);

      const file = new File(['content'], 'test.xlsx');
      const promise = readFileAsArrayBuffer(file);

      // Simulate file read error
      const error = new Error('File read failed');
      mockFileReader.onerror(error);

      await expect(promise).rejects.toThrow('File read failed');
    });
  });

  describe('generateFileName', () => {
    it('generates filename with correct format', () => {
      const originalName = 'test.xlsx';
      const result = generateFileName(originalName);

      expect(result).toBe(`test_cx_2025-08-06_${mockNow}.xlsx`);
    });

    it('handles filename without extension', () => {
      const originalName = 'test';
      const result = generateFileName(originalName);

      expect(result).toBe(`test_cx_2025-08-06_${mockNow}.xlsx`);
    });

    it('handles filename with different case extension', () => {
      const originalName = 'TEST.XLSX';
      const result = generateFileName(originalName);

      expect(result).toBe(`TEST_cx_2025-08-06_${mockNow}.xlsx`);
    });

    it('handles filename with mixed case extension', () => {
      const originalName = 'test.XlSx';
      const result = generateFileName(originalName);

      expect(result).toBe(`test_cx_2025-08-06_${mockNow}.xlsx`);
    });

    it('preserves complex filename', () => {
      const originalName = 'my-complex_file name (1).xlsx';
      const result = generateFileName(originalName);

      expect(result).toBe(`my-complex_file name (1)_cx_2025-08-06_${mockNow}.xlsx`);
    });
  });

  describe('createUploadRequest', () => {
    const mockFile = new File(['content'], 'test.xlsx');
    const mockFileContent = [1, 2, 3, 4];
    const fiscalWeekNbr = 42;

    it('creates upload request with regular username', () => {
      const userName = 'John Doe';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.query).toBe('mocked-query');
      expect(result.variables.fileUploadToBlob).toEqual({
        fileName: `test_cx_2025-08-06_${mockNow}.xlsx`,
        fileContent: mockFileContent,
        uploadDataPersistenceEnabled: true,
        user: 'John Doe',
        fiscalWeekNbr,
      });
    });

    it('strips contractor suffix from username', () => {
      const userName = 'John Doe (Contractor)';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('John Doe');
    });

    it('handles username with spaces around contractor suffix', () => {
      const userName = 'John Doe  (Contractor)  ';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('John Doe');
    });

    it('handles username ending with contractor without parentheses', () => {
      const userName = 'John Doe Contractor';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('John Doe Contractor');
    });

    it('handles username with contractor in the middle', () => {
      const userName = 'John (Contractor) Doe';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('John (Contractor) Doe');
    });

    it('handles empty username', () => {
      const userName = '';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('');
    });

    it('handles username with only contractor suffix', () => {
      const userName = '(Contractor)';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('');
    });

    it('handles username with spaces only', () => {
      const userName = '   ';
      
      const result = createUploadRequest(mockFile, mockFileContent, userName, fiscalWeekNbr);

      expect(result.variables.fileUploadToBlob.user).toBe('');
    });

    it('preserves other file properties', () => {
      const complexFile = new File(['content'], 'complex-file-name.xlsx', { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const largeFileContent = Array.from({ length: 1000 }, (_, i) => i);
      const userName = 'Jane Smith (Contractor)';
      const weekNumber = 52;
      
      const result = createUploadRequest(complexFile, largeFileContent, userName, weekNumber);

      expect(result.variables.fileUploadToBlob).toEqual({
        fileName: `complex-file-name_cx_2025-08-06_${mockNow}.xlsx`,
        fileContent: largeFileContent,
        uploadDataPersistenceEnabled: true,
        user: 'Jane Smith',
        fiscalWeekNbr: weekNumber,
      });
    });
  });
});
