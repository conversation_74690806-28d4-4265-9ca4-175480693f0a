# Development Scripts

This folder contains Node.js scripts for managing the development workflow between the UI and BFF codebases.

## Scripts Overview

### `update-and-start.js`
**Purpose**: Updates both UI and BFF repositories and starts both servers
**Usage**: `npm run update-and-start`
**What it does**:
- Pulls latest changes from `origin master` for both repositories
- Starts the UI server (`npm start`)
- Starts the BFF server (`npm run codegen && npm run start:local`)
- Opens each server in a separate terminal window

### `update-repos.js`
**Purpose**: Updates both UI and BFF repositories only
**Usage**: `npm run update-repos`
**What it does**:
- Pulls latest changes from `origin master` for both repositories
- Does not start any servers

### `start-both.js`
**Purpose**: Starts both UI and BFF servers only
**Usage**: `npm run start-both`
**What it does**:
- Starts the UI server (`npm start`)
- Starts the BFF server (`npm run codegen && npm run start:local`)
- Opens each server in a separate terminal window

## Requirements

- Both `menfpt-category-ui` and `menfpt-category-bff` must be located in parallel directories
- Git must be installed and configured
- Node.js and npm must be installed
- Both projects must have their dependencies installed (`npm install`)

## Directory Structure

The scripts expect this directory structure:
```
parent-directory/
├── menfpt-category-ui/     ← Run scripts from here
└── menfpt-category-bff/    ← Automatically detected
```

## Cross-Platform Support

- **Windows**: Uses PowerShell to open new windows
- **macOS/Linux**: Uses AppleScript to open Terminal windows

## Error Handling

- Validates that the BFF directory exists before proceeding
- Shows clear error messages with colored output
- Provides helpful guidance if directories are not found
