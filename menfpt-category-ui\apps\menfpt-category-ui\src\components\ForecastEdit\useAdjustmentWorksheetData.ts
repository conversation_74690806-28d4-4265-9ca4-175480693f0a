import { useSelectorWrap } from '../../rtk/rtk-utilities';


type weekFields = {
  fiscalWeekNbr: string;
  line1SalesToPublicNbr: string;
  line1SalesToPublicPct: string;
  line5BookGrossProfitNbr: string;
  line5BookGrossProfitPct: string;
  line5MarkDownsNbr: string;
  line5MarkDownsPct: string;
  line5ShrinkNbr: string;
  line5ShrinkPct: string;
  line6SuppliesPackagingNbr: string;
  line6SuppliesPackagingPct: string;
  line7RetailsAllowancesNbr: string;
  line7RetailsAllowancesPct: string;
  editedColumns: string;
  reason: string;
  comment: string;
};

export interface WeekAPIFields {
  aggregatedLevel: string;
  comment: string | null;
  createdBy: string | null;
  createdTs: string | null;
  fiscalPeriodNbr: number;
  fiscalQuarterNbr: number | null;
  fiscalWeekEnding: string;
  fiscalWeekNbr: number;
  fiscalYearNbr: number | null;
  forecastType: string;
  lastUpdatedUserRole: string | null;
  line5BookGrossProfitNbr: number;
  line5BookGrossProfitPct: number;
  line5MarkDownsNbr: number;
  line5MarkDownsPct: number;
  line1PublicToSalesNbr: number;
  line1PublicToSalesPct: number;
  line5ShrinkNbr: number;
  line5ShrinkPct: number;
  line5RealGrossProfitNbr: number;
  line5RealGrossProfitPct: number;
  line6SuppliesPackagingNbr: number;
  line7RetailsAllowancesNbr: number;
  line7RetailsAllowancesPct: number;
  line8RealGrossProfitNbr: number;
  line8RealGrossProfitPct: number;
  mainRow: string;
  reason: string | null;
  state: string | null;
  subRow: string;
  updatedBy: string | null;
  updatedTs: string | null;
  versionNbr: number | null;
}

const useAdjustmentWorksheetData = () => {
  const { data: adjustmentWorksheetData } = useSelectorWrap('adjustmentWorkSheetFilter_rn');

  const initialEntries = Array.from({ length: 13 }, () => ({
    fiscalWeekNbr: '',
    line1SalesToPublicNbr: '',
    line1SalesToPublicPct: '',
    line5BookGrossProfitNbr: '',
    line5BookGrossProfitPct: '',
    line5MarkDownsNbr: '',
    line5MarkDownsPct: '',
    line5ShrinkNbr: '',
    line5ShrinkPct: '',
    line6SuppliesPackagingNbr: '',
    line6SuppliesPackagingPct: '',
    line7RetailsAllowancesNbr: '',
    line7RetailsAllowancesPct: '',
    editedColumns: '',
    reason: '',
    comment: '',
  }));

  // 6,22 is week 0 to week 12. 
  // 0 is the first week, 12 is the last week
  // items[3] is projection Data
  // details[0] is the details object
  // data is stored in adjustmentWorksheetData.slice(6,22)[0].items[3].details[0]
  const filledEntries = Array.isArray(adjustmentWorksheetData)
  ? adjustmentWorksheetData.slice(6, 22).map((weekData: any) => {
      const weekNbr = weekData.items[0].details[0].fiscalWeekNbr;
      const details = weekData.items[3]?.details[0] || {};
      return {
        fiscalWeekNbr: weekData.fiscalWeekNbr || '',
        line1SalesToPublicNbr: details.line1SalesToPublicNbr || '',
        line1SalesToPublicPct: details.line1SalesToPublicPct || '',
        line5BookGrossProfitNbr: details.line5BookGrossProfitNbr || '',
        line5BookGrossProfitPct: details.line5BookGrossProfitPct || '',
        line5MarkDownsNbr: details.line5MarkDownsNbr || '',
        line5MarkDownsPct: details.line5MarkDownsPct || '',
        line5ShrinkNbr: details.line5ShrinkNbr || '',
        line5ShrinkPct: details.line5ShrinkPct || '',
        line6SuppliesPackagingNbr: details.line6SuppliesPackagingNbr || '',
        line6SuppliesPackagingPct: details.line6SuppliesPackagingPct || '',
        line7RetailsAllowancesNbr: details.line7RetailsAllowancesNbr || '',
        line7RetailsAllowancesPct: details.line7RetailsAllowancesPct || '',
        editedColumns: details.editedColumns || '',
        reason: details.reason || '',
        comment: details.comment || '',
      };
    })
  : initialEntries;

  return filledEntries;
};

export default useAdjustmentWorksheetData;