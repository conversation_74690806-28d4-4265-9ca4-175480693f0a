import DashboardTitleComponent from "./dashboard-title-component";
import DashboardTabs from "./dashboard-tabs";

import TableContainer from "./worksheet-table-container";
import { useCombinedFiltersAndQuarters } from "../features/worksheetFilter/useCombinedFiltersAndQuarters";
import { WorksheetFilterContainer } from "../features/worksheetFilter/worksheetFilterContainer";
import HelpIcon from '../components/HelpIcon';
import { useState } from 'react';
import Spinner from "@albertsons/uds/molecule/Spinner";

const Home : React.FunctionComponent<any> = () => {
    // Use the combined hook to fetch both filter and quarters data in a single API call
    const { filterLoading, filterData } = useCombinedFiltersAndQuarters();

    const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
    const openFilterModal = () => setIsFilterModalOpen(true);

    return (
      <>
      {filterLoading ? <Spinner /> : (
      <div className="bg-gray-206">
          <div className="flex items-center justify-between px-2 py-2 overflow-x-auto">
            <DashboardTitleComponent />
            <WorksheetFilterContainer
              FiltersList={filterData}
              isFilterModalOpen={isFilterModalOpen}
              setIsFilterModalOpen={setIsFilterModalOpen}
              openFilterModal={openFilterModal}
            />
          </div>
        {!filterLoading && <DashboardTabs />}
        <HelpIcon variant="dashboard" />
        {/* <h2>Home</h2> */}
        {/* <h2>{ menfptCategoryTitle?.message ? menfptCategoryTitle?.message:""}</h2> */}
        {/* <WorksheetTable/> */}

        {/* <div className="flex items-center px-7 w-full bg-gray-207 border-t border-gray-204 flex-wrap justify-center min-h-[52px] gap-[4vw]" data-testid="dashboard-timestamp-footer">
          <span className="text-[14px] text-light-text leading-6 font-normal">Forecast last updated on NA</span>
          <span className="text-[14px] text-light-text leading-6 font-normal">Forecast last aggregated on NA</span>
          <span className="text-[14px] text-light-text leading-6 font-normal">Projection last aggregated on NA</span>
          <span className="text-[14px] text-light-text leading-6 font-normal">Actuals last updated on NA</span>
        </div> */}
      </div>)}
      </>
    );
  }

  export default Home;