{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "mep-ui", "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"]}}}, "targetDefaults": {"build": {"dependsOn": ["^build"]}, "e2e": {"inputs": ["default", "^production"]}, "test": {"inputs": ["default", "^default", "{workspaceRoot}/jest.preset.js"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s"], "sharedGlobals": ["{workspaceRoot}/babel.config.json"]}, "generators": {"@nrwl/react": {"application": {"style": "scss", "linter": "eslint", "bundler": "webpack", "babel": true}, "component": {"style": "scss"}, "library": {"style": "scss", "linter": "eslint"}}}, "defaultProject": "menfpt-category-ui-static-server"}