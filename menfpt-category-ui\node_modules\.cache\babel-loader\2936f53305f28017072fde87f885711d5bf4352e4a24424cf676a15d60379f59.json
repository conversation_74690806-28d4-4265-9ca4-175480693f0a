{"ast": null, "code": "import { formatPercentage } from '../../components/AllocatrInsights/utils/insightsFormatters';\nfunction formatNegativeCurrency(val, formatCurrency) {\n  const num = Number(val);\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\n  if (num < 0) {\n    return `($${Math.abs(num).toLocaleString('en-US', {\n      maximumFractionDigits: 0\n    })})`;\n  }\n  return formatCurrency(val);\n}\nfunction formatActualOrForecast(val, formatCurrency) {\n  const num = Number(val);\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\n  if (num < 0) {\n    return `($${Math.abs(num).toLocaleString('en-US', {\n      maximumFractionDigits: 0\n    })})`;\n  }\n  return formatCurrency(val);\n}\nfunction displayDashIfZero(val, formatter) {\n  const num = Number(val);\n  if (num === 0) return '--';\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\n  return formatter(val);\n}\nexport function mapRow(baseRow, data, formatCurrency, type = '', period = '', week = '') {\n  var _data$vsLY, _data$vsProjection, _data$bookGrossProfit, _data$bookGrossProfit2, _data$bookGrossProfit3, _data$bookGrossProfit4, _data$bookGrossProfit5, _data$markdown$projec, _data$markdown, _data$markdown2, _data$markdown3, _data$markdown4, _data$markdown5, _data$shrink$projecti, _data$shrink, _data$shrink2, _data$shrink3, _data$shrink4, _data$shrink5, _data$line, _data$line2, _data$line3, _data$line4, _data$line5, _data$line6, _data$line7, _data$line8, _data$line9, _data$line10, _data$line11, _data$line12, _data$line13, _data$line14, _data$line15, _data$line16, _data$line17, _data$line18;\n  return Object.assign({}, baseRow, {\n    '$ Projection': displayDashIfZero(data.line1Projection, formatCurrency),\n    '$ Last Year': displayDashIfZero(data.lastYear, formatCurrency),\n    '$Actual/Merch.Forecast': displayDashIfZero(data.actualOrForecast, formatCurrency),\n    'Keeper% (Includes ID)': formatPercentage(data.idPercentage),\n    '$ vs LY': formatCurrency((_data$vsLY = data.vsLY) == null ? void 0 : _data$vsLY.value),\n    '$ vs Projection': formatCurrency((_data$vsProjection = data.vsProjection) == null ? void 0 : _data$vsProjection.value),\n    '$ Projection (BGP)': displayDashIfZero((_data$bookGrossProfit = data.bookGrossProfit) == null ? void 0 : _data$bookGrossProfit.projectionValue, formatCurrency),\n    '% Projection (BGP)': displayDashIfZero((_data$bookGrossProfit2 = data.bookGrossProfit) == null ? void 0 : _data$bookGrossProfit2.projectionPct, formatPercentage),\n    '$Actual/Merch.Forecast (BGP)': displayDashIfZero((_data$bookGrossProfit3 = data.bookGrossProfit) == null ? void 0 : _data$bookGrossProfit3.actualOrForecast, formatCurrency),\n    '%Actual/Merch.Forecast': formatPercentage((_data$bookGrossProfit4 = data.bookGrossProfit) == null ? void 0 : _data$bookGrossProfit4.percentActualOrForecast),\n    '% vs Projection': formatPercentage((_data$bookGrossProfit5 = data.bookGrossProfit) == null ? void 0 : _data$bookGrossProfit5.vsProjection),\n    '$ Projection (Markdown)': displayDashIfZero(Math.abs((_data$markdown$projec = (_data$markdown = data.markdown) == null ? void 0 : _data$markdown.projectionValue) != null ? _data$markdown$projec : 0), formatCurrency),\n    '% Projection (Markdown)': displayDashIfZero((_data$markdown2 = data.markdown) == null ? void 0 : _data$markdown2.projectionPct, formatPercentage),\n    '$Actual/Merch.Forecast (Markdown)': displayDashIfZero((_data$markdown3 = data.markdown) == null ? void 0 : _data$markdown3.actualOrForecast, v => formatActualOrForecast(v, formatCurrency)),\n    '%Actual/Merch.Forecast (Markdown)': formatPercentage((_data$markdown4 = data.markdown) == null ? void 0 : _data$markdown4.percentActualOrForecast),\n    '% vs Projection (Markdowns)': formatPercentage((_data$markdown5 = data.markdown) == null ? void 0 : _data$markdown5.vsProjection),\n    '$ Projection (Shrink)': displayDashIfZero(Math.abs((_data$shrink$projecti = (_data$shrink = data.shrink) == null ? void 0 : _data$shrink.projectionValue) != null ? _data$shrink$projecti : 0), formatCurrency),\n    '% Projection (Shrink)': displayDashIfZero((_data$shrink2 = data.shrink) == null ? void 0 : _data$shrink2.projectionPct, formatPercentage),\n    '$Actual/Merch.Forecast (Shrink)': displayDashIfZero((_data$shrink3 = data.shrink) == null ? void 0 : _data$shrink3.actualOrForecast, v => formatActualOrForecast(v, formatCurrency)),\n    '%Actual/Merch.Forecast (Shrink)': formatPercentage((_data$shrink4 = data.shrink) == null ? void 0 : _data$shrink4.percentActualOrForecast),\n    '% Projection (Shrinks)': formatPercentage((_data$shrink5 = data.shrink) == null ? void 0 : _data$shrink5.vsProjection),\n    '$ Projection (RGP)': displayDashIfZero((_data$line = data.line5) == null ? void 0 : _data$line.projectionValue, formatCurrency),\n    '% Projection (RGP)': displayDashIfZero((_data$line2 = data.line5) == null ? void 0 : _data$line2.projectionPct, formatPercentage),\n    '$Actual/Merch.Forecast (RGP)': displayDashIfZero((_data$line3 = data.line5) == null ? void 0 : _data$line3.actualOrForecast, formatCurrency),\n    '%Actual/Merch.Forecast (RGP)': formatPercentage((_data$line4 = data.line5) == null ? void 0 : _data$line4.percentActualOrForecast),\n    '$ vs Projection (RGP)': formatCurrency((_data$line5 = data.line5) == null ? void 0 : _data$line5.vsProjection),\n    '% vs Projection (RGP)': formatPercentage((_data$line6 = data.line5) == null ? void 0 : _data$line6.percentVsProjection),\n    '$ Projection (Supplies Packaging)': displayDashIfZero((_data$line7 = data.line6) == null ? void 0 : _data$line7.projection, v => formatNegativeCurrency(v, formatCurrency)),\n    '$Actual/Merch.Forecast (Supplies Packaging)': displayDashIfZero((_data$line8 = data.line6) == null ? void 0 : _data$line8.actualOrForecast, v => formatNegativeCurrency(v, formatCurrency)),\n    '$ vs Projection (Supplies Packaging)': formatCurrency((_data$line9 = data.line6) == null ? void 0 : _data$line9.vsProjection),\n    '$ Projection (Retail Allowance)': displayDashIfZero((_data$line10 = data.line7) == null ? void 0 : _data$line10.projection, formatCurrency),\n    '$Actual/Merch.Forecast (Retail Allowance)': displayDashIfZero((_data$line11 = data.line7) == null ? void 0 : _data$line11.actualOrForecast, formatCurrency),\n    '$ vs Projection (Retail Allowance)': formatCurrency((_data$line12 = data.line7) == null ? void 0 : _data$line12.vsProjection),\n    '$ Projection(Sales)': displayDashIfZero((_data$line13 = data.line8) == null ? void 0 : _data$line13.projectionValue, formatCurrency),\n    '% Projection(Sales)': displayDashIfZero((_data$line14 = data.line8) == null ? void 0 : _data$line14.projectionPct, formatPercentage),\n    '$Actual/Merch.Forecast (Sales)': displayDashIfZero((_data$line15 = data.line8) == null ? void 0 : _data$line15.actualOrForecast, formatCurrency),\n    '%Actual/Merch.Forecast (Sales)': formatPercentage((_data$line16 = data.line8) == null ? void 0 : _data$line16.percentActualOrForecast),\n    '$ vs Projection (Sales)': formatCurrency((_data$line17 = data.line8) == null ? void 0 : _data$line17.vsProjection),\n    '% vs Projection (Sales)': formatPercentage((_data$line18 = data.line8) == null ? void 0 : _data$line18.percentVsProjection)\n  });\n}\nexport function groupDataForExcel(data) {\n  const groupedData = new Map();\n  data.forEach(department => {\n    // Assuming 'divisionName' and 'bannerName' are available on the department object\n    const divisionName = department.divisionName || 'Default Division';\n    const bannerName = department.bannerName || 'Default Banner';\n    if (!groupedData.has(divisionName)) {\n      groupedData.set(divisionName, new Map());\n    }\n    const banners = groupedData.get(divisionName);\n    if (!banners.has(bannerName)) {\n      banners.set(bannerName, []);\n    }\n    const departments = banners.get(bannerName);\n    departments.push(department);\n  });\n  return groupedData;\n}\nexport const handleDownloadClick = (dashboardData, smicData, appliedFilters, handleDownloadExcel) => {\n  if (!dashboardData || dashboardData.length === 0) {\n    alert('No dashboard data to export!');\n    return;\n  }\n  handleDownloadExcel(dashboardData, smicData, appliedFilters);\n};\nexport const getParentHeaderRow = quarterDisplay => [quarterDisplay,\n// A (1)\n'Line 1 (Sales to Public)', '', '', '', '', '',\n// B-G (6)\n'Line 5: Book Gross Profit', '', '', '', '',\n// H-L (5)\n'Line 5: Markdown', '', '', '', '',\n// M-Q (5)\n'Line 5: Shrink %', '', '', '', '',\n// R-V (5)\n'Line 5 (Realized Gross Profit): Total', '', '', '', '', '',\n// W-AB (6)\n'Line 6 (Supplies Packaging)', '', '',\n// AC-AE (3)\n'Line 7 (Retail Allowance)', '', '',\n// AF-AH (3)\n'Line 8 (Realized Gross Profit Before Other Revenue - Sales)', '', '', '', '', '' // AI-AN (6)\n];\nexport const COMMON_HEADERS = ['',\n// Department/Period/Week \n'$ Projection', '$ Last Year', '$Actual/Merch.Forecast', 'Keeper% (Includes ID)', '$ vs LY', '$ vs Projection',\n//book gross profit\n'$Projection', '% Projection', '$Actual/Merch.Forecast', '%Actual/Merch.Forecast', '% vs Projection',\n//markdown\n'$Projection', '% Projection', '$Actual/Merch.Forecast', '%Actual/Merch.Forecast', '% vs Projection',\n//shrink\n'$Projection', '% Projection', '$Actual/Merch.Forecast', '%Actual/Merch.Forecast', '% vs Projection',\n//RGP\n'$Projection', '% Projection', '$Actual/Merch.Forecast', '%Actual/Merch.Forecast', '$ vs Projection', '% vs Projection',\n//line6\n'$ Projection', '$Actual/Merch.Forecast', '$ vs Projection',\n//line7\n'$ Projection', '$Actual/Merch.Forecast', '$ vs Projection',\n//line8\n'$ Projection', '% Projection', '$Actual/Merch.Forecast', '%Actual/Merch.Forecast', '$ vs Projection', '% vs Projection'];\nexport const VS_PROJECTION_HEADERS = ['$ vs LY', '$ vs Projection', '% vs Projection', '% vs Projection (Book Gross Profit)', '% vs Projection (Markdown)', '% vs Projection (Shrink)', '$ vs Projection (RGP)', '% vs Projection (RGP)', '$ vs Projection (Supplies Packaging)', '$ vs Projection (Retail Allowance)', '$ vs Projection (Sales)', '% vs Projection (Sales)'];\nexport const VS_PROJECTION_DOLLAR_HEADERS = ['$ vs LY', '$ vs Projection', '$ vs Projection (RGP)', '$ vs Projection (Supplies Packaging)', '$ vs Projection (Retail Allowance)', '$ vs Projection (Sales)'];", "map": {"version": 3, "names": ["formatPercentage", "formatNegativeCurrency", "val", "formatCurrency", "num", "Number", "undefined", "isNaN", "Math", "abs", "toLocaleString", "maximumFractionDigits", "formatActualOrForecast", "displayDashIfZero", "formatter", "mapRow", "baseRow", "data", "type", "period", "week", "_data$vsLY", "_data$vsProjection", "_data$bookGrossProfit", "_data$bookGrossProfit2", "_data$bookGrossProfit3", "_data$bookGrossProfit4", "_data$bookGrossProfit5", "_data$markdown$projec", "_data$markdown", "_data$markdown2", "_data$markdown3", "_data$markdown4", "_data$markdown5", "_data$shrink$projecti", "_data$shrink", "_data$shrink2", "_data$shrink3", "_data$shrink4", "_data$shrink5", "_data$line", "_data$line2", "_data$line3", "_data$line4", "_data$line5", "_data$line6", "_data$line7", "_data$line8", "_data$line9", "_data$line10", "_data$line11", "_data$line12", "_data$line13", "_data$line14", "_data$line15", "_data$line16", "_data$line17", "_data$line18", "Object", "assign", "line1Projection", "lastYear", "actualOrForecast", "idPercentage", "vsLY", "value", "vsProjection", "bookGrossProfit", "projectionValue", "projectionPct", "percentActualOrForecast", "markdown", "v", "shrink", "line5", "percentVsProjection", "line6", "projection", "line7", "line8", "groupDataForExcel", "groupedData", "Map", "for<PERSON>ach", "department", "divisionName", "bannerName", "has", "set", "banners", "get", "departments", "push", "handleDownloadClick", "dashboardData", "smicData", "appliedFilters", "handleDownloadExcel", "length", "alert", "getParentHeaderRow", "quarterDisplay", "COMMON_HEADERS", "VS_PROJECTION_HEADERS", "VS_PROJECTION_DOLLAR_HEADERS"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/components/DashboardDownloadExcel/DashboardDownloadExcelHelper.tsx"], "sourcesContent": ["import { formatPercentage } from '../../components/AllocatrInsights/utils/insightsFormatters';\r\n\r\nfunction formatNegativeCurrency(val: any, formatCurrency: (v: any) => string) {\r\n  const num = Number(val);\r\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\r\n  if (num < 0) {\r\n    return `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;\r\n  }\r\n  return formatCurrency(val);\r\n}\r\nfunction formatActualOrForecast(val: any, formatCurrency: (v: any) => string) {\r\n  const num = Number(val);\r\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\r\n  if (num < 0) {\r\n    return `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;\r\n  }\r\n  return formatCurrency(val);\r\n}\r\nfunction displayDashIfZero(val: any, formatter: (v: any) => string) {\r\n  const num = Number(val);\r\n  if (num === 0) return '--';\r\n  if (val === null || val === undefined || val === '' || isNaN(num)) return '';\r\n  return formatter(val);\r\n}\r\n\r\nexport function mapRow(\r\n  baseRow: any,\r\n  data: any,\r\n  formatCurrency: (v: any) => string,\r\n  type = '',\r\n  period = '',\r\n  week = ''\r\n) {\r\n  return {\r\n    ...baseRow,\r\n    '$ Projection': displayDashIfZero(data.line1Projection, formatCurrency),\r\n    '$ Last Year': displayDashIfZero(data.lastYear, formatCurrency),\r\n    '$Actual/Merch.Forecast': displayDashIfZero(data.actualOrForecast, formatCurrency),\r\n    'Keeper% (Includes ID)': formatPercentage(data.idPercentage), \r\n    '$ vs LY': formatCurrency(data.vsLY?.value), \r\n    '$ vs Projection': formatCurrency(data.vsProjection?.value), \r\n    '$ Projection (BGP)': displayDashIfZero(data.bookGrossProfit?.projectionValue,formatCurrency), \r\n    '% Projection (BGP)': displayDashIfZero(data.bookGrossProfit?.projectionPct,formatPercentage), \r\n    '$Actual/Merch.Forecast (BGP)': displayDashIfZero(data.bookGrossProfit?.actualOrForecast,formatCurrency), \r\n    '%Actual/Merch.Forecast': formatPercentage(data.bookGrossProfit?.percentActualOrForecast), \r\n    '% vs Projection': formatPercentage(data.bookGrossProfit?.vsProjection), \r\n    '$ Projection (Markdown)': displayDashIfZero(Math.abs(data.markdown?.projectionValue ?? 0),formatCurrency), \r\n    '% Projection (Markdown)': displayDashIfZero(data.markdown?.projectionPct,formatPercentage),\r\n    '$Actual/Merch.Forecast (Markdown)': displayDashIfZero(data.markdown?.actualOrForecast,v=>formatActualOrForecast(v, formatCurrency)),\r\n    '%Actual/Merch.Forecast (Markdown)': formatPercentage(data.markdown?.percentActualOrForecast),\r\n    '% vs Projection (Markdowns)':formatPercentage(data.markdown?.vsProjection),\r\n    '$ Projection (Shrink)': displayDashIfZero(Math.abs(data.shrink?.projectionValue ?? 0),formatCurrency), \r\n    '% Projection (Shrink)': displayDashIfZero(data.shrink?.projectionPct,formatPercentage), \r\n    '$Actual/Merch.Forecast (Shrink)': displayDashIfZero(data.shrink?.actualOrForecast,v=>formatActualOrForecast(v, formatCurrency)),\r\n    '%Actual/Merch.Forecast (Shrink)': formatPercentage(data.shrink?.percentActualOrForecast),\r\n    '% Projection (Shrinks)': formatPercentage(data.shrink?.vsProjection), \r\n    '$ Projection (RGP)': displayDashIfZero(data.line5?.projectionValue,formatCurrency), \r\n    '% Projection (RGP)': displayDashIfZero(data.line5?.projectionPct,formatPercentage),\r\n    '$Actual/Merch.Forecast (RGP)': displayDashIfZero(data.line5?.actualOrForecast, formatCurrency), \r\n    '%Actual/Merch.Forecast (RGP)': formatPercentage(data.line5?.percentActualOrForecast), \r\n    '$ vs Projection (RGP)': formatCurrency(data.line5?.vsProjection), \r\n    '% vs Projection (RGP)': formatPercentage(data.line5?.percentVsProjection), \r\n    '$ Projection (Supplies Packaging)': displayDashIfZero(data.line6?.projection, v => formatNegativeCurrency(v, formatCurrency)),\r\n    '$Actual/Merch.Forecast (Supplies Packaging)': displayDashIfZero(data.line6?.actualOrForecast, v => formatNegativeCurrency(v, formatCurrency)),\r\n    '$ vs Projection (Supplies Packaging)': formatCurrency(data.line6?.vsProjection), \r\n    '$ Projection (Retail Allowance)': displayDashIfZero(data.line7?.projection,formatCurrency), \r\n    '$Actual/Merch.Forecast (Retail Allowance)': displayDashIfZero(data.line7?.actualOrForecast,formatCurrency), \r\n    '$ vs Projection (Retail Allowance)': formatCurrency(data.line7?.vsProjection), \r\n    '$ Projection(Sales)': displayDashIfZero(data.line8?.projectionValue,formatCurrency), \r\n    '% Projection(Sales)': displayDashIfZero(data.line8?.projectionPct,formatPercentage), \r\n    '$Actual/Merch.Forecast (Sales)': displayDashIfZero(data.line8?.actualOrForecast,formatCurrency), \r\n    '%Actual/Merch.Forecast (Sales)': formatPercentage(data.line8?.percentActualOrForecast), \r\n    '$ vs Projection (Sales)': formatCurrency(data.line8?.vsProjection), \r\n    '% vs Projection (Sales)': formatPercentage(data.line8?.percentVsProjection), \r\n  };\r\n}\r\n\r\nexport function groupDataForExcel(data: any[]) {\r\n  const groupedData = new Map();\r\n\r\n  data.forEach(department => {\r\n    // Assuming 'divisionName' and 'bannerName' are available on the department object\r\n    const divisionName = department.divisionName || 'Default Division';\r\n    const bannerName = department.bannerName || 'Default Banner';\r\n\r\n    if (!groupedData.has(divisionName)) {\r\n      groupedData.set(divisionName, new Map());\r\n    }\r\n\r\n    const banners = groupedData.get(divisionName);\r\n    if (!banners.has(bannerName)) {\r\n      banners.set(bannerName, []);\r\n    }\r\n\r\n    const departments = banners.get(bannerName);\r\n    departments.push(department);\r\n  });\r\n\r\n  return groupedData;\r\n}\r\n\r\nexport const handleDownloadClick = (\r\n  dashboardData: any[],\r\n  smicData: any,\r\n  appliedFilters: any,\r\n  handleDownloadExcel: (\r\n    dashboardData: any[],\r\n    smicData: any,\r\n    appliedFilters: any\r\n  ) => void\r\n) => {\r\n  if (!dashboardData || dashboardData.length === 0) {\r\n    alert('No dashboard data to export!');\r\n    return;\r\n  }\r\n  handleDownloadExcel(dashboardData,smicData, appliedFilters );\r\n};\r\n\r\nexport const getParentHeaderRow = (quarterDisplay: string) => [\r\n  quarterDisplay, // A (1)\r\n  'Line 1 (Sales to Public)', '', '', '', '', '', // B-G (6)\r\n  'Line 5: Book Gross Profit', '', '', '', '',    // H-L (5)\r\n  'Line 5: Markdown', '', '', '', '',             // M-Q (5)\r\n  'Line 5: Shrink %', '', '', '', '',             // R-V (5)\r\n  'Line 5 (Realized Gross Profit): Total', '', '', '', '', '', // W-AB (6)\r\n  'Line 6 (Supplies Packaging)', '', '',          // AC-AE (3)\r\n  'Line 7 (Retail Allowance)', '', '',            // AF-AH (3)\r\n  'Line 8 (Realized Gross Profit Before Other Revenue - Sales)', '', '', '', '', '' // AI-AN (6)\r\n];\r\n\r\nexport const COMMON_HEADERS = [\r\n  '', // Department/Period/Week \r\n  '$ Projection',\r\n  '$ Last Year',\r\n  '$Actual/Merch.Forecast',\r\n  'Keeper% (Includes ID)',\r\n  '$ vs LY',\r\n  '$ vs Projection', //book gross profit\r\n  '$Projection',\r\n  '% Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '%Actual/Merch.Forecast',\r\n  '% vs Projection',//markdown\r\n  '$Projection',\r\n  '% Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '%Actual/Merch.Forecast',\r\n  '% vs Projection',//shrink\r\n  '$Projection',\r\n  '% Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '%Actual/Merch.Forecast',\r\n  '% vs Projection',//RGP\r\n  '$Projection',\r\n  '% Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '%Actual/Merch.Forecast',\r\n  '$ vs Projection',\r\n  '% vs Projection',//line6\r\n  '$ Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '$ vs Projection',//line7\r\n  '$ Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '$ vs Projection',//line8\r\n  '$ Projection',\r\n  '% Projection',\r\n  '$Actual/Merch.Forecast',\r\n  '%Actual/Merch.Forecast',\r\n  '$ vs Projection',\r\n  '% vs Projection',\r\n];\r\n\r\nexport const VS_PROJECTION_HEADERS = [\r\n  '$ vs LY',\r\n'$ vs Projection',\r\n  '% vs Projection',\r\n  '% vs Projection (Book Gross Profit)',\r\n  '% vs Projection (Markdown)',\r\n  '% vs Projection (Shrink)',\r\n  '$ vs Projection (RGP)',\r\n  '% vs Projection (RGP)',\r\n  '$ vs Projection (Supplies Packaging)',\r\n  '$ vs Projection (Retail Allowance)',\r\n  '$ vs Projection (Sales)',\r\n  '% vs Projection (Sales)'\r\n];\r\nexport const VS_PROJECTION_DOLLAR_HEADERS = [\r\n  '$ vs LY',\r\n  '$ vs Projection', \r\n  '$ vs Projection (RGP)',\r\n  '$ vs Projection (Supplies Packaging)',\r\n  '$ vs Projection (Retail Allowance)',\r\n  '$ vs Projection (Sales)'\r\n];\r\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4DAA4D;AAE7F,SAASC,sBAAsBA,CAACC,GAAQ,EAAEC,cAAkC,EAAE;EAC5E,MAAMC,GAAG,GAAGC,MAAM,CAACH,GAAG,CAAC;EACvB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKI,SAAS,IAAIJ,GAAG,KAAK,EAAE,IAAIK,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;EAC5E,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,KAAKI,IAAI,CAACC,GAAG,CAACL,GAAG,CAAC,CAACM,cAAc,CAAC,OAAO,EAAE;MAAEC,qBAAqB,EAAE;IAAE,CAAC,CAAC,GAAG;EACpF;EACA,OAAOR,cAAc,CAACD,GAAG,CAAC;AAC5B;AACA,SAASU,sBAAsBA,CAACV,GAAQ,EAAEC,cAAkC,EAAE;EAC5E,MAAMC,GAAG,GAAGC,MAAM,CAACH,GAAG,CAAC;EACvB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKI,SAAS,IAAIJ,GAAG,KAAK,EAAE,IAAIK,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;EAC5E,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX,OAAO,KAAKI,IAAI,CAACC,GAAG,CAACL,GAAG,CAAC,CAACM,cAAc,CAAC,OAAO,EAAE;MAAEC,qBAAqB,EAAE;IAAE,CAAC,CAAC,GAAG;EACpF;EACA,OAAOR,cAAc,CAACD,GAAG,CAAC;AAC5B;AACA,SAASW,iBAAiBA,CAACX,GAAQ,EAAEY,SAA6B,EAAE;EAClE,MAAMV,GAAG,GAAGC,MAAM,CAACH,GAAG,CAAC;EACvB,IAAIE,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI;EAC1B,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKI,SAAS,IAAIJ,GAAG,KAAK,EAAE,IAAIK,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;EAC5E,OAAOU,SAAS,CAACZ,GAAG,CAAC;AACvB;AAEA,OAAO,SAASa,MAAMA,CACpBC,OAAY,EACZC,IAAS,EACTd,cAAkC,EAClCe,IAAI,GAAG,EAAE,EACTC,MAAM,GAAG,EAAE,EACXC,IAAI,GAAG,EAAE,EACT;EAAA,IAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;EACA,OAAAC,MAAA,CAAAC,MAAA,KACK3C,OAAO;IACV,cAAc,EAAEH,iBAAiB,CAACI,IAAI,CAAC2C,eAAe,EAAEzD,cAAc,CAAC;IACvE,aAAa,EAAEU,iBAAiB,CAACI,IAAI,CAAC4C,QAAQ,EAAE1D,cAAc,CAAC;IAC/D,wBAAwB,EAAEU,iBAAiB,CAACI,IAAI,CAAC6C,gBAAgB,EAAE3D,cAAc,CAAC;IAClF,uBAAuB,EAAEH,gBAAgB,CAACiB,IAAI,CAAC8C,YAAY,CAAC;IAC5D,SAAS,EAAE5D,cAAc,EAAAkB,UAAA,GAACJ,IAAI,CAAC+C,IAAI,qBAAT3C,UAAA,CAAW4C,KAAK,CAAC;IAC3C,iBAAiB,EAAE9D,cAAc,EAAAmB,kBAAA,GAACL,IAAI,CAACiD,YAAY,qBAAjB5C,kBAAA,CAAmB2C,KAAK,CAAC;IAC3D,oBAAoB,EAAEpD,iBAAiB,EAAAU,qBAAA,GAACN,IAAI,CAACkD,eAAe,qBAApB5C,qBAAA,CAAsB6C,eAAe,EAACjE,cAAc,CAAC;IAC7F,oBAAoB,EAAEU,iBAAiB,EAAAW,sBAAA,GAACP,IAAI,CAACkD,eAAe,qBAApB3C,sBAAA,CAAsB6C,aAAa,EAACrE,gBAAgB,CAAC;IAC7F,8BAA8B,EAAEa,iBAAiB,EAAAY,sBAAA,GAACR,IAAI,CAACkD,eAAe,qBAApB1C,sBAAA,CAAsBqC,gBAAgB,EAAC3D,cAAc,CAAC;IACxG,wBAAwB,EAAEH,gBAAgB,EAAA0B,sBAAA,GAACT,IAAI,CAACkD,eAAe,qBAApBzC,sBAAA,CAAsB4C,uBAAuB,CAAC;IACzF,iBAAiB,EAAEtE,gBAAgB,EAAA2B,sBAAA,GAACV,IAAI,CAACkD,eAAe,qBAApBxC,sBAAA,CAAsBuC,YAAY,CAAC;IACvE,yBAAyB,EAAErD,iBAAiB,CAACL,IAAI,CAACC,GAAG,EAAAmB,qBAAA,IAAAC,cAAA,GAACZ,IAAI,CAACsD,QAAQ,qBAAb1C,cAAA,CAAeuC,eAAe,YAAAxC,qBAAA,GAAI,CAAC,CAAC,EAACzB,cAAc,CAAC;IAC1G,yBAAyB,EAAEU,iBAAiB,EAAAiB,eAAA,GAACb,IAAI,CAACsD,QAAQ,qBAAbzC,eAAA,CAAeuC,aAAa,EAACrE,gBAAgB,CAAC;IAC3F,mCAAmC,EAAEa,iBAAiB,EAAAkB,eAAA,GAACd,IAAI,CAACsD,QAAQ,qBAAbxC,eAAA,CAAe+B,gBAAgB,EAACU,CAAC,IAAE5D,sBAAsB,CAAC4D,CAAC,EAAErE,cAAc,CAAC,CAAC;IACpI,mCAAmC,EAAEH,gBAAgB,EAAAgC,eAAA,GAACf,IAAI,CAACsD,QAAQ,qBAAbvC,eAAA,CAAesC,uBAAuB,CAAC;IAC7F,6BAA6B,EAACtE,gBAAgB,EAAAiC,eAAA,GAAChB,IAAI,CAACsD,QAAQ,qBAAbtC,eAAA,CAAeiC,YAAY,CAAC;IAC3E,uBAAuB,EAAErD,iBAAiB,CAACL,IAAI,CAACC,GAAG,EAAAyB,qBAAA,IAAAC,YAAA,GAAClB,IAAI,CAACwD,MAAM,qBAAXtC,YAAA,CAAaiC,eAAe,YAAAlC,qBAAA,GAAI,CAAC,CAAC,EAAC/B,cAAc,CAAC;IACtG,uBAAuB,EAAEU,iBAAiB,EAAAuB,aAAA,GAACnB,IAAI,CAACwD,MAAM,qBAAXrC,aAAA,CAAaiC,aAAa,EAACrE,gBAAgB,CAAC;IACvF,iCAAiC,EAAEa,iBAAiB,EAAAwB,aAAA,GAACpB,IAAI,CAACwD,MAAM,qBAAXpC,aAAA,CAAayB,gBAAgB,EAACU,CAAC,IAAE5D,sBAAsB,CAAC4D,CAAC,EAAErE,cAAc,CAAC,CAAC;IAChI,iCAAiC,EAAEH,gBAAgB,EAAAsC,aAAA,GAACrB,IAAI,CAACwD,MAAM,qBAAXnC,aAAA,CAAagC,uBAAuB,CAAC;IACzF,wBAAwB,EAAEtE,gBAAgB,EAAAuC,aAAA,GAACtB,IAAI,CAACwD,MAAM,qBAAXlC,aAAA,CAAa2B,YAAY,CAAC;IACrE,oBAAoB,EAAErD,iBAAiB,EAAA2B,UAAA,GAACvB,IAAI,CAACyD,KAAK,qBAAVlC,UAAA,CAAY4B,eAAe,EAACjE,cAAc,CAAC;IACnF,oBAAoB,EAAEU,iBAAiB,EAAA4B,WAAA,GAACxB,IAAI,CAACyD,KAAK,qBAAVjC,WAAA,CAAY4B,aAAa,EAACrE,gBAAgB,CAAC;IACnF,8BAA8B,EAAEa,iBAAiB,EAAA6B,WAAA,GAACzB,IAAI,CAACyD,KAAK,qBAAVhC,WAAA,CAAYoB,gBAAgB,EAAE3D,cAAc,CAAC;IAC/F,8BAA8B,EAAEH,gBAAgB,EAAA2C,WAAA,GAAC1B,IAAI,CAACyD,KAAK,qBAAV/B,WAAA,CAAY2B,uBAAuB,CAAC;IACrF,uBAAuB,EAAEnE,cAAc,EAAAyC,WAAA,GAAC3B,IAAI,CAACyD,KAAK,qBAAV9B,WAAA,CAAYsB,YAAY,CAAC;IACjE,uBAAuB,EAAElE,gBAAgB,EAAA6C,WAAA,GAAC5B,IAAI,CAACyD,KAAK,qBAAV7B,WAAA,CAAY8B,mBAAmB,CAAC;IAC1E,mCAAmC,EAAE9D,iBAAiB,EAAAiC,WAAA,GAAC7B,IAAI,CAAC2D,KAAK,qBAAV9B,WAAA,CAAY+B,UAAU,EAAEL,CAAC,IAAIvE,sBAAsB,CAACuE,CAAC,EAAErE,cAAc,CAAC,CAAC;IAC9H,6CAA6C,EAAEU,iBAAiB,EAAAkC,WAAA,GAAC9B,IAAI,CAAC2D,KAAK,qBAAV7B,WAAA,CAAYe,gBAAgB,EAAEU,CAAC,IAAIvE,sBAAsB,CAACuE,CAAC,EAAErE,cAAc,CAAC,CAAC;IAC9I,sCAAsC,EAAEA,cAAc,EAAA6C,WAAA,GAAC/B,IAAI,CAAC2D,KAAK,qBAAV5B,WAAA,CAAYkB,YAAY,CAAC;IAChF,iCAAiC,EAAErD,iBAAiB,EAAAoC,YAAA,GAAChC,IAAI,CAAC6D,KAAK,qBAAV7B,YAAA,CAAY4B,UAAU,EAAC1E,cAAc,CAAC;IAC3F,2CAA2C,EAAEU,iBAAiB,EAAAqC,YAAA,GAACjC,IAAI,CAAC6D,KAAK,qBAAV5B,YAAA,CAAYY,gBAAgB,EAAC3D,cAAc,CAAC;IAC3G,oCAAoC,EAAEA,cAAc,EAAAgD,YAAA,GAAClC,IAAI,CAAC6D,KAAK,qBAAV3B,YAAA,CAAYe,YAAY,CAAC;IAC9E,qBAAqB,EAAErD,iBAAiB,EAAAuC,YAAA,GAACnC,IAAI,CAAC8D,KAAK,qBAAV3B,YAAA,CAAYgB,eAAe,EAACjE,cAAc,CAAC;IACpF,qBAAqB,EAAEU,iBAAiB,EAAAwC,YAAA,GAACpC,IAAI,CAAC8D,KAAK,qBAAV1B,YAAA,CAAYgB,aAAa,EAACrE,gBAAgB,CAAC;IACpF,gCAAgC,EAAEa,iBAAiB,EAAAyC,YAAA,GAACrC,IAAI,CAAC8D,KAAK,qBAAVzB,YAAA,CAAYQ,gBAAgB,EAAC3D,cAAc,CAAC;IAChG,gCAAgC,EAAEH,gBAAgB,EAAAuD,YAAA,GAACtC,IAAI,CAAC8D,KAAK,qBAAVxB,YAAA,CAAYe,uBAAuB,CAAC;IACvF,yBAAyB,EAAEnE,cAAc,EAAAqD,YAAA,GAACvC,IAAI,CAAC8D,KAAK,qBAAVvB,YAAA,CAAYU,YAAY,CAAC;IACnE,yBAAyB,EAAElE,gBAAgB,EAAAyD,YAAA,GAACxC,IAAI,CAAC8D,KAAK,qBAAVtB,YAAA,CAAYkB,mBAAmB;EAAC;AAEhF;AAEA,OAAO,SAASK,iBAAiBA,CAAC/D,IAAW,EAAE;EAC7C,MAAMgE,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAE7BjE,IAAI,CAACkE,OAAO,CAACC,UAAU,IAAI;IACzB;IACA,MAAMC,YAAY,GAAGD,UAAU,CAACC,YAAY,IAAI,kBAAkB;IAClE,MAAMC,UAAU,GAAGF,UAAU,CAACE,UAAU,IAAI,gBAAgB;IAE5D,IAAI,CAACL,WAAW,CAACM,GAAG,CAACF,YAAY,CAAC,EAAE;MAClCJ,WAAW,CAACO,GAAG,CAACH,YAAY,EAAE,IAAIH,GAAG,CAAC,CAAC,CAAC;IAC1C;IAEA,MAAMO,OAAO,GAAGR,WAAW,CAACS,GAAG,CAACL,YAAY,CAAC;IAC7C,IAAI,CAACI,OAAO,CAACF,GAAG,CAACD,UAAU,CAAC,EAAE;MAC5BG,OAAO,CAACD,GAAG,CAACF,UAAU,EAAE,EAAE,CAAC;IAC7B;IAEA,MAAMK,WAAW,GAAGF,OAAO,CAACC,GAAG,CAACJ,UAAU,CAAC;IAC3CK,WAAW,CAACC,IAAI,CAACR,UAAU,CAAC;EAC9B,CAAC,CAAC;EAEF,OAAOH,WAAW;AACpB;AAEA,OAAO,MAAMY,mBAAmB,GAAGA,CACjCC,aAAoB,EACpBC,QAAa,EACbC,cAAmB,EACnBC,mBAIS,KACN;EACH,IAAI,CAACH,aAAa,IAAIA,aAAa,CAACI,MAAM,KAAK,CAAC,EAAE;IAChDC,KAAK,CAAC,8BAA8B,CAAC;IACrC;EACF;EACAF,mBAAmB,CAACH,aAAa,EAACC,QAAQ,EAAEC,cAAe,CAAC;AAC9D,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAIC,cAAsB,IAAK,CAC5DA,cAAc;AAAE;AAChB,0BAA0B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAE;AAChD,2BAA2B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAK;AAChD,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAc;AAChD,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAc;AAChD,uCAAuC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAE;AAC7D,6BAA6B,EAAE,EAAE,EAAE,EAAE;AAAW;AAChD,2BAA2B,EAAE,EAAE,EAAE,EAAE;AAAa;AAChD,6DAA6D,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAAA,CACnF;AAED,OAAO,MAAMC,cAAc,GAAG,CAC5B,EAAE;AAAE;AACJ,cAAc,EACd,aAAa,EACb,wBAAwB,EACxB,uBAAuB,EACvB,SAAS,EACT,iBAAiB;AAAE;AACnB,aAAa,EACb,cAAc,EACd,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB;AAAC;AAClB,aAAa,EACb,cAAc,EACd,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB;AAAC;AAClB,aAAa,EACb,cAAc,EACd,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB;AAAC;AAClB,aAAa,EACb,cAAc,EACd,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB,EACjB,iBAAiB;AAAC;AAClB,cAAc,EACd,wBAAwB,EACxB,iBAAiB;AAAC;AAClB,cAAc,EACd,wBAAwB,EACxB,iBAAiB;AAAC;AAClB,cAAc,EACd,cAAc,EACd,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB,EACjB,iBAAiB,CAClB;AAED,OAAO,MAAMC,qBAAqB,GAAG,CACnC,SAAS,EACX,iBAAiB,EACf,iBAAiB,EACjB,qCAAqC,EACrC,4BAA4B,EAC5B,0BAA0B,EAC1B,uBAAuB,EACvB,uBAAuB,EACvB,sCAAsC,EACtC,oCAAoC,EACpC,yBAAyB,EACzB,yBAAyB,CAC1B;AACD,OAAO,MAAMC,4BAA4B,GAAG,CAC1C,SAAS,EACT,iBAAiB,EACjB,uBAAuB,EACvB,sCAAsC,EACtC,oCAAoC,EACpC,yBAAyB,CAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}