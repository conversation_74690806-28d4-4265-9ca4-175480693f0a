import '@testing-library/jest-dom/extend-expect';
import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import ClearSelection from './clearSelection';

describe('ClearSelection', () => {
  it('renders nothing when show is false', () => {
    const { container } = render(<ClearSelection show={false} onClick={jest.fn()} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders button when show is true', () => {
    const { getByTestId } = render(<ClearSelection show={true} onClick={jest.fn()} />);
    expect(getByTestId('clear-selection-link')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const onClick = jest.fn();
    const { getByTestId } = render(<ClearSelection show={true} onClick={onClick} />);
    fireEvent.click(getByTestId('clear-selection-link'));
    expect(onClick).toHaveBeenCalled();
  });
});
