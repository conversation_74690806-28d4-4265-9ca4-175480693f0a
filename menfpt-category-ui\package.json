{"name": "menfpt-category-ui", "version": "23.05.01", "license": "MIT", "scripts": {"start": "nx serve menfpt-category-ui", "startsrv:dev": "(cd src-msal-server  && node index.js)", "test": "npm run comp-test", "build": "nx build menfpt-category-ui", "serve": "nx serve-static menfpt-category-ui", "clean": "rm -rf dist", "build:comp": "nx build menfpt-category-ui --configuration production", "build:menfpt-category-ui-static-server": "nx build menfpt-category-ui-static-server --configuration production", "copyfiles": "copyfiles -u 1 dist/apps/menfpt-category-ui/* dist/apps/menfpt-category-ui-static-server", "build:app": "npm run build:comp && npm run build:menfpt-category-ui-static-server && npm run copyfiles", "coverage": "nx test --codeCoverage", "sonar": "node sonar-project.js", "comp-test": "nx test menfpt-category-ui --silent --codeCoverage", "update-and-start": "node scripts/update-and-start.js", "update-repos": "node scripts/update-repos.js", "start-both": "node scripts/start-both.js"}, "private": true, "jestSonar": {"reportPath": "coverage/"}, "dependencies": {"@albertsons/uds": "^1.8.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@material-tailwind/react": "^1.2.3", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@reduxjs/toolkit": "^1.9.1", "ajv": "^8.17.1", "chart.js": "^4.2.1", "core-js": "^3.6.5", "cors": "^2.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "express": "^4.18.1", "file-saver": "^2.0.5", "formik": "^2.4.6", "jest-sonar-reporter": "^2.0.0", "lucide-react": "^0.401.0", "mui-chips-input": "^2.1.4", "powerbi-client-react": "^2.0.0", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "18.2.0", "react-grid-heatmap": "^1.3.0", "react-heatmap-grid": "^0.9.1", "react-hook-form": "^7.41.0", "react-redux": "^8.0.5", "react-router-dom": "^6.5.0", "react-toastify": "^9.1.3", "react-tooltip": "^5.28.0", "react-viewport-list": "^7.1.1", "redux-logger": "^3.0.6", "redux-mock-store": "^1.5.4", "regenerator-runtime": "0.13.7", "tslib": "^2.3.0", "uuid": "^9.0.1", "yup": "^1.7.0"}, "devDependencies": {"@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.18.6", "@nrwl/cli": "15.2.1", "@nrwl/cypress": "15.3.3", "@nrwl/eslint-plugin-nx": "15.3.3", "@nrwl/express": "15.4.0", "@nrwl/jest": "^15.3.3", "@nrwl/linter": "15.3.3", "@nrwl/node": "15.4.0", "@nrwl/nx-cloud": "latest", "@nrwl/react": "15.3.3", "@nrwl/web": "15.3.3", "@nrwl/workspace": "15.3.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@stagewise/toolbar-react": "^0.1.2", "@svgr/webpack": "^6.1.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "13.4.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/cypress": "^1.1.3", "@types/express": "4.17.13", "@types/file-saver": "^2.0.7", "@types/jest": "28.1.1", "@types/lodash": "^4.14.195", "@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@types/react-router-dom": "5.3.3", "@types/redux-logger": "^3.0.9", "@types/redux-mock-store": "^1.0.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "autoprefixer": "^10.4.13", "babel-jest": "28.1.1", "check-code-coverage": "^1.10.4", "copyfiles": "^2.4.1", "cypress": "^11.2.0", "eslint": "~8.15.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-react": "7.31.11", "eslint-plugin-react-hooks": "4.6.0", "jest": "28.1.1", "jest-environment-jsdom": "28.1.1", "jest-sonar-reporter": "^2.0.0", "json-server": "^0.17.1", "nx": "15.3.3", "postcss": "^8.4.19", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "react-test-renderer": "18.2.0", "rimraf": "^3.0.2", "sonarqube-scanner": "^2.9.1", "tailwindcss": "^3.2.4", "ts-jest": "28.0.5", "ts-node": "10.9.1", "typescript": "~4.8.2", "url-loader": "^4.1.1", "web-vitals": "^3.1.0"}}