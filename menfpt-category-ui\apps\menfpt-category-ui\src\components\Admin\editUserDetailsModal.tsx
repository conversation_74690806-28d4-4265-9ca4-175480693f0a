import React, { useState } from 'react';
import Modal from '@albertsons/uds/molecule/Modal';
import Button from '@albertsons/uds/molecule/Button';
import { AddUserForm } from './addUserForm';

interface EditUserDetailsModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onBack?: () => void;
  showTriggerButton?: boolean;
}

const EditUserDetailsModal: React.FC<EditUserDetailsModalProps> = ({ 
  isOpen: externalIsOpen, 
  onClose: externalOnClose,
  onBack,
  showTriggerButton = true 
}) => {
  const [internalIsOpen, setInternalOpen] = useState<boolean>(false);
  
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const handleClose = () => {
    if (externalOnClose) {
      externalOnClose();
    } else {
      setInternalOpen(false);
    }
  };

  return (
    <>

      <Modal isOpen={isOpen} onClose={handleClose} width={800}>
        <div className='select-none font-bold text-[28px] mt-4 ml-6'>
         Edit User Details
        </div>
        <div className="w-[800px] h-px bg-[#c8daeb]" />
        <div className='p-4'>
          <AddUserForm 
            onBack={onBack}
            onCancel={handleClose}
          />
          {showTriggerButton && (
        <div className='h-10'>
          <Button fixed onClick={() => setInternalOpen(true)}>
           Edit User Details
          </Button>
        </div>
      )}
        </div>
      </Modal>
    </>
  );
};
export default EditUserDetailsModal;