import { subtractForecastVarianceData } from './Forecastvariance';

describe('subtractForecastVarianceData', () => {
  const lastFridayArray = [
    {
      id: '3270000',
      name: '',
      quarter: {
        line1Projection: 100,
        lastYear: 200,
        actualOrForecast: 300,
        idPercentage: 0.1,
        vsLY: { value: 10, percentage: 0.1 },
        vsProjection: { value: 20, percentage: 0.2 },
        bookGrossProfit: { projection: 0.3, actualOrForecast: 0.4, vsProjection: 0.1 },
        markdown: { projection: -0.1, actualOrForecast: -0.2, vsProjection: -0.1 },
        shrink: { projection: -0.01, actualOrForecast: -0.02, vsProjection: -0.01 },
        line5: { actualOrForecast: 50, percentActualOrForecast: 0.5, vsProjection: 5, percentVsProjection: 0.05 },
        line6: { projection: 0, actualOrForecast: 10, vsProjection: 10 },
        line7: { projection: 20, actualOrForecast: 30, vsProjection: 10 },
        line8: { actualOrForecast: 80, percentActualOrForecast: 0.8, vsProjection: 8, percentVsProjection: 0.08 }
      },
      periods: [
        {
          id: 'Period-1',
          periodNumber: 1,
          line1Projection: 10,
          lastYear: 20,
          actualOrForecast: 30,
          idPercentage: 0.01,
          vsLY: { value: 1, percentage: 0.01 },
          vsProjection: { value: 2, percentage: 0.02 },
          bookGrossProfit: { projection: 0.03, actualOrForecast: 0.04, vsProjection: 0.01 },
          markdown: { projection: -0.01, actualOrForecast: -0.02, vsProjection: -0.01 },
          shrink: { projection: -0.001, actualOrForecast: -0.002, vsProjection: -0.001 },
          line5: { actualOrForecast: 5, percentActualOrForecast: 0.05, vsProjection: 0.5, percentVsProjection: 0.005 },
          line6: { projection: 0, actualOrForecast: 1, vsProjection: 1 },
          line7: { projection: 2, actualOrForecast: 3, vsProjection: 1 },
          line8: { actualOrForecast: 8, percentActualOrForecast: 0.08, vsProjection: 0.8, percentVsProjection: 0.008 }
        }
      ],
      weeks: [
        {
          id: 'Week-1',
          weekNumber: 1,
          periodNumber: 1,
          line1Projection: 1,
          lastYear: 2,
          actualOrForecast: 3,
          idPercentage: 0.001,
          vsLY: { value: 0.1, percentage: 0.001 },
          vsProjection: { value: 0.2, percentage: 0.002 },
          bookGrossProfit: { projection: 0.003, actualOrForecast: 0.004, vsProjection: 0.001 },
          markdown: { projection: -0.001, actualOrForecast: -0.002, vsProjection: -0.001 },
          shrink: { projection: -0.0001, actualOrForecast: -0.0002, vsProjection: -0.0001 },
          line5: { actualOrForecast: 0.5, percentActualOrForecast: 0.005, vsProjection: 0.05, percentVsProjection: 0.0005 },
          line6: { projection: 0, actualOrForecast: 0.1, vsProjection: 0.1 },
          line7: { projection: 0.2, actualOrForecast: 0.3, vsProjection: 0.1 },
          line8: { actualOrForecast: 0.8, percentActualOrForecast: 0.008, vsProjection: 0.08, percentVsProjection: 0.0008 }
        }
      ]
    }
  ];

  const currentArray = [
    {
      id: '3270000',
      name: '',
      quarter: {
        line1Projection: 110,
        lastYear: 210,
        actualOrForecast: 310,
        idPercentage: 0.11,
        vsLY: { value: 11, percentage: 0.11 },
        vsProjection: { value: 21, percentage: 0.21 },
        bookGrossProfit: { projection: 0.31, actualOrForecast: 0.41, vsProjection: 0.11 },
        markdown: { projection: -0.11, actualOrForecast: -0.21, vsProjection: -0.11 },
        shrink: { projection: -0.011, actualOrForecast: -0.021, vsProjection: -0.011 },
        line5: { actualOrForecast: 51, percentActualOrForecast: 0.51, vsProjection: 5.1, percentVsProjection: 0.051 },
        line6: { projection: 0, actualOrForecast: 11, vsProjection: 11 },
        line7: { projection: 21, actualOrForecast: 31, vsProjection: 11 },
        line8: { actualOrForecast: 81, percentActualOrForecast: 0.81, vsProjection: 8.1, percentVsProjection: 0.081 }
      },
      periods: [
        {
          id: 'Period-1',
          periodNumber: 1,
          line1Projection: 11,
          lastYear: 21,
          actualOrForecast: 31,
          idPercentage: 0.011,
          vsLY: { value: 1.1, percentage: 0.011 },
          vsProjection: { value: 2.1, percentage: 0.021 },
          bookGrossProfit: { projection: 0.031, actualOrForecast: 0.041, vsProjection: 0.011 },
          markdown: { projection: -0.011, actualOrForecast: -0.021, vsProjection: -0.011 },
          shrink: { projection: -0.0011, actualOrForecast: -0.0021, vsProjection: -0.0011 },
          line5: { actualOrForecast: 5.1, percentActualOrForecast: 0.051, vsProjection: 0.51, percentVsProjection: 0.0051 },
          line6: { projection: 0, actualOrForecast: 1.1, vsProjection: 1.1 },
          line7: { projection: 2.1, actualOrForecast: 3.1, vsProjection: 1.1 },
          line8: { actualOrForecast: 8.1, percentActualOrForecast: 0.081, vsProjection: 0.81, percentVsProjection: 0.0081 }
        }
      ],
      weeks: [
        {
          id: 'Week-1',
          weekNumber: 1,
          periodNumber: 1,
          line1Projection: 1.1,
          lastYear: 2.1,
          actualOrForecast: 3.1,
          idPercentage: 0.0011,
          vsLY: { value: 0.11, percentage: 0.0011 },
          vsProjection: { value: 0.21, percentage: 0.0021 },
          bookGrossProfit: { projection: 0.0031, actualOrForecast: 0.0041, vsProjection: 0.0011 },
          markdown: { projection: -0.0011, actualOrForecast: -0.0021, vsProjection: -0.0011 },
          shrink: { projection: -0.00011, actualOrForecast: -0.00021, vsProjection: -0.00011 },
          line5: { actualOrForecast: 0.51, percentActualOrForecast: 0.0051, vsProjection: 0.051, percentVsProjection: 0.00051 },
          line6: { projection: 0, actualOrForecast: 0.11, vsProjection: 0.11 },
          line7: { projection: 0.21, actualOrForecast: 0.31, vsProjection: 0.11 },
          line8: { actualOrForecast: 0.81, percentActualOrForecast: 0.0081, vsProjection: 0.081, percentVsProjection: 0.00081 }
        }
      ]
    }
  ];

  it('should subtract all numeric fields at department, period, and week level', () => {
    const result = subtractForecastVarianceData(currentArray, lastFridayArray,true);

    // Quarter level
    expect(result[0].quarter.line1Projection).toBeCloseTo(10);
    expect(result[0].quarter.lastYear).toBeCloseTo(10);
    expect(result[0].quarter.actualOrForecast).toBeCloseTo(10);
    expect(result[0].quarter.idPercentage).toBeCloseTo(0.01);
    expect(result[0].quarter.vsLY.value).toBeCloseTo(1);
    expect(result[0].quarter.vsLY.percentage).toBeCloseTo(0.01);
    expect(result[0].quarter.vsProjection.value).toBeCloseTo(1);
    expect(result[0].quarter.vsProjection.percentage).toBeCloseTo(0.01);
    expect(result[0].quarter.bookGrossProfit.projection).toBeCloseTo(0.01);
    expect(result[0].quarter.bookGrossProfit.actualOrForecast).toBeCloseTo(0.01);
    expect(result[0].quarter.bookGrossProfit.vsProjection).toBeCloseTo(0.01);
    expect(result[0].quarter.markdown.projection).toBeCloseTo(-0.01);
    expect(result[0].quarter.markdown.actualOrForecast).toBeCloseTo(-0.01);
    expect(result[0].quarter.markdown.vsProjection).toBeCloseTo(-0.01);
    expect(result[0].quarter.shrink.projection).toBeCloseTo(-0.001);
    expect(result[0].quarter.shrink.actualOrForecast).toBeCloseTo(-0.001);
    expect(result[0].quarter.shrink.vsProjection).toBeCloseTo(-0.001);
    expect(result[0].quarter.line5.actualOrForecast).toBeCloseTo(1);
    expect(result[0].quarter.line5.percentActualOrForecast).toBeCloseTo(0.01);
    expect(result[0].quarter.line5.vsProjection).toBeCloseTo(0.1);
    expect(result[0].quarter.line5.percentVsProjection).toBeCloseTo(0.001);
    expect(result[0].quarter.line6.projection).toBeCloseTo(0);
    expect(result[0].quarter.line6.actualOrForecast).toBeCloseTo(1);
    expect(result[0].quarter.line6.vsProjection).toBeCloseTo(1);
    expect(result[0].quarter.line7.projection).toBeCloseTo(1);
    expect(result[0].quarter.line7.actualOrForecast).toBeCloseTo(1);
    expect(result[0].quarter.line7.vsProjection).toBeCloseTo(1);
    expect(result[0].quarter.line8.actualOrForecast).toBeCloseTo(1);
    expect(result[0].quarter.line8.percentActualOrForecast).toBeCloseTo(0.01);
    expect(result[0].quarter.line8.vsProjection).toBeCloseTo(0.1);
    expect(result[0].quarter.line8.percentVsProjection).toBeCloseTo(0.001);

    // Period level
    expect(result[0].periods[0].line1Projection).toBeCloseTo(1);
    expect(result[0].periods[0].lastYear).toBeCloseTo(1);
    expect(result[0].periods[0].actualOrForecast).toBeCloseTo(1);
    expect(result[0].periods[0].idPercentage).toBeCloseTo(0.001);
    expect(result[0].periods[0].vsLY.value).toBeCloseTo(0.1);
    expect(result[0].periods[0].vsLY.percentage).toBeCloseTo(0.001);
    expect(result[0].periods[0].vsProjection.value).toBeCloseTo(0.1);
    expect(result[0].periods[0].vsProjection.percentage).toBeCloseTo(0.001);
    expect(result[0].periods[0].bookGrossProfit.projection).toBeCloseTo(0.001);
    expect(result[0].periods[0].bookGrossProfit.actualOrForecast).toBeCloseTo(0.001);
    expect(result[0].periods[0].bookGrossProfit.vsProjection).toBeCloseTo(0.001);
    expect(result[0].periods[0].markdown.projection).toBeCloseTo(-0.001);
    expect(result[0].periods[0].markdown.actualOrForecast).toBeCloseTo(-0.001);
    expect(result[0].periods[0].markdown.vsProjection).toBeCloseTo(-0.001);
    expect(result[0].periods[0].shrink.projection).toBeCloseTo(-0.0001);
    expect(result[0].periods[0].shrink.actualOrForecast).toBeCloseTo(-0.0001);
    expect(result[0].periods[0].shrink.vsProjection).toBeCloseTo(-0.0001);
    expect(result[0].periods[0].line5.actualOrForecast).toBeCloseTo(0.1);
    expect(result[0].periods[0].line5.percentActualOrForecast).toBeCloseTo(0.001);
    expect(result[0].periods[0].line5.vsProjection).toBeCloseTo(0.01);
    expect(result[0].periods[0].line5.percentVsProjection).toBeCloseTo(0.0001);
    expect(result[0].periods[0].line6.projection).toBeCloseTo(0);
    expect(result[0].periods[0].line6.actualOrForecast).toBeCloseTo(0.1);
    expect(result[0].periods[0].line6.vsProjection).toBeCloseTo(0.1);
    expect(result[0].periods[0].line7.projection).toBeCloseTo(0.1);
    expect(result[0].periods[0].line7.actualOrForecast).toBeCloseTo(0.1);
    expect(result[0].periods[0].line7.vsProjection).toBeCloseTo(0.1);
    expect(result[0].periods[0].line8.actualOrForecast).toBeCloseTo(0.1);
    expect(result[0].periods[0].line8.percentActualOrForecast).toBeCloseTo(0.001);
    expect(result[0].periods[0].line8.vsProjection).toBeCloseTo(0.01);
    expect(result[0].periods[0].line8.percentVsProjection).toBeCloseTo(0.0001);

    // Week level (first week is always 0 due to skip logic)
    expect(result[0].weeks[0].line1Projection).toBeCloseTo(0);
    expect(result[0].weeks[0].lastYear).toBeCloseTo(0);
    expect(result[0].weeks[0].actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].idPercentage).toBeCloseTo(0);
    expect(result[0].weeks[0].vsLY.value).toBeCloseTo(0);
    expect(result[0].weeks[0].vsLY.percentage).toBeCloseTo(0);
    expect(result[0].weeks[0].vsProjection.value).toBeCloseTo(0);
    expect(result[0].weeks[0].vsProjection.percentage).toBeCloseTo(0);
    expect(result[0].weeks[0].bookGrossProfit.projection).toBeCloseTo(0);
    expect(result[0].weeks[0].bookGrossProfit.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].bookGrossProfit.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].markdown.projection).toBeCloseTo(0);
    expect(result[0].weeks[0].markdown.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].markdown.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].shrink.projection).toBeCloseTo(0);
    expect(result[0].weeks[0].shrink.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].shrink.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line5.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line5.percentActualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line5.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line5.percentVsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line6.projection).toBeCloseTo(0);
    expect(result[0].weeks[0].line6.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line6.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line7.projection).toBeCloseTo(0);
    expect(result[0].weeks[0].line7.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line7.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line8.actualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line8.percentActualOrForecast).toBeCloseTo(0);
    expect(result[0].weeks[0].line8.vsProjection).toBeCloseTo(0);
    expect(result[0].weeks[0].line8.percentVsProjection).toBeCloseTo(0);
  });
});
