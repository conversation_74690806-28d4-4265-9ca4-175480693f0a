import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import RxForecastAlerts from './rxforecastAlerts';

// Mock Alert component
jest.mock('@albertsons/uds/molecule/Alert', () => ({ 
  children, 
  isOpen, 
  variant, 
  timeout, 
  sticky, 
  autoClose, 
  dismissible, 
  onClose 
}: any) => 
  isOpen ? (
    <div 
      data-testid="uds-alert" 
      data-variant={variant}
      data-timeout={timeout}
      data-sticky={sticky}
      data-auto-close={autoClose}
      data-dismissible={dismissible}
    >
      {children}
      {dismissible && onClose && (
        <button data-testid="alert-close-button" onClick={onClose}>
          Close
        </button>
      )}
    </div>
  ) : null
);

describe('RxForecastAlerts', () => {
  const defaultProps = {
    message: 'Test message',
    type: 'success' as const,
    open: true,
  };

  it('renders alert when open is true', () => {
    render(<RxForecastAlerts {...defaultProps} />);

    expect(screen.getByTestId('uds-alert')).toBeInTheDocument();
    expect(screen.getByText('Test message')).toBeInTheDocument();
  });

  it('does not render alert when open is false', () => {
    render(<RxForecastAlerts {...defaultProps} open={false} />);

    expect(screen.queryByTestId('uds-alert')).not.toBeInTheDocument();
  });

  it('displays the provided message', () => {
    const message = 'Custom alert message';
    render(<RxForecastAlerts {...defaultProps} message={message} />);

    expect(screen.getByText(message)).toBeInTheDocument();
  });

  it('uses default title when none provided', () => {
    render(<RxForecastAlerts {...defaultProps} />);

    expect(screen.getByText('Document upload fail!')).toBeInTheDocument();
  });

  it('displays custom title when provided', () => {
    const customTitle = 'Custom Title';
    render(<RxForecastAlerts {...defaultProps} title={customTitle} />);

    expect(screen.getByText(customTitle)).toBeInTheDocument();
    expect(screen.queryByText('Document upload fail!')).not.toBeInTheDocument();
  });

  it('applies correct variant to Alert component', () => {
    render(<RxForecastAlerts {...defaultProps} type="error" />);

    expect(screen.getByTestId('uds-alert')).toHaveAttribute('data-variant', 'error');
  });

  it('passes correct props to Alert component', () => {
    render(<RxForecastAlerts {...defaultProps} />);

    const alert = screen.getByTestId('uds-alert');
    expect(alert).toHaveAttribute('data-timeout', '4');
    expect(alert).toHaveAttribute('data-sticky', 'true');
    expect(alert).toHaveAttribute('data-auto-close', 'true');
    expect(alert).toHaveAttribute('data-dismissible', 'true');
    expect(alert).toHaveAttribute('data-variant', 'success');
  });

  it('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    render(<RxForecastAlerts {...defaultProps} onClose={mockOnClose} />);

    const closeButton = screen.getByTestId('alert-close-button');
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('does not render close button when onClose is not provided', () => {
    render(<RxForecastAlerts {...defaultProps} />);

    expect(screen.queryByTestId('alert-close-button')).not.toBeInTheDocument();
  });

  

  it('applies correct styling to title and message', () => {
    render(<RxForecastAlerts {...defaultProps} title="Test Title" />);

    const title = screen.getByText('Test Title');
    const message = screen.getByText('Test message');

    expect(title).toHaveClass('font-semibold', 'text-base');
    expect(message).toHaveClass('text-sm');
  });

  it('renders with success type', () => {
    render(<RxForecastAlerts {...defaultProps} type="success" />);

    expect(screen.getByTestId('uds-alert')).toHaveAttribute('data-variant', 'success');
  });

  it('renders with error type', () => {
    render(<RxForecastAlerts {...defaultProps} type="error" />);

    expect(screen.getByTestId('uds-alert')).toHaveAttribute('data-variant', 'error');
  });

  it('has proper content structure', () => {
    render(<RxForecastAlerts {...defaultProps} title="Test Title" />);

    const title = screen.getByText('Test Title');
    const message = screen.getByText('Test message');

    // Both should be in a flex flex-col container
    expect(title.parentElement).toBe(message.parentElement);
    expect(title.parentElement).toHaveClass('flex', 'flex-col');
  });

  it('handles long messages properly', () => {
    const longMessage = 'This is a very long message that should be displayed properly in the alert component without breaking the layout or causing any issues';
    render(<RxForecastAlerts {...defaultProps} message={longMessage} />);

    expect(screen.getByText(longMessage)).toBeInTheDocument();
  });
});
