import React, { forwardRef } from 'react';
import { QuarterData } from '../../interfaces/allocatr-insights';
import { PlusSquare, MinusSquare, ChevronUp, ChevronDown } from 'lucide-react';
import { renderAllRows } from './utils/tableCell';

interface AllocatrTotalRowProps {
  quarter: QuarterData;
  expanded: boolean;
  onToggle: () => void;
  onToggleTotalDivisions:()=>void;
  isQuarterActualUsed?: boolean;
  divisionCount: number;
}

const AllocatrTotalRow = forwardRef<HTMLTableRowElement, AllocatrTotalRowProps>(({ quarter, expanded, onToggle,onToggleTotalDivisions, isQuarterActualUsed, divisionCount }, ref) => (

  <tr ref={ref} key={`total-row`} className="total-row division-row bg-[#E7F5FE]">
    <td>
      <button className="cursor-pointer" onClick={onToggleTotalDivisions}>
        {expanded ? <ChevronUp size={14}/> : <ChevronDown size={14} />}
      </button>
    </td>
    <td className="division-cell">
      <div className="flex items-center justify-start h-full">
        <button className="cursor-pointer mr-1" onClick={onToggle}>
          {expanded ? <MinusSquare size={14}/> : <PlusSquare size={14} />}
        </button>
        <div className="max-w-[180px] min-w-0 flex items-center">
          <span className="division-name-cell font-bold block w-full min-w-0 break-words" title={`Total ${divisionCount} Divisions`}>
            Total of {divisionCount} Divisions
          </span>
        </div>
      </div>
    </td>
    {renderAllRows(quarter, isQuarterActualUsed)}
  </tr>
));

export default AllocatrTotalRow;
