import React, { useEffect, useState, useRef, useCallback } from 'react';
import Card from '@albertsons/uds/molecule/Card';
import Spinner from '@albertsons/uds/molecule/Spinner';
import { ReactComponent as DownloadIcon } from '../../assets/download-icon-dashboard.svg';
import { PharmaUploadDocumentsReq, UploadedDocument } from '../../interfaces/downloadFilePharma';
import { useDownloadFilePharmaMutation } from '../../server/Api/menfptCategoryAPI';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { getRelativeTime } from '../../util/timeUtils';
import NoDocumentsMessage from './NoDocumentsMessage';

interface DocumentsProps {
  refreshTrigger?: number;
}

const Documents: React.FC<DocumentsProps> = ({ refreshTrigger = 0 }) => {
  const [files, setFiles] = useState<UploadedDocument[]>([]);
  const [resetLoader, setResetLoader] = useState(false);
  const [downloadFilePharma, { isLoading, isError, data }] = useDownloadFilePharmaMutation();
  const lastCallRef = useRef<string>('');
  const isCallingApiRef = useRef<boolean>(false);

  const { data: userInfo } = useSelectorWrap('userInfo_rn');
  const { data: displayDate } = useSelectorWrap('displayDate_rn');

  const fetchDocuments = useCallback(async () => {
    // Don't make API call if displayDate is not available yet
    if (!displayDate || !displayDate.fiscalWeekNumber) {
      console.info("irk-fetchDocuments-skipped-no-displayDate", JSON.stringify({
        displayDate,
        fiscalWeekNumber: displayDate?.fiscalWeekNumber
      }));
      setResetLoader(false);
      isCallingApiRef.current = false;
      return;
    }
    
    const requestParams = {
      fetchAll: false
    };
    
    console.info("irk-fetchDocuments-called", JSON.stringify({
      requestParams,
      timestamp: new Date().toISOString()
    }));
    
    try {
      const result = await downloadFilePharma(requestParams).unwrap();
      console.info("irk-api-call-success", JSON.stringify({
        result: result,
        uploadedDocumentsCount: result.uploadedDocuments?.length || 0
      }));
      setFiles(result.uploadedDocuments || []);
    } catch (error) {
      console.info("irk-api-call-error", JSON.stringify({ error }));
      // console.error('GraphQL connection failed:', error);
    } finally {
      setResetLoader(false);
      isCallingApiRef.current = false;
    }
  }, [displayDate, downloadFilePharma]);

  useEffect(() => {
    console.info("irk-displayDate-debug", JSON.stringify({
      displayDate,
      fiscalWeekNumber: displayDate?.fiscalWeekNumber,
      hasDisplayDate: !!displayDate,
      userInfo: userInfo
    }));
    
    // Don't make API call if displayDate is not available yet
    if (!displayDate || !displayDate.fiscalWeekNumber) {
      console.info("irk-skipping-api-call-no-displayDate", JSON.stringify({
        displayDate,
        fiscalWeekNumber: displayDate?.fiscalWeekNumber
      }));
      return;
    }
    
    // Create a unique key for this API call
    const callKey = `fetch-documents-${Date.now()}`;
    
    console.info("irk-useEffect-triggered", JSON.stringify({
      callKey,
      lastCallKey: lastCallRef.current,
      isCallingApi: isCallingApiRef.current,
      userInfo: userInfo,
      displayDate: displayDate
    }));
    
    // Prevent duplicate API calls in StrictMode
    if (lastCallRef.current === callKey) {
      console.info("irk-duplicate-call-prevented", JSON.stringify({ callKey }));
      return;
    }
    
    // Prevent if already calling API
    if (isCallingApiRef.current) {
      console.info("irk-api-call-in-progress", JSON.stringify({ callKey }));
      return;
    }
    
    console.info("irk-making-api-call", JSON.stringify({ callKey }));
    setResetLoader(true);
    lastCallRef.current = callKey;
    isCallingApiRef.current = true;
    
    fetchDocuments();
  }, [userInfo, displayDate]);

  // Effect to handle refresh trigger from parent component
  useEffect(() => {
    if (refreshTrigger > 0) {
      console.info("irk-refresh-triggered", JSON.stringify({ refreshTrigger }));
      if (!isCallingApiRef.current) {
        setResetLoader(true);
        isCallingApiRef.current = true;
        fetchDocuments();
      }
    }
  }, [refreshTrigger, fetchDocuments]);

  const handleDownload = async (fileContent: string, fileName: string) => {
    try {
      const cleanedFileName = fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '.$1');
      const byteCharacters = atob(fileContent);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);

      const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = cleanedFileName.endsWith('.xlsx') ? cleanedFileName : `${cleanedFileName}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      // console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const LeftIcon = () => (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.5 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V7.5L14.5 2Z" stroke="#5A697B" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14 2V8H20" stroke="#5A697B" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );

  const getUniqueFiles = (filesArr: UploadedDocument[]) => {
    const fileMap = new Map<string, { fileContent: string; updatedTs: string }>();
    filesArr.forEach(fileObj => {
      fileObj.files.split(',').map(f => f.trim()).forEach(fileName => {
        if (!fileMap.has(fileName) && fileObj.fileName === fileName) {
          fileMap.set(fileName, {
            fileContent: fileObj.fileContent,
            updatedTs: fileObj.updatedTs || ''
          });
        }
      });
    });
    return Array.from(fileMap.entries());
  };

  const getDisplayName = (fileName: string) => {
    return fileName.replace(/_cx_\d{4}-\d{2}-\d{2}_\d+\.(xlsx|xls)$/i, '');
  };

  const calculateFileSize = (base64Content: string): string => {
    try {
      const base64Data = base64Content.replace(/^data:[^;]+;base64,/, '');
      const paddingCount = (base64Data.match(/=/g) || []).length;
      const sizeInBytes = (base64Data.length * 3) / 4 - paddingCount;
      if (sizeInBytes < 1024) {
        return `${sizeInBytes.toFixed(0)} B`;
      } else if (sizeInBytes < 1024 * 1024) {
        return `${(sizeInBytes / 1024).toFixed(1)} KB`;
      } else {
        return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
      }
    } catch (error) {
      return 'Unknown size';
    }
  };

  return (
    <div className="pt-5">
      <Card className="w-[997px]">
        <Card.Header>
          <div className="flex items-center header Sans  text-[#2b303c] nunito text-lg font-extrabold leading-6">
            Documents for Download
          </div>
        </Card.Header>
        <Card.Content>
          {resetLoader ? (
            <div className="flex justify-center items-center py-10">
              <Spinner variant="solid" size="xs" />
            </div>
          ) : files.length === 0 ? (
            <div >
              <NoDocumentsMessage />
            </div>
          ) : (
            <div className="flex flex-col gap-4 mt-2">
              {getUniqueFiles(files).map(([fileName, { fileContent, updatedTs }]) => (
                <div
                  key={fileName}
                  className="flex items-center justify-between bg-white rounded-md"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-[#F1F5F9] rounded-full flex items-center justify-center">
                      <LeftIcon />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-sm text-gray-900">{getDisplayName(fileName)}</span>
                      <span className="text-xs text-gray-500">{calculateFileSize(fileContent)}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    {updatedTs && (
                      <span className="text-xs text-gray-600">
                        Updated {getRelativeTime(updatedTs)}
                      </span>
                    )}
                    <button
                      onClick={() => handleDownload(fileContent, fileName)}
                      className="flex items-center gap-1 text-[#1B6EBB] hover:underline text-sm font-medium"
                      aria-label={`Download ${fileName}`}
                    >
                      Download
                      <DownloadIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card.Content>
      </Card>
    </div>
  );
};

export default Documents;
