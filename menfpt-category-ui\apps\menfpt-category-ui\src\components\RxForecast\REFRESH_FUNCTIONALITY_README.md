# Upload Document Refresh Functionality

## Overview

This document describes the implementation of the refresh functionality that automatically updates the "Documents for Download" section after a successful file upload.

## Implementation Details

### 1. Upload Document Component Changes

**File:** `uploadDocument.tsx`

#### Key Changes:
- Added `refreshDocuments` state to track refresh triggers
- Added `triggerDocumentsRefresh` callback function
- Modified `setUploadAlerts` to trigger refresh on successful upload
- Passed `refreshTrigger` prop to Documents component

#### Code Structure:
```typescript
const [refreshDocuments, setRefreshDocuments] = useState(0);

const triggerDocumentsRefresh = useCallback(() => {
  setRefreshDocuments(prev => prev + 1);
}, []);

const setUploadAlerts = (allUploadsSuccessful: boolean): void => {
  if (allUploadsSuccessful) {
    setSuccessAlertOpen(true);
    setAlertOpen(false);
    // Trigger documents refresh after successful upload
    triggerDocumentsRefresh();
  } else {
    // ... error handling
  }
};
```

### 2. Documents Component Changes

**File:** `rxforecastDocuments.tsx`

#### Key Changes:
- Added `DocumentsProps` interface with optional `refreshTrigger` prop
- Added `isCallingApiRef` to prevent duplicate API calls
- Extracted `fetchDocuments` function with `useCallback`
- Added useEffect to handle refresh triggers
- Added comprehensive logging for debugging

#### Code Structure:
```typescript
interface DocumentsProps {
  refreshTrigger?: number;
}

const Documents: React.FC<DocumentsProps> = ({ refreshTrigger = 0 }) => {
  const isCallingApiRef = useRef<boolean>(false);
  
  const fetchDocuments = useCallback(async () => {
    // ... API call logic with logging
  }, [userInfo, displayDate, downloadFilePharma]);

  // Effect to handle refresh trigger from parent component
  useEffect(() => {
    if (refreshTrigger > 0) {
      console.info("irk-refresh-triggered", JSON.stringify({ refreshTrigger }));
      if (!isCallingApiRef.current) {
        setResetLoader(true);
        isCallingApiRef.current = true;
        fetchDocuments();
      }
    }
  }, [refreshTrigger, fetchDocuments]);
};
```

## Testing Strategy

### 1. New Test File: `uploadDocument.refresh.spec.tsx`

This dedicated test file covers:
- Documents component refresh trigger functionality
- Upload document integration with refresh
- Refresh trigger state management
- Console logging for debugging

#### Test Categories:

**Documents Component Refresh Trigger:**
- Accepts refreshTrigger prop
- Fetches documents when refreshTrigger changes
- Shows loading state during refresh
- Updates files list after successful refresh
- Handles missing displayDate gracefully
- Prevents duplicate API calls

**Upload Document Integration:**
- Triggers refresh after successful upload
- Does not trigger refresh after failed upload
- Handles partial upload failures
- Handles upload errors gracefully

**Console Logging:**
- Logs when fetchDocuments is called
- Logs successful API calls
- Logs refresh triggers
- Logs when skipping API calls

### 2. Updated Existing Tests

**File:** `rxforecastDocuments.spec.tsx`
- Added "Refresh Trigger Functionality" describe block
- Tests refresh trigger prop acceptance
- Tests refresh trigger behavior
- Tests error handling during refresh
- Tests multiple refresh triggers

**File:** `uploadDocument.spec.tsx`
- Added "Refresh Trigger Functionality" describe block
- Tests successful upload triggers refresh
- Tests failed upload does not trigger refresh
- Tests partial upload failures
- Tests upload errors
- Tests state persistence across re-renders

## Usage

### Basic Usage

The refresh functionality works automatically:

1. User uploads a file via the upload component
2. If upload is successful, the Documents component automatically refreshes
3. The latest documents are fetched and displayed

### Manual Refresh (if needed)

```typescript
// In a parent component
const [refreshTrigger, setRefreshTrigger] = useState(0);

const triggerRefresh = () => {
  setRefreshTrigger(prev => prev + 1);
};

// Pass to Documents component
<Documents refreshTrigger={refreshTrigger} />
```

## Error Handling

### 1. Missing DisplayDate
- Logs when displayDate is not available
- Skips API call gracefully
- Prevents unnecessary network requests

### 2. API Errors
- Logs API errors for debugging
- Removes loading state even on error
- Maintains component stability

### 3. Duplicate API Calls
- Uses `isCallingApiRef` to prevent concurrent calls
- Logs when duplicate calls are prevented
- Ensures efficient API usage

## Logging

The implementation includes comprehensive logging for debugging:

```typescript
// Log when fetchDocuments is called
console.info("irk-fetchDocuments-called", JSON.stringify({
  requestParams,
  timestamp: new Date().toISOString()
}));

// Log successful API calls
console.info("irk-api-call-success", JSON.stringify({
  result: result,
  uploadedDocumentsCount: result.uploadedDocuments?.length || 0
}));

// Log when refresh is triggered
console.info("irk-refresh-triggered", JSON.stringify({ refreshTrigger }));

// Log when skipping API call
console.info("irk-fetchDocuments-skipped-no-displayDate", JSON.stringify({
  displayDate,
  fiscalWeekNumber: displayDate?.fiscalWeekNumber
}));
```

## Performance Considerations

### 1. Debouncing
- Uses `isCallingApiRef` to prevent duplicate calls
- Ensures only one API call is active at a time

### 2. State Management
- Uses `useCallback` for `fetchDocuments` to prevent unnecessary re-renders
- Properly manages dependencies in useEffect hooks

### 3. Loading States
- Shows spinner during refresh operations
- Removes loading state on completion (success or error)

## Future Enhancements

### 1. Debounced Refresh
- Could implement debouncing for rapid refresh triggers
- Prevent excessive API calls in quick succession

### 2. Optimistic Updates
- Could show uploaded files immediately
- Refresh in background for consistency

### 3. Retry Logic
- Could add retry mechanism for failed refreshes
- Exponential backoff for network issues

## Troubleshooting

### Common Issues:

1. **Refresh not triggering:**
   - Check if upload was successful
   - Verify `refreshTrigger` prop is being passed
   - Check console logs for debugging info

2. **Duplicate API calls:**
   - Verify `isCallingApiRef` is working correctly
   - Check for multiple refresh triggers in quick succession

3. **Missing documents:**
   - Check if `displayDate` is available
   - Verify API response format
   - Check console logs for API errors

### Debug Commands:

```javascript
// Check refresh trigger value
console.log('Refresh trigger:', refreshTrigger);

// Check if API call is in progress
console.log('Is calling API:', isCallingApiRef.current);

// Check displayDate availability
console.log('Display date:', displayDate);
```

## Conclusion

The refresh functionality provides a seamless user experience by automatically updating the documents list after successful uploads. The implementation includes comprehensive error handling, logging, and testing to ensure reliability and maintainability.
