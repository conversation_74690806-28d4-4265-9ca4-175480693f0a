.edit-forecast {
    font-weight: normal;
    .notification-banner-text {
        font-weight: 700;
    }
    .link-decoration {
        text-decoration: none !important;
        cursor: pointer;
    }
  .save-button-width {
        width: 200px !important;
    }
    .edit-notifications{
        display: flex;
        align-items: center;
        padding: 0.5rem !important;
        margin-top: 5px;
        max-width: 100%;
        font-size: 0.8rem;
    }

    [name^="comment"] {
        height: 150px;
    }

    .border-red-600 [name^="comment"] {
        border: 2px solid #bf2912
    }

    .edit-notifications-text{
        color:#BF2912;
        font-weight: 700;
        font-size: 14px;
    }
    .metrics {
        .text-error {
            color: #80450E;
        }
        .border-error {
            border-color: #F0A92C
        }
    }
}
#save-spinner {
    border-color: white;
}

