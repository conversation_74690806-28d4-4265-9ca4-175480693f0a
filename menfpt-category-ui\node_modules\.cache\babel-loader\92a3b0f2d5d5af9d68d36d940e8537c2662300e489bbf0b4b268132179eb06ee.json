{"ast": null, "code": "import ExcelJS from 'exceljs';\nimport { saveAs } from 'file-saver';\nimport { toTitleCase } from '@ui/utils';\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\nexport const formatCurrency = value => {\n  if (value === null || value === undefined || value === '') return '';\n  const num = Number(value);\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', {\n    maximumFractionDigits: 0\n  })}`;\n};\nexport const getDeptName = (smicData, deptId, fallback) => {\n  const found = smicData.find(item => String(item.deptId).trim() === String(deptId).trim());\n  return toTitleCase((found == null ? void 0 : found.deptName) || fallback || '');\n};\n\n// Add these functions to help with division and banner names\nexport const getDivisionName = (smicData, divisionId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId).trim() === String(divisionId).trim());\n  return toTitleCase((found == null ? void 0 : found.divisionName) || fallback || divisionId);\n};\nexport const getBannerName = (smicData, divisionId, bannerId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId).trim() === String(divisionId).trim() && String(item.bannerId).trim() === String(bannerId).trim());\n  return toTitleCase((found == null ? void 0 : found.bannerName) || fallback || bannerId);\n};\nconst addRows = (rows, dept, smicData, useWeekId = false) => {\n  var _dept$name, _dept$periods;\n  const quarter = dept.quarter || {};\n  const deptName = getDeptName(smicData, dept.id, (_dept$name = dept == null ? void 0 : dept.name) != null ? _dept$name : '');\n  const isTotal = dept.id === 'Total';\n  const baseRow = {\n    departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}`\n  };\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\n  const weeksByPeriod = {};\n  (dept.weeks || []).forEach(week => {\n    var _ref, _week$periodNumber;\n    const periodNum = (_ref = (_week$periodNumber = week.periodNumber) != null ? _week$periodNumber : week.periodNbr) != null ? _ref : '';\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\n    weeksByPeriod[periodNum].push(week);\n  });\n  (_dept$periods = dept.periods) == null || _dept$periods.forEach(period => {\n    var _ref2, _period$periodNumber;\n    const periodNum = (_ref2 = (_period$periodNumber = period.periodNumber) != null ? _period$periodNumber : period.periodNbr) != null ? _ref2 : '';\n    rows.push(mapRow(Object.assign({}, baseRow, {\n      departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period'\n    }), period, formatCurrency, 'Period', periodNum));\n    const weeks = weeksByPeriod[periodNum] || [];\n    const sortedWeeks = weeks.slice().sort((a, b) => {\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n      return aNum - bNum;\n    });\n    sortedWeeks.forEach(week => {\n      let weekNum = '--';\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\n        weekNum = String(parseInt(week.id.slice(-2), 10));\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\n        weekNum = String(week.weekNumber % 100);\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n      }\n      rows.push(mapRow(Object.assign({}, baseRow, {\n        departmentName: `Week ${weekNum} (fiscal wk ${weekNum})`\n      }), week, formatCurrency, 'Week', '', String(weekNum)));\n    });\n  });\n};\nexport const styleWorksheet = worksheet => {\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (colNumber !== 1) {\n      var _cell$value;\n      worksheet.getColumn(colNumber).width = Math.max(String((_cell$value = cell.value) != null ? _cell$value : '').length + 1, 16);\n    }\n  });\n  let maxA = 0;\n  worksheet.eachRow((row, rowNumber) => {\n    var _row$getCell$value;\n    const cellValue = String((_row$getCell$value = row.getCell(1).value) != null ? _row$getCell$value : '');\n    if (cellValue.length > maxA) maxA = cellValue.length;\n  });\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\n  const thinLightBlack = {\n    style: 'thin',\n    color: {\n      argb: 'FF222222'\n    }\n  };\n  const thinLightBlackBorder = {\n    top: thinLightBlack,\n    left: thinLightBlack,\n    bottom: thinLightBlack,\n    right: thinLightBlack\n  };\n  worksheet.eachRow(row => {\n    row.eachCell(cell => {\n      cell.border = thinLightBlackBorder;\n    });\n  });\n  const lightGrayFill = {\n    type: \"pattern\",\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFD3D3D3'\n    }\n  };\n  worksheet.getRow(1).eachCell(cell => {\n    cell.fill = lightGrayFill;\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getRow(2).eachCell(cell => {\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A2').fill = lightGrayFill;\n  worksheet.getCell('A2').font = {\n    bold: true\n  };\n  const lightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFA8F1FF'\n    }\n  };\n  const highlightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FF6FE6FC'\n    }\n  };\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      const firstCell = row.getCell(1).value;\n      if (typeof firstCell === 'string') {\n        if (firstCell.trim() === 'Total' || /^[0-9]+ - /.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = highlightBlueFill;\n          });\n        } else if (/^Period\\b/.test(firstCell)) {\n          row.eachCell(cell => {\n            cell.fill = lightBlueFill;\n          });\n        }\n      }\n    }\n  });\n  worksheet.getCell('A1').alignment = {\n    vertical: 'middle',\n    horizontal: 'center'\n  };\n  ['A1', 'A2'].forEach(cell => {\n    worksheet.getCell(cell).alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A3').font = Object.assign({}, worksheet.getCell('A3').font, {\n    bold: true\n  });\n};\nexport const styleVsProjection = worksheet => {\n  const vsProjectionColIndices = [];\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\n  });\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      vsProjectionColIndices.forEach(colIdx => {\n        const cell = row.getCell(colIdx);\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\n        const num = Number(raw);\n        if (!isNaN(num) && raw !== '') {\n          cell.font = Object.assign({}, cell.font, {\n            color: {\n              argb: num < 0 ? 'FFFF0000' : 'FF008000'\n            }\n          });\n          const header = worksheet.getRow(2).getCell(colIdx).value;\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\n            if (num < 0) {\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })})`;\n            } else {\n              cell.value = `$${num.toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })}`;\n            }\n          }\n        }\n      });\n    }\n  });\n};\nexport const handleDownloadExcel = async (dashboardData, smicData = [], appliedFilters, fileName = 'Dashboard Excel Download.xlsx') => {\n  var _appliedFilters$timef, _appliedFilters$timef2;\n  // if (!dashboardData?.length) return alert('No dashboard data to export!');\n  if (!(dashboardData != null && dashboardData.length)) return; // Return silently if no data\n  console.log('Data for Excel from slice:', dashboardData);\n\n  // Extract quarter information\n  let quarterNumber = appliedFilters == null || (_appliedFilters$timef = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef.quarter;\n  if (!quarterNumber && dashboardData.length) {\n    var _dashboardData$, _dashboardData$2;\n    quarterNumber = ((_dashboardData$ = dashboardData[0]) == null || (_dashboardData$ = _dashboardData$.quarter) == null ? void 0 : _dashboardData$.quarterNumber) || ((_dashboardData$2 = dashboardData[0]) == null ? void 0 : _dashboardData$2.quarterNumber) || '';\n    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {\n      const qStr = String(quarterNumber);\n      if (qStr.length === 6) quarterNumber = Number(qStr.slice(4, 6));\n    }\n  }\n  const fiscalYear = (appliedFilters == null || (_appliedFilters$timef2 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef2.fiscalYear) || '';\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\n  const isVariance = fileName.toLowerCase().includes('variance');\n\n  // Create the Excel workbook and worksheet\n  const workbook = new ExcelJS.Workbook();\n  const worksheet = workbook.addWorksheet('Dashboard');\n  worksheet.addRow(parentHeaderRow);\n  worksheet.addRow(COMMON_HEADERS);\n\n  // Process divisions, banners, departments, periods, and weeks\n  const rows = [];\n\n  // For each division\n  dashboardData.forEach(division => {\n    // Add division row\n    const divisionName = getDeptName(smicData, division.id, division.name || '');\n    rows.push(mapRow({\n      departmentName: `Division: ${division.id} - ${divisionName}`\n    }, division.quarter || {}, formatCurrency, 'Division'));\n\n    // Process ALL banners (don't filter)\n    if (Array.isArray(division.banners)) {\n      division.banners.forEach(banner => {\n        const bannerName = banner.name || `Banner ${banner.id}`;\n        rows.push(mapRow({\n          departmentName: `  Banner: ${banner.id} - ${bannerName}`\n        }, banner.quarter || {}, formatCurrency, 'Banner'));\n\n        // Process departments under this banner\n        if (Array.isArray(banner.departments)) {\n          banner.departments.forEach(dept => {\n            addDepartmentAndChildren(rows, dept, smicData, isVariance, '    ');\n          });\n        }\n      });\n    }\n  });\n\n  // Add all rows to the worksheet\n  rows.forEach(row => worksheet.addRow(Object.values(row)));\n\n  // Apply styles and formatting\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\n  styleWorksheet(worksheet);\n  styleVsProjection(worksheet);\n  applyPrintSettings(worksheet);\n\n  // Freeze panes to make navigation easier\n  setupFreezePanes(worksheet);\n\n  // Generate and save the Excel file\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), fileName);\n};\n\n// Helper function to add department and its children (periods, weeks)\nconst addDepartmentAndChildren = (rows, dept, smicData, useWeekId = false, indent = '') => {\n  var _dept$name2, _dept$periods2;\n  const deptName = getDeptName(smicData, dept.id, (_dept$name2 = dept == null ? void 0 : dept.name) != null ? _dept$name2 : '');\n  const isTotal = dept.id === 'Total';\n  const baseRow = {\n    departmentName: `${indent}${isTotal ? 'Total' : `${dept.id} - ${deptName}`}`\n  };\n\n  // Add department row\n  rows.push(mapRow(baseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\n\n  // Process periods\n  const weeksByPeriod = {};\n  (dept.weeks || []).forEach(week => {\n    var _ref3, _week$periodNumber2;\n    const periodNum = (_ref3 = (_week$periodNumber2 = week.periodNumber) != null ? _week$periodNumber2 : week.periodNbr) != null ? _ref3 : '';\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\n    weeksByPeriod[periodNum].push(week);\n  });\n  (_dept$periods2 = dept.periods) == null || _dept$periods2.forEach(period => {\n    var _ref4, _period$periodNumber2;\n    const periodNum = (_ref4 = (_period$periodNumber2 = period.periodNumber) != null ? _period$periodNumber2 : period.periodNbr) != null ? _ref4 : '';\n    rows.push(mapRow(Object.assign({}, baseRow, {\n      departmentName: `${indent}  Period ${parseInt(String(periodNum).slice(-2), 10)}`\n    }), period, formatCurrency, 'Period', periodNum));\n\n    // Process weeks for this period\n    const weeks = weeksByPeriod[periodNum] || [];\n    const sortedWeeks = weeks.slice().sort((a, b) => {\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n      return aNum - bNum;\n    });\n    sortedWeeks.forEach(week => {\n      let weekNum = '--';\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\n        weekNum = String(parseInt(week.id.slice(-2), 10));\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\n        weekNum = String(week.weekNumber % 100);\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n      }\n      rows.push(mapRow(Object.assign({}, baseRow, {\n        departmentName: `${indent}    Week ${weekNum} (fiscal wk ${weekNum})`\n      }), week, formatCurrency, 'Week', '', String(weekNum)));\n    });\n  });\n};\n\n// Helper function to set up freeze panes\nconst setupFreezePanes = worksheet => {\n  let ySplit = 2;\n  const totalRows = worksheet.rowCount;\n\n  // Find the first department row to freeze at\n  for (let i = 3; i <= totalRows; i++) {\n    var _worksheet$getRow$get;\n    const cellValue = String((_worksheet$getRow$get = worksheet.getRow(i).getCell(1).value) != null ? _worksheet$getRow$get : '');\n    if (cellValue.includes('Division:') || /^\\d+ - /.test(cellValue.trim())) {\n      ySplit = i - 1;\n      break;\n    }\n    if (i === totalRows) {\n      ySplit = totalRows;\n    }\n  }\n  worksheet.views = [{\n    state: 'frozen',\n    ySplit,\n    xSplit: 1\n  }];\n};\nexport const handleDownloadBothExcel = async (performanceSummaryData, forecastVarianceData, smicData = [], appliedFilters) => {\n  if (!(performanceSummaryData != null && performanceSummaryData.length) && !(forecastVarianceData != null && forecastVarianceData.length)) {\n    return alert('No dashboard data to export!');\n  }\n  const workbook = new ExcelJS.Workbook();\n  if (performanceSummaryData != null && performanceSummaryData.length) {\n    var _appliedFilters$timef3, _appliedFilters$timef4;\n    const worksheet1 = workbook.addWorksheet('Performance Summary');\n    const quarterNumber = (appliedFilters == null || (_appliedFilters$timef3 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef3.quarter) || '';\n    const fiscalYear = (appliedFilters == null || (_appliedFilters$timef4 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef4.fiscalYear) || '';\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n    worksheet1.addRow(getParentHeaderRow(quarterDisplay));\n    worksheet1.addRow(COMMON_HEADERS);\n    performanceSummaryData.forEach(dept => {\n      const rows = [];\n      addRows(rows, dept, smicData);\n      rows.forEach(row => worksheet1.addRow(Object.values(row)));\n    });\n    styleWorksheet(worksheet1);\n    styleVsProjection(worksheet1);\n    applyPrintSettings(worksheet1);\n  }\n  if (forecastVarianceData != null && forecastVarianceData.length) {\n    var _appliedFilters$timef5, _appliedFilters$timef6;\n    const worksheet2 = workbook.addWorksheet('Forecast Variance');\n    const quarterNumber = (appliedFilters == null || (_appliedFilters$timef5 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef5.quarter) || '';\n    const fiscalYear = (appliedFilters == null || (_appliedFilters$timef6 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef6.fiscalYear) || '';\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n    worksheet2.addRow(getParentHeaderRow(quarterDisplay));\n    worksheet2.addRow(COMMON_HEADERS);\n    forecastVarianceData.forEach(dept => {\n      const rows = [];\n      addRows(rows, dept, smicData, true);\n      rows.forEach(row => worksheet2.addRow(Object.values(row)));\n    });\n    styleWorksheet(worksheet2);\n    styleVsProjection(worksheet2);\n    applyPrintSettings(worksheet2);\n  }\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), 'Dashboard Excel Download.xlsx');\n};", "map": {"version": 3, "names": ["ExcelJS", "saveAs", "toTitleCase", "getParentHeaderRow", "COMMON_HEADERS", "VS_PROJECTION_HEADERS", "VS_PROJECTION_DOLLAR_HEADERS", "mapRow", "applyPrintSettings", "formatCurrency", "value", "undefined", "num", "Number", "isNaN", "toLocaleString", "maximumFractionDigits", "getDeptName", "smicData", "deptId", "fallback", "found", "find", "item", "String", "trim", "deptName", "getDivisionName", "divisionId", "divisionName", "getBannerName", "bannerId", "bannerName", "addRows", "rows", "dept", "useWeekId", "_dept$name", "_dept$periods", "quarter", "id", "name", "isTotal", "baseRow", "departmentName", "push", "weeksByPeriod", "weeks", "for<PERSON>ach", "week", "_ref", "_week$periodNumber", "periodNum", "periodNumber", "periodNbr", "periods", "period", "_ref2", "_period$periodNumber", "Object", "assign", "parseInt", "slice", "sortedWeeks", "sort", "a", "b", "aNum", "weekNumber", "bNum", "weekNum", "startsWith", "styleWorksheet", "worksheet", "getRow", "eachCell", "cell", "colNumber", "_cell$value", "getColumn", "width", "Math", "max", "length", "maxA", "eachRow", "row", "rowNumber", "_row$getCell$value", "cellValue", "getCell", "thinLightBlack", "style", "color", "argb", "thinLightBlackBorder", "top", "left", "bottom", "right", "border", "lightGrayFill", "type", "pattern", "fgColor", "fill", "font", "bold", "alignment", "vertical", "horizontal", "lightBlueFill", "highlightBlueFill", "firstCell", "test", "styleVsProjection", "vsProjectionColIndices", "includes", "colIdx", "raw", "replace", "header", "abs", "handleDownloadExcel", "dashboardData", "appliedFilters", "fileName", "_appliedFilters$timef", "_appliedFilters$timef2", "console", "log", "quarterNumber", "timeframe", "_dashboardData$", "_dashboardData$2", "qStr", "fiscalYear", "quarterDisplay", "parentHeaderRow", "is<PERSON><PERSON>ce", "toLowerCase", "workbook", "Workbook", "addWorksheet", "addRow", "division", "Array", "isArray", "banners", "banner", "departments", "addDepartmentAndChildren", "values", "mergeRanges", "range", "mergeCells", "setupFreezePanes", "styledBuffer", "xlsx", "writeBuffer", "Blob", "indent", "_dept$name2", "_dept$periods2", "_ref3", "_week$periodNumber2", "_ref4", "_period$periodNumber2", "ySplit", "totalRows", "rowCount", "i", "_worksheet$getRow$get", "views", "state", "xSplit", "handleDownloadBothExcel", "performanceSummaryData", "forecastVarianceData", "alert", "_appliedFilters$timef3", "_appliedFilters$timef4", "worksheet1", "_appliedFilters$timef5", "_appliedFilters$timef6", "worksheet2"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/components/DashboardDownloadExcel/DashboardDownloadExcel.tsx"], "sourcesContent": ["import ExcelJS from 'exceljs';\r\nimport { saveAs } from 'file-saver';\r\nimport { toTitleCase } from '@ui/utils';\r\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\r\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\r\n\r\nexport const formatCurrency = (value: any) => {\r\n  if (value === null || value === undefined || value === '') return '';\r\n  const num = Number(value);\r\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n};\r\nexport const getDeptName = (smicData: any[], deptId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.deptId).trim() === String(deptId).trim());\r\n  return toTitleCase(found?.deptName || fallback || '');\r\n};\r\n\r\n// Add these functions to help with division and banner names\r\nexport const getDivisionName = (smicData: any[], divisionId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.divisionId).trim() === String(divisionId).trim());\r\n  return toTitleCase(found?.divisionName || fallback || divisionId);\r\n};\r\n\r\nexport const getBannerName = (smicData: any[], divisionId: string, bannerId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => \r\n    String(item.divisionId).trim() === String(divisionId).trim() && \r\n    String(item.bannerId).trim() === String(bannerId).trim()\r\n  );\r\n  return toTitleCase(found?.bannerName || fallback || bannerId);\r\n};\r\n\r\nconst addRows = (rows: any[], dept: any, smicData: any[], useWeekId: boolean = false) => {\r\n  const quarter = dept.quarter || {};\r\n  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n  const isTotal = dept.id === 'Total';\r\n  const baseRow = { departmentName: isTotal ? 'Total' : `${dept.id} - ${deptName}` };\r\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\r\n\r\n  const weeksByPeriod: Record<string, any[]> = {};\r\n  (dept.weeks || []).forEach((week: any) => {\r\n    const periodNum = week.periodNumber ?? week.periodNbr ?? '';\r\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\r\n    weeksByPeriod[periodNum].push(week);\r\n  });\r\n\r\n  dept.periods?.forEach((period: any) => {\r\n    const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n    rows.push(\r\n      mapRow(\r\n        { ...baseRow, departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },\r\n        period,\r\n        formatCurrency,\r\n        'Period',\r\n        periodNum\r\n      )\r\n    );\r\n\r\n    const weeks = weeksByPeriod[periodNum] || [];\r\n    const sortedWeeks = weeks.slice().sort((a, b) => {\r\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n      return aNum - bNum;\r\n    });\r\n    sortedWeeks.forEach((week: any) => {\r\n      let weekNum = '--';\r\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n        weekNum = String(parseInt(week.id.slice(-2), 10));\r\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\r\n        weekNum = String(week.weekNumber % 100);\r\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\r\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n      }\r\n      rows.push(\r\n        mapRow(\r\n          { ...baseRow, departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },\r\n          week,\r\n          formatCurrency,\r\n          'Week',\r\n          '',\r\n          String(weekNum)\r\n        )\r\n      );\r\n    });\r\n  });\r\n};\r\nexport const styleWorksheet = worksheet => {\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (colNumber !== 1) {\r\n      worksheet.getColumn(colNumber).width = Math.max(String(cell.value ?? '').length + 1, 16);\r\n    }\r\n  });\r\n  let maxA = 0;\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    const cellValue = String(row.getCell(1).value ?? '');\r\n    if (cellValue.length > maxA) maxA = cellValue.length;\r\n  });\r\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\r\n\r\n  const thinLightBlack = { style: 'thin', color: { argb: 'FF222222' } };\r\n  const thinLightBlackBorder = {\r\n  top: thinLightBlack, left: thinLightBlack,bottom: thinLightBlack,right: thinLightBlack\r\n  };\r\n   worksheet.eachRow(row => {\r\n    row.eachCell(cell => {\r\n      cell.border = thinLightBlackBorder;\r\n    });\r\n  });\r\n  const lightGrayFill = { type: \"pattern\", pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };\r\n  worksheet.getRow(1).eachCell(cell => { \r\n    cell.fill = lightGrayFill; \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });\r\n  worksheet.getRow(2).eachCell(cell => { \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' }; \r\n  });\r\n  worksheet.getCell('A2').fill = lightGrayFill; worksheet.getCell('A2').font = { bold: true };\r\n  const lightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFA8F1FF' } };\r\n  const highlightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6FE6FC' } }; \r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      const firstCell = row.getCell(1).value;\r\n      if (typeof firstCell === 'string') {\r\n        if ( firstCell.trim() === 'Total' || /^[0-9]+ - /.test(firstCell)   ) {\r\n          row.eachCell(cell => { cell.fill = highlightBlueFill; });\r\n        } else if (/^Period\\b/.test(firstCell)) {\r\n          row.eachCell(cell => { cell.fill = lightBlueFill; });\r\n        }\r\n      }\r\n    }\r\n  });\r\n  worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };\r\n  ['A1', 'A2'].forEach(cell => {\r\n    worksheet.getCell(cell).alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });worksheet.getCell('A3').font = { ...worksheet.getCell('A3').font, bold: true };\r\n}\r\nexport const styleVsProjection = (worksheet: ExcelJS.Worksheet) => {\r\n  const vsProjectionColIndices: number[] = [];\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\r\n  });\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      vsProjectionColIndices.forEach(colIdx => {\r\n        const cell = row.getCell(colIdx);\r\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\r\n        const num = Number(raw);\r\n        if (!isNaN(num) && raw !== '') {\r\n          cell.font = { ...cell.font, color: { argb: num < 0 ? 'FFFF0000' : 'FF008000' } };\r\n          const header = worksheet.getRow(2).getCell(colIdx).value;\r\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\r\n            if (num < 0) {\r\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;\r\n            } else {\r\n              cell.value = `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  });\r\n};\r\nexport const handleDownloadExcel = async (\r\n  dashboardData: any[],\r\n  smicData: any[] = [],\r\n  appliedFilters?: any,\r\n  fileName: string = 'Dashboard Excel Download.xlsx'\r\n) => {\r\n  // if (!dashboardData?.length) return alert('No dashboard data to export!');\r\n  if (!dashboardData?.length) return; // Return silently if no data\r\n  console.log('Data for Excel from slice:', dashboardData);\r\n  \r\n  // Extract quarter information\r\n  let quarterNumber = appliedFilters?.timeframe?.quarter;\r\n  if (!quarterNumber && dashboardData.length) {\r\n    quarterNumber = dashboardData[0]?.quarter?.quarterNumber || dashboardData[0]?.quarterNumber || '';\r\n    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {\r\n      const qStr = String(quarterNumber); \r\n      if (qStr.length === 6) quarterNumber = Number(qStr.slice(4, 6));\r\n    }\r\n  }\r\n  const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\r\n  const isVariance = fileName.toLowerCase().includes('variance');\r\n  \r\n  // Create the Excel workbook and worksheet\r\n  const workbook = new ExcelJS.Workbook();\r\n  const worksheet = workbook.addWorksheet('Dashboard');\r\n  worksheet.addRow(parentHeaderRow);\r\n  worksheet.addRow(COMMON_HEADERS);\r\n  \r\n  // Process divisions, banners, departments, periods, and weeks\r\n  const rows: any[] = [];\r\n  \r\n  // For each division\r\n  dashboardData.forEach(division => {\r\n    // Add division row\r\n    const divisionName = getDeptName(smicData, division.id, division.name || '');\r\n    rows.push(mapRow({ departmentName: `Division: ${division.id} - ${divisionName}` }, \r\n                    division.quarter || {}, formatCurrency, 'Division'));\r\n    \r\n    // Process ALL banners (don't filter)\r\n    if (Array.isArray(division.banners)) {\r\n      division.banners.forEach(banner => {\r\n        const bannerName = banner.name || `Banner ${banner.id}`;\r\n        rows.push(mapRow({ departmentName: `  Banner: ${banner.id} - ${bannerName}` }, \r\n                        banner.quarter || {}, formatCurrency, 'Banner'));\r\n        \r\n        // Process departments under this banner\r\n        if (Array.isArray(banner.departments)) {\r\n          banner.departments.forEach(dept => {\r\n            addDepartmentAndChildren(rows, dept, smicData, isVariance, '    ');\r\n          });\r\n        }\r\n      });\r\n    }\r\n  });\r\n  \r\n  // Add all rows to the worksheet\r\n  rows.forEach(row => worksheet.addRow(Object.values(row)));\r\n  \r\n  // Apply styles and formatting\r\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\r\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\r\n  styleWorksheet(worksheet);\r\n  styleVsProjection(worksheet);\r\n  applyPrintSettings(worksheet);\r\n  \r\n  // Freeze panes to make navigation easier\r\n  setupFreezePanes(worksheet);\r\n  \r\n  // Generate and save the Excel file\r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), fileName);\r\n};\r\n\r\n// Helper function to add department and its children (periods, weeks)\r\nconst addDepartmentAndChildren = (rows: any[], dept: any, smicData: any[], useWeekId: boolean = false, indent: string = '') => {\r\n  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n  const isTotal = dept.id === 'Total';\r\n  const baseRow = { departmentName: `${indent}${isTotal ? 'Total' : `${dept.id} - ${deptName}`}` };\r\n  \r\n  // Add department row\r\n  rows.push(mapRow(baseRow, dept.quarter || {}, formatCurrency, 'Quarter'));\r\n  \r\n  // Process periods\r\n  const weeksByPeriod: Record<string, any[]> = {};\r\n  (dept.weeks || []).forEach((week: any) => {\r\n    const periodNum = week.periodNumber ?? week.periodNbr ?? '';\r\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\r\n    weeksByPeriod[periodNum].push(week);\r\n  });\r\n  \r\n  dept.periods?.forEach((period: any) => {\r\n    const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n    rows.push(\r\n      mapRow(\r\n        { ...baseRow, departmentName: `${indent}  Period ${parseInt(String(periodNum).slice(-2), 10)}` },\r\n        period,\r\n        formatCurrency,\r\n        'Period',\r\n        periodNum\r\n      )\r\n    );\r\n    \r\n    // Process weeks for this period\r\n    const weeks = weeksByPeriod[periodNum] || [];\r\n    const sortedWeeks = weeks.slice().sort((a, b) => {\r\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n      return aNum - bNum;\r\n    });\r\n    \r\n    sortedWeeks.forEach((week: any) => {\r\n      let weekNum = '--';\r\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n        weekNum = String(parseInt(week.id.slice(-2), 10));\r\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\r\n        weekNum = String(week.weekNumber % 100);\r\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\r\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n      }\r\n      \r\n      rows.push(\r\n        mapRow(\r\n          { ...baseRow, departmentName: `${indent}    Week ${weekNum} (fiscal wk ${weekNum})` },\r\n          week,\r\n          formatCurrency,\r\n          'Week',\r\n          '',\r\n          String(weekNum)\r\n        )\r\n      );\r\n    });\r\n  });\r\n};\r\n\r\n// Helper function to set up freeze panes\r\nconst setupFreezePanes = (worksheet: ExcelJS.Worksheet) => {\r\n  let ySplit = 2;\r\n  const totalRows = worksheet.rowCount;\r\n  \r\n  // Find the first department row to freeze at\r\n  for (let i = 3; i <= totalRows; i++) {\r\n    const cellValue = String(worksheet.getRow(i).getCell(1).value ?? '');\r\n    if (cellValue.includes('Division:') || /^\\d+ - /.test(cellValue.trim())) {\r\n      ySplit = i - 1;\r\n      break;\r\n    }\r\n    if (i === totalRows) {\r\n      ySplit = totalRows;\r\n    }\r\n  }\r\n  \r\n  worksheet.views = [{ state: 'frozen', ySplit, xSplit: 1 }];\r\n};\r\n\r\nexport const handleDownloadBothExcel = async (\r\n  performanceSummaryData: any[],\r\n  forecastVarianceData: any[],\r\n  smicData: any[] = [],\r\n  appliedFilters?: any\r\n) => {\r\n  if (!performanceSummaryData?.length && !forecastVarianceData?.length) {\r\n    return alert('No dashboard data to export!');\r\n  }\r\n  const workbook = new ExcelJS.Workbook();\r\n  if (performanceSummaryData?.length) {\r\n    const worksheet1 = workbook.addWorksheet('Performance Summary');\r\n    const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n    worksheet1.addRow(getParentHeaderRow(quarterDisplay));\r\n    worksheet1.addRow(COMMON_HEADERS);\r\n    performanceSummaryData.forEach(dept => {\r\n      const rows: any[] = [];\r\n      addRows(rows, dept, smicData);\r\n      rows.forEach(row => worksheet1.addRow(Object.values(row)));\r\n    });\r\n    styleWorksheet(worksheet1);\r\n    styleVsProjection(worksheet1);\r\n    applyPrintSettings(worksheet1);\r\n  }\r\n  if (forecastVarianceData?.length) {\r\n    const worksheet2 = workbook.addWorksheet('Forecast Variance');\r\n    const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n    worksheet2.addRow(getParentHeaderRow(quarterDisplay));\r\n    worksheet2.addRow(COMMON_HEADERS);\r\n    forecastVarianceData.forEach(dept => {\r\n      const rows: any[] = [];\r\n      addRows(rows, dept, smicData, true);\r\n      rows.forEach(row => worksheet2.addRow(Object.values(row)));\r\n    });\r\n    styleWorksheet(worksheet2);\r\n    styleVsProjection(worksheet2);\r\n    applyPrintSettings(worksheet2);\r\n  }\r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), 'Dashboard Excel Download.xlsx');\r\n};\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,SAAS;AAC7B,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,MAAM,QAAQ,gCAAgC;AAChJ,SAASC,kBAAkB,QAAQ,+BAA+B;AAElE,OAAO,MAAMC,cAAc,GAAIC,KAAU,IAAK;EAC5C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE,OAAO,EAAE;EACpE,MAAME,GAAG,GAAGC,MAAM,CAACH,KAAK,CAAC;EACzB,OAAOI,KAAK,CAACF,GAAG,CAAC,GAAGF,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;IAAEC,qBAAqB,EAAE;EAAE,CAAC,CAAC,EAAE;AAC7F,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAe,EAAEC,MAAc,EAAEC,QAAgB,KAAK;EAChF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACL,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;EAChG,OAAOvB,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEK,QAAQ,KAAIN,QAAQ,IAAI,EAAE,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMO,eAAe,GAAGA,CAACT,QAAe,EAAEU,UAAkB,EAAER,QAAgB,KAAK;EACxF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,CAACH,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACI,UAAU,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;EACxG,OAAOvB,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEQ,YAAY,KAAIT,QAAQ,IAAIQ,UAAU,CAAC;AACnE,CAAC;AAED,OAAO,MAAME,aAAa,GAAGA,CAACZ,QAAe,EAAEU,UAAkB,EAAEG,QAAgB,EAAEX,QAAgB,KAAK;EACxG,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IACpCC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,CAACH,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACI,UAAU,CAAC,CAACH,IAAI,CAAC,CAAC,IAC5DD,MAAM,CAACD,IAAI,CAACQ,QAAQ,CAAC,CAACN,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACO,QAAQ,CAAC,CAACN,IAAI,CAAC,CACzD,CAAC;EACD,OAAOvB,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEW,UAAU,KAAIZ,QAAQ,IAAIW,QAAQ,CAAC;AAC/D,CAAC;AAED,MAAME,OAAO,GAAGA,CAACC,IAAW,EAAEC,IAAS,EAAEjB,QAAe,EAAEkB,SAAkB,GAAG,KAAK,KAAK;EAAA,IAAAC,UAAA,EAAAC,aAAA;EACvF,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC;EAClC,MAAMb,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAH,UAAA,GAAEF,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAJ,UAAA,GAAI,EAAE,CAAC;EACjE,MAAMK,OAAO,GAAGP,IAAI,CAACK,EAAE,KAAK,OAAO;EACnC,MAAMG,OAAO,GAAG;IAAEC,cAAc,EAAEF,OAAO,GAAG,OAAO,GAAG,GAAGP,IAAI,CAACK,EAAE,MAAMd,QAAQ;EAAG,CAAC;EAClFQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACoC,OAAO,EAAEJ,OAAO,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;EAE9D,MAAMqC,aAAoC,GAAG,CAAC,CAAC;EAC/C,CAACX,IAAI,CAACY,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAEC,IAAS,IAAK;IAAA,IAAAC,IAAA,EAAAC,kBAAA;IACxC,MAAMC,SAAS,IAAAF,IAAA,IAAAC,kBAAA,GAAGF,IAAI,CAACI,YAAY,YAAAF,kBAAA,GAAIF,IAAI,CAACK,SAAS,YAAAJ,IAAA,GAAI,EAAE;IAC3D,IAAI,CAACJ,aAAa,CAACM,SAAS,CAAC,EAAEN,aAAa,CAACM,SAAS,CAAC,GAAG,EAAE;IAC5DN,aAAa,CAACM,SAAS,CAAC,CAACP,IAAI,CAACI,IAAI,CAAC;EACrC,CAAC,CAAC;EAEF,CAAAX,aAAA,GAAAH,IAAI,CAACoB,OAAO,aAAZjB,aAAA,CAAcU,OAAO,CAAEQ,MAAW,IAAK;IAAA,IAAAC,KAAA,EAAAC,oBAAA;IACrC,MAAMN,SAAS,IAAAK,KAAA,IAAAC,oBAAA,GAAGF,MAAM,CAACH,YAAY,YAAAK,oBAAA,GAAIF,MAAM,CAACF,SAAS,YAAAG,KAAA,GAAI,EAAE;IAC/DvB,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;MAAEC,cAAc,EAAEQ,SAAS,GAAG,UAAUS,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;IAAQ,IAC1GN,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;IAED,MAAML,KAAK,GAAGD,aAAa,CAACM,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAMW,WAAW,GAAGhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/C,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,OAAOK,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IACFN,WAAW,CAACf,OAAO,CAAEC,IAAS,IAAK;MACjC,IAAIqB,OAAO,GAAG,IAAI;MAClB,IAAIlC,SAAS,IAAI,OAAOa,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3ED,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI,CAAC1B,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,CAAChC,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D;MACA5B,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;QAAEC,cAAc,EAAE,QAAQ0B,OAAO,eAAeA,OAAO;MAAG,IACtErB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAME,cAAc,GAAGC,SAAS,IAAI;EACzCA,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,KAAK,CAAC,EAAE;MAAA,IAAAC,WAAA;MACnBL,SAAS,CAACM,SAAS,CAACF,SAAS,CAAC,CAACG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC1D,MAAM,EAAAsD,WAAA,GAACF,IAAI,CAAClE,KAAK,YAAAoE,WAAA,GAAI,EAAE,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;IAC1F;EACF,CAAC,CAAC;EACF,IAAIC,IAAI,GAAG,CAAC;EACZX,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IAAA,IAAAC,kBAAA;IACpC,MAAMC,SAAS,GAAGjE,MAAM,EAAAgE,kBAAA,GAACF,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK,YAAA8E,kBAAA,GAAI,EAAE,CAAC;IACpD,IAAIC,SAAS,CAACN,MAAM,GAAGC,IAAI,EAAEA,IAAI,GAAGK,SAAS,CAACN,MAAM;EACtD,CAAC,CAAC;EACFV,SAAS,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACE,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;EAErD,MAAMO,cAAc,GAAG;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAW;EAAE,CAAC;EACrE,MAAMC,oBAAoB,GAAG;IAC7BC,GAAG,EAAEL,cAAc;IAAEM,IAAI,EAAEN,cAAc;IAACO,MAAM,EAAEP,cAAc;IAACQ,KAAK,EAAER;EACxE,CAAC;EACAlB,SAAS,CAACY,OAAO,CAACC,GAAG,IAAI;IACxBA,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;MACnBA,IAAI,CAACwB,MAAM,GAAGL,oBAAoB;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMM,aAAa,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1FrB,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC6B,IAAI,GAAGJ,aAAa;IACzBzB,IAAI,CAAC8B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B/B,IAAI,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFrC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC8B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B/B,IAAI,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFrC,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACe,IAAI,GAAGJ,aAAa;EAAE5B,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAG;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC3F,MAAMI,aAAa,GAAG;IAAET,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1F,MAAMkB,iBAAiB,GAAG;IAAEV,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC9FrB,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,MAAM0B,SAAS,GAAG3B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK;MACtC,IAAI,OAAOuG,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAKA,SAAS,CAACxF,IAAI,CAAC,CAAC,KAAK,OAAO,IAAI,YAAY,CAACyF,IAAI,CAACD,SAAS,CAAC,EAAK;UACpE3B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGO,iBAAiB;UAAE,CAAC,CAAC;QAC1D,CAAC,MAAM,IAAI,WAAW,CAACE,IAAI,CAACD,SAAS,CAAC,EAAE;UACtC3B,GAAG,CAACX,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC6B,IAAI,GAAGM,aAAa;UAAE,CAAC,CAAC;QACtD;MACF;IACF;EACF,CAAC,CAAC;EACFtC,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACkB,SAAS,GAAG;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAS,CAAC;EAChF,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC9D,OAAO,CAAC4B,IAAI,IAAI;IAC3BH,SAAS,CAACiB,OAAO,CAACd,IAAI,CAAC,CAACgC,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAClF,CAAC,CAAC;EAACrC,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQa,SAAS,CAACiB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI;IAAEC,IAAI,EAAE;EAAI,EAAE;AACnF,CAAC;AACD,OAAO,MAAMQ,iBAAiB,GAAI1C,SAA4B,IAAK;EACjE,MAAM2C,sBAAgC,GAAG,EAAE;EAC3C3C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIxE,qBAAqB,CAACgH,QAAQ,CAAC7F,MAAM,CAACoD,IAAI,CAAClE,KAAK,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC,EAAE2F,sBAAsB,CAACvE,IAAI,CAACgC,SAAS,CAAC;EACvG,CAAC,CAAC;EACFJ,SAAS,CAACY,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB6B,sBAAsB,CAACpE,OAAO,CAACsE,MAAM,IAAI;QACvC,MAAM1C,IAAI,GAAGU,GAAG,CAACI,OAAO,CAAC4B,MAAM,CAAC;QAChC,IAAIC,GAAG,GAAG,OAAO3C,IAAI,CAAClE,KAAK,KAAK,QAAQ,GAAGkE,IAAI,CAAClE,KAAK,CAAC8G,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC/F,IAAI,CAAC,CAAC,GAAGmD,IAAI,CAAClE,KAAK;QACrG,MAAME,GAAG,GAAGC,MAAM,CAAC0G,GAAG,CAAC;QACvB,IAAI,CAACzG,KAAK,CAACF,GAAG,CAAC,IAAI2G,GAAG,KAAK,EAAE,EAAE;UAC7B3C,IAAI,CAAC8B,IAAI,GAAA/C,MAAA,CAAAC,MAAA,KAAQgB,IAAI,CAAC8B,IAAI;YAAEb,KAAK,EAAE;cAAEC,IAAI,EAAElF,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG;YAAW;UAAC,EAAE;UAChF,MAAM6G,MAAM,GAAGhD,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACgB,OAAO,CAAC4B,MAAM,CAAC,CAAC5G,KAAK;UACxD,IAAIJ,4BAA4B,CAAC+G,QAAQ,CAAC7F,MAAM,CAACiG,MAAM,CAAC,CAAChG,IAAI,CAAC,CAAC,CAAC,EAAE;YAChE,IAAIb,GAAG,GAAG,CAAC,EAAE;cACXgE,IAAI,CAAClE,KAAK,GAAG,KAAKuE,IAAI,CAACyC,GAAG,CAAC9G,GAAG,CAAC,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,GAAG;YAC1F,CAAC,MAAM;cACL4D,IAAI,CAAClE,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,EAAE;YAC9E;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAM2G,mBAAmB,GAAG,MAAAA,CACjCC,aAAoB,EACpB1G,QAAe,GAAG,EAAE,EACpB2G,cAAoB,EACpBC,QAAgB,GAAG,+BAA+B,KAC/C;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACH;EACA,IAAI,EAACJ,aAAa,YAAbA,aAAa,CAAEzC,MAAM,GAAE,OAAO,CAAC;EACpC8C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEN,aAAa,CAAC;;EAExD;EACA,IAAIO,aAAa,GAAGN,cAAc,aAAAE,qBAAA,GAAdF,cAAc,CAAEO,SAAS,qBAAzBL,qBAAA,CAA2BxF,OAAO;EACtD,IAAI,CAAC4F,aAAa,IAAIP,aAAa,CAACzC,MAAM,EAAE;IAAA,IAAAkD,eAAA,EAAAC,gBAAA;IAC1CH,aAAa,GAAG,EAAAE,eAAA,GAAAT,aAAa,CAAC,CAAC,CAAC,cAAAS,eAAA,GAAhBA,eAAA,CAAkB9F,OAAO,qBAAzB8F,eAAA,CAA2BF,aAAa,OAAAG,gBAAA,GAAIV,aAAa,CAAC,CAAC,CAAC,qBAAhBU,gBAAA,CAAkBH,aAAa,KAAI,EAAE;IACjG,IAAI,OAAOA,aAAa,KAAK,QAAQ,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MAC1E,MAAMI,IAAI,GAAG/G,MAAM,CAAC2G,aAAa,CAAC;MAClC,IAAII,IAAI,CAACpD,MAAM,KAAK,CAAC,EAAEgD,aAAa,GAAGtH,MAAM,CAAC0H,IAAI,CAACzE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE;EACF;EACA,MAAM0E,UAAU,GAAG,CAAAX,cAAc,aAAAG,sBAAA,GAAdH,cAAc,CAAEO,SAAS,qBAAzBJ,sBAAA,CAA2BQ,UAAU,KAAI,EAAE;EAC9D,MAAMC,cAAc,GAAG,IAAIN,aAAa,IAAIK,UAAU,EAAE;EACxD,MAAME,eAAe,GAAGvI,kBAAkB,CAACsI,cAAc,CAAC;EAC1D,MAAME,UAAU,GAAGb,QAAQ,CAACc,WAAW,CAAC,CAAC,CAACvB,QAAQ,CAAC,UAAU,CAAC;;EAE9D;EACA,MAAMwB,QAAQ,GAAG,IAAI7I,OAAO,CAAC8I,QAAQ,CAAC,CAAC;EACvC,MAAMrE,SAAS,GAAGoE,QAAQ,CAACE,YAAY,CAAC,WAAW,CAAC;EACpDtE,SAAS,CAACuE,MAAM,CAACN,eAAe,CAAC;EACjCjE,SAAS,CAACuE,MAAM,CAAC5I,cAAc,CAAC;;EAEhC;EACA,MAAM8B,IAAW,GAAG,EAAE;;EAEtB;EACA0F,aAAa,CAAC5E,OAAO,CAACiG,QAAQ,IAAI;IAChC;IACA,MAAMpH,YAAY,GAAGZ,WAAW,CAACC,QAAQ,EAAE+H,QAAQ,CAACzG,EAAE,EAAEyG,QAAQ,CAACxG,IAAI,IAAI,EAAE,CAAC;IAC5EP,IAAI,CAACW,IAAI,CAACtC,MAAM,CAAC;MAAEqC,cAAc,EAAE,aAAaqG,QAAQ,CAACzG,EAAE,MAAMX,YAAY;IAAG,CAAC,EACjEoH,QAAQ,CAAC1G,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,UAAU,CAAC,CAAC;;IAEpE;IACA,IAAIyI,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACG,OAAO,CAAC,EAAE;MACnCH,QAAQ,CAACG,OAAO,CAACpG,OAAO,CAACqG,MAAM,IAAI;QACjC,MAAMrH,UAAU,GAAGqH,MAAM,CAAC5G,IAAI,IAAI,UAAU4G,MAAM,CAAC7G,EAAE,EAAE;QACvDN,IAAI,CAACW,IAAI,CAACtC,MAAM,CAAC;UAAEqC,cAAc,EAAE,aAAayG,MAAM,CAAC7G,EAAE,MAAMR,UAAU;QAAG,CAAC,EAC7DqH,MAAM,CAAC9G,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,QAAQ,CAAC,CAAC;;QAEhE;QACA,IAAIyI,KAAK,CAACC,OAAO,CAACE,MAAM,CAACC,WAAW,CAAC,EAAE;UACrCD,MAAM,CAACC,WAAW,CAACtG,OAAO,CAACb,IAAI,IAAI;YACjCoH,wBAAwB,CAACrH,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAEyH,UAAU,EAAE,MAAM,CAAC;UACpE,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;;EAEF;EACAzG,IAAI,CAACc,OAAO,CAACsC,GAAG,IAAIb,SAAS,CAACuE,MAAM,CAACrF,MAAM,CAAC6F,MAAM,CAAClE,GAAG,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAMmE,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC5GA,WAAW,CAACzG,OAAO,CAAC0G,KAAK,IAAIjF,SAAS,CAACkF,UAAU,CAACD,KAAK,CAAC,CAAC;EACzDlF,cAAc,CAACC,SAAS,CAAC;EACzB0C,iBAAiB,CAAC1C,SAAS,CAAC;EAC5BjE,kBAAkB,CAACiE,SAAS,CAAC;;EAE7B;EACAmF,gBAAgB,CAACnF,SAAS,CAAC;;EAE3B;EACA,MAAMoF,YAAY,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtD9J,MAAM,CAAC,IAAI+J,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAE/B,QAAQ,CAAC;AAC5C,CAAC;;AAED;AACA,MAAMyB,wBAAwB,GAAGA,CAACrH,IAAW,EAAEC,IAAS,EAAEjB,QAAe,EAAEkB,SAAkB,GAAG,KAAK,EAAE6H,MAAc,GAAG,EAAE,KAAK;EAAA,IAAAC,WAAA,EAAAC,cAAA;EAC7H,MAAMzI,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAA0H,WAAA,GAAE/H,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAyH,WAAA,GAAI,EAAE,CAAC;EACjE,MAAMxH,OAAO,GAAGP,IAAI,CAACK,EAAE,KAAK,OAAO;EACnC,MAAMG,OAAO,GAAG;IAAEC,cAAc,EAAE,GAAGqH,MAAM,GAAGvH,OAAO,GAAG,OAAO,GAAG,GAAGP,IAAI,CAACK,EAAE,MAAMd,QAAQ,EAAE;EAAG,CAAC;;EAEhG;EACAQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACoC,OAAO,EAAER,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;;EAEzE;EACA,MAAMqC,aAAoC,GAAG,CAAC,CAAC;EAC/C,CAACX,IAAI,CAACY,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAEC,IAAS,IAAK;IAAA,IAAAmH,KAAA,EAAAC,mBAAA;IACxC,MAAMjH,SAAS,IAAAgH,KAAA,IAAAC,mBAAA,GAAGpH,IAAI,CAACI,YAAY,YAAAgH,mBAAA,GAAIpH,IAAI,CAACK,SAAS,YAAA8G,KAAA,GAAI,EAAE;IAC3D,IAAI,CAACtH,aAAa,CAACM,SAAS,CAAC,EAAEN,aAAa,CAACM,SAAS,CAAC,GAAG,EAAE;IAC5DN,aAAa,CAACM,SAAS,CAAC,CAACP,IAAI,CAACI,IAAI,CAAC;EACrC,CAAC,CAAC;EAEF,CAAAkH,cAAA,GAAAhI,IAAI,CAACoB,OAAO,aAAZ4G,cAAA,CAAcnH,OAAO,CAAEQ,MAAW,IAAK;IAAA,IAAA8G,KAAA,EAAAC,qBAAA;IACrC,MAAMnH,SAAS,IAAAkH,KAAA,IAAAC,qBAAA,GAAG/G,MAAM,CAACH,YAAY,YAAAkH,qBAAA,GAAI/G,MAAM,CAACF,SAAS,YAAAgH,KAAA,GAAI,EAAE;IAC/DpI,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;MAAEC,cAAc,EAAE,GAAGqH,MAAM,YAAYpG,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAAE,IAC9FN,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;;IAED;IACA,MAAML,KAAK,GAAGD,aAAa,CAACM,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAMW,WAAW,GAAGhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/C,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,OAAOK,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEFN,WAAW,CAACf,OAAO,CAAEC,IAAS,IAAK;MACjC,IAAIqB,OAAO,GAAG,IAAI;MAClB,IAAIlC,SAAS,IAAI,OAAOa,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3ED,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI,CAAC1B,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,CAAChC,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D;MAEA5B,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;QAAEC,cAAc,EAAE,GAAGqH,MAAM,YAAY3F,OAAO,eAAeA,OAAO;MAAG,IACnFrB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMsF,gBAAgB,GAAInF,SAA4B,IAAK;EACzD,IAAI+F,MAAM,GAAG,CAAC;EACd,MAAMC,SAAS,GAAGhG,SAAS,CAACiG,QAAQ;;EAEpC;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAAA,IAAAC,qBAAA;IACnC,MAAMnF,SAAS,GAAGjE,MAAM,EAAAoJ,qBAAA,GAACnG,SAAS,CAACC,MAAM,CAACiG,CAAC,CAAC,CAACjF,OAAO,CAAC,CAAC,CAAC,CAAChF,KAAK,YAAAkK,qBAAA,GAAI,EAAE,CAAC;IACpE,IAAInF,SAAS,CAAC4B,QAAQ,CAAC,WAAW,CAAC,IAAI,SAAS,CAACH,IAAI,CAACzB,SAAS,CAAChE,IAAI,CAAC,CAAC,CAAC,EAAE;MACvE+I,MAAM,GAAGG,CAAC,GAAG,CAAC;MACd;IACF;IACA,IAAIA,CAAC,KAAKF,SAAS,EAAE;MACnBD,MAAM,GAAGC,SAAS;IACpB;EACF;EAEAhG,SAAS,CAACoG,KAAK,GAAG,CAAC;IAAEC,KAAK,EAAE,QAAQ;IAAEN,MAAM;IAAEO,MAAM,EAAE;EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG,MAAAA,CACrCC,sBAA6B,EAC7BC,oBAA2B,EAC3BhK,QAAe,GAAG,EAAE,EACpB2G,cAAoB,KACjB;EACH,IAAI,EAACoD,sBAAsB,YAAtBA,sBAAsB,CAAE9F,MAAM,KAAI,EAAC+F,oBAAoB,YAApBA,oBAAoB,CAAE/F,MAAM,GAAE;IACpE,OAAOgG,KAAK,CAAC,8BAA8B,CAAC;EAC9C;EACA,MAAMtC,QAAQ,GAAG,IAAI7I,OAAO,CAAC8I,QAAQ,CAAC,CAAC;EACvC,IAAImC,sBAAsB,YAAtBA,sBAAsB,CAAE9F,MAAM,EAAE;IAAA,IAAAiG,sBAAA,EAAAC,sBAAA;IAClC,MAAMC,UAAU,GAAGzC,QAAQ,CAACE,YAAY,CAAC,qBAAqB,CAAC;IAC/D,MAAMZ,aAAa,GAAG,CAAAN,cAAc,aAAAuD,sBAAA,GAAdvD,cAAc,CAAEO,SAAS,qBAAzBgD,sBAAA,CAA2B7I,OAAO,KAAI,EAAE;IAC9D,MAAMiG,UAAU,GAAG,CAAAX,cAAc,aAAAwD,sBAAA,GAAdxD,cAAc,CAAEO,SAAS,qBAAzBiD,sBAAA,CAA2B7C,UAAU,KAAI,EAAE;IAC9D,MAAMC,cAAc,GAAG,IAAIN,aAAa,IAAIK,UAAU,EAAE;IACxD8C,UAAU,CAACtC,MAAM,CAAC7I,kBAAkB,CAACsI,cAAc,CAAC,CAAC;IACrD6C,UAAU,CAACtC,MAAM,CAAC5I,cAAc,CAAC;IACjC6K,sBAAsB,CAACjI,OAAO,CAACb,IAAI,IAAI;MACrC,MAAMD,IAAW,GAAG,EAAE;MACtBD,OAAO,CAACC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,CAAC;MAC7BgB,IAAI,CAACc,OAAO,CAACsC,GAAG,IAAIgG,UAAU,CAACtC,MAAM,CAACrF,MAAM,CAAC6F,MAAM,CAAClE,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC;IACFd,cAAc,CAAC8G,UAAU,CAAC;IAC1BnE,iBAAiB,CAACmE,UAAU,CAAC;IAC7B9K,kBAAkB,CAAC8K,UAAU,CAAC;EAChC;EACA,IAAIJ,oBAAoB,YAApBA,oBAAoB,CAAE/F,MAAM,EAAE;IAAA,IAAAoG,sBAAA,EAAAC,sBAAA;IAChC,MAAMC,UAAU,GAAG5C,QAAQ,CAACE,YAAY,CAAC,mBAAmB,CAAC;IAC7D,MAAMZ,aAAa,GAAG,CAAAN,cAAc,aAAA0D,sBAAA,GAAd1D,cAAc,CAAEO,SAAS,qBAAzBmD,sBAAA,CAA2BhJ,OAAO,KAAI,EAAE;IAC9D,MAAMiG,UAAU,GAAG,CAAAX,cAAc,aAAA2D,sBAAA,GAAd3D,cAAc,CAAEO,SAAS,qBAAzBoD,sBAAA,CAA2BhD,UAAU,KAAI,EAAE;IAC9D,MAAMC,cAAc,GAAG,IAAIN,aAAa,IAAIK,UAAU,EAAE;IACxDiD,UAAU,CAACzC,MAAM,CAAC7I,kBAAkB,CAACsI,cAAc,CAAC,CAAC;IACrDgD,UAAU,CAACzC,MAAM,CAAC5I,cAAc,CAAC;IACjC8K,oBAAoB,CAAClI,OAAO,CAACb,IAAI,IAAI;MACnC,MAAMD,IAAW,GAAG,EAAE;MACtBD,OAAO,CAACC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAE,IAAI,CAAC;MACnCgB,IAAI,CAACc,OAAO,CAACsC,GAAG,IAAImG,UAAU,CAACzC,MAAM,CAACrF,MAAM,CAAC6F,MAAM,CAAClE,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC;IACFd,cAAc,CAACiH,UAAU,CAAC;IAC1BtE,iBAAiB,CAACsE,UAAU,CAAC;IAC7BjL,kBAAkB,CAACiL,UAAU,CAAC;EAChC;EACA,MAAM5B,YAAY,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtD9J,MAAM,CAAC,IAAI+J,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAE,+BAA+B,CAAC;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}