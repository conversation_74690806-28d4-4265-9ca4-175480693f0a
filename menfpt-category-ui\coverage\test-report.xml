<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\util\authProvider.spec.ts">
    <testCase name="authProvider getDefaultHeaders returns correct headers with token" duration="7">
      <failure message="Error: expect(received).toBe(expected) // Object.is equality"><![CDATA[Error: expect(received).toBe(expected) // Object.is equality

Expected: "abc123"
Received: "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ij9KZum9II4bi_0d-T1we3IiR191AcdR2B-ri8jxleBgfXZyKm_vKMPHYrFRY4UcIhEOv0vXTFk--nJFwY3KEzwj9yrYiArR8wzfLNhg6BaLLZCHEOpd4R7zjNs38AtUVU9cIdlod_16YQ8gTnDDSV3oLU3DnFTuUzimwz4hjK_DTHw2SABjYCaDBR4DQIcKRQVJnAXXiF0S_cz72BydklxGx-yl1juV6RDEVCnl8V6z4fCnhkPeLDPsNzJkyOlTnXLrcd2Nieq2vR0H6brw1JEg7TCbbBgRXBBqWClQe31k2FmMwhh2wdX8u8wewPmben5qhMLHOHApkeirORDl4g"
    at Object.toBe (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\util\authProvider.spec.ts:49:38)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="authProvider returns undefined token and triggers alert/redirect if no cookie" duration="2"/>
    <testCase name="authProvider returns accessToken if cookie is present" duration="1"/>
    <testCase name="authProvider handles invalid cookie JSON gracefully" duration="1"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts">
    <testCase name="useWorksheetFilterState GIVEN the filter modal is open AND multiple department selection is enabled WHEN no specific department is initially applied for a selected division THEN it should preselect all available departments for that division" duration="308">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;type&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'type')
    at type (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:111:51)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:433:39
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:441:13
    at mockConstructor (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:154:19)
    at dispatch (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.ts:184:9)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:12999:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14422:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14382:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14369:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14357:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16248:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16197:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16012:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2521:11)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\native\pure.js:73:33)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\core\index.js:114:5)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:241:50)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="useWorksheetFilterState GIVEN the filter modal is open AND multiple department selection is enabled WHEN handleDeskChange is called THEN it should update selectedDesk, reset department/SM/ASM, and update categories" duration="13">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;type&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'type')
    at type (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:111:51)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:433:39
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:441:13
    at mockConstructor (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:154:19)
    at dispatch (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.ts:184:9)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:12999:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14422:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14382:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14369:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14357:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16248:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16197:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16012:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2521:11)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\native\pure.js:73:33)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\core\index.js:114:5)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:304:50)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="useWorksheetFilterState GIVEN the filter modal is open AND multiple department selection is enabled WHEN handleTimeframeChange is called THEN it should update selectedTimeframe" duration="6">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;type&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'type')
    at type (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:111:51)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:433:39
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:441:13
    at mockConstructor (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-mock\build\index.js:154:19)
    at dispatch (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.ts:184:9)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:12999:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14422:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14382:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14369:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14357:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16248:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16197:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:16012:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2521:11)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\native\pure.js:73:33)
    at render (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react-hooks\lib\core\index.js:114:5)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\hooks\useWorksheetFilterState.spec.ts:370:40)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\home.spec.tsx">
    <testCase name="Home renders all main dashboard components" duration="30"/>
    <testCase name="Home shows loading state when filterLoading is true" duration="12">
      <failure message="TestingLibraryElementError: Unable to find an element by: [data-testid=&quot;dashboard-title-component&quot;]"><![CDATA[TestingLibraryElementError: Unable to find an element by: [data-testid="dashboard-title-component"]

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="flex justify-center items-center"
    >
      <div
        class="w-[100px] h-[100px] border-brand border-4 border-dotted rounded-full animate-spin-slow "
      />
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByTestId (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\home.spec.tsx:73:19)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="Home passes correct props to WorksheetFilterContainer" duration="6"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\asmRoleUsersList.spec.tsx">
    <testCase name="AsmRoleUsersList renders ASM list and header" duration="3">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;data&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'data')
    at Object.data (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\asmRoleUsersList.spec.tsx:28:60)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="AsmRoleUsersList shows ClearSelection when more than one ASM" duration="1">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;data&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'data')
    at Object.data (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\asmRoleUsersList.spec.tsx:44:60)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="AsmRoleUsersList handles ASM selection" duration="1">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;data&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'data')
    at Object.data (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\asmRoleUsersList.spec.tsx:60:60)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="AsmRoleUsersList handles empty ASM list" duration="64"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx">
    <testCase name="SmRoleUsersList renders SM list and header" duration="1091">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:25:46)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="SmRoleUsersList shows ClearSelection when more than one SM" duration="80">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:36:35)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="SmRoleUsersList calls handleSmChange and updates selection when SM radio is clicked" duration="66">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:45:35)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="SmRoleUsersList handles empty SM list" duration="46"/>
    <testCase name="SmRoleUsersList shows empty state when no smData and no search" duration="29"/>
    <testCase name="SmRoleUsersList shows empty state with search active and no matching SM" duration="61">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:80:33)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="SmRoleUsersList filters SM list when search is active and cascadeSearchSelectedItemType is sm" duration="68">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:94:37)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="SmRoleUsersList ClearSelection link calls dispatch when clicked" duration="53">
      <failure message="TypeError: str.toLowerCase is not a function"><![CDATA[TypeError: str.toLowerCase is not a function
    at toLowerCase (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:53:16)
    at formatString (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:62:10)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:34:22
    at Array.map (<anonymous>)
    at map (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.tsx:32:63)
    at Component (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:16305:18)
    at renderWithHooks (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:20074:13)
    at mountIndeterminateComponent (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:21587:16)
    at beginWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27426:14)
    at beginWork$1 (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26560:12)
    at performUnitOfWork (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26466:5)
    at workLoopSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26434:7)
    at renderRootSync (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25850:20)
    at recoverFromConcurrentError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:25750:22)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\smRoleUsersList.spec.tsx:104:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\timeframe\timeframeSelector.spec.tsx">
    <testCase name="TimeframeSelector Rendering behavior should display the Timeframe title" duration="1153"/>
    <testCase name="TimeframeSelector Rendering behavior should display all available timeframe options" duration="42"/>
    <testCase name="TimeframeSelector Search functionality should filter timeframes based on search query" duration="918">
      <failure message="TestingLibraryElementError: Unable to find an accessible element with the role &quot;textbox&quot;"><![CDATA[TestingLibraryElementError: Unable to find an accessible element with the role "textbox"

Here are the accessible roles:

  radio:

  Name "Q1 FY2025":
  <input
    checked=""
    class="hidden"
    data-testid="selectable-item-2025-1"
    type="radio"
  />

  Name "Q4 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-4"
    type="radio"
  />

  Name "Q3 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-3"
    type="radio"
  />

  Name "Q2 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-2"
    type="radio"
  />

  --------------------------------------------------
  checkbox:

  Name "Period NaN":
  <input
    class="hidden"
    data-testid="selectable-item-undefined"
    type="checkbox"
  />

  --------------------------------------------------
  button:

  Name "Expand":
  <button
    aria-label="Expand"
    class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
  />

  --------------------------------------------------

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col flex-1 min-h-0"
    >
      <div
        class="flex items-center justify-start text-black text-base font-semibold font-['Nunito_Sans'] leading-normal pb-2"
      >
        Timeframe
      </div>
      <div
        class="pr-[5px] self-stretch flex flex-col justify-start items-start nfpt-scrollbar overflow-y-auto nfpt-scrollbar transition-all duration-200"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="True"
            data-state="Default"
            title="Q1 FY2025"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text after:bg-blue-brand-500 after:content[''] after:rounded-full after:w-[10px] after:h-[10px] hover:after:bg-[#124C81] hover:border-[#124C81] active:after:bg-[#033B69] active:border-[#033B69] hover:bg-white active:bg-white w-5 h-5 border-brand"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q1 FY2025"
                      >
                        Q1 FY2025
                      </span>
                    </div>
                    <input
                      checked=""
                      class="hidden"
                      data-testid="selectable-item-2025-1"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div
            class="bg-white flex flex-col w-full"
          >
            <div
              class="self-stretch flex flex-col justify-start items-start"
              data-scroll-bar="true"
              data-slot="false"
            >
              <div>
                <div
                  class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
                  data-checked="False"
                  data-state="Default"
                  title="Period NaN"
                >
                  <div
                    class="flex items-center justify-between w-full"
                  >
                    <div
                      class="flex-1"
                    >
                      <div
                        class=""
                        data-testid="checkbox-id"
                      >
                        <label
                          class="flex flex-col w-fit cursor-pointer"
                        >
                          <div
                            class="flex items-center"
                          >
                            <input
                              class="hidden"
                              data-testid="selectable-item-undefined"
                              type="checkbox"
                            />
                            <div
                              class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                            />
                            <div
                              class="ml-2"
                            >
                              <span
                                class="ellipsis-label"
                                title="Period NaN"
                              >
                                Period NaN
                              </span>
                            </div>
                          </div>
                        </label>
                      </div>
                    </div>
                    <button
                      aria-label="Expand"
                      class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
                    >
                      <svg
                        class="transition-transform duration-200 "
                        fill="none"
                        height="16"
                        viewBox="0 0 16 16"
                        width="16"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6 12L10 8L6 4"
                          stroke="#1B6EBB"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1.5"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q4 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q4 FY2024"
                      >
                        Q4 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-4"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q3 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q3 FY2024"
                      >
                        Q3 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-3"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q2 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q2 FY2024"
                      >
                        Q2 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-2"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByRole (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\timeframe\timeframeSelector.spec.tsx:94:34)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="TimeframeSelector Search functionality should display empty state when no timeframes match search" duration="702">
      <failure message="TestingLibraryElementError: Unable to find an accessible element with the role &quot;textbox&quot;"><![CDATA[TestingLibraryElementError: Unable to find an accessible element with the role "textbox"

Here are the accessible roles:

  radio:

  Name "Q1 FY2025":
  <input
    checked=""
    class="hidden"
    data-testid="selectable-item-2025-1"
    type="radio"
  />

  Name "Q4 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-4"
    type="radio"
  />

  Name "Q3 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-3"
    type="radio"
  />

  Name "Q2 FY2024":
  <input
    class="hidden"
    data-testid="selectable-item-2024-2"
    type="radio"
  />

  --------------------------------------------------
  checkbox:

  Name "Period NaN":
  <input
    class="hidden"
    data-testid="selectable-item-undefined"
    type="checkbox"
  />

  --------------------------------------------------
  button:

  Name "Expand":
  <button
    aria-label="Expand"
    class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
  />

  --------------------------------------------------

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col flex-1 min-h-0"
    >
      <div
        class="flex items-center justify-start text-black text-base font-semibold font-['Nunito_Sans'] leading-normal pb-2"
      >
        Timeframe
      </div>
      <div
        class="pr-[5px] self-stretch flex flex-col justify-start items-start nfpt-scrollbar overflow-y-auto nfpt-scrollbar transition-all duration-200"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="True"
            data-state="Default"
            title="Q1 FY2025"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text after:bg-blue-brand-500 after:content[''] after:rounded-full after:w-[10px] after:h-[10px] hover:after:bg-[#124C81] hover:border-[#124C81] active:after:bg-[#033B69] active:border-[#033B69] hover:bg-white active:bg-white w-5 h-5 border-brand"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q1 FY2025"
                      >
                        Q1 FY2025
                      </span>
                    </div>
                    <input
                      checked=""
                      class="hidden"
                      data-testid="selectable-item-2025-1"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div
            class="bg-white flex flex-col w-full"
          >
            <div
              class="self-stretch flex flex-col justify-start items-start"
              data-scroll-bar="true"
              data-slot="false"
            >
              <div>
                <div
                  class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
                  data-checked="False"
                  data-state="Default"
                  title="Period NaN"
                >
                  <div
                    class="flex items-center justify-between w-full"
                  >
                    <div
                      class="flex-1"
                    >
                      <div
                        class=""
                        data-testid="checkbox-id"
                      >
                        <label
                          class="flex flex-col w-fit cursor-pointer"
                        >
                          <div
                            class="flex items-center"
                          >
                            <input
                              class="hidden"
                              data-testid="selectable-item-undefined"
                              type="checkbox"
                            />
                            <div
                              class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                            />
                            <div
                              class="ml-2"
                            >
                              <span
                                class="ellipsis-label"
                                title="Period NaN"
                              >
                                Period NaN
                              </span>
                            </div>
                          </div>
                        </label>
                      </div>
                    </div>
                    <button
                      aria-label="Expand"
                      class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
                    >
                      <svg
                        class="transition-transform duration-200 "
                        fill="none"
                        height="16"
                        viewBox="0 0 16 16"
                        width="16"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6 12L10 8L6 4"
                          stroke="#1B6EBB"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1.5"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q4 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q4 FY2024"
                      >
                        Q4 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-4"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q3 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q3 FY2024"
                      >
                        Q3 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-3"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="p-2.5 bg-white rounded-lg flex flex-col justify-start items-start"
            data-checked="False"
            data-state="Default"
            title="Q2 FY2024"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class="flex justify-start items-start "
                >
                  <label
                    class="inline-flex items-center cursor-pointer"
                  >
                    <span
                      class="flex items-center justify-center bg-white border-[2px] rounded-full hover:bg-[#E0EDFB] active:bg-[#BCDFFD] focus-visible:ring-4 focus-visible:ring-[#C8DAEB] focus-visible:ring-offset-2 focus-visible:!border-light-text w-5 h-5 border-light-text"
                      data-testid="radio-check"
                      tabindex="0"
                    />
                    <div
                      class="ml-2"
                    >
                      <span
                        class="ellipsis-label"
                        title="Q2 FY2024"
                      >
                        Q2 FY2024
                      </span>
                    </div>
                    <input
                      class="hidden"
                      data-testid="selectable-item-2024-2"
                      type="radio"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByRole (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\timeframe\timeframeSelector.spec.tsx:111:34)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="TimeframeSelector Timeframe selection should call onTimeframeChange when a timeframe is selected" duration="43"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\periodClose\modals\periodModalsContainer.spec.tsx">
    <testCase name="usePeriodModalsContainer renders without modal when worksheetDataLoaded is false" duration="372"/>
    <testCase name="usePeriodModalsContainer opens period close modal when quarter is open and not dismissed" duration="58"/>
    <testCase name="usePeriodModalsContainer opens period locked modal when quarter is locked and not dismissed" duration="19"/>
    <testCase name="usePeriodModalsContainer does not open modal when close alert is already dismissed" duration="8"/>
    <testCase name="usePeriodModalsContainer does not open modal when locked alert is already dismissed" duration="6"/>
    <testCase name="usePeriodModalsContainer handles corrupted localStorage data" duration="14"/>
    <testCase name="usePeriodModalsContainer saves dismissal to localStorage when modal is opened" duration="14"/>
    <testCase name="usePeriodModalsContainer handles missing prevQuarterTabData" duration="4"/>
    <testCase name="usePeriodModalsContainer handles undefined currentQuarterNbr" duration="5"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx">
    <testCase name="PeriodSelector renders message if no periods available" duration="45"/>
    <testCase name="PeriodSelector renders periods when available" duration="23">
      <failure message="TestingLibraryElementError: Unable to find an element with the text: Period 1. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."><![CDATA[TestingLibraryElementError: Unable to find an element with the text: Period 1. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="flex items-center justify-center py-4 w-full text-gray-500"
      >
        Please select Quarter to view
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:31:19)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector calls onPeriodChange when a period is selected" duration="12">
      <failure message="TestingLibraryElementError: Unable to find an element with the text: Period 1. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."><![CDATA[TestingLibraryElementError: Unable to find an element with the text: Period 1. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="flex items-center justify-center py-4 w-full text-gray-500"
      >
        Please select Quarter to view
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:38:34)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector calls onWeeksChange when a week is selected" duration="15">
      <failure message="TestingLibraryElementError: Unable to find a label with the text of: Expand"><![CDATA[TestingLibraryElementError: Unable to find a label with the text of: Expand

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="flex items-center justify-center py-4 w-full text-gray-500"
      >
        Please select Quarter to view
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\queries\label-text.js:103:38)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:109:15)
    at Object.getAllByLabelText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:47:30)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector shows indeterminate state for periods with some weeks selected" duration="6">
      <failure message="TestingLibraryElementError: Unable to find a label with the text of: Expand"><![CDATA[TestingLibraryElementError: Unable to find a label with the text of: Expand

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="flex items-center justify-center py-4 w-full text-gray-500"
      >
        Please select Quarter to view
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\queries\label-text.js:103:38)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:109:15)
    at Object.getAllByLabelText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:61:30)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector calls onWeeksChange when a week is toggled" duration="1245">
      <failure message="Error: Unable to find a label with the text of: Week 1"><![CDATA[Error: Unable to find a label with the text of: Week 1

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="self-stretch flex flex-col justify-start items-start"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="False"
            data-state="Default"
            title="Period 1"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-1"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                      />
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 1"
                        >
                          Period 1
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="True"
            data-state="Default"
            title="Period 2"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-2"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 bg-brand border-brand hover:border-brand-medium active:border-blue-301 hover:bg-brand-medium active:bg-blue-301"
                      >
                        <svg
                          class="lucide lucide-check"
                          fill="none"
                          height="14"
                          stroke="#FFFFFF"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="4"
                          viewBox="0 0 24 24"
                          width="14"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <polyline
                            points="20 6 9 17 4 12"
                          />
                        </svg>
                      </div>
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 2"
                        >
                          Period 2
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at waitForWrapper (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\wait-for.js:166:27)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:86:32
    at Object.findByLabelText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:136:32)]]></failure>
    </testCase>
    <testCase name="PeriodSelector calls onPeriodChange and onWeeksChange when select all is clicked" duration="30">
      <failure message="TestingLibraryElementError: Unable to find a label with the text of: /select/i"><![CDATA[TestingLibraryElementError: Unable to find a label with the text of: /select/i

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="self-stretch flex flex-col justify-start items-start"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="True"
            data-state="Default"
            title="Period 1"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-1"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 bg-brand border-brand hover:border-brand-medium active:border-blue-301 hover:bg-brand-medium active:bg-blue-301"
                      >
                        <svg
                          class="lucide lucide-check"
                          fill="none"
                          height="14"
                          stroke="#FFFFFF"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="4"
                          viewBox="0 0 24 24"
                          width="14"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <polyline
                            points="20 6 9 17 4 12"
                          />
                        </svg>
                      </div>
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 1"
                        >
                          Period 1
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="True"
            data-state="Default"
            title="Period 2"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-2"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 bg-brand border-brand hover:border-brand-medium active:border-blue-301 hover:bg-brand-medium active:bg-blue-301"
                      >
                        <svg
                          class="lucide lucide-check"
                          fill="none"
                          height="14"
                          stroke="#FFFFFF"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="4"
                          viewBox="0 0 24 24"
                          width="14"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <polyline
                            points="20 6 9 17 4 12"
                          />
                        </svg>
                      </div>
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 2"
                        >
                          Period 2
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\queries\label-text.js:103:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByLabelText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:165:30)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector renders without crashing" duration="3"/>
    <testCase name="PeriodSelector renders periods and alert" duration="20">
      <failure message="TestingLibraryElementError: Unable to find an element with the text: Please select period(s). This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."><![CDATA[TestingLibraryElementError: Unable to find an element with the text: Please select period(s). This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="self-stretch flex flex-col justify-start items-start"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="False"
            data-state="Default"
            title="Period 1"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-1"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                      />
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 1"
                        >
                          Period 1
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="False"
            data-state="Default"
            title="Period 2"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-2"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                      />
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 2"
                        >
                          Period 2
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:76:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:203:17)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector calls onPeriodChange when a period is clicked" duration="72"/>
    <testCase name="PeriodSelector selects all periods when select all checkbox is clicked" duration="28">
      <failure message="TestingLibraryElementError: Unable to find a label with the text of: /select/i"><![CDATA[TestingLibraryElementError: Unable to find a label with the text of: /select/i

Ignored nodes: comments, script, style
<body>
  <div>
    <div
      class="bg-white flex flex-col w-full"
    >
      <div
        class="self-stretch flex flex-col justify-start items-start"
        data-scroll-bar="true"
        data-slot="false"
      >
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="False"
            data-state="Default"
            title="Period 1"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-1"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                      />
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 1"
                        >
                          Period 1
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div>
          <div
            class="py-2.5 pl-2.5 bg-white rounded-lg flex flex-col justify-start items-start gap-2"
            data-checked="False"
            data-state="Default"
            title="Period 2"
          >
            <div
              class="flex items-center justify-between w-full"
            >
              <div
                class="flex-1"
              >
                <div
                  class=""
                  data-testid="checkbox-id"
                >
                  <label
                    class="flex flex-col w-fit cursor-pointer"
                  >
                    <div
                      class="flex items-center"
                    >
                      <input
                        class="hidden"
                        data-testid="selectable-item-2"
                        type="checkbox"
                      />
                      <div
                        class="flex items-center justify-center border-2 rounded-sm focus-visible:ring-4 focus-visible:ring-gray-204 focus-visible:ring-offset-2 w-5 h-5 hover:bg-blue-306 active:bg-blue-305  border-light-text"
                      />
                      <div
                        class="ml-2"
                      >
                        <span
                          class="ellipsis-label"
                          title="Period 2"
                        >
                          Period 2
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <button
                aria-label="Expand"
                class="hover:bg-gray-100 rounded transition-colors duration-200 flex items-center justify-center ml-2"
              >
                <svg
                  class="transition-transform duration-200 "
                  fill="none"
                  height="16"
                  viewBox="0 0 16 16"
                  width="16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="#1B6EBB"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
    at Object.getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\config.js:37:19)
    at getElementError (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\queries\label-text.js:103:38)
    at allQuery (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:52:17)
    at query (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\dom\dist\query-helpers.js:95:19)
    at Object.getByLabelText (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\period\periodSelector.spec.tsx:234:28)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="PeriodSelector shows alert when no quarter is selected" duration="6"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx">
    <testCase name="DepartmentDeskSelector renders with desk display enabled" duration="796">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:42:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector renders with multiple departments selection allowed" duration="37">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:55:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector renders AlertBox when no department is selected and multiple selection is allowed" duration="47"/>
    <testCase name="DepartmentDeskSelector calls onDepartmentChange with array when multiple selection is allowed" duration="15">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:84:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector filters departments by search" duration="20">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:100:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector filters desks by search" duration="15">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:113:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector maps selectedDepartment from string to object" duration="24">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:128:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector maps selectedDepartment from array of strings to objects" duration="21"/>
    <testCase name="DepartmentDeskSelector calls onDepartmentChange when department is clicked" duration="16">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:152:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector calls onDeskChange when desk is changed" duration="15">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:167:11)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector renders desk tab and allows desk selection" duration="14">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.handlers.ts:59:46)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.tsx:62:35
    at handleNormalSmDataChange (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\department\departmentSelection.effects.ts:23:5)
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:189:13)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector renders with cascade role logic" duration="19">
      <failure message="TypeError: selectedDeptId?.includes is not a function"><![CDATA[TypeError: selectedDeptId?.includes is not a function
    at includes (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:5:28)
    at Array.filter (<anonymous>)
    at filter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:4:38)
    at filterDesksByDivisionAndDept (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\roles\rolesUtils.ts:13:35)
    at C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.tsx:56:59
    at create (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:23150:26)
    at commitHookEffectListMount (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24931:11)
    at commitPassiveMountOnFiber (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24891:9)
    at commitPassiveMountEffects_complete (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24878:7)
    at commitPassiveMountEffects_begin (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:24866:3)
    at commitPassiveMountEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:27039:3)
    at flushPassiveEffectsImpl (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26984:14)
    at flushPassiveEffects (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react-dom\cjs\react-dom.development.js:26769:9)
    at callback (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2667:24)
    at flushActQueue (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\react\cjs\react.development.js:2582:11)
    at actImplementation (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\act-compat.js:63:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:159:25)
    at renderRoot (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\@testing-library\react\dist\pure.js:246:10)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\departmentDeskSelector.spec.tsx:209:13)
    at Promise.then.completed (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:333:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\utils.js:259:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:277:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:209:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:97:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:91:9)
    at run (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\run.js:31:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:135:21)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:74:19)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="DepartmentDeskSelector renders with empty departments and desks" duration="6"/>
    <testCase name="DepartmentDeskSelector renders AlertBox with custom props" duration="56"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\dashboard-title-component.spec.tsx">
    <testCase name="TitleComponent renders the title from menfptCategoryTitle" duration="44"/>
    <testCase name="TitleComponent renders HelpIcon" duration="115"/>
    <testCase name="TitleComponent renders Select components when FeatureFlags is true" duration="40"/>
    <testCase name="TitleComponent does not render Select components when FeatureFlags is false" duration="10"/>
    <testCase name="TitleComponent handles Select onChange events" duration="25"/>
    <testCase name="TitleComponent handles empty menfptCategoryTitle" duration="44"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\_tests_\dashboard-tabs.spec.tsx">
    <testCase name="DashboardTabs Coverage Tests Basic Rendering should render without crashing" duration="926"/>
    <testCase name="DashboardTabs Coverage Tests Basic Rendering should call useSelectorWrap multiple times" duration="17"/>
    <testCase name="DashboardTabs Coverage Tests Basic Rendering should handle component initialization" duration="24"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle workSheetFilterList_rn selector" duration="13"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle displayDate_rn selector" duration="15"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle appliedFilter_rn selector" duration="13"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle null selector returns" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle undefined selector returns" duration="15"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle empty data objects" duration="12"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle missing smicData" duration="20"/>
    <testCase name="DashboardTabs Coverage Tests State Management Coverage should handle empty smicData array" duration="17"/>
    <testCase name="DashboardTabs Coverage Tests Component Lifecycle Coverage should handle mount and unmount" duration="16"/>
    <testCase name="DashboardTabs Coverage Tests Component Lifecycle Coverage should handle re-rendering" duration="25"/>
    <testCase name="DashboardTabs Coverage Tests Component Lifecycle Coverage should handle multiple instantiations" duration="20"/>
    <testCase name="DashboardTabs Coverage Tests Error Handling Coverage should handle selector errors gracefully" duration="45"/>
    <testCase name="DashboardTabs Coverage Tests Error Handling Coverage should handle malformed data" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Error Handling Coverage should handle deeply nested null values" duration="13"/>
    <testCase name="DashboardTabs Coverage Tests Data Structure Coverage should handle complex data structures" duration="13"/>
    <testCase name="DashboardTabs Coverage Tests Data Structure Coverage should handle boolean data types" duration="14"/>
    <testCase name="DashboardTabs Coverage Tests Data Structure Coverage should handle numeric data types" duration="14"/>
    <testCase name="DashboardTabs Coverage Tests Data Structure Coverage should handle string data types" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Data Structure Coverage should handle array data types" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Edge Cases Coverage should handle very large datasets" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests Edge Cases Coverage should handle empty string values" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Edge Cases Coverage should handle zero values" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Edge Cases Coverage should handle NaN values" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Edge Cases Coverage should handle Infinity values" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Function Coverage should handle function selector calls" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Function Coverage should handle multiple selector types" duration="18"/>
    <testCase name="DashboardTabs Coverage Tests Integration Coverage should work with different store configurations" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Integration Coverage should handle store state changes" duration="14"/>
    <testCase name="DashboardTabs Coverage Tests Performance Coverage should handle rapid re-renders" duration="42"/>
    <testCase name="DashboardTabs Coverage Tests Performance Coverage should handle component stress testing" duration="37"/>
    <testCase name="DashboardTabs Coverage Tests User Interaction Coverage should handle tab switching" duration="29"/>
    <testCase name="DashboardTabs Coverage Tests User Interaction Coverage should handle drawer operations" duration="14"/>
    <testCase name="DashboardTabs Coverage Tests User Interaction Coverage should handle download button interactions" duration="17"/>
    <testCase name="DashboardTabs Coverage Tests User Interaction Coverage should handle sync monitor button click" duration="12"/>
    <testCase name="DashboardTabs Coverage Tests Component State Coverage should handle different tab selections" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Component State Coverage should handle loading states" duration="16"/>
    <testCase name="DashboardTabs Coverage Tests Component State Coverage should handle data arrays with content" duration="15"/>
    <testCase name="DashboardTabs Coverage Tests Data Loading Coverage should handle performance summary callback execution" duration="8"/>
    <testCase name="DashboardTabs Coverage Tests Data Loading Coverage should handle forecast variance callback execution" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Data Loading Coverage should handle dashboard loading state transitions" duration="15"/>
    <testCase name="DashboardTabs Coverage Tests Conditional Rendering Coverage should render correct tab content based on selection" duration="12"/>
    <testCase name="DashboardTabs Coverage Tests Conditional Rendering Coverage should handle download button conditional logic" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Conditional Rendering Coverage should handle tooltip and alert icon rendering" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Conditional Rendering Coverage should handle drawer content and header" duration="12"/>
    <testCase name="DashboardTabs Coverage Tests Event Handler Coverage should handle week change functionality" duration="21"/>
    <testCase name="DashboardTabs Coverage Tests Event Handler Coverage should handle data loading callbacks" duration="19"/>
    <testCase name="DashboardTabs Coverage Tests Event Handler Coverage should handle download click events" duration="11"/>
    <testCase name="DashboardTabs Coverage Tests Complex Data Scenarios should handle nested data structures" duration="16"/>
    <testCase name="DashboardTabs Coverage Tests Complex Data Scenarios should handle mixed data types in selectors" duration="9"/>
    <testCase name="DashboardTabs Coverage Tests Complex Data Scenarios should handle array data with various structures" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests Integration Scenarios should handle component with all features active" duration="19"/>
    <testCase name="DashboardTabs Coverage Tests Integration Scenarios should handle re-rendering with different props" duration="14"/>
    <testCase name="DashboardTabs Coverage Tests Integration Scenarios should handle component mount and unmount cycles" duration="29"/>
    <testCase name="DashboardTabs Coverage Tests Edge Case Handling should handle selectors returning functions" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests Edge Case Handling should handle circular reference in data" duration="7"/>
    <testCase name="DashboardTabs Coverage Tests Edge Case Handling should handle extremely large data sets" duration="648"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover download button enabled state with performance summary data" duration="8"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover download button enabled state with forecast variance data" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover tab rendering with different configurations" duration="15"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover switch case logic for tab content rendering" duration="7"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover loading state with dashboard loading true" duration="16"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover download disabled state logic" duration="7"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should test performance summary data loading callback" duration="10"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should test forecast variance data loading callback" duration="6"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover conditional rendering of performance variance tooltip" duration="6"/>
    <testCase name="DashboardTabs Coverage Tests Targeted Coverage Tests should cover download click functionality" duration="10"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\worksheetFilterContainer.spec.tsx">
    <testCase name="WorksheetFilterContainer should render without crashing" duration="38"/>
    <testCase name="WorksheetFilterContainer should call openFilterModal when passed as prop" duration="93"/>
    <testCase name="WorksheetFilterContainer should handle modal open/close logic" duration="54"/>
    <testCase name="WorksheetFilterContainer should handle empty FiltersList gracefully" duration="5"/>
    <testCase name="WorksheetFilterContainer should not crash if localStorage returns empty object" duration="7"/>
    <testCase name="WorksheetFilterContainer should apply filters and close modal when onApply is called" duration="45"/>
    <testCase name="WorksheetFilterContainer should handle missing division and desk props gracefully" duration="8"/>
    <testCase name="WorksheetFilterContainer should initialize division from FiltersList" duration="10"/>
    <testCase name="WorksheetFilterContainer should updateInvalidDivisions when division mismatch" duration="12"/>
    <testCase name="WorksheetFilterContainer should apply stored preferences with department and desk" duration="8"/>
    <testCase name="WorksheetFilterContainer should call handleApplyFilters and updateAppliedFilters" duration="55"/>
    <testCase name="WorksheetFilterContainer should call historyModalOpen and resetEditMessage handlers" duration="10"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\deptDesk\departmentDeskTabs.spec.tsx">
    <testCase name="DepartmentDeskTabs renders department and desk tabs" duration="507"/>
    <testCase name="DepartmentDeskTabs calls onDepartmentChange when department changes" duration="60"/>
    <testCase name="DepartmentDeskTabs calls onDeskChange when desk changes" duration="55"/>
    <testCase name="DepartmentDeskTabs shows SmRoleUsersList when isDisplayDeptRoleCascade is true" duration="38"/>
    <testCase name="DepartmentDeskTabs handles empty departments and desks" duration="42"/>
    <testCase name="DepartmentDeskTabs handles multiple department selection" duration="56"/>
    <testCase name="DepartmentDeskTabs handles search queries for department and desk" duration="42"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\worksheet-table-container.spec.tsx">
    <testCase name="renders Spinner when isLoading is true" duration="623"/>
    <testCase name="handles error in fetchWorksheetData gracefully" duration="12"/>
    <testCase name="renders with different appliedFilters and selectedQuarter" duration="25"/>
    <testCase name="TableContainer renders modals only when worksheet data is loaded" duration="18"/>
    <testCase name="TableContainer triggers onEditForecast and opens modal" duration="43"/>
    <testCase name="TableContainer renders Spinner when isLoading is true" duration="8"/>
    <testCase name="TableContainer handles error in fetchWorksheetData gracefully" duration="7"/>
    <testCase name="TableContainer renders with different appliedFilters and selectedQuarter" duration="9"/>
    <testCase name="TableContainer renders Spinner when loading" duration="14"/>
    <testCase name="TableContainer renders UdsTable when not loading" duration="8"/>
    <testCase name="TableContainer handles empty filterList gracefully" duration="8"/>
    <testCase name="TableContainer renders modals when worksheet data is loaded" duration="10"/>
    <testCase name="TableContainer renders modals when worksheet data is loaded" duration="13"/>
    <testCase name="TableContainer calls resetWeek and triggers saveAdjustment" duration="20"/>
    <testCase name="TableContainer calls onEditForecast and updates state" duration="11"/>
    <testCase name="TableContainer renders ForecastEdit when modal is open" duration="11"/>
    <testCase name="TableContainer renders with selectedQuarter prop" duration="8"/>
    <testCase name="TableContainer handles edge case: no department, desk, division, or category" duration="8"/>
    <testCase name="TableContainer handles edge case: undefined filterList" duration="17"/>
    <testCase name="TableContainer handles edge case: null filterList" duration="9"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\_tests_\history-drawer.spec.tsx">
    <testCase name="History Drawer Component Test Suite should render the history drawer" duration="58"/>
    <testCase name="History Drawer Component Test Suite should show correct text when no data found" duration="26"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\udsTable.spec.tsx">
    <testCase name="UdsTable renders table with rows" duration="528"/>
    <testCase name="UdsTable renders footer with formatted dates" duration="26"/>
    <testCase name="UdsTable handles empty data gracefully" duration="21"/>
    <testCase name="UdsTable handles edge cases: zero values" duration="26"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\weekSelection.spec.tsx">
    <testCase name="WeekSelection Component renders week numbers correctly" duration="315"/>
    <testCase name="WeekSelection Component calls onWeekSelect with correct weeks when multiple weeks are selected" duration="10"/>
    <testCase name="WeekSelection Component renders custom styles for error and valid weeks" duration="17"/>
    <testCase name="WeekSelection Component deselects a week correctly" duration="9"/>
    <testCase name="WeekSelection Component handles single week selection mode correctly" duration="10"/>
    <testCase name="WeekSelection Component does not select any week initially" duration="19"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\AllocatrInsights\AllocatrInsightsTable.spec.tsx">
    <testCase name="AllocatrInsightsTable renders table headers" duration="30"/>
    <testCase name="AllocatrInsightsTable renders department row with id and name" duration="377"/>
    <testCase name="AllocatrInsightsTable handles empty departments" duration="14"/>
    <testCase name="AllocatrInsightsTable handles departments with id Total" duration="23"/>
    <testCase name="AllocatrInsightsTable renders with a different selectedTab (should still render table)" duration="16"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\RxForecast\uploadDocument.spec.tsx">
    <testCase name="UploadDocument Component Rendering renders the upload document component correctly" duration="44"/>
    <testCase name="UploadDocument Component Rendering renders upload warning when upload is not allowed" duration="10"/>
    <testCase name="UploadDocument Component Rendering renders upload section when upload is allowed" duration="13"/>
    <testCase name="UploadDocument Component Rendering initially shows no file statuses" duration="14"/>
    <testCase name="UploadDocument Upload Day Validation prevents upload when not allowed and shows alert" duration="10"/>
    <testCase name="UploadDocument Upload Day Validation shows file upload functionality when upload is allowed" duration="10"/>
    <testCase name="UploadDocument File Validation handles valid files successfully" duration="58"/>
    <testCase name="UploadDocument File Validation handles oversized files" duration="14"/>
    <testCase name="UploadDocument File Validation handles empty file list" duration="12"/>
    <testCase name="UploadDocument File Upload Process handles single file upload successfully" duration="16"/>
    <testCase name="UploadDocument File Upload Process handles multiple file uploads" duration="22"/>
    <testCase name="UploadDocument File Upload Process handles mixed success and failure" duration="13"/>
    <testCase name="UploadDocument API Response Handling handles successful response" duration="27"/>
    <testCase name="UploadDocument API Response Handling handles response with errors" duration="29"/>
    <testCase name="UploadDocument API Response Handling handles response without data" duration="17"/>
    <testCase name="UploadDocument API Response Handling handles response with success: false" duration="27"/>
    <testCase name="UploadDocument API Response Handling handles unexpected response structure" duration="16"/>
    <testCase name="UploadDocument API Response Handling handles network/upload errors" duration="33"/>
    <testCase name="UploadDocument Alert Interactions closes error alert when close button is clicked" duration="16"/>
    <testCase name="UploadDocument Alert Interactions closes success alert when close button is clicked" duration="34"/>
    <testCase name="UploadDocument Edge Cases and Error Handling handles missing user info gracefully" duration="18"/>
    <testCase name="UploadDocument Edge Cases and Error Handling handles missing display date gracefully" duration="23"/>
    <testCase name="UploadDocument Edge Cases and Error Handling handles file reading errors" duration="29"/>
    <testCase name="UploadDocument Edge Cases and Error Handling handles contractor user name correctly" duration="36"/>
    <testCase name="UploadDocument Edge Cases and Error Handling handles undefined env variables" duration="12"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\RxForecast\rxforecastDocuments.spec.tsx">
    <testCase name="Documents renders loading state initially" duration="14"/>
    <testCase name="Documents renders files after fetch" duration="139"/>
    <testCase name="Documents removes spinner after files are loaded" duration="14"/>
    <testCase name="Documents shows alert if download fails" duration="50"/>
    <testCase name="Documents renders no files if API returns empty" duration="9"/>
    <testCase name="Documents displays NoDocumentsMessage when no files are available" duration="13"/>
    <testCase name="Documents does not display NoDocumentsMessage when files are available" duration="32"/>
    <testCase name="Documents does not display NoDocumentsMessage when loading" duration="6"/>
    <testCase name="Documents displays NoDocumentsMessage when API returns undefined uploadedDocuments" duration="8"/>
    <testCase name="Documents renders correct display name for files" duration="11"/>
    <testCase name="Documents does not render download button if fileContent is missing" duration="21"/>
    <testCase name="Documents should call API with correct params" duration="6"/>
    <testCase name="Documents should not break if userInfo or displayDate is missing" duration="3"/>
    <testCase name="Documents handles API error gracefully and hides spinner" duration="7"/>
    <testCase name="Documents renders nothing if fileName is empty" duration="10"/>
    <testCase name="Documents shows spinner when resetLoader is true" duration="12"/>
    <testCase name="Documents renders correctly if API returns undefined uploadedDocuments" duration="9"/>
    <testCase name="handleDownload should create a download link and trigger click with correct filename" duration="3"/>
    <testCase name="handleDownload should alert if atob throws" duration="1"/>
    <testCase name="getUniqueFiles returns unique files when there are duplicates" duration="1"/>
    <testCase name="getUniqueFiles returns empty array if input is empty" duration="0"/>
    <testCase name="getUniqueFiles handles files with extra spaces and commas" duration="1"/>
    <testCase name="getUniqueFiles does not add file if fileName does not match files entry" duration="0"/>
    <testCase name="getUniqueFiles ignores empty file names in files string" duration="0"/>
    <testCase name="getUniqueFiles handles duplicate fileName with different content, keeps first" duration="0"/>
    <testCase name="getDisplayName removes suffix from file name" duration="0"/>
    <testCase name="calculateFileSize Bytes calculation calculates size for small content (&lt; 1024 bytes)" duration="0"/>
    <testCase name="calculateFileSize Bytes calculation calculates size for content under 1024 bytes" duration="2"/>
    <testCase name="calculateFileSize Bytes calculation calculates size for content with no padding" duration="0"/>
    <testCase name="calculateFileSize Bytes calculation calculates size for content with single padding" duration="0"/>
    <testCase name="calculateFileSize Bytes calculation calculates size for content with double padding" duration="0"/>
    <testCase name="calculateFileSize Data URI handling strips data URI prefix before calculation" duration="1"/>
    <testCase name="calculateFileSize Data URI handling handles different MIME types in data URI" duration="1"/>
    <testCase name="calculateFileSize Edge cases and error handling returns &quot;Unknown size&quot; when an error occurs" duration="1"/>
    <testCase name="calculateFileSize Edge cases and error handling handles empty base64 string" duration="0"/>
    <testCase name="calculateFileSize Edge cases and error handling handles null and undefined input gracefully" duration="1"/>
    <testCase name="calculateFileSize Edge cases and error handling calculates correct size with mixed padding scenarios" duration="1"/>
    <testCase name="calculateFileSize Precision and rounding rounds bytes to whole numbers" duration="0"/>
    <testCase name="calculateFileSize Precision and rounding displays KB with 1 decimal place" duration="1"/>
    <testCase name="calculateFileSize Precision and rounding displays MB with 1 decimal place" duration="301"/>
    <testCase name="handleDownload implementation (download logic) creates a Blob, link, triggers click, and revokes URL" duration="4"/>
    <testCase name="handleDownload implementation (download logic) appends .xlsx if cleanedFileName does not end with .xlsx" duration="2"/>
    <testCase name="handleDownload implementation (download logic) alerts if atob throws and does not call revokeObjectURL" duration="1"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\SnapShotDropDown\release-week-select.spec.tsx">
    <testCase name="SelectWeek renders Select with options for Performance Summary tab" duration="304"/>
    <testCase name="SelectWeek does not render Select for other tabs" duration="3"/>
    <testCase name="SelectWeek calls weekChange and dispatches on option change" duration="14"/>
    <testCase name="SelectWeek handles empty calendarWeek" duration="7"/>
    <testCase name="SelectWeek handles missing appliedFilters and displayDate" duration="7"/>
    <testCase name="SelectWeek handles non-current quarter" duration="6"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\periodClose\usePeriodCloseEffect.spec.ts">
    <testCase name="usePeriodCloseEffect returns lastQuarterNbr from useLastQuarterNbr" duration="15"/>
    <testCase name="usePeriodCloseEffect calls handleCalendarApiResp if type is quarterDisplayedInTable_periodClose" duration="3"/>
    <testCase name="usePeriodCloseEffect does not call handleCalendarApiResp if type is not quarterDisplayedInTable_periodClose" duration="3"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\DashboardDownloadExcel.spec.tsx">
    <testCase name="handleDownloadExcel should call saveAs with the correct filename for both summary and variance" duration="5">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any<Blob>, "Allocatr Insights Performance and Variance Mock Excel Download-2025-09-16.xlsx"

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\DashboardDownloadExcel.spec.tsx:37:30)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)]]></failure>
    </testCase>
    <testCase name="getDeptName should return the department name from smicData if found" duration="1"/>
    <testCase name="getDeptName should fallback to provided fallback if not found" duration="1"/>
    <testCase name="getDeptName should return empty string if both missing" duration="1"/>
    <testCase name="getDeptName should trim deptId and match correctly" duration="1"/>
    <testCase name="getDeptName should handle null deptName in smicData" duration="1"/>
    <testCase name="getDivisionName should return formatted division name with fallback" duration="1"/>
    <testCase name="getDivisionName should return default division name when fallback is empty" duration="1"/>
    <testCase name="getBannerName should return formatted banner name from smicData" duration="1"/>
    <testCase name="getBannerName should return formatted fallback when banner not found in smicData" duration="0"/>
    <testCase name="getBannerName should return bannerId when no fallback and not found in smicData" duration="0"/>
    <testCase name="formatCurrency should return &quot;&quot; for null, undefined, or empty string" duration="1"/>
    <testCase name="formatCurrency should format numbers as $X,XXX" duration="6"/>
    <testCase name="formatCurrency should return original value if not a number" duration="1"/>
    <testCase name="formatCurrency should handle negative numbers" duration="1"/>
    <testCase name="formatCurrency should handle zero" duration="1"/>
    <testCase name="formatCurrency should handle numbers with decimals" duration="1"/>
    <testCase name="print settings functionality getDefaultPrintSettings should return correct default settings" duration="2"/>
    <testCase name="print settings functionality applyPrintSettings should set worksheet.pageSetup with correct values" duration="1"/>
    <testCase name="print settings functionality applyPrintSettings should overwrite existing pageSetup" duration="1"/>
    <testCase name="print settings functionality applyPrintSettings should work if worksheet.pageSetup is undefined" duration="1"/>
    <testCase name="print settings functionality getDefaultPrintSettings returns a new object each time" duration="0"/>
    <testCase name="handleDownloadExcel with new divisions structure should handle new divisions and banners structure with Total row first" duration="109"/>
    <testCase name="handleDownloadExcel with new divisions structure should handle empty divisions array" duration="21"/>
    <testCase name="handleDownloadExcel with new divisions structure should fallback to old structure when divisions not present" duration="16"/>
    <testCase name="handleDownloadExcel with new divisions structure should include Total row with periods and weeks at the beginning" duration="18"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\HelpIcon.spec.tsx">
    <testCase name="HelpIcon Component renders the default help icon correctly" duration="19"/>
    <testCase name="HelpIcon Component renders the dashboard variant correctly" duration="4"/>
    <testCase name="HelpIcon Component renders the dashboard-title variant correctly" duration="3"/>
    <testCase name="HelpIcon Component applies additional className when provided" duration="3"/>
    <testCase name="HelpIcon Component has the correct URL in the href attribute" duration="50"/>
    <testCase name="HelpIcon Component opens in a new tab when clicked" duration="8"/>
    <testCase name="HelpIcon Component opens the view URL in a new tab when clicked" duration="0"/>
    <testCase name="HelpIcon Component logs URLs to console when clicked" duration="0"/>
    <testCase name="HelpIcon Component calls getEndpoints to get the correct URLs" duration="0"/>
    <testCase name="HelpIcon Component handles errors when getEndpoints fails" duration="0"/>
    <testCase name="HelpIcon Component uses correct icon source based on variant" duration="4"/>
    <testCase name="HelpIcon Component renders with correct title attribute" duration="3"/>
    <testCase name="HelpIcon Component renders inside a container div" duration="5"/>
    <testCase name="HelpIcon Component handles local environment correctly" duration="0"/>
    <testCase name="HelpIcon Component handles null or undefined props gracefully" duration="5"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\quarterTabs.spec.tsx">
    <testCase name="QuarterTabs should not render if data is missing" duration="4"/>
    <testCase name="QuarterTabs should render tabs with correct labels" duration="412"/>
    <testCase name="QuarterTabs should call onQuarterChange and dispatch correct action when last quarter tab is clicked" duration="14"/>
    <testCase name="QuarterTabs should call onQuarterChange and dispatch correct action when current quarter tab is clicked" duration="12"/>
    <testCase name="QuarterTabs should show correct tooltip message for unlocked and locked states" duration="24"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\division\divisionSelector.spec.tsx">
    <testCase name="DivisionSelector renders with no divisions" duration="19"/>
    <testCase name="DivisionSelector auto-selects the single division if none is preselected and single-select" duration="11"/>
    <testCase name="DivisionSelector does not auto-select if single division is already selected" duration="6"/>
    <testCase name="DivisionSelector does not auto-select if multiple divisions are present" duration="7"/>
    <testCase name="DivisionSelector Multi-select mode shows indeterminate state when some but not all are selected" duration="125"/>
    <testCase name="DivisionSelector Cascade logic does not dispatch actions to clear SM/ASM when division changes (single)" duration="19"/>
    <testCase name="DivisionSelector shows alert if no division is selected" duration="8"/>
    <testCase name="DivisionSelector does not show alert if a division is selected" duration="6"/>
    <testCase name="DivisionSelector formats division names using formatName" duration="8"/>
    <testCase name="DivisionSelector Edge cases auto-selects if divisions changes from multiple to single and none selected" duration="13"/>
    <testCase name="DivisionSelector Edge cases does not auto-select if divisions changes to single but already selected" duration="17"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\_tests_\EPCBSSyncMonitor.spec.tsx">
    <testCase name="EPBCSSyncMonitor renders sync history rows" duration="26"/>
    <testCase name="EPBCSSyncMonitor renders fallback for missing syncData" duration="9"/>
    <testCase name="EPBCSSyncMonitor renders only summary cards and no sync history or sessions when all data is missing" duration="14"/>
    <testCase name="EPBCSSyncMonitor renders correct week number when fiscalWeekNumber is a three-digit number" duration="15"/>
    <testCase name="EPBCSSyncMonitor renders spinner when loading" duration="4"/>
    <testCase name="EPBCSSyncMonitor renders fallback for empty syncHistory" duration="18"/>
    <testCase name="EPBCSSyncMonitor handles syncSessions with missing fields gracefully" duration="12"/>
    <testCase name="EPBCSSyncMonitor handles missing fiscalWeekNumber" duration="19"/>
    <testCase name="EPBCSSyncMonitor renders unknown status with no tag" duration="20"/>
    <testCase name="EPBCSSyncMonitor renders fallback for empty syncHistory" duration="18"/>
    <testCase name="EPBCSSyncMonitor handles missing fiscalWeekNumber" duration="28"/>
    <testCase name="EPBCSSyncMonitor shows spinner when loading" duration="3"/>
    <testCase name="EPBCSSyncMonitor renders unknown status with no tag" duration="11"/>
    <testCase name="EPBCSSyncMonitor renders sync history rows" duration="19"/>
    <testCase name="EPBCSSyncMonitor renders fallback for missing syncData" duration="8"/>
    <testCase name="EPBCSSyncMonitor renders fallback for missing displayDate" duration="23"/>
    <testCase name="EPBCSSyncMonitor renders fallback for missing nextSync/lastSync" duration="16"/>
    <testCase name="EPBCSSyncMonitor renders fallback for empty syncHistory" duration="15"/>
    <testCase name="EPBCSSyncMonitor handles syncSessions with missing fields gracefully" duration="13"/>
    <testCase name="EPBCSSyncMonitor renders correct week number when fiscalWeekNumber is undefined" duration="13"/>
    <testCase name="EPBCSSyncMonitor renders no tag for unknown status" duration="10"/>
    <testCase name="EPBCSSyncMonitor renders the component without crashing" duration="20"/>
    <testCase name="EPBCSSyncMonitor renders &quot;-&quot; for missing Next Sync and Last Sync values" duration="25"/>
    <testCase name="EPBCSSyncMonitor renders no Completed/Processing/Failed tags if syncSessions are empty" duration="9"/>
    <testCase name="EPBCSSyncMonitor renders no tag for unknown status" duration="12"/>
    <testCase name="EPBCSSyncMonitor shows the correct current week number" duration="24"/>
    <testCase name="EPBCSSyncMonitor renders the Sync Day and Scheduled time headers" duration="21"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\_tests_\history-timeline.spec.tsx">
    <testCase name="HistoryTimeline Component renders the timeline with data" duration="45"/>
    <testCase name="HistoryTimeline Component renders &quot;No Records Found&quot; when data is empty" duration="3"/>
    <testCase name="HistoryTimeline Component renders the tooltip with reason and comment" duration="12"/>
    <testCase name="HistoryTimeline Component applies a line-through style for struck fields" duration="11"/>
    <testCase name="HistoryTimeline Component checks for non-struck fields" duration="11"/>
    <testCase name="HistoryTimeline Component formats the timestamp to PST" duration="10"/>
    <testCase name="HistoryTimeline Component renders timeline with multiple items" duration="24"/>
    <testCase name="HistoryTimeline Component handles missing calendarWeek data gracefully" duration="10"/>
    <testCase name="HistoryTimeline Component handles editedColumns with no strike-through" duration="20"/>
    <testCase name="HistoryTimeline Component handles edge date (not matching any week)" duration="10"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\worksheetFilterContext.spec.tsx">
    <testCase name="WorksheetFilterContext should provide default filter state" duration="13"/>
    <testCase name="WorksheetFilterContext should open and close filter modal" duration="14"/>
    <testCase name="WorksheetFilterContext should apply filters correctly" duration="8"/>
    <testCase name="WorksheetFilterContext should throw error when used outside provider" duration="396"/>
    <testCase name="WorksheetFilterContext should update filter state when modal is opened" duration="13"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\AllocatrInsights\AllocatrInsights.spec.tsx">
    <testCase name="AllocatrInsights renders loading spinner" duration="334"/>
    <testCase name="AllocatrInsights renders table for Performance Summary and calls onDataLoaded" duration="49"/>
    <testCase name="AllocatrInsights renders table for Forecast Variance and calls onDataLoaded" duration="27"/>
    <testCase name="AllocatrInsights renders empty table if no departments" duration="3754">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: []
Received
       1: {"divisions": [], "id": "", "name": ""}
       2: {"id": 1, "name": "Dept1"}
       3: {"id": 1, "name": "Dept1"}

Number of calls: 428
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\AllocatrInsights\AllocatrInsights.spec.tsx:133:28)]]></failure>
    </testCase>
    <testCase name="AllocatrInsights handles error state (isError=true)" duration="5007">
      <failure message="Error: thrown: &quot;Exceeded timeout of 5000 ms for a test."><![CDATA[Error: thrown: "Exceeded timeout of 5000 ms for a test.
Use jest.setTimeout(newTimeout) to increase the timeout value, if this is a long-running test."
    at it (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\AllocatrInsights\AllocatrInsights.spec.tsx:137:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\index.js:105:26)
    at describe (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\index.js:60:5)
    at Object.describe (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\AllocatrInsights\AllocatrInsights.spec.tsx:58:1)
    at Runtime._execModule (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runtime\build\index.js:1714:24)
    at Runtime._loadModule (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runtime\build\index.js:1223:12)
    at Runtime.requireModule (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runtime\build\index.js:1047:12)
    at jestAdapter (C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:72:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at runTestInternal (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:411:16)
    at runTest (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\runTest.js:499:34)
    at Object.worker (c:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\node_modules\jest-runner\build\testWorker.js:133:12)]]></failure>
    </testCase>
    <testCase name="AllocatrInsights renders without onDataLoaded prop" duration="5334"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\confirmationModal.spec.tsx">
    <testCase name="ConfirmationModal should render the modal with given title and message" duration="60"/>
    <testCase name="ConfirmationModal should call setIsOpen with false when Cancel button is clicked" duration="17"/>
    <testCase name="ConfirmationModal should render the confirm button with given text" duration="5"/>
    <testCase name="ConfirmationModal should not render the modal when isOpen is false" duration="3"/>
    <testCase name="ConfirmationModal should have the correct classes for title and message" duration="10"/>
    <testCase name="ConfirmationModal should have the correct classes for buttons" duration="7"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\filterModal\ModalContent.spec.tsx">
    <testCase name="ModalContent renders division, department desk, and category sections" duration="28"/>
    <testCase name="ModalContent passes correct props to ModalDivisionSection" duration="9"/>
    <testCase name="ModalContent passes correct props to ModalDepartmentDeskSection" duration="8"/>
    <testCase name="ModalContent updates desk display flag when useDeskDisplay returns false" duration="9"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\filterModal\ModalTimeframeSection.spec.tsx">
    <testCase name="ModalTimeframeSection renders with no selectedTimeframe" duration="10"/>
    <testCase name="ModalTimeframeSection renders with selectedTimeframe" duration="3"/>
    <testCase name="ModalTimeframeSection calls onTimeframeChange when selector triggers change" duration="4"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\pages\adjustment-worksheet.spec.tsx">
    <testCase name="AdjustmentWorkSheet renders all main components when not loading" duration="16"/>
    <testCase name="AdjustmentWorkSheet shows success message in AlertMessage when alertState.success is true" duration="2"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\RxForecast\rxforecast-title-component.spec.tsx">
    <testCase name="RxForecastTitleComponent renders the component correctly" duration="15"/>
    <testCase name="RxForecastTitleComponent displays the correct title text" duration="7"/>
    <testCase name="RxForecastTitleComponent renders HelpIcon with default variant" duration="3"/>
    <testCase name="RxForecastTitleComponent has proper container structure" duration="4"/>
    <testCase name="RxForecastTitleComponent renders title and help icon in the same container" duration="5"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\RxForecast\rxforecast.spec.tsx">
    <testCase name="Rxforecast renders the component correctly" duration="8"/>
    <testCase name="Rxforecast renders title component" duration="4"/>
    <testCase name="Rxforecast renders without any props" duration="2"/>
    <testCase name="Rxforecast has correct component hierarchy" duration="3"/>
    <testCase name="Rxforecast renders consistently on multiple renders" duration="2"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\app-main-container.spec.tsx">
    <testCase name="Given NFPTContainer component When it is loading data Then it should render the spinner if display date is loading" duration="210"/>
    <testCase name="Given NFPTContainer component When it is loading data Then it should render the spinner if user info is loading" duration="4"/>
    <testCase name="Given NFPTContainer component When data has been fetched successfully Then it should render children components" duration="5"/>
    <testCase name="Given NFPTContainer component When data has been fetched successfully Then it should dispatch setDisplayDate with the correct data" duration="5"/>
    <testCase name="Given NFPTContainer component When data has been fetched successfully Then it should dispatch setUserInfo with the correct data" duration="4"/>
    <testCase name="Given NFPTContainer component When data has been fetched successfully Then it should dispatch setPeriodStatuses on route change" duration="4"/>
    <testCase name="Given NFPTContainer component When data fetching has edge cases Then it should not dispatch setDisplayDate if data is empty" duration="3"/>
    <testCase name="Given NFPTContainer component When data fetching has edge cases Then it should not dispatch setUserInfo if userInfo is not present" duration="3"/>
    <testCase name="Given NFPTContainer component When usePdfHelp hook is used Then it should be called" duration="4"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should skip API calls for access-denied route" duration="3"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should skip API calls for error route" duration="3"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should skip API calls for 404 route" duration="4"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should skip API calls for unauthorized route" duration="3"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should skip API calls for maintenance route" duration="2"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should not skip API calls for regular routes" duration="3"/>
    <testCase name="Given NFPTContainer component When on routes that should skip API calls Then it should handle nested paths with skip routes" duration="6"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\DashboardDownloadExcelHelper.spec.tsx">
    <testCase name="DashboardDownloadExcelHelper mapRow should map all fields correctly with valid data" duration="27"/>
    <testCase name="DashboardDownloadExcelHelper mapRow should return empty string for null/undefined/empty values" duration="6"/>
    <testCase name="DashboardDownloadExcelHelper mapRow should handle negative values for line6 fields" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper handleDownloadClick should alert if dashboardData is empty array" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper handleDownloadClick should alert if dashboardData object has no divisions or id" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper handleDownloadClick should call handleDownloadExcel if array data exists" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper handleDownloadClick should call handleDownloadExcel if object data with divisions exists" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper handleDownloadClick should call handleDownloadExcel if object data with id exists" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper getParentHeaderRow should return correct header row" duration="1"/>
    <testCase name="DashboardDownloadExcelHelper header constants COMMON_HEADERS should be an array" duration="0"/>
    <testCase name="DashboardDownloadExcelHelper header constants VS_PROJECTION_HEADERS should be an array" duration="0"/>
    <testCase name="DashboardDownloadExcelHelper header constants VS_PROJECTION_DOLLAR_HEADERS should be an array" duration="1"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\server\Api\menfptCategoryAPI.spec.ts">
    <testCase name="entitlementApi getmenfptCategoryTitle endpoint should be defined" duration="1"/>
    <testCase name="entitlementApi getmenfptCategoryTitle endpoint should create a POST request with the correct body" duration="0"/>
    <testCase name="entitlementApi getmenfptCategoryTitle endpoint should have the correct headers" duration="1"/>
    <testCase name="entitlementApi getmenfptCategoryTitle endpoint should transform the response correctly" duration="6"/>
    <testCase name="entitlementApi getDisplayDate endpoint should be defined" duration="1"/>
    <testCase name="entitlementApi getDisplayDate endpoint should create a POST request with the correct body and headers" duration="2"/>
    <testCase name="entitlementApi getDisplayDate endpoint should transform the response to return calendarDetails" duration="1"/>
    <testCase name="entitlementApi getDisplayDate endpoint should return an empty array if calendarDetails are missing" duration="0"/>
    <testCase name="entitlementApi getWorkSheetFilter endpoint should be defined" duration="1"/>
    <testCase name="entitlementApi getWorkSheetFilter endpoint should create a POST request with correct body and headers" duration="2"/>
    <testCase name="entitlementApi getWorkSheetFilter endpoint should transform the response correctly" duration="0"/>
    <testCase name="entitlementApi getJobRunsFromDatabricks endpoint should be defined" duration="1"/>
    <testCase name="entitlementApi getJobRunsFromDatabricks endpoint should create a POST request with the correct query and variables" duration="2"/>
    <testCase name="entitlementApi getJobRunsFromDatabricks endpoint should transform the response correctly" duration="1"/>
    <testCase name="entitlementApi getJobRunsFromDatabricks endpoint should handle an empty response" duration="1"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\periodClose\periodClose.slice.spec.ts">
    <testCase name="prevQuarterTabSlice should set prevQuarterTab data" duration="2"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\envVariables\envVariables.slice.spec.ts">
    <testCase name="envVariablesSlice should handle initial state" duration="7"/>
    <testCase name="envVariablesSlice should handle setEnvVariables" duration="2"/>
    <testCase name="envVariablesSlice should handle setEnvVariablesError" duration="1"/>
    <testCase name="envVariablesSlice should handle clearEnvVariables" duration="0"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\server\Reducer\menfpt-category.slice.spec.ts">
    <testCase name="menfpt-category slices menfptCategorySlice should handle setMenfptCategory" duration="1"/>
    <testCase name="menfpt-category slices workSheetFilterListSlice should handle setWorkSheetFilterList" duration="1"/>
    <testCase name="menfpt-category slices displayDateSlice should handle setDisplayDate" duration="1"/>
    <testCase name="menfpt-category slices userInfoSlice should handle setUserInfo" duration="1"/>
    <testCase name="menfpt-category slices publishHistorySlice should handle setPublishHistory" duration="1"/>
    <testCase name="menfpt-category slices appliedFilterSlice should handle setAppliedFilter" duration="1"/>
    <testCase name="menfpt-category slices adjustmentWorkSheetFilterSlice should handle setAdjustmentWorkSheetFilter" duration="1"/>
    <testCase name="menfpt-category slices featureFlags should handle setFeatureFlags" duration="0"/>
    <testCase name="menfpt-category slices quartersSlice should handle setQuarters" duration="1"/>
    <testCase name="menfpt-category slices alertState should handle setAlertState" duration="0"/>
    <testCase name="menfpt-category slices roleMappingInfo should handle setRoleMappingInfo" duration="0"/>
    <testCase name="menfpt-category slices editAdjustmentPermission should handle setEditAdjustmentPermission" duration="0"/>
    <testCase name="menfpt-category slices helpPdfUrl should handle setHelpPdfUrl" duration="6"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\periodClose\periodClose.flags.spec.ts">
    <testCase name="periodClose.flags useShouldDisableEditForecastButton returns true if locked or notCertifiedButLocked has keys" duration="1"/>
    <testCase name="periodClose.flags useShouldDisableEditForecastButton returns true if notCertifiedButLocked has keys" duration="0"/>
    <testCase name="periodClose.flags useShouldDisableEditForecastButton returns true if both are empty" duration="0"/>
    <testCase name="periodClose.flags useShouldDisableEditForecastButton returns false if notLocked has keys" duration="1"/>
    <testCase name="periodClose.flags handlePrevQuarterPeriodClose does nothing if calendarApiResp is falsy" duration="0"/>
    <testCase name="periodClose.flags handlePrevQuarterPeriodClose does nothing if type is not lastQrtr_periodClose" duration="0"/>
    <testCase name="periodClose.flags handlePrevQuarterPeriodClose dispatches correct actions for valid input" duration="1"/>
    <testCase name="periodClose.flags useWeeksToBeDisabledForQuarter returns combined locked and notCertifiedButLocked weeks" duration="1"/>
    <testCase name="periodClose.flags useWeeksToBeDisabledForQuarter returns empty array if periodStatuses is falsy" duration="0"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\worksheetFilter\components\filterModal\useModalContentState.spec.ts">
    <testCase name="useModalContentState should initialize and filter departments" duration="15"/>
    <testCase name="useModalContentState should filter desks" duration="2"/>
    <testCase name="useModalContentState should handle dept roles search and dispatch actions" duration="2"/>
    <testCase name="useModalContentState should set and get search focus and suggestions visibility" duration="1"/>
    <testCase name="useModalContentState should return correct search handler and value" duration="1"/>
    <testCase name="useModalContentState should handle click outside and hide suggestions" duration="4"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\features\_tests_\WorksheetHeaderControls.spec.tsx">
    <testCase name="WorksheetHeaderControls renders adjustment worksheet with fiscal year and quarter" duration="442"/>
    <testCase name="WorksheetHeaderControls renders today information with week and quarter" duration="19"/>
    <testCase name="WorksheetHeaderControls renders Audit History button" duration="25"/>
    <testCase name="WorksheetHeaderControls renders Edit forecast button" duration="17"/>
    <testCase name="WorksheetHeaderControls opens audit history modal when button is clicked" duration="132"/>
    <testCase name="WorksheetHeaderControls opens edit forecast modal when button is clicked" duration="40"/>
    <testCase name="WorksheetHeaderControls disables edit forecast button when ASM is selected" duration="12"/>
    <testCase name="WorksheetHeaderControls disables edit forecast button when user role is ASM" duration="16"/>
    <testCase name="WorksheetHeaderControls disables edit forecast button when desk is selected" duration="12"/>
    <testCase name="WorksheetHeaderControls handles quarter change callback" duration="11"/>
    <testCase name="WorksheetHeaderControls handles localStorage with valid data" duration="8"/>
    <testCase name="WorksheetHeaderControls handles localStorage with invalid divisions" duration="11"/>
    <testCase name="WorksheetHeaderControls opens filter modal when Change link is clicked" duration="12"/>
    <testCase name="WorksheetHeaderControls displays alert when no division is selected and Apply is clicked" duration="23"/>
    <testCase name="WorksheetHeaderControls resets filters correctly" duration="23"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\app.spec.tsx">
    <testCase name="App renders app without crashing" duration="530"/>
    <testCase name="App should include access denied route" duration="33"/>
    <testCase name="App should have proper route protection structure" duration="24"/>
    <testCase name="App should handle different user roles correctly" duration="19"/>
  </file>
  <file path="C:\Users\<USER>\Desktop\NFPT\menfpt-category-ui\apps\menfpt-category-ui\src\components\_tests_\editForecastAdjustment.spec.tsx">
    <testCase name="ForecastEdit Component should render without crashing" duration="0"/>
    <testCase name="ForecastEdit Component should allow me to select week 1 from Applied Weeks " duration="0"/>
    <testCase name="ForecastEdit Component should auto scroll to errrors" duration="0"/>
    <testCase name="ForecastEdit Component should show the correct total after selecting two weeks in group form" duration="0"/>
    <testCase name="ForecastEdit Component should find and select the &quot;Enter for each week separately&quot; radio option" duration="0"/>
    <testCase name="ForecastEdit Component should allow week select in separate week mode and week 1 in both group and single should be same" duration="0"/>
    <testCase name="ForecastEdit Component should allow week select in separate week mode and week 1 and week 2 " duration="0"/>
    <testCase name="ForecastEdit Component should show correct value in grossProfit when you enter select week 1 and enter 1 in salesPublic" duration="0"/>
    <testCase name="ForecastEdit Component should show correct value in marksDown when you enter select week 1 and enter 1 in salesPublic" duration="0"/>
    <testCase name="ForecastEdit Component should show correct value in totalShrink when you enter select week 1 and enter 1 in salesPublic" duration="0"/>
    <testCase name="ForecastEdit Component should allow individual enter of values in grossProfit, marksDown, totalShrink when selecting week 1" duration="0"/>
    <testCase name="ForecastEdit Component should show all adjustment reasons after clicking on Make a selection dropdown" duration="0"/>
    <testCase name="ForecastEdit Component should show Extreme Weather after selecting it from the dropdown" duration="0"/>
    <testCase name="ForecastEdit Component should allow user to select supplies packaging, and allowance  and enter values in the input a" duration="0"/>
    <testCase name="ForecastEdit Component should allow user to type comment in comment-1 (comment section for week1) textarea" duration="0"/>
    <testCase name="ForecastEdit Component should enter salesPublic as 1, ensure the grossProfit,marksDown,totalShrink are updated, select Extreme weather from the dropdown, and enter a comment" duration="0"/>
    <testCase name="ForecastEdit Component Group week test suite should find and select the &quot;Enter same values for selected weeks&quot; radio option" duration="0"/>
    <testCase name="ForecastEdit Component Group week test suite should allow selecting multiple weeks and they all should be selected" duration="0"/>
    <testCase name="ForecastEdit Component Group week test suite should allow selecting multiple weeks and salesPublic" duration="0"/>
  </file>
</testExecutions>