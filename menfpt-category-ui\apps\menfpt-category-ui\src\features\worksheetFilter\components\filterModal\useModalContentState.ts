import { useState, useEffect, useRef, useMemo } from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import { filterByDeptRolesSearch, filterBySearch } from '../../searchUtils';
import { useDispatch, useSelector } from 'react-redux';
import { setDeptRoleSuggestions } from '../suggestions/deptRoleSuggestions.slice';
import { useSelectorWrap } from '../../../../rtk/rtk-utilities';
import { setDepartmentDeskSearchQuery } from '../deptDesk/departmentDeskSearchQuery.slice';
import { clearCascadeSearchSelectedItem } from '../suggestions/deptRoleSuggestions.slice';

interface UseModalContentStateProps {
  desks: DropdownType[];
  isDisplayDeptRoleCascade: boolean;
  activeTabInFilter: any;
}

export const useModalContentState = ({
  desks,
  isDisplayDeptRoleCascade,
  activeTabInFilter,
}: UseModalContentStateProps) => {
  // Reduce excessive logging - only log on mount or prop changes
  const prevActiveTab = useRef(activeTabInFilter);
  const prevDeskLength = useRef(desks.length);
  
  if (prevActiveTab.current !== activeTabInFilter || prevDeskLength.current !== desks.length) {
    prevActiveTab.current = activeTabInFilter;
    prevDeskLength.current = desks.length;
  }
  
  const departmentsState = useSelectorWrap('departments_rn');
  
  // Only log departmentsState when it actually changes
  const prevDepartmentsState = useRef(departmentsState);
  if (prevDepartmentsState.current !== departmentsState) {
    prevDepartmentsState.current = departmentsState;
  }
  
  // Memoize departments to prevent unnecessary re-renders
  const departments = useMemo(() => departmentsState.data || [], [departmentsState.data]);
  
  const [searchQueryDepartment, setSearchQueryDepartment] = useState('');
  const searchQueryDeptRole = useSelectorWrap('departmentDeskSearchQuery_rn').data?.value || '';
  const [searchQueryDesk, setSearchQueryDesk] = useState('');

  // Memoize filtered results to prevent unnecessary re-calculations
  const filteredDepartments = useMemo(() => 
    filterBySearch(departments, searchQueryDepartment), 
    [departments, searchQueryDepartment]
  );
  
  const filteredDesks = useMemo(() => 
    filterBySearch(desks, searchQueryDesk), 
    [desks, searchQueryDesk]
  );

  const [filteredDeptRoles, setFilteredDeptRoles] = useState([]);

  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isSuggestionsVisible, setIsSuggestionsVisible] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  const dispatch = useDispatch();

  // Remove the old useEffect hooks since we're using useMemo now

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setIsSuggestionsVisible(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const suggestions = filterByDeptRolesSearch({departments, searchQueryDeptRole});
    dispatch(setDeptRoleSuggestions(suggestions));
  }, [searchQueryDeptRole, departments, dispatch]);

  // Log searchQueryDeptRole on every change
  useEffect(() => {
  }, [searchQueryDeptRole]);

  const handleDepartmentSearch = (query: string) => {
    setSearchQueryDepartment(query);
  };
  const handleDeptRolesSearch = (query: string) => {
    if (query === "") {
      dispatch(clearCascadeSearchSelectedItem());
    }
    dispatch(setDepartmentDeskSearchQuery(query));
  };
  const handleDeskSearch = (query: string) => {
    setSearchQueryDesk(query);
  };

  const getSearchHandler = () => {
    if (activeTabInFilter && activeTabInFilter.length > 0) {
      if (activeTabInFilter[0] === 'department') {
        return isDisplayDeptRoleCascade
          ? handleDeptRolesSearch
          : handleDepartmentSearch;
      } else {
        return handleDeskSearch;
      }
    }
    return handleDepartmentSearch;
  };

  const getSearchValue = () => {
    if (activeTabInFilter && activeTabInFilter.length > 0) {
      if (activeTabInFilter[0] === 'department') {
        return isDisplayDeptRoleCascade
          ? searchQueryDeptRole
          : searchQueryDepartment;
      } else {
        return searchQueryDesk;
      }
    }
    return searchQueryDepartment;
  };

  return {
    filteredDepartments,
    filteredDesks,
    searchQueryDepartment,
    searchQueryDeptRole,
    searchQueryDesk,
    isSearchFocused,
    setIsSearchFocused,
    isSuggestionsVisible,
    setIsSuggestionsVisible,
    searchContainerRef,
    handleDepartmentSearch,
    handleDeptRolesSearch,
    handleDeskSearch,
    getSearchHandler,
    getSearchValue,
  };
}
