import { createGenericSlice } from '../../../../rtk/rtk-slice';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

export const departmentsSlice = createGenericSlice({
  name: 'departments_rn',
  initialState: { status: 'loading', data: [] as DropdownType[] },
})({
  setDepartments(state, { payload }) {
    state.data = payload;
  },
});

export const { setDepartments } = departmentsSlice.actions; 