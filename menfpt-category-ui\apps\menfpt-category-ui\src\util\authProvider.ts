import "react-toastify/dist/ReactToastify.css";
import { v4 as uuidv4 } from 'uuid';
const readCookie = (name: string) => {
  try {
    const cookieValue = document.cookie
      .split("; ")
      .find((row) => row.startsWith(`${name}=`))
      ?.split("=")[1];
    return decodeURIComponent(cookieValue as string);
  } catch (e) {
    console.error("Error reading cookie.", e);
    return "";
  }
};
 
const readEntAbsAuthCookie = () => {
  const cookieValue = document ? readCookie("ent-abs-auth") : undefined;
  if (cookieValue !== "undefined") {
    const json = JSON.parse(cookieValue as string);
    return json.accessToken;
  } else return undefined;
};
 
const authProvider = () => {
  //const accessTokenValidation = () => {
    let accessToken: any = readEntAbsAuthCookie();
 
  if (accessToken) {
    return accessToken;
  } else {
    const protocol = window.location.protocol + "//";
    const hostname = window.location.hostname;
    const origin = protocol + hostname;
    alert("Session Expired");
    window.location.replace(`${origin}/memsp-ui-shell/`);
  }
};
 
export const getDefaultHeaders = () => ({
  "content-type": "application/json",
  //  Authorization: authProvider(),
   Authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ij9KZum9II4bi_0d-T1we3IiR191AcdR2B-ri8jxleBgfXZyKm_vKMPHYrFRY4UcIhEOv0vXTFk--nJFwY3KEzwj9yrYiArR8wzfLNhg6BaLLZCHEOpd4R7zjNs38AtUVU9cIdlod_16YQ8gTnDDSV3oLU3DnFTuUzimwz4hjK_DTHw2SABjYCaDBR4DQIcKRQVJnAXXiF0S_cz72BydklxGx-yl1juV6RDEVCnl8V6z4fCnhkPeLDPsNzJkyOlTnXLrcd2Nieq2vR0H6brw1JEg7TCbbBgRXBBqWClQe31k2FmMwhh2wdX8u8wewPmben5qhMLHOHApkeirORDl4g',
  "ui-trace-id": uuidv4()
});
