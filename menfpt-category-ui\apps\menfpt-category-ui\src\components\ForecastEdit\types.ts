export interface EditForecastAdjustmentProps {
    isOpen: boolean;
    setOpen: (isOpen: boolean) => void;
    historyModalOpen: (isOpen: boolean, direction: string) => void;
    selectedEditWeek?: number | null;
}

  type WeekFormData = {
    salesPublic: string;
    salesPublicPct: string;
    grossProfit: string;
    grossProfitPct: string;
    marksDown: string;
    marksDownPct: string;
    totalShrink: string;
    totalShrinkPct: string;
    suppliesPackaging: string;
    allowances: string;
    selling: string;
    nonSelling: string;
    selectedWeeks: number[];
    adjustmentReason: any;
    comment: string;
    touched:  Set<string>;
  }

  export type FormValues = {
    salesPublic: string;
    salesPublicPct: string;
    grossProfit: string;
    grossProfitPct: string;
    marksDown: string;
    marksDownPct: string;
    totalShrink: string;
    totalShrinkPct: string;
    allowancesPct: string;
    suppliesPackaging: string;
    allowances: string;
    selectedWeeks: number[];
    adjustmentReason: any;
    selling: string;
    nonSelling: string;
    sellingPct: string;
    nonSellingPct: string;
    comment: string;
    selectedRadio: 'groupweeks' | 'singleweek';
    metrics: string;
    weekData?: Record<number, WeekFormData | any>,
    errors: any,
    touched: Set<string>,
    touchedWeeks: any
  }