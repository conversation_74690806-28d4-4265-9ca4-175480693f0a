import React from 'react';
import { DropdownType } from '../../../../interfaces/worksheetFilter';
import TimeframeSelector from '../timeframe/timeframeSelector';

interface ModalTimeframeSectionProps {
  selectedTimeframe?: DropdownType;
  onTimeframeChange: (timeframe: DropdownType) => void;
  selectedPeriods?: DropdownType[];
  selectedWeeks?: { periodNum: number; weekNum: number }[];
  handlePeriodChange?: (periods: DropdownType[]) => void;
  handleWeeksChange?: (weeks: { periodNum: number; weekNum: number }[]) => void;
}

const ModalTimeframeSection: React.FC<ModalTimeframeSectionProps> = ({
  selectedTimeframe,
  onTimeframeChange,
}) => (
  <div className="min-h-0 max-h-[500px] flex flex-col flex-1 min-w-0 grow overflow-hidden border-r border-[#c8daeb] pr-4 pl-6">
    <TimeframeSelector
      selectedTimeframe={selectedTimeframe}
      onTimeframeChange={onTimeframeChange}
    />
  </div>
);

export default ModalTimeframeSection; 