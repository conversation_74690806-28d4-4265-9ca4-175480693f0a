import React from 'react';
import './EPBCSSyncMonitor.scss';
import Tag from '@albertsons/uds/molecule/Tag';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { useGetJobRunsFromDatabricksQuery } from '../server/Api/menfptCategoryAPI';
import Spinner from '@albertsons/uds/molecule/Spinner';

const EPBCSSyncMonitor: React.FC = () => {
  const displayDateSelector = useSelectorWrap('displayDate_rn');
  const displayDate = displayDateSelector?.data || {};
  const currentWeek = displayDate?.fiscalWeekNumber
    ? Math.floor(displayDate.fiscalWeekNumber % 100)
    : 4;

  const { data: syncData, isLoading } = useGetJobRunsFromDatabricksQuery({
    input: {
      jobName: process.env.DATABRICKS_JOB ?? '', 
      limit: null,
    },
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-20">
        <Spinner />
      </div>
    );
  }

 const nextSync = syncData?.getJobRunsFromDatabricks?.nextSync;
  const lastSync = syncData?.getJobRunsFromDatabricks?.lastSync;
const syncSessions = syncData?.getJobRunsFromDatabricks?.syncSessions || [];
const syncHistoryWeeks = syncData?.getJobRunsFromDatabricks?.syncHistory?.weeks || [];

const renderStatusTag = (status: string | null) => {
  if (!status) return null;
  const normalized = status.toLowerCase();
  if (normalized === 'complete') return <Tag preset="green" label="Completed" className="square-tag" />;
  if (normalized === 'processing') return <Tag preset="blue" label="Processing" className="square-tag" />;
  if (normalized === 'failed' || normalized === 'fail') return <Tag preset="red" label="Fail" className="square-tag" />;
  return null;
};

  const renderHeader = (headers: string[]) =>
    headers.map((header, index) => (
      <div key={index} className={`${index === 0 ? 'w-24' : 'flex-1'} h-12 p-2 table-header flex justify-start items-center gap-1 overflow-hidden relative`}>
        <div className="divider-base left-0 top-0" />
        <div className="header-text">{header}</div>
        <div className="divider-base right-0 top-0" />
      </div>
    ));

  const renderScheduleColumn = (startIdx: number) => {
    const sessions = syncSessions.slice(startIdx, startIdx + 6);
    return (
      <>
        <div className="flex-column-stretch">
          {sessions.map((item, idx) => {
            const globalIdx = startIdx + idx;
            const isLast = globalIdx === syncSessions.length - 1;
            return (
              <div key={idx} className="self-stretch h-8 p-2 cell-base">
                <div className="w-full h-px left-0 top-[31px] absolute bg-gray-200" />
                <div className="divider-base right-0 top-0" />
                <div className="flex-start-container">
                  <div className="cell-text">
                    {item.time} PDT
                    {isLast && (
                      <span className="ml-2 text">(Cut off)</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="flex-column-center">
          {sessions.map((item, idx) => (
            <div key={idx} className="self-stretch h-8 p-2 cell-base">
              <div className="w-56 h-px left-0 top-[31px] absolute bg-gray-200" />
              <div className="divider-base right-0 top-0" />
              {renderStatusTag(item.status)}
            </div>
          ))}
        </div>
      </>
    );
  };

const renderHistoryRows = () =>
  syncHistoryWeeks
    .filter((weekObj: any) => weekObj.lastRun) 
    .map((weekObj: any, idx: number) => {
      const lastRun = weekObj.lastRun;
      return (
        <div key={idx} className="self-stretch inline-flex justify-start items-center">
          <div className="w-24 min-h-12 p-2 cell-base">
            <div className="divider-base left-0 top-0" />
            <div className="w-24 h-px left-0 top-[47px] absolute bg-gray-200" />
            <div className="divider-base right-0 top-0" />
            <div className="flex-start-container">
              <div className="cell-text">
                {`Week ${lastRun.weekNumber}`}
              </div>
            </div>
          </div>
          <div className="flex-1 min-h-12 p-2 cell-base">
            <div className="w-[460px] h-px left-0 top-[47px] absolute bg-gray-200" />
            <div className="divider-base right-0 top-0" />
            <div className="flex-start-container">
              <div className="cell-text">
                {`${lastRun.date} ${lastRun.time} PDT`}
              </div>
            </div>
          </div>
        </div>
      );
    });

const uniqueDayDatePairs = syncSessions.reduce((acc: any[], curr: any) => {
  if (!acc.some(item => item.day === curr.day && item.date === curr.date)) {
    acc.push({ day: curr.day, date: curr.date });
  }
  return acc;
}, []);

  return (
    <div className="mb-4">
      <div className="flex-column-container">
        <div className="flex-inline-container">
          {['Week', 'Next Sync', 'Last Sync'].map((title, index) => (
            <div key={index} className="card-style">
              <div className="header-text">{title}</div>
              <div className="cell-text">
                {index === 0
                  ? currentWeek
                  : index === 1
                  ? nextSync
                    ? `${nextSync.sync_day} ${nextSync.sync_time}`
                    : '-'
                  : lastSync
                  ? `${lastSync.sync_day} ${lastSync.sync_time}`
                  : '-'}
              </div>
            </div>
          ))}
        </div>
        <div className="sync-container !p-0 !m-0">
          <div className="self-stretch inline-flex justify-start items-center">{renderHeader(['Sync Day', 'Scheduled time', 'Status'])}</div>
          {uniqueDayDatePairs.map((session, colIndex) => {
   const startIdx = syncSessions.findIndex(s => s.day === session.day && s.date === session.date);
  if (startIdx === -1) return null;
    return (
      <div key={colIndex} className="self-stretch inline-flex justify-start items-stretch !m-0 !p-0">
        <div className="w-24 min-h-12 p-2 cell-base">
          <div className="divider-base left-0 top-0" />
          <div className="w-24 h-px left-0 bottom-0 absolute bg-gray-200" />
          <div className="divider-base right-0 top-0" />
          <div className="flex justify-start items-start overflow-hidden">
            <div className="cell-text">
              {session.day}
              <br />
              {session.date}
            </div>
          </div>
        </div>
        {renderScheduleColumn(startIdx)}
      </div>
    );
  })}
        </div>
        <div className="mt-2">
          <div className="text-lg font-bold mb-4">Sync History</div>
          <div className="container-style">
            <div className="self-stretch inline-flex justify-start items-center">{renderHeader(['Week', 'Last Sync Time'])}</div>
            {renderHistoryRows()}
            <div className="w-full h-px bg-gray-300" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EPBCSSyncMonitor;
