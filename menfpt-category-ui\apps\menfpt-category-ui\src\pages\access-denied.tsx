import Link from '@albertsons/uds/molecule/Link';
import React from 'react';
import { useSearchParams } from 'react-router-dom';

const AccessDenied: React.FC = () => {
  const [searchParams] = useSearchParams();
  const pageName = searchParams.get('page') || 'this page';

  return (
    <div className="flex flex-col flex-shrink-0 justify-center items-center gap-5 min-h-screen">
      <div className="flex flex-col items-center gap-5">
        <div className="flex flex-col items-center">
          <div className="text-[#0d2d49] text-center font-nunito text-[2rem] font-bold leading-10">
            Access Denied
          </div>
          <div className="text-[#2b303c] text-center font-nunito text-sm leading-6">
            We are sorry, but you don't have access to {pageName}
          </div>        
          <Link href={`${window.location.origin}/memsp-ui-shell/`}>Back to home page</Link>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
