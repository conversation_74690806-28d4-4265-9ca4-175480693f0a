import React from 'react';
import Alert from '@albertsons/uds/molecule/Alert';
import { FileUploadStatus } from '@albertsons/uds/molecule/FileUpload';
import FileUploadBox from './FileUploadBox';
import RxForecastAlertMessage from './rxforecastAlertMessage';
import RxForecastAlerts from './rxforecastAlerts';
import { FileItem } from './uploadDocument.types';
import Spinner from '@albertsons/uds/molecule/Spinner';
interface AlertComponentsProps {
  alertOpen: boolean;
  successAlertOpen: boolean;
  alertMessage: string;
  onCloseAlert: () => void;
  onCloseSuccessAlert: () => void;
}

export const AlertComponents: React.FC<AlertComponentsProps> = ({
  alertOpen,
  successAlertOpen,
  alertMessage,
  onCloseAlert,
  onCloseSuccessAlert
}) => (
  <div>
    {alertOpen && (
      <RxForecastAlerts
        title="Document upload fail!"
        message={alertMessage}
        type="error"
        open={alertOpen}
        onClose={onCloseAlert}
      />
    )}
    {successAlertOpen && (
      <RxForecastAlerts
        title="Document successfuly uploaded!"
        message="The uploaded file is available for download below.."
        type="success"
        open={successAlertOpen}
        onClose={onCloseSuccessAlert}
      />
    )}
  </div>
);

interface UploadDayWarningProps {
  allowedDaysMessage: string;
}

export const UploadDayWarning: React.FC<UploadDayWarningProps> = ({ allowedDaysMessage }) => {
  const message = `You can only upload the RX forecast file on ${allowedDaysMessage}, please visit back on these days.`;
  return (
    <Alert isOpen={true} variant="informational" size="medium">
     <div className="text-[#1b6ebb] font-bold">
    {message}
    </div>
    </Alert>
  );
};

interface UploadSectionProps {
  onFileUpload: (files: File[] | FileList) => void;
  isUploading?: boolean;
  onValidationError?: (message: string) => void;
}

export const UploadSection: React.FC<UploadSectionProps> = ({ onFileUpload, isUploading = false, onValidationError }) => {
  if (isUploading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spinner />
      </div>
    );
  }

  return (
    <>
      <RxForecastAlertMessage />
      <div className="header text-black text-sm font-bold pt-2">Add document</div>
      <FileUploadBox onFileUpload={onFileUpload} onValidationError={onValidationError} />
    </>
  );
};

interface FileStatusListProps {
  filesUploaded: FileItem[];
}

// export const FileStatusList: React.FC<FileStatusListProps> = ({ filesUploaded }) => (
//   <>
//     {filesUploaded.length > 0 && (
//       <div className="mt-4">
//         <p>Document{filesUploaded.length > 1 ? 's' : ''} {filesUploaded.length}</p>
//       </div>
//     )}
//     {filesUploaded.map((item: FileItem, index) => (
//       <FileUploadStatus
//         key={index}
//         id={`${index}`}
//         showProgress={item.isLoading}
//         fileName={item.file.name}
//         imageUrl={URL.createObjectURL(item.file)}
//         fileSize={Math.round((item.file.size / 1000000) * 10) / 10}
//         uploadStatus={item.status}
//         fileType={item.file.type}
//       />
//     ))}
//   </>
// ); 