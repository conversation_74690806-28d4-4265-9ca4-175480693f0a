import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Suppress CSS parsing warnings
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (typeof args[0] === 'string' && args[0].includes('Could not parse CSS stylesheet')) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Mock all external dependencies with minimal implementations
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn()
}));

const mockUseSelectorWrap = require('../../rtk/rtk-utilities').useSelectorWrap;

jest.mock('../../pages/report', () => () => 'Report Component');
jest.mock('../../pages/lagging-indicator-page', () => () => 'Lagging Indicator');
jest.mock('../EPBCSSyncMonitor', () => () => 'EPBCS Sync Monitor');
jest.mock('../../components/AllocatrInsights/AllocatrInsights', () => () => 'AllocatrInsights');
jest.mock('../../components/SnapShotDropDown/release-week-select', () => ({
  SelectWeek: () => 'Select Week'
}));
jest.mock('../calendarServiceUtils', () => ({
  useCurrentQuarterNbr: jest.fn(() => ({ data: 1 }))
}));
jest.mock('../../components/DashboardDownloadExcel/DashboardDownloadExcel', () => ({
  handleDownloadExcel: jest.fn()
}));
jest.mock('../../util/dateUtils', () => ({
  getNowInPST: jest.fn(() => new Date('2024-01-15'))
}));

// Import the component after mocks are set up
import DashboardTabs from '../../pages/dashboard-tabs';

const mockStore = configureStore({
  reducer: {
    test: (state = {}) => state
  },
});

describe('DashboardTabs Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseSelectorWrap.mockImplementation((selector) => {
      if (typeof selector === 'function') {
        return { data: { smicData: [{ id: 1, name: 'Test Data' }] } };
      }
      return { data: {} };
    });
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <DashboardTabs />
      </Provider>
    );
  };

  describe('Basic Rendering', () => {
    it('should render without crashing', () => {
      expect(() => renderComponent()).not.toThrow();
    });

    it('should call useSelectorWrap multiple times', () => {
      renderComponent();
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle component initialization', () => {
      const { container } = renderComponent();
      expect(container).toBeInTheDocument();
    });
  });

  describe('State Management Coverage', () => {
    it('should handle workSheetFilterList_rn selector', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return { data: { smicData: [{ id: 1, name: 'SMIC Data' }] } };
        }
        return { data: {} };
      });
      
      renderComponent();
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle displayDate_rn selector', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'displayDate_rn') {
          return { data: { fiscalWeekNumber: 1, fiscalYearNumber: 2024 } };
        }
        return { data: {} };
      });
      
      renderComponent();
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle appliedFilter_rn selector', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'appliedFilter_rn') {
          return { data: { division: 'Test Division' } };
        }
        return { data: {} };
      });
      
      renderComponent();
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle null selector returns', () => {
      mockUseSelectorWrap.mockImplementation(() => ({ data: { smicData: null } }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle undefined selector returns', () => {
      mockUseSelectorWrap.mockImplementation(() => ({ data: undefined }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle empty data objects', () => {
      mockUseSelectorWrap.mockImplementation(() => ({ data: {} }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle missing smicData', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return { data: {} }; // No smicData property
        }
        return { data: {} };
      });
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle empty smicData array', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return { data: { smicData: [] } };
        }
        return { data: {} };
      });
      
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Component Lifecycle Coverage', () => {
    it('should handle mount and unmount', () => {
      const { unmount } = renderComponent();
      expect(() => unmount()).not.toThrow();
    });

    it('should handle re-rendering', () => {
      const { rerender } = renderComponent();
      
      rerender(
        <Provider store={mockStore}>
          <DashboardTabs />
        </Provider>
      );
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle multiple instantiations', () => {
      renderComponent();
      renderComponent();
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });
  });

  describe('Error Handling Coverage', () => {
    it('should handle selector errors gracefully', () => {
      mockUseSelectorWrap.mockImplementation(() => {
        throw new Error('Selector error');
      });

      // Should not crash the test when error occurs during component initialization
      expect(() => renderComponent()).toThrow('Selector error');
    });    it('should handle malformed data', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: 'not an object'
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle deeply nested null values', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return { data: { smicData: null } };
        }
        return { data: null };
      });
      
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Data Structure Coverage', () => {
    it('should handle complex data structures', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return {
            data: {
              smicData: [
                { id: 1, name: 'Item 1', nested: { value: 'test' } },
                { id: 2, name: 'Item 2', nested: { value: 'test2' } }
              ]
            }
          };
        }
        if (selector === 'displayDate_rn') {
          return {
            data: {
              fiscalWeekNumber: 42,
              fiscalYearNumber: 2024,
              additionalData: { extra: 'info' }
            }
          };
        }
        if (selector === 'appliedFilter_rn') {
          return {
            data: {
              division: 'Complex Division',
              department: 'Complex Department',
              filters: ['filter1', 'filter2']
            }
          };
        }
        return { data: {} };
      });
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle boolean data types', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: true
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle numeric data types', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: 42
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle string data types', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: 'string data'
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle array data types', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: [1, 2, 3, 'mixed', { array: true }]
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Edge Cases Coverage', () => {
    it('should handle very large datasets', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return {
            data: {
              smicData: Array.from({ length: 1000 }, (_, i) => ({
                id: i,
                name: `Item ${i}`,
                data: `Data ${i}`
              }))
            }
          };
        }
        return { data: {} };
      });
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle empty string values', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: ''
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle zero values', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: 0
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle NaN values', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: NaN
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle Infinity values', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: Infinity
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Function Coverage', () => {
    it('should handle function selector calls', () => {
      let capturedSelector: any = null;
      
      mockUseSelectorWrap.mockImplementation((selector) => {
        capturedSelector = selector;
        return { data: { smicData: [{ id: 1 }] } };
      });
      
      renderComponent();
      
      expect(capturedSelector).toBeDefined();
      if (typeof capturedSelector === 'function') {
        // Test that the selector function can be called
        const result = capturedSelector({ mockState: 'test' });
        expect(result).toBeDefined();
      }
    });

    it('should handle multiple selector types', () => {
      const selectorCalls: any[] = [];
      
      mockUseSelectorWrap.mockImplementation((selector) => {
        selectorCalls.push(selector);
        return { data: {} };
      });
      
      renderComponent();
      
      expect(selectorCalls.length).toBeGreaterThan(0);
      
      // Test different selector types
      selectorCalls.forEach((selector, index) => {
        if (typeof selector === 'function') {
          expect(selector({ test: `state${index}` })).toBeDefined();
        } else if (typeof selector === 'string') {
          expect(selector.length).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Integration Coverage', () => {
    it('should work with different store configurations', () => {
      const customStore = configureStore({
        reducer: {
          custom: (state = { value: 'test' }) => state
        }
      });
      
      expect(() => {
        render(
          <Provider store={customStore}>
            <DashboardTabs />
          </Provider>
        );
      }).not.toThrow();
    });

    it('should handle store state changes', () => {
      const { rerender } = renderComponent();
      
      // Simulate state change
      mockUseSelectorWrap.mockImplementation(() => ({
        data: { smicData: [{ id: 999, name: 'Updated' }] }
      }));
      
      rerender(
        <Provider store={mockStore}>
          <DashboardTabs />
        </Provider>
      );
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });
  });

  describe('Performance Coverage', () => {
    it('should handle rapid re-renders', () => {
      const { rerender } = renderComponent();
      
      // Perform multiple rapid re-renders
      for (let i = 0; i < 10; i++) {
        rerender(
          <Provider store={mockStore}>
            <DashboardTabs />
          </Provider>
        );
      }
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle component stress testing', () => {
      // Render multiple instances
      for (let i = 0; i < 5; i++) {
        const { unmount } = renderComponent();
        unmount();
      }
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });
  });

  describe('User Interaction Coverage', () => {
    it('should handle tab switching', () => {
      renderComponent();
      
      // Tab switching is handled by the Tabs component
      expect(screen.getByText('Performance Summary')).toBeInTheDocument();
      expect(screen.getByText('Leading Indicators')).toBeInTheDocument();
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
    });

    it('should handle drawer operations', () => {
      renderComponent();
      
      // Drawer should be in the DOM
      expect(screen.getByText('EPBCS Sync Monitor')).toBeInTheDocument();
    });

    it('should handle download button interactions', () => {
      renderComponent();
      
      // Download button should be in the DOM
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
    });

    it('should handle sync monitor button click', () => {
      renderComponent();
      
      const syncButton = screen.getByText('EPBCS Sync Monitor');
      expect(syncButton).toBeInTheDocument();
    });
  });

  describe('Component State Coverage', () => {
    it('should handle different tab selections', () => {
      // Test each tab type in renderTabContent function
      const tabConfigs = [
        'Leading Indicators',
        'Performance Summary', 
        'Performance Variance'
      ];
      
      renderComponent();
      // Use getAllByText to handle multiple instances
      expect(screen.getByText('Leading Indicators')).toBeInTheDocument();
      expect(screen.getAllByText('Performance Summary').length).toBeGreaterThan(0);
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
    });

    it('should handle loading states', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [],
          loading: true
        }
      }));
      
      renderComponent();
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
    });

    it('should handle data arrays with content', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return {
            data: {
              smicData: [
                { id: 1, name: 'Test Item 1', category: 'A' },
                { id: 2, name: 'Test Item 2', category: 'B' }
              ]
            }
          };
        }
        return { data: { value: 'test-data' } };
      });
      
      renderComponent();
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
    });
  });

  describe('Data Loading Coverage', () => {
    it('should handle performance summary callback execution', () => {
      const mockCallback = jest.fn();
      
      // Mock the handlePerformanceSummaryData function
      renderComponent();
      
      // Component should render AllocatrInsights component
      expect(screen.getByText('Performance Summary')).toBeInTheDocument();
    });

    it('should handle forecast variance callback execution', () => {
      renderComponent();
      
      // Component should render with forecast variance functionality
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
    });

    it('should handle dashboard loading state transitions', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [{ id: 1, loaded: true }],
          status: 'loaded'
        }
      }));
      
      renderComponent();
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
    });
  });

  describe('Conditional Rendering Coverage', () => {
    it('should render correct tab content based on selection', () => {
      renderComponent();
      
      // All tab options should be available
      expect(screen.getByText('Leading Indicators')).toBeInTheDocument();
      expect(screen.getByText('Performance Summary')).toBeInTheDocument(); 
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
    });

    it('should handle download button conditional logic', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [{ id: 1, name: 'Test' }],
          hasData: true
        }
      }));
      
      renderComponent();
      
      const downloadButton = screen.getByText('Download as Excel');
      expect(downloadButton).toBeInTheDocument();
    });

    it('should handle tooltip and alert icon rendering', () => {
      renderComponent();
      
      // Performance Variance tab should have tooltip functionality
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
    });

    it('should handle drawer content and header', () => {
      renderComponent();
      
      // Drawer header and content should be present
      expect(screen.getByText('EPBCS Sync Monitor')).toBeInTheDocument();
    });
  });

  describe('Event Handler Coverage', () => {
    it('should handle week change functionality', () => {
      renderComponent();
      
      // SelectWeek component should be rendered with week change handler
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle data loading callbacks', () => {
      renderComponent();
      
      // Data loading callbacks should be properly configured
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle download click events', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [{ id: 1 }],
          loaded: true
        }
      }));
      
      renderComponent();
      
      const downloadButton = screen.getByText('Download as Excel');
      expect(downloadButton).toBeInTheDocument();
    });
  });

  describe('Complex Data Scenarios', () => {
    it('should handle nested data structures', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        if (selector === 'workSheetFilterList_rn') {
          return {
            data: {
              smicData: [
                {
                  id: 1,
                  department: {
                    name: 'Sales',
                    manager: { name: 'John Doe', id: 'jdoe' },
                    metrics: {
                      performance: [
                        { quarter: 'Q1', value: 95.5 },
                        { quarter: 'Q2', value: 98.2 }
                      ]
                    }
                  }
                }
              ]
            }
          };
        }
        return {
          data: {
            displayDate: '2024-08-22T10:30:00Z',
            appliedFilters: {
              department: ['Sales', 'Marketing'],
              dateRange: {
                start: '2024-01-01',
                end: '2024-12-31'
              }
            }
          }
        };
      });
      
      renderComponent();
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
    });

    it('should handle mixed data types in selectors', () => {
      mockUseSelectorWrap.mockImplementation((selector) => {
        switch (selector) {
          case 'workSheetFilterList_rn':
            return { data: { smicData: ['string', 123, true, null, { object: 'value' }] } };
          case 'displayDate_rn':
            return { data: 1234567890 }; // timestamp
          case 'appliedFilter_rn':
            return { data: ['filter1', 'filter2', 'filter3'] };
          default:
            return { data: 'default' };
        }
      });
      
      renderComponent();
    });

    it('should handle array data with various structures', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [
            'simple string',
            42,
            { complex: { nested: { data: 'deep value' } } },
            [1, 2, 3, 4, 5],
            null,
            undefined,
            { id: 'last', final: true }
          ]
        }
      }));
      
      renderComponent();
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle component with all features active', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [
            { id: 1, name: 'Active Feature 1', enabled: true },
            { id: 2, name: 'Active Feature 2', enabled: true }
          ],
          displayDate: '2024-08-22',
          appliedFilters: {
            active: true,
            filters: ['all', 'enabled', 'active']
          }
        }
      }));
      
      renderComponent();
      
      // All major UI elements should be present
      expect(screen.getByText('Leading Indicators')).toBeInTheDocument();
      expect(screen.getByText('Performance Summary')).toBeInTheDocument();
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
      expect(screen.getByText('Download as Excel')).toBeInTheDocument();
      expect(screen.getByText('EPBCS Sync Monitor')).toBeInTheDocument();
    });

    it('should handle re-rendering with different props', () => {
      const { rerender } = renderComponent();
      
      // Change selector data and re-render
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [{ id: 'changed', name: 'Updated Data' }],
          displayDate: '2024-12-31',
          appliedFilters: { updated: true }
        }
      }));
      
      rerender(
        <Provider store={mockStore}>
          <DashboardTabs />
        </Provider>
      );
      
      expect(mockUseSelectorWrap).toHaveBeenCalled();
    });

    it('should handle component mount and unmount cycles', () => {
      // Test multiple mount/unmount cycles
      for (let i = 0; i < 3; i++) {
        const { unmount } = renderComponent();
        expect(screen.getByText('Performance Summary')).toBeInTheDocument();
        unmount();
      }
    });
  });

  describe('Edge Case Handling', () => {
    it('should handle selectors returning functions', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: () => [{ id: 1, name: 'Function Result' }],
          callback: jest.fn()
        }
      }));
      
      renderComponent();
    });

    it('should handle circular reference in data', () => {
      const circularData: any = { id: 1, name: 'Test' };
      circularData.self = circularData;
      
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: [circularData]
        }
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle extremely large data sets', () => {
      mockUseSelectorWrap.mockImplementation(() => ({
        data: {
          smicData: Array.from({ length: 10000 }, (_, i) => ({
            id: i,
            name: `Large Dataset Item ${i}`,
            data: Array.from({ length: 100 }, (_, j) => `data-${i}-${j}`)
          }))
        }
      }));
      
      expect(() => renderComponent()).not.toThrow();
    });
  });

  // Additional targeted coverage tests for specific uncovered lines
  describe('Targeted Coverage Tests', () => {
    it('should cover download button enabled state with performance summary data', () => {
      // Mock data to enable download button
      const mockPerformanceData = [{ id: 1, value: 'test' }];
      const mockSmicData = [{ facility: 'Test Facility' }];
      
      mockUseSelectorWrap.mockImplementation(() => ({
        data: { 
          smicData: mockSmicData,
          loading: false 
        },
        performanceSummaryData: mockPerformanceData,
        forecastVarianceData: []
      }));

      renderComponent();
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });

    it('should cover download button enabled state with forecast variance data', () => {
      // Mock data to enable download button for forecast variance
      const mockForecastData = [{ id: 1, variance: 'test' }];
      const mockSmicData = [{ facility: 'Test Facility' }];
      
      mockUseSelectorWrap.mockImplementation(() => ({
        data: { 
          smicData: mockSmicData,
          loading: false 
        },
        performanceSummaryData: [],
        forecastVarianceData: mockForecastData
      }));

      renderComponent();
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });


    it('should cover tab rendering with different configurations', () => {
      renderComponent();
      
      // Find tab elements
      const tabs = screen.getAllByTestId('tab');
      expect(tabs.length).toBeGreaterThan(0);
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });

    it('should cover switch case logic for tab content rendering', () => {
      // Test each switch case individually
      renderComponent();
      
      // Verify all tab content is available
      expect(screen.getAllByText('AllocatrInsights').length).toBeGreaterThan(0);
    });

    it('should cover loading state with dashboard loading true', () => {
      mockUseSelectorWrap.mockImplementation(() => ({ 
        data: { smicData: [], loading: true } 
      }));
      renderComponent();
      
      // Should disable download button when loading
      const downloadElements = screen.getAllByText(/Download as Excel/i);
      downloadElements.forEach(element => {
        expect(element.closest('a')).toHaveClass('opacity-50');
      });
    });

    it('should cover download disabled state logic', () => {
      // Test with no performance summary or forecast variance data
      mockUseSelectorWrap.mockImplementation(() => ({
        data: { smicData: [], loading: false },
        performanceSummaryData: [],
        forecastVarianceData: []
      }));

      renderComponent();
      
      // Download button should be disabled
      const downloadElements = screen.getAllByText(/Download as Excel/i);
      downloadElements.forEach(element => {
        expect(element.closest('a')).toHaveClass('opacity-50');
      });
    });

  

    it('should test performance summary data loading callback', () => {
      renderComponent();
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });

    it('should test forecast variance data loading callback', () => {
      renderComponent();
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });

    it('should cover conditional rendering of performance variance tooltip', () => {
      renderComponent();
      
      // Look for Performance Variance text which includes tooltip logic
      expect(screen.getByText('Performance Variance')).toBeInTheDocument();
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });


    it('should cover download click functionality', () => {
      // Mock data to enable download
      mockUseSelectorWrap.mockImplementation(() => ({
        data: { smicData: [{ facility: 'Test' }], loading: false },
        performanceSummaryData: { id: 'Total', divisions: [{ id: '34' }] },
        forecastVarianceData: null
      }));

      renderComponent();
      
      // Find download button and click it
      const downloadButtons = screen.getAllByText(/Download as Excel/i);
      if (downloadButtons.length > 0) {
        const enabledButton = downloadButtons.find(btn => 
          !btn.closest('a')?.classList.contains('opacity-50')
        );
        if (enabledButton) {
          fireEvent.click(enabledButton);
        }
      }
      expect(screen.getByText('AllocatrInsights')).toBeInTheDocument();
    });
  });
});
