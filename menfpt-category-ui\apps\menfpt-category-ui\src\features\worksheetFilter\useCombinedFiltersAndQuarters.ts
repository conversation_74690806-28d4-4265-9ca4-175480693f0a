import { useDispatch } from 'react-redux';
import { useGetCombinedFiltersAndQuartersQuery } from '../../server/Api/menfptCategoryAPI';
import { CombinedFilterAndQuartersQuery } from '../../server/Query/combinedFilterAndQuartersQuery';
import { MenfptWorkSheetFilterListQuery } from '../../server/Query/menfptWorkSheetFiltersQuery';
import { setWorkSheetFilterList, setQuarters, setRoleMappingInfo } from '../../server/Reducer/menfpt-category.slice';
import { useEffect, useMemo } from 'react';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useLocation } from 'react-router-dom';
import { worksheetFilterConfig } from './worksheetFilterConfig';
import { generateQuartersForYear } from './utils/quarterUtils';

/**
 * Custom hook to fetch both worksheet filter data and quarters data in a single API call
 * Only fetches quarters data for routes where timeframe is displayed
 * @returns Object containing worksheet filters data, quarters data, and loading state
 */
export const useCombinedFiltersAndQuarters = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { data: userInfo } = useSelectorWrap('userInfo_rn') || { data: {} };

  // Check if the current route should display timeframe
  const shouldDisplayTimeFrame = useMemo(() => {
    // Extract the route without the leading slash
    const pathname = location.pathname;

    // Split the path by '/' and get the last segment which is the actual route
    // This handles both '/dashboard' and '/menfpt/dashboard' and '/memsp-ui-shell/menfpt/dashboard'
    const pathSegments = pathname.split('/').filter(segment => segment !== '');
    const lastSegment = pathSegments[pathSegments.length - 1];

    return worksheetFilterConfig.isDisplayTimeFrame.includes(lastSegment);
  }, [location.pathname]);

  // Prepare query parameters
  const currentYear = new Date().getFullYear();
  const workSheetFilterRequest = { userId: userInfo?.userId };

  // Create the query parameters based on whether timeframe should be displayed
  const queryArgs = shouldDisplayTimeFrame
    ? {
        query: CombinedFilterAndQuartersQuery,
        variables: {
          workSheetFilterRequest,
          timeRangeInYearReq: {
            fiscalYearNumber: [currentYear, currentYear - 1, currentYear - 2],
            level: "Week"
          }
        },
      }
    : {
        query: MenfptWorkSheetFilterListQuery,
        variables: {
          workSheetFilterRequest
        },
      };

  // Use the RTK Query hook which handles caching automatically
  const { data: combinedData, isLoading, isError } = useGetCombinedFiltersAndQuartersQuery(queryArgs, {
    // Skip the query if we don't have a user ID
    skip: !userInfo?.userId,
  });

  // Process and store data in Redux when it's received
  useEffect(() => {
    if (combinedData) {
      // Process and store worksheet filter data
      const worksheetFilterData = combinedData?.getWorkSheetFilter;
      if (worksheetFilterData) {
        dispatch(setWorkSheetFilterList(worksheetFilterData));
        dispatch(setRoleMappingInfo({userRole: worksheetFilterData.userRole}));
      }

      // Process and store quarters data if needed for this route
      if (shouldDisplayTimeFrame && combinedData?.getAllTimeRangeInYear) {
        const currentDate = new Date();
        const quartersData = combinedData.getAllTimeRangeInYear
          .map((quarter) => ({
            ...quarter,
            fiscalQuarterNumber: quarter.fiscalQuarterNumber,
            fiscalQuarterId: parseInt(`${quarter.fiscalYearNumber}${quarter.fiscalQuarterNumber.toString().padStart(2, '0')}`)
          }))
          .filter((quarter) => {
            if (quarter.fiscalYearNumber === currentYear) {
              const startDate = new Date(quarter.fiscalQuarterStartDate);              
              return currentDate >= startDate ;
            }
            return true;
          });
          dispatch(setQuarters(quartersData));
      }
    }
  }, [combinedData, dispatch, shouldDisplayTimeFrame, currentYear]);

  // Extract worksheet filter data
  const worksheetFiltersList = combinedData?.getWorkSheetFilter || [];

  return {
    worksheetFiltersList,
    filterLoading: isLoading,
    filterData: worksheetFiltersList?.smicData,
    quartersData: combinedData?.getAllQuartersInYr || [],
    shouldDisplayTimeFrame,
    isError
  };
};
