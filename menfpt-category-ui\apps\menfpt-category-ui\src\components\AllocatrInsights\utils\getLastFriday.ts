export function getLastFriday(): string {
  const now = new Date();
  const dayOfWeek = now.getUTCDay();
  let daysToSubtract;
  if (dayOfWeek === 5) {
    daysToSubtract = 7;
  } else if (dayOfWeek === 6) {
    daysToSubtract = 1;
  } else {
    daysToSubtract = (dayOfWeek + 2) % 7;
  }

  const lastFriday = new Date(Date.UTC(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate()
  ));
  lastFriday.setUTCDate(lastFriday.getUTCDate() - daysToSubtract);
  lastFriday.setUTCHours(23, 59, 59, 0); // End of day in UTC

  return lastFriday.toISOString();
}

export function getFridayOfSelectedWeek(weekEndDate: string): string {
  // weekEndDate is in MM/DD/YYYY format
  const [month, day, year] = weekEndDate.split('/').map(Number);
  const weekEnd = new Date(Date.UTC(year, month - 1, day));
  
  // Find the Friday of this week (weekEnd is Saturday, so Friday is 1 day before)
  const friday = new Date(weekEnd);
  friday.setUTCDate(friday.getUTCDate() - 1);
  friday.setUTCHours(23, 59, 59, 0); // End of day in UTC
  
  return friday.toISOString();
}

export function getLastFridayInQuarter(dateStr: string): string {
  const [month, day, year] = dateStr.split('/').map(Number);
  let endDate = new Date(Date.UTC(year, month - 1, day));
  while (endDate.getUTCDay() !== 5) {
    endDate.setUTCDate(endDate.getUTCDate() - 1);
  }
  const isoDate = endDate.toISOString().split('T')[0];
  return isoDate;
}
export function getNextMondayAfterQuarter(dateStr: string): string {
  const [month, day, year] = dateStr.split('/').map(Number);
  let date = new Date(Date.UTC(year, month - 1, day));
  const currentDay = date.getUTCDay(); 
  const daysToAdd = (8 - currentDay) % 7 || 7; 

  date.setUTCDate(date.getUTCDate() + daysToAdd);

  const isoDate = date.toISOString().split('T')[0];
  return isoDate;
}
