# Ubuntu Base image with nodejs14.x
FROM escoacrprod01.azurecr.io/esco/ubuntu22/nodejs:18.x
USER root
#FROM node

# Adding application specific group and user
RUN groupadd -g 1999 menfpt-grp && useradd -r -u 1999 -g root menfpt

# Creating app directory and switching
WORKDIR /app


COPY --chown=menfpt:menfpt-grp ./dist/apps/menfpt-category-ui-static-server /app/
COPY .npmrc /app
COPY package.json /app
COPY package-lock.json /app
RUN npm i --package-lock-only
RUN npm ci --exclude=dev

EXPOSE 3010
EXPOSE 8080

# Switching user to application user from root
USER menfpt

# Running the application
CMD [ "node", "main.js"]
