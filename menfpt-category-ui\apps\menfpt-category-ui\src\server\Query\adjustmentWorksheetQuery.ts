export const adjustmentWorksheetQuery =`query GetAdjustmentWorksheetData($adjustmentWorksheetReq: AdjustmentWorksheetReq) {
  getAdjustmentWorksheetData(adjustmentWorksheetReq: $adjustmentWorksheetReq) {
    adjustmentWorksheetData {
      aggregatedLevel
      subRow
      mainRow
      line1PublicToSalesNbr
      line1PublicToSalesPct
      line5BookGrossProfitNbr
      line5BookGrossProfitPct
      line5MarkDownsNbr
      line5MarkDownsPct
      line5ShrinkNbr
      line5ShrinkPct
      line5RealGrossProfitNbr
      line5RealGrossProfitPct
      line6SuppliesPackagingNbr
      line7RetailsAllowancesNbr
      line7RetailsAllowancesPct
      line7RetailsSellingAllowancesNbr
      line7RetailsSellingAllowancesPct
      line7RetailsNonSellingAllowancesNbr
      line7RetailsNonSellingAllowancesPct
      line8RealGrossProfitNbr
      line8RealGrossProfitPct
      fiscalYearNbr
      fiscalQuarterNbr
      fiscalPeriodNbr
      fiscalWeekNbr
      fiscalWeekEnding
      comment
      forecastType
      lastUpdatedUserRole
      reason
      state
      versionNbr
      createdTs
      updatedTs
      sourceTs
      createdBy
      updatedBy
    }
    forecastData {
      fiscalYearNbr
      fiscalQuarterNbr
      fiscalPeriodNbr
      fiscalWeekNbr
      smicCategoryId
      deptId
      divisionId
      line1PublicToSalesNbr
      line5BookGrossProfitNbr
      line5BookGrossProfitPct
      line1BillOutGrossPct
      line5MarkDownsNbr
      line5MarkDownsPct
      line5ShrinkNbr
      line5ShrinkPct
      line4CostOfSalesNbr
      line5RealGrossProfitNbr
      line5RealGrossProfitPct
      line6SuppliesPackagingNbr
      line7RetailsAllowancesNbr
      line7RetailsAllowancesPct
      line8RealGrossProfitNbr
      line8RealGrossProfitPct
      comment
      forecastType
      lastUpdatedUserRole
      reason
      state
      versionNbr
      createdTs
      updatedTs
      sourceTs
      createdBy
      updatedBy
    }
  }
}
`