{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\pages\\\\dashboard-tabs.tsx\";\nimport React, { useState, useRef } from 'react';\n// import { createPortal } from 'react-dom';\nimport Report from './report';\nimport \"./dashboard-tabs.scss\";\nimport Drawer from '@albertsons/uds/molecule/Drawer';\nimport Button from '@albertsons/uds/molecule/Button';\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\n// Update the import path and casing to match the actual file location\nimport { SelectWeek } from './../components/SnapShotDropDown/release-week-select';\nimport { CircleAlert } from 'lucide-react';\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';\nimport Icon from '@albertsons/uds/molecule/Link';\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg';\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\nimport { getNowInPST } from '../util/dateUtils';\nimport { format } from 'date-fns-tz';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar TabsLabels = /*#__PURE__*/function (TabsLabels) {\n  TabsLabels[\"LEADING_INDICATORS\"] = \"Leading Indicators\";\n  TabsLabels[\"PERFORMANCE_SUMMARY\"] = \"Performance Summary\";\n  TabsLabels[\"FORECAST_VARIANCE\"] = \"Performance Variance\";\n  return TabsLabels;\n}(TabsLabels || {}); // const tabClassNames = {\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\n// };\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\nconst DashboardTabs = () => {\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [selectedWeek, setSelectedWeek] = useState(null);\n  const [performanceSummaryData, setPerformanceSummaryData] = useState([]);\n  const [forecastVarianceData, setForecastVarianceData] = useState([]);\n  const [dashboardLoading, setDashboardLoading] = useState(true);\n  const {\n    data: worksheetFilters = {}\n  } = useSelectorWrap('workSheetFilterList_rn');\n  const [showMessage, setShowMessage] = useState(false);\n  const [tooltipPosition, setTooltipPosition] = useState(null);\n  const alertIconRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const smicData = worksheetFilters.smicData || [];\n\n  // Safely access displayDate with a fallback\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\n  const displayDate = (displayDateSelector == null ? void 0 : displayDateSelector.data) || {};\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n  const handleTabChange = tabIndex => {\n    setDashboardLoading(true); // Reset loading state on tab change\n    setSelectedTab(visibleTabs[tabIndex]);\n  };\n  const handleSyncMonitorClick = () => {\n    setIsDrawerOpen(true);\n  };\n  const handleWeekChange = item => {\n    setSelectedWeek(item);\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\n  };\n  const handlePerformanceSummaryData = data => {\n    setPerformanceSummaryData(data);\n    setDashboardLoading(false);\n  };\n  const handleForecastVarianceData = data => {\n    setForecastVarianceData(data);\n    setDashboardLoading(false);\n  };\n  const renderTabContent = tab => {\n    switch (tab) {\n      case TabsLabels.LEADING_INDICATORS:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Report, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.PERFORMANCE_SUMMARY:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.PERFORMANCE_SUMMARY,\n            onDataLoaded: handlePerformanceSummaryData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.FORECAST_VARIANCE:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.FORECAST_VARIANCE,\n            onDataLoaded: handleForecastVarianceData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const visibleTabs = [TabsLabels.LEADING_INDICATORS, TabsLabels.PERFORMANCE_SUMMARY, TabsLabels.FORECAST_VARIANCE];\n  const classes = 'flex justify-center items-center h-48 text';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-2 py-2 overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\",\n          style: {\n            margin: '5px 10px',\n            padding: '5px',\n            width: '600px',\n            borderColor: 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            initialTab: visibleTabs.indexOf(selectedTab),\n            variant: \"light\",\n            onChange: handleTabChange,\n            className: \"w-full border-transparent dashboard-tab\",\n            children: visibleTabs.map((tab, idx) => /*#__PURE__*/_jsxDEV(Tab, {\n              className: classes,\n              children: /*#__PURE__*/_jsxDEV(Tab.Header, {\n                children: tab === TabsLabels.FORECAST_VARIANCE ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  tabIndex: 2,\n                  onBlur: () => setShowMessage(false),\n                  style: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    gap: '4px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tool-tip-initilizer-top\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      zIndex: 9999,\n                      anchor: \"top\",\n                      variant: \"dark\",\n                      className: 'uds-tooltip-top',\n                      label: ' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.',\n                      children: /*#__PURE__*/_jsxDEV(CircleAlert, {\n                        size: 16,\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        color: \" #1B6EBB\",\n                        onClick: e => {\n                          e.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 19\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 25\n                  }, this), tab]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 23\n                }, this) : tab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          before: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-4 h-4 flex items-center text-[#1B6EBB]\",\n            children: [\" \", /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 75\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this),\n          className: `flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\n              ${dashboardLoading || selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length || selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length ? 'opacity-50 pointer-events-none' : ''}`,\n          onClick: dashboardLoading || selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length || selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length ? undefined : () => {\n            if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\n              handleDownloadExcel(performanceSummaryData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n            } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\n              handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\n            }\n          },\n          children: \"Download as Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-6\",\n          children: /*#__PURE__*/_jsxDEV(SelectWeek, {\n            weekChange: handleWeekChange,\n            selectedTab: selectedTab\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"sync-button\",\n            size: \"xs\",\n            variant: \"secondary\",\n            onClick: handleSyncMonitorClick,\n            children: \"EPBCS Sync Monitor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: renderTabContent(selectedTab)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      isOpen: isDrawerOpen,\n      setOpen: setIsDrawerOpen,\n      hideBackdrop: false,\n      width: \"608px\",\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"EPBCS Sync Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(EPBCSSyncMonitor, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\nexport default DashboardTabs;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Report", "Drawer", "<PERSON><PERSON>", "useSelectorWrap", "Tabs", "Tab", "EPBCSSyncMonitor", "AllocatrInsights", "SelectWeek", "Circle<PERSON>lert", "<PERSON><PERSON><PERSON>", "Icon", "ReactComponent", "Download", "handleDownloadExcel", "getNowInPST", "format", "jsxDEV", "_jsxDEV", "TabsLabels", "downloadedDate", "DashboardTabs", "selectedTab", "setSelectedTab", "PERFORMANCE_SUMMARY", "isDrawerOpen", "setIsDrawerOpen", "selectedWeek", "setSelectedWeek", "performanceSummaryData", "setPerformanceSummaryData", "forecastVarianceData", "setForecastVarianceData", "dashboardLoading", "setDashboardLoading", "data", "worksheetFilters", "showMessage", "setShowMessage", "tooltipPosition", "setTooltipPosition", "alertIconRef", "tooltipRef", "smicData", "displayDateSelector", "displayDate", "appliedFilters", "handleTabChange", "tabIndex", "visibleTabs", "handleSyncMonitorClick", "handleWeekChange", "item", "handlePerformanceSummaryData", "handleForecastVarianceData", "renderTabContent", "tab", "LEADING_INDICATORS", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataLoaded", "FORECAST_VARIANCE", "classes", "className", "style", "margin", "padding", "width", "borderColor", "initialTab", "indexOf", "variant", "onChange", "map", "idx", "Header", "onBlur", "display", "alignItems", "gap", "position", "zIndex", "anchor", "label", "size", "cursor", "color", "onClick", "e", "stopPropagation", "before", "length", "undefined", "weekChange", "isOpen", "<PERSON><PERSON><PERSON>", "hideBackdrop", "header"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\n// import { createPortal } from 'react-dom';\r\nimport Report from './report';\r\nimport LaggingIndicatorPage from './lagging-indicator-page';\r\nimport \"./dashboard-tabs.scss\";\r\nimport Drawer from '@albertsons/uds/molecule/Drawer';\r\nimport Button from '@albertsons/uds/molecule/Button';\r\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\r\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\r\nimport Tag from '@albertsons/uds/molecule/Tag';\r\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\r\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\r\n// Update the import path and casing to match the actual file location\r\nimport {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';\r\nimport { CircleAlert } from 'lucide-react';\r\nimport { useCurrentQuarterNbr } from '../features/calendarServiceUtils';\r\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';\r\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; \r\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\r\nimport { getNowInPST } from '../util/dateUtils';\r\nimport { format } from 'date-fns-tz';\r\n\r\nenum TabsLabels {\r\n  LEADING_INDICATORS = 'Leading Indicators',\r\n  PERFORMANCE_SUMMARY = 'Performance Summary',\r\n  FORECAST_VARIANCE = 'Performance Variance'\r\n}\r\n\r\n// const tabClassNames = {\r\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\r\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\r\n// };\r\n\r\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\r\nconst DashboardTabs = () => {\r\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);\r\n  const [performanceSummaryData, setPerformanceSummaryData] = useState<any[]>([]);\r\n  const [forecastVarianceData, setForecastVarianceData] = useState<any[]>([]);\r\n  const [dashboardLoading, setDashboardLoading] = useState(true);\r\n  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');\r\n   const [showMessage, setShowMessage] = useState(false);\r\n  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);\r\n  const alertIconRef = useRef<HTMLDivElement>(null);\r\n  const tooltipRef = useRef<HTMLDivElement>(null);\r\n  const smicData = worksheetFilters.smicData || [];\r\n\r\n  // Safely access displayDate with a fallback\r\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\r\n  const displayDate = displayDateSelector?.data || {};\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n\r\n  const handleTabChange = (tabIndex: number) => {\r\n    setDashboardLoading(true); // Reset loading state on tab change\r\n    setSelectedTab(visibleTabs[tabIndex]);\r\n  };\r\n\r\n  const handleSyncMonitorClick = () => {\r\n    setIsDrawerOpen(true);\r\n  };\r\n\r\n  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {\r\n    setSelectedWeek(item);\r\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\r\n  };\r\n\r\n  const handlePerformanceSummaryData = (data: any[]) => {\r\n    setPerformanceSummaryData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n\r\n  const handleForecastVarianceData = (data: any[]) => {\r\n    setForecastVarianceData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n  const renderTabContent = (tab: TabsLabels) => {\r\n    switch (tab) {\r\n      case TabsLabels.LEADING_INDICATORS:\r\n        return <div><Report /></div>;\r\n      case TabsLabels.PERFORMANCE_SUMMARY:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>\r\n      case TabsLabels.FORECAST_VARIANCE:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const visibleTabs = [\r\n    TabsLabels.LEADING_INDICATORS,\r\n    TabsLabels.PERFORMANCE_SUMMARY,\r\n    TabsLabels.FORECAST_VARIANCE\r\n  ];\r\n  const classes = 'flex justify-center items-center h-48 text';\r\n\r\n  return (\r\n    <div>\r\n\r\n      <div className=\"flex items-center justify-between px-2 py-2 overflow-x-auto\">\r\n        <div className=\"tabs-container\">\r\n\r\n          <div\r\n            className=\"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\"\r\n            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}\r\n          >\r\n\r\n            <Tabs\r\n              initialTab={visibleTabs.indexOf(selectedTab)}\r\n              variant='light'\r\n              onChange={handleTabChange}\r\n              className='w-full border-transparent dashboard-tab'\r\n            >\r\n              {visibleTabs.map((tab, idx) => (\r\n                <Tab className={classes} key={tab}>\r\n                  <Tab.Header>\r\n\r\n                    {tab === TabsLabels.FORECAST_VARIANCE ? (\r\n                      <span\r\n                        tabIndex={2}\r\n                        onBlur={() => setShowMessage(false)}\r\n                        style={{\r\n                          display: 'inline-flex',\r\n                          alignItems: 'center',\r\n                          gap: '4px',\r\n                          position: 'relative'\r\n                        }}>\r\n\r\n                        <div \r\n                          className=\"relative inline-block\"\r\n                        >\r\n                          <span className='tool-tip-initilizer-top'></span>\r\n                        <Tooltip\r\n                  zIndex={9999}\r\n                  anchor='top'\r\n                  variant='dark'\r\n                  className={'uds-tooltip-top'}\r\n                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>\r\n                  <CircleAlert\r\n                    size={16}\r\n                    style={{ cursor: 'pointer' }}\r\n                    color=\" #1B6EBB\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                        </div>\r\n                        \r\n                        {tab}\r\n                      </span>\r\n                    ) : (\r\n                      tab\r\n                    )}\r\n                  </Tab.Header>\r\n              </Tab>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </div>\r\n        <div className=\"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\">\r\n          <Icon\r\n            before={\r\n              <span className=\"w-4 h-4 flex items-center text-[#1B6EBB]\"> <Download/> </span>\r\n            }\r\n            className={`flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\r\n              ${(dashboardLoading || (selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length) || (selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length)) ? 'opacity-50 pointer-events-none': '' }`}\r\n            onClick={\r\n              dashboardLoading ||\r\n              (selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !performanceSummaryData.length) ||\r\n              (selectedTab === TabsLabels.FORECAST_VARIANCE && !forecastVarianceData.length)\r\n                ? undefined\r\n                : () => {\r\n                    if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\r\n                      handleDownloadExcel(performanceSummaryData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\r\n                    } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\r\n                      handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\r\n                    }\r\n                  }\r\n            }\r\n          >Download as Excel\r\n          </Icon>\r\n        </div>\r\n      <div className='flex items-center gap-4'>\r\n           <div className='mr-6'>\r\n        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>\r\n      </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              className=\"sync-button\"\r\n              size=\"xs\"\r\n              variant=\"secondary\"\r\n              onClick={handleSyncMonitorClick}\r\n            >\r\n              EPBCS Sync Monitor\r\n            </Button>\r\n          </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        {renderTabContent(selectedTab)}\r\n      </div>\r\n\r\n      <Drawer\r\n        anchor=\"right\"\r\n        isOpen={isDrawerOpen}\r\n        setOpen={setIsDrawerOpen}\r\n        hideBackdrop={false}\r\n        width=\"608px\"\r\n        header={<div>EPBCS Sync Monitor</div>}\r\n      >\r\n        <EPBCSSyncMonitor />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,iCAAiC;AACpD,OAAOC,MAAM,MAAM,iCAAiC;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAOC,IAAI,IAAIC,GAAG,QAAQ,+BAA+B;AAEzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E;AACA,SAAUC,UAAU,QAAQ,sDAAsD;AAClF,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAOC,OAAO,MAAM,kCAAkC;AAAC,OAAOC,IAAI,MAAM,+BAA+B;AACvG,SAASC,cAAc,IAAIC,QAAQ,QAAQ,uCAAuC;AAClF,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,IAEhCC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA,EAAVA,UAAU,SAMf;AACA;AACA;AACA;AAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACD,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;AAC1D,MAAMM,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAACqB,UAAU,CAACK,mBAAmB,CAAC;EAC9E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAyE,IAAI,CAAC;EAC9H,MAAM,CAAC+B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhC,QAAQ,CAAQ,EAAE,CAAC;EAC/E,MAAM,CAACiC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlC,QAAQ,CAAQ,EAAE,CAAC;EAC3E,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM;IAAEqC,IAAI,EAAEC,gBAAgB,GAAG,CAAC;EAAE,CAAC,GAAGjC,eAAe,CAAC,wBAAwB,CAAC;EAChF,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAM2C,YAAY,GAAG1C,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAM2C,UAAU,GAAG3C,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM4C,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,IAAI,EAAE;;EAEhD;EACA,MAAMC,mBAAmB,GAAGzC,eAAe,CAAC,gBAAgB,CAAC;EAC7D,MAAM0C,WAAW,GAAG,CAAAD,mBAAmB,oBAAnBA,mBAAmB,CAAET,IAAI,KAAI,CAAC,CAAC;EACnD,MAAM;IAAEA,IAAI,EAAEW;EAAe,CAAC,GAAG3C,eAAe,CAAC,kBAAkB,CAAC;EAEpE,MAAM4C,eAAe,GAAIC,QAAgB,IAAK;IAC5Cd,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3BX,cAAc,CAAC0B,WAAW,CAACD,QAAQ,CAAC,CAAC;EACvC,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyB,gBAAgB,GAAIC,IAAqE,IAAK;IAClGxB,eAAe,CAACwB,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMC,4BAA4B,GAAIlB,IAAW,IAAK;IACpDL,yBAAyB,CAACK,IAAI,CAAC;IAC/BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMoB,0BAA0B,GAAInB,IAAW,IAAK;IAClDH,uBAAuB,CAACG,IAAI,CAAC;IAC7BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMqB,gBAAgB,GAAIC,GAAe,IAAK;IAC5C,QAAQA,GAAG;MACT,KAAKrC,UAAU,CAACsC,kBAAkB;QAChC,oBAAOvC,OAAA;UAAAwC,QAAA,eAAKxC,OAAA,CAAClB,MAAM;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9B,KAAK3C,UAAU,CAACK,mBAAmB;QACjC,oBAAON,OAAA;UAAAwC,QAAA,eAAKxC,OAAA,CAACX,gBAAgB;YAACe,WAAW,EAAEH,UAAU,CAACK,mBAAoB;YAACuC,YAAY,EAAEV;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChI,KAAK3C,UAAU,CAAC6C,iBAAiB;QAC/B,oBAAO9C,OAAA;UAAAwC,QAAA,eAAKxC,OAAA,CAACX,gBAAgB;YAACe,WAAW,EAAEH,UAAU,CAAC6C,iBAAkB;YAACD,YAAY,EAAET;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5H;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMb,WAAW,GAAG,CAClB9B,UAAU,CAACsC,kBAAkB,EAC7BtC,UAAU,CAACK,mBAAmB,EAC9BL,UAAU,CAAC6C,iBAAiB,CAC7B;EACD,MAAMC,OAAO,GAAG,4CAA4C;EAE5D,oBACE/C,OAAA;IAAAwC,QAAA,gBAEExC,OAAA;MAAKgD,SAAS,EAAC,6DAA6D;MAAAR,QAAA,gBAC1ExC,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAR,QAAA,eAE7BxC,OAAA;UACEgD,SAAS,EAAC,8IAA8I;UACxJC,KAAK,EAAE;YAAEC,MAAM,EAAE,UAAU;YAAEC,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAC,OAAO;YAAEC,WAAW,EAAE;UAAc,CAAE;UAAAb,QAAA,eAGzFxC,OAAA,CAACd,IAAI;YACHoE,UAAU,EAAEvB,WAAW,CAACwB,OAAO,CAACnD,WAAW,CAAE;YAC7CoD,OAAO,EAAC,OAAO;YACfC,QAAQ,EAAE5B,eAAgB;YAC1BmB,SAAS,EAAC,yCAAyC;YAAAR,QAAA,EAElDT,WAAW,CAAC2B,GAAG,CAAC,CAACpB,GAAG,EAAEqB,GAAG,kBACxB3D,OAAA,CAACb,GAAG;cAAC6D,SAAS,EAAED,OAAQ;cAAAP,QAAA,eACtBxC,OAAA,CAACb,GAAG,CAACyE,MAAM;gBAAApB,QAAA,EAERF,GAAG,KAAKrC,UAAU,CAAC6C,iBAAiB,gBACnC9C,OAAA;kBACE8B,QAAQ,EAAE,CAAE;kBACZ+B,MAAM,EAAEA,CAAA,KAAMzC,cAAc,CAAC,KAAK,CAAE;kBACpC6B,KAAK,EAAE;oBACLa,OAAO,EAAE,aAAa;oBACtBC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,KAAK;oBACVC,QAAQ,EAAE;kBACZ,CAAE;kBAAAzB,QAAA,gBAEFxC,OAAA;oBACEgD,SAAS,EAAC,uBAAuB;oBAAAR,QAAA,gBAEjCxC,OAAA;sBAAMgD,SAAS,EAAC;oBAAyB;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnD5C,OAAA,CAACR,OAAO;sBACd0E,MAAM,EAAE,IAAK;sBACbC,MAAM,EAAC,KAAK;sBACZX,OAAO,EAAC,MAAM;sBACdR,SAAS,EAAE,iBAAkB;sBAC7BoB,KAAK,EAAE,0HAA2H;sBAAA5B,QAAA,eAClIxC,OAAA,CAACT,WAAW;wBACV8E,IAAI,EAAE,EAAG;wBACTpB,KAAK,EAAE;0BAAEqB,MAAM,EAAE;wBAAU,CAAE;wBAC7BC,KAAK,EAAC,UAAU;wBAChBC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACrB;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,EAELN,GAAG;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GAEPN;cACD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC,GAxCeN,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyC9B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJ5C,OAAA;QAAKgD,SAAS,EAAC,oEAAoE;QAAAR,QAAA,eACjFxC,OAAA,CAACP,IAAI;UACHkF,MAAM,eACJ3E,OAAA;YAAMgD,SAAS,EAAC,0CAA0C;YAAAR,QAAA,GAAC,GAAC,eAAAxC,OAAA,CAACL,QAAQ;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC/E;UACDI,SAAS,EAAE;AACvB,gBAAiBjC,gBAAgB,IAAKX,WAAW,KAAKH,UAAU,CAACK,mBAAmB,IAAI,CAACK,sBAAsB,CAACiE,MAAO,IAAKxE,WAAW,KAAKH,UAAU,CAAC6C,iBAAiB,IAAI,CAACjC,oBAAoB,CAAC+D,MAAO,GAAI,gCAAgC,GAAE,EAAE,EAAI;UACzOJ,OAAO,EACLzD,gBAAgB,IACfX,WAAW,KAAKH,UAAU,CAACK,mBAAmB,IAAI,CAACK,sBAAsB,CAACiE,MAAO,IACjFxE,WAAW,KAAKH,UAAU,CAAC6C,iBAAiB,IAAI,CAACjC,oBAAoB,CAAC+D,MAAO,GAC1EC,SAAS,GACT,MAAM;YACJ,IAAIzE,WAAW,KAAKH,UAAU,CAACK,mBAAmB,EAAE;cAClDV,mBAAmB,CAACe,sBAAsB,EAAEc,QAAQ,EAAEG,cAAc,EAAE,wDAAwD1B,cAAc,OAAO,CAAC;YACtJ,CAAC,MAAM,IAAIE,WAAW,KAAKH,UAAU,CAAC6C,iBAAiB,EAAE;cACvDlD,mBAAmB,CAACiB,oBAAoB,EAAEY,QAAQ,EAAEG,cAAc,EAAE,qDAAqD1B,cAAc,OAAO,CAAC;YACjJ;UACF,CACL;UAAAsC,QAAA,EACF;QACD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR5C,OAAA;QAAKgD,SAAS,EAAC,yBAAyB;QAAAR,QAAA,gBACnCxC,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAR,QAAA,eACxBxC,OAAA,CAACV,UAAU;YAACwF,UAAU,EAAE7C,gBAAiB;YAAC7B,WAAW,EAAEA;UAAY;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACF5C,OAAA;UAAKgD,SAAS,EAAC,yBAAyB;UAAAR,QAAA,eACtCxC,OAAA,CAAChB,MAAM;YACLgE,SAAS,EAAC,aAAa;YACvBqB,IAAI,EAAC,IAAI;YACTb,OAAO,EAAC,WAAW;YACnBgB,OAAO,EAAExC,sBAAuB;YAAAQ,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN5C,OAAA;MAAKgD,SAAS,EAAC,iBAAiB;MAAAR,QAAA,EAC7BH,gBAAgB,CAACjC,WAAW;IAAC;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAEN5C,OAAA,CAACjB,MAAM;MACLoF,MAAM,EAAC,OAAO;MACdY,MAAM,EAAExE,YAAa;MACrByE,OAAO,EAAExE,eAAgB;MACzByE,YAAY,EAAE,KAAM;MACpB7B,KAAK,EAAC,OAAO;MACb8B,MAAM,eAAElF,OAAA;QAAAwC,QAAA,EAAK;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eAEtCxC,OAAA,CAACZ,gBAAgB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAED,eAAezC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}