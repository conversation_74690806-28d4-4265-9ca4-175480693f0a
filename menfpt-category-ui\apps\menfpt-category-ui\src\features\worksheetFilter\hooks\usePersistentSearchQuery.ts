import { useRef, useState } from 'react';

/**
 * usePersistentSearchQuery
 * Manages a search query state and prevents clearing on blur (by ignoring onChange("") if it happens as a result of blur).
 * Returns: [searchQuery, handleSearchQuery, handleBlur]
 */
export function usePersistentSearchQuery(initialValue = ''): [string, (query: string) => void, (e: React.FocusEvent<HTMLInputElement>) => void] {
  const [searchQuery, setSearchQuery] = useState<string>(initialValue);
  const isBlurring = useRef(false);

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    isBlurring.current = true;
    setTimeout(() => { isBlurring.current = false; }, 0);
    e.preventDefault();
  };

  const handleSearchQuery = (query: string) => {
    if (isBlurring.current && query === '') {
      // Ignore clearing on blur
      return;
    }
    setSearchQuery(query);
  };

  return [searchQuery, handleSearchQuery, handleBlur];
} 