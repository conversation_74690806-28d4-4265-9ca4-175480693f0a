import React, { forwardRef } from 'react';
import { WeekData } from '../../interfaces/allocatr-insights';
import { formatCurrency, formatPercentage, formatWeekNumber,  } from './utils/insightsFormatters';
import RenderRowCloseStatus from '../renderRowCloseStatus';
import { borderClass } from './AllocatrInsightsHelper';
import { renderAllRows } from './utils/tableCell';
interface AllocatrWeekRowProps {
  week: WeekData;
  isLastFiscalWeek?: boolean;
  onLastFiscalWeekHover?: () => void;
  onMouseLeaveLastFiscalWeek?: () => void;
  isActualUsed?: boolean; // <-- add this line
  isCurrentWeek?:boolean;
}

const AllocatrWeekRow = forwardRef<HTMLTableRowElement, AllocatrWeekRowProps>(({ week, isLastFiscalWeek = false, onLastFiscalWeekHover, onMouseLeaveLastFiscalWeek, isActualUsed,isCurrentWeek }, ref) => (
  <tr
    ref={ref}
    key={`week-${week.id}`}
    className="week-row"
    onMouseEnter={() => {
      if (isLastFiscalWeek && onLastFiscalWeekHover) {
        onLastFiscalWeekHover();      
      }
    }}
    onMouseLeave={() => {
      if (isLastFiscalWeek && onMouseLeaveLastFiscalWeek) {
        onMouseLeaveLastFiscalWeek();
      }
    }}
  >
    <td></td>
    <td className="period-cell  bg-white">
  <div className="flex items-center h-full space-x-1 pl-1">
    <RenderRowCloseStatus
      weekNbr={String(week.weekNumber)}
      className="w-[16px] h-[16px]"
    />
    <div
      title={week.fiscalWeekLabel}
      className={`font-medium truncated-text-period${isCurrentWeek ? 'px-0.5 py-[0.5px] rounded-md bg-[#E8F1FC] text-sm font-medium text-[#1E2B3C]' : ''}`}
    >
      {formatWeekNumber(week.id)}
    </div>
  </div>
</td>

    {renderAllRows(week, isActualUsed)}
  </tr>
));

export default AllocatrWeekRow;