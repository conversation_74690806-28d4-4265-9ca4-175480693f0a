import { useState } from 'react';
import Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';
import './quarterTabs.scss';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import { CircleAlert } from 'lucide-react';
import { useCurrentQuarterNbr } from '../features/calendarServiceUtils';
import { useDispatch } from 'react-redux';
import { setActiveQuarterTab } from './activeQuarterTab.slice';
import Tooltip from '@albertsons/uds/molecule/Tooltip'
// Utility function to format a date string as 'Month Day' (e.g., 'July 1')
function formatMonthDay(dateString: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  const options: Intl.DateTimeFormatOptions = { month: 'long', day: 'numeric' };
  return date.toLocaleDateString('en-US', options);
}
export const QuarterTabs = ({ onQuarterChange }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const { data } = useSelectorWrap('prevQuarterTab_rn');
  const currentQuarterNbr = useCurrentQuarterNbr();
  const dispatch = useDispatch();
  if (!data || !data.isDisplayLastQtrTab || !data.lastQtrNbr) {
    return null;
  }
  const lastQtrNbr = data.lastQtrNbr;
  const lastQtrNbrStr = lastQtrNbr.toString();
  const quarter =
    lastQtrNbrStr.length >= 6 ? lastQtrNbrStr.slice(-2) : lastQtrNbrStr;
  const year = lastQtrNbrStr.length >= 6 ? lastQtrNbrStr.slice(0, 4) : '';
  const tabLabel = `Q${parseInt(quarter, 10)} FY${year}`;
  const currentQtrNbrStr = currentQuarterNbr?.toString() ?? '';
  const currentQuarter =
    currentQtrNbrStr && currentQtrNbrStr.length >= 6
      ? currentQtrNbrStr.slice(-2)
      : currentQtrNbrStr || '';
  const currentYear =
    currentQtrNbrStr && currentQtrNbrStr.length >= 6
      ? currentQtrNbrStr.slice(0, 4)
      : '';
  const currentTab = `Q${parseInt(currentQuarter, 10)} FY${currentYear}`;

  const handleTabChange = (tabIndex: number) => {
    setActiveTab(tabIndex);
    // Send the correct quarter number
    if (tabIndex === 0) {
      onQuarterChange?.(lastQtrNbr);
      dispatch(setActiveQuarterTab('lastQtr'));
    } else {
      onQuarterChange?.(currentQuarterNbr);
      dispatch(setActiveQuarterTab('currentQtr'));
    }
  };
  return (
    <div className="quarter-tabs" data-testid="quarter-tabs">
      <Tabs
        className="QuarterTabHierarchy"
        variant="light"
        onChange={handleTabChange}
        initialTab={1}
      >
        <Tab className="rounded-4xl">
          <Tab.Header>
            <div>
              <span
                tabIndex={0}
                onBlur={() => setShowMessage(false)}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
                data-testid="quarter-tab-last"
              >
                {tabLabel}
               <Tooltip
                  zIndex={999}
                  anchor='right'
                  variant='dark'
                  className={'uds-tooltip-right'}
                  label={
                    data.isNotLocked
                      ? "You can edit the last period until it's closed on Monday 10 am PT. No edits are possible after this cut-off time."
                      : `You can only view this period as the updates have been closed and submitted on ${formatMonthDay(data.quarterLockoutDate)} at 10 pm PT.`
                  }>
                  <CircleAlert
                    size={16}
                    style={{ cursor: 'pointer' }}
                    color=" #1B6EBB"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Tooltip>
              </span>
            </div>
          </Tab.Header>
        </Tab>
        <Tab>
          <Tab.Header>
            <span data-testid="quarter-tab-current">{currentTab}</span>
          </Tab.Header>
        </Tab>
      </Tabs>
    </div>
  );
};