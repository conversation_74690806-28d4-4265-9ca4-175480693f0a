[{"userName": "<PERSON>", "role": "Sales Manager", "accessStatus": "Active", "effectiveStart": "01/15/2022", "effectiveEnd": "--", "oracleDepartment": "3010000 - Grocery Foo...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Finance", "accessStatus": "Active", "effectiveStart": "03/10/2023", "effectiveEnd": "--", "oracleDepartment": "All Department", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "VPMM", "accessStatus": "Active", "effectiveStart": "02/23/2024", "effectiveEnd": "--", "oracleDepartment": "All Department", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Sales Manager", "accessStatus": "Active", "effectiveStart": "02/14/2025", "effectiveEnd": "12/15/2026", "oracleDepartment": "3090000 - Deli/Prepar...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Assistant Sales Manager", "accessStatus": "Active", "effectiveStart": "04/30/2025", "effectiveEnd": "--", "oracleDepartment": "3010000 - Grocery Foo...", "desk": "25-301000-Market...", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Sales Manager", "accessStatus": "Active", "effectiveStart": "06/01/2025", "effectiveEnd": "--", "oracleDepartment": "3140000 - <PERSON><PERSON>", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "VPMM", "accessStatus": "Inactive", "effectiveStart": "01/20/2025", "effectiveEnd": "02/22/2025", "oracleDepartment": "3150000 - <PERSON><PERSON> and...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Finance", "accessStatus": "Active", "effectiveStart": "03/05/2025", "effectiveEnd": "--", "oracleDepartment": "3160000 - In-Store Ba...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Finance", "accessStatus": "Active", "effectiveStart": "05/15/2025", "effectiveEnd": "--", "oracleDepartment": "3180000 - <PERSON><PERSON><PERSON> Foo...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Finance", "accessStatus": "Active", "effectiveStart": "02/28/2025", "effectiveEnd": "--", "oracleDepartment": "3200000 - Meat and S...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Sales Manager", "accessStatus": "Active", "effectiveStart": "04/10/2025", "effectiveEnd": "--", "oracleDepartment": "3220000 - Snacks and...", "desk": "--", "lastUpdatedBy": "<PERSON>"}, {"userName": "Amelia Hall", "role": "Assistant Sales Manager", "accessStatus": "Active", "effectiveStart": "01/30/2025", "effectiveEnd": "--", "oracleDepartment": "3010000 - Grocery Foo...", "desk": "25-301000-Market...", "lastUpdatedBy": "<PERSON>"}, {"userName": "<PERSON>", "role": "Assistant Sales Manager", "accessStatus": "Active", "effectiveStart": "06/15/2025", "effectiveEnd": "--", "oracleDepartment": "3010000 - Grocery Foo...", "desk": "25-301000-Market...", "lastUpdatedBy": "<PERSON>"}]