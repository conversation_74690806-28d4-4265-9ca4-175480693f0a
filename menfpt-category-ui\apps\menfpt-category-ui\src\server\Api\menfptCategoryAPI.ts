import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getEnvParamVal } from '../../util/envVarsManager';
import { getDefaultHeaders } from '../../util/authProvider';
import { GQLParams } from '../../interfaces/menfpt-category';
import { GetJobRunsFromDatabricksQuery } from '../Query/EPBCSSyncMonitorQuery';
import { GetEnvVariablesQuery } from '../Query/envVariablesQuery';
import { GET_UPLOADED_DOCUMENTS } from '../Query/downloadFilePharmaQuery';
import { PharmaUploadDocumentsReq, GetUploadedDocumentsResponse } from '../../interfaces/downloadFilePharma';

const entitlementApi = createApi({
  reducerPath: 'menfptCategoryApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getEnvParamVal('MENFPT_GRAPHQL_ENDPOINT')}/api`
  }),
  tagTypes: ['DisplayDate', 'WorkSheetFilter', 'CombinedFiltersAndQuarters'],
  endpoints: (builder) => ({
    getmenfptCategoryTitle: builder.mutation<any, GQLParams>({
      query: (params) => ({
        url: ``,
        method: 'POST',
        headers: { "content-type": "application/json"},
        body: JSON.stringify(params),
      }),
      transformResponse: (response: { data: any }) =>
        response.data,
    }),
    getDisplayDate: builder.query<any, any>({
      query: (payload) => ({
        url: ``,
        method: 'POST',
        headers: getDefaultHeaders(),
        body: JSON.stringify({
          query: `query GetDisplayDate($displayDateReq: DisplayDateReq) {
            getDisplayDate(displayDateReq: $displayDateReq) {
              calendarDetails {
                fiscalYearNumber
                fiscalWeekNumber
                fiscalQuarterNumber
                fiscalPeriodNumber
                fiscalQuarterStartDate
                fiscalQuarterEndDate
                fiscalWeekStartDate
                fiscalWeekEndDate
                fiscalPeriodStartDate
                fiscalPeriodEndDate
                createTimestamp
                updateTimestamp
                fiscalPeriodLockoutDate
                fiscalPeriodCertificationDate
              }
            }
          }`,
          variables: { displayDateReq: payload }
        }),
      }),
      transformResponse: (response: { data: any }) => response.data?.getDisplayDate?.calendarDetails || [],
      providesTags: ['DisplayDate'],
    }),
    getWorkSheetFilter: builder.query<any,GQLParams>({
      query: (params) => ({
        url: ``,
        method: 'POST',
        headers: getDefaultHeaders(),
        body: JSON.stringify(params),
      }),
      transformResponse: (response:{data:any}) => response.data,
      providesTags: ['WorkSheetFilter'],
    }),
  getUserInfo: builder.query<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: { data: any }) =>
      response.data,
  }),
  getWorksheetTableData: builder.mutation<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: { data: any }) =>
      response.data,
  }),
  getPublishHistory: builder.mutation<any, GQLParams>({
    // TODO: Might have to look into injectEndpoint for codegeneration overall
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: { data: any }) =>
      response.data,
  }),
  saveAdjustmentEdits: builder.mutation<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: {data: any}) =>
      response.data
  }),
  getQuarters: builder.mutation<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: {data: any}) =>
      response.data
  }),
  getCombinedFiltersAndQuarters: builder.query<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: {data: any}) => response.data,
    providesTags: ['CombinedFiltersAndQuarters', 'WorkSheetFilter']
  }),
  getAllocatrTableData: builder.mutation<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: { data: any }) =>
      response.data,
  }),

  getJobRunsFromDatabricks: builder.query<any, { input: { jobName: string; limit?: number | null; jobId?:  string | number | bigint } }>(
  {
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify({
        query: GetJobRunsFromDatabricksQuery,
        variables: params,
      }),
    }),
    transformResponse: (response: { data: any }) => response.data,
  }
),
uploadFilePharma: builder.mutation<any, GQLParams>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify(params),
    }),
    transformResponse: (response: { data: any }) =>
      response.data,
  }),
  getEnvVariables: builder.query<any, void>({
    query: () => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify({
        query: GetEnvVariablesQuery,
        variables: {},
      }),
    }),
    transformResponse: (response: { data: any }) => response.data,
  }),
  downloadFilePharma: builder.mutation<GetUploadedDocumentsResponse, PharmaUploadDocumentsReq>({
    query: (params) => ({
      url: ``,
      method: 'POST',
      headers: getDefaultHeaders(),
      body: JSON.stringify({
        query: GET_UPLOADED_DOCUMENTS,
        variables: { getUploadedDocuments: params },
      }),
    }),
    transformResponse: (response: { data: any }) =>
      response.data?.getUploadedDocuments || { uploadedDocuments: [] },
  }),
})
});

export const {
  useGetmenfptCategoryTitleMutation,
  useGetDisplayDateQuery,
  useGetWorkSheetFilterQuery,
  useGetUserInfoQuery,
  useGetWorksheetTableDataMutation,
  useGetPublishHistoryMutation,
  useSaveAdjustmentEditsMutation,
  useGetQuartersMutation,
  useGetCombinedFiltersAndQuartersQuery,
  useGetAllocatrTableDataMutation,
  useGetJobRunsFromDatabricksQuery,
  useUploadFilePharmaMutation,
  useGetEnvVariablesQuery,
  useDownloadFilePharmaMutation,
} = entitlementApi;

export default entitlementApi;
