
import React from 'react';
import { render } from '@testing-library/react';
import AllocatrWeekRow from './AllocatrWeekRow';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import * as rtkUtils from '../../rtk/rtk-utilities';

const mockStore = configureStore([]);
const store = mockStore({});

// Mock useSelectorWrap to always provide periodStatuses for children
jest.spyOn(rtkUtils, 'useSelectorWrap').mockImplementation(() => ({ data: {} }));

describe('AllocatrWeekRow', () => {
  const baseWeek = {
    id: 'W1',
    weekNumber: 1,
    periodNumber: 1,
    fiscalWeekLabel: 'Week 1',
    fiscalWeekStartDate: '2024-07-01',
    fiscalWeekEndDate: '2024-07-07',
    line1Projection: 0,
    lastYear: 0,
    actualOrForecast: 0,
    idPercentage: 0,
    vsLY: { value: 0, percentage: 0 },
    vsProjection: { value: 0, percentage: 0 },
    bookGrossProfit: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    markdown: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    shrink: { projectionValue: 0, projectionPct: 0, actualOrForecast: 0, percentActualOrForecast: 0, vsProjection: 0 },
    line5: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
    line6: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
    line7: { projection: 0, actualOrForecast: 0, vsProjection: 0 },
    line8: { actualOrForecast: 0, percentActualOrForecast: 0, projectionValue: 0, projectionPct: 0, vsProjection: 0, percentVsProjection: 0 },
    hasMerchantForecast: false,
  };

  it('should render AllocatrWeekRow', () => {
    const { getByText, getByRole } = render(
      <Provider store={store}>
        <table>
          <tbody>
            <AllocatrWeekRow week={baseWeek} />
          </tbody>
        </table>
      </Provider>
    );
    // Check that the week label is rendered
   expect(getByText('W1')).toBeInTheDocument();
    // Check that a table row is rendered
    expect(getByRole('row')).toBeInTheDocument();
  });

});
