import { formatToPST } from './timezoneInPST';

describe('formatToPST', () => {
  it('formats a valid ISO timestamp to PST', () => {
    // 2023-07-28T12:34:56Z is 05:34:56 AM PST (or PDT, but function appends PST)
    const result = formatToPST('2023-07-28T12:34:56Z');
    expect(result).toMatch(/\d{2}\/\d{2}\/\d{2}, \d{2}:\d{2}:\d{2} (AM|PM) PST/);
  });

  it('formats a valid date string to PST', () => {
    const date = new Date('2023-12-25T18:00:00Z');
    const result = formatToPST(date.toISOString());
    expect(result.endsWith('PST')).toBe(true);
    expect(result).toContain('/');
  });

  it('returns empty string and logs error for invalid date', () => {
    const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const result = formatToPST('not-a-date');
    expect(result).toBe('');
    expect(spy).toHaveBeenCalledWith(
      expect.stringContaining('Error formatting timestamp to PST:'),
      expect.any(Error)
    );
    spy.mockRestore();
  });
});
