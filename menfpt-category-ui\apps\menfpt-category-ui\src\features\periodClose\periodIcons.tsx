import React from 'react';

export const certifiedIcon = () => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{ display: 'block', margin: 'auto' }}
  >
    <path
      d="M6.40008 8.00008L7.46675 9.06675L9.60008 6.93341M8.00008 13.3334C10.9457 13.3334 13.3334 10.9457 13.3334 8.00008C13.3334 5.05448 10.9457 2.66675 8.00008 2.66675C5.05448 2.66675 2.66675 5.05448 2.66675 8.00008C2.66675 10.9457 5.05448 13.3334 8.00008 13.3334Z"
      stroke="#1BA418"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const  closeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    style={{ display: 'block', margin: 'auto' }}
  >
    <path
      d="M5.33328 7.46675V5.33341C5.33328 4.62617 5.61424 3.94789 6.11433 3.4478C6.61443 2.9477 7.29271 2.66675 7.99995 2.66675C8.7072 2.66675 9.38547 2.9477 9.88557 3.4478C10.3857 3.94789 10.6666 4.62617 10.6666 5.33341V7.46675M4.26662 7.46675H11.7333C12.3224 7.46675 12.8 7.94431 12.8 8.53341V12.2667C12.8 12.8559 12.3224 13.3334 11.7333 13.3334H4.26662C3.67751 13.3334 3.19995 12.8559 3.19995 12.2667V8.53341C3.19995 7.94431 3.67751 7.46675 4.26662 7.46675Z"
      stroke="#2B303C"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
