import { MenfptSaveAdjustmentQuery } from '../../server/Query/menfptSaveAdjustmentQuery';
import { Adjustment } from '../../interfaces/edit-forecast-adjustments';
import { setSaveAdjustmentApiStatus } from './editForecast.slice';
import { setAlertState } from '../../server/Reducer/menfpt-category.slice';

/**
 * Utility to create the Adjustment API body from provided params.
 */
export function createAdjustmentApiBody(appliedFilters: any, userInfo: any, weeks: any, bannerId?: string[]): Adjustment {
  const apiBody: Adjustment = {
    deptId: appliedFilters?.department?.num?.toString(),
    divisionIds: appliedFilters.division.map((item) =>
      item.num.toString()
    ),
    smicCategoryIds: appliedFilters?.category?.map((item) =>
      Number(item.num) || []
    ),
    updatedBy: userInfo?.userId || '',
    deskId: appliedFilters?.desk?.num || '',
    weeks,
    ...(bannerId && { bannerId })
  };
  return apiBody;
}

export async function saveAdjustment(postBody: Adjustment | undefined,
  dispatch,
  saveAdjustmentEdits,
): Promise<boolean> {
  const filterArgs = {
    query: MenfptSaveAdjustmentQuery,
    variables: { adjustment: { ...postBody } as Adjustment }
  };
  try {
    const saveAdjustmentResponse = await saveAdjustmentEdits(filterArgs);
    if ('data' in saveAdjustmentResponse && saveAdjustmentResponse.data?.saveAdjustment?.success) {
      dispatch(setSaveAdjustmentApiStatus(true));
      dispatch(setAlertState({ success: true, error: false }));
      return true;
    } else {
      dispatch(setAlertState({ success: false, error: true }));
      return false;
    }
  } catch (error) {
    dispatch(setAlertState({ success: false, error: true }));
    return false;
  }
}

export const getFiscalWeekNumber = (previousFcstData, previousLYData) => {
  if (previousFcstData?.['fiscalWeekNbr']) {
    return Number(previousFcstData['fiscalWeekNbr']);
  }
  if (previousLYData?.['fiscalWeekNbr']) {
    const prevWeekNbr = Number(previousLYData['fiscalWeekNbr']);
    const year = Math.floor(prevWeekNbr / 100);
    const week = prevWeekNbr % 100;
    const nextYear = year + 1;
    return nextYear * 100 + week;
  }
  return 0;
}