/* eslint-disable */
export default {
  collectCoverage: true, // Enable coverage collection
  displayName: 'menfpt-category-ui',
  preset: '../../jest.preset.js',
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nrwl/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nrwl/react/babel'] }],
  },
  transformIgnorePatterns: ['"node_modules/(?!@albertsons/uds)/"'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory: '../../coverage/apps/menfpt-category-ui',
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}', // Include all source files
    '!src/**/*.d.ts', // Exclude type declaration files
    '!src/**/index.{js,ts}', // Exclude index files
  ],
  coveragePathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/src/test',
    '<rootDir>/src/assets',
    '<rootDir>/src/polyfills.ts',
    '<rootDir>/src/main.ts',
    '<rootDir>/src/bootstrap.tsx',
  ],
  coverageReporters: ['lcov', 'json', 'text', 'text-summary'],
  setupFilesAfterEnv: ['<rootDir>/src/test-setup.ts'],
  // coverageThreshold: {
  //   global: {
  //     branches: 60,
  //     functions: 70,
  //     lines: 70,
  //     statements: 70,
  //   },
  // },
  // maxWorkers: '50%',
  // moduleNameMapper: {
  //   '@hookform/resolvers/yup/dist/yup': '@hookform/resolvers/yup',
  // },
};
