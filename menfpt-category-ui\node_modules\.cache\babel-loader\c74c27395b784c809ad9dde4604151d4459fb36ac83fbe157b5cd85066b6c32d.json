{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NFPT\\\\menfpt-category-ui\\\\apps\\\\menfpt-category-ui\\\\src\\\\pages\\\\dashboard-tabs.tsx\";\nimport React, { useState, useRef } from 'react';\n// import { createPortal } from 'react-dom';\nimport Report from './report';\nimport \"./dashboard-tabs.scss\";\nimport Drawer from '@albertsons/uds/molecule/Drawer';\nimport Button from '@albertsons/uds/molecule/Button';\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\n// Update the import path and casing to match the actual file location\nimport { SelectWeek } from './../components/SnapShotDropDown/release-week-select';\nimport { CircleAlert } from 'lucide-react';\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';\nimport Icon from '@albertsons/uds/molecule/Link';\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg';\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\nimport { getNowInPST } from '../util/dateUtils';\nimport { format } from 'date-fns-tz';\n\n// Extend the Window interface to include __ALLOCATR_DATA__\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar TabsLabels = /*#__PURE__*/function (TabsLabels) {\n  TabsLabels[\"LEADING_INDICATORS\"] = \"Leading Indicators\";\n  TabsLabels[\"PERFORMANCE_SUMMARY\"] = \"Performance Summary\";\n  TabsLabels[\"FORECAST_VARIANCE\"] = \"Performance Variance\";\n  return TabsLabels;\n}(TabsLabels || {}); // const tabClassNames = {\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\n// };\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\n\n// Add this function above your DashboardTabs component\nconst transformDataForExcel = flatData => {\n  if (!flatData || flatData.length === 0) return [];\n\n  // Create a hierarchical structure\n  const divisions = {};\n  const result = [];\n  flatData.forEach(item => {\n    // Extract division info from \"Q3 2025\" or similar field\n    const divisionInfo = item[\"Q3 2025\"] || '';\n\n    // Check if it's a division (format: \"17 - Southwest\")\n    if (/^\\d+ - /.test(divisionInfo)) {\n      const [divId, ...divNameParts] = divisionInfo.split(' - ');\n      const divName = divNameParts.join(' - ');\n      if (!divisions[divId]) {\n        const division = {\n          id: divId,\n          name: divName,\n          quarter: {\n            line1Projection: item[\"$ Projection\"],\n            lastYear: item[\"$ Last Year\"],\n            actualOrForecast: item[\"$ Actual/Merch. Forecast\"],\n            idPercentage: item[\"Keeper% (Includes ID)\"],\n            vsLY: {\n              value: item[\"$ vs LY\"]\n            },\n            vsProjection: {\n              value: item[\"$ vs Projection\"]\n            }\n          },\n          banners: [{\n            id: \"00\",\n            // Default banner\n            name: \"Default\",\n            quarter: {},\n            departments: []\n          }]\n        };\n        divisions[divId] = division;\n        result.push(division);\n      }\n    }\n    // Check if it's a department (format: \"3070000 - Tobacco\")\n    else if (/^\\d{7} - /.test(divisionInfo)) {\n      const [deptId, ...deptNameParts] = divisionInfo.split(' - ');\n      const deptName = deptNameParts.join(' - ');\n\n      // Add to each division's default banner\n      result.forEach(division => {\n        division.banners[0].departments.push({\n          id: deptId,\n          name: deptName,\n          quarter: {\n            line1Projection: item[\"$ Projection\"],\n            lastYear: item[\"$ Last Year\"],\n            actualOrForecast: item[\"$ Actual/Merch. Forecast\"],\n            idPercentage: item[\"Keeper% (Includes ID)\"],\n            vsLY: {\n              value: item[\"$ vs LY\"]\n            },\n            vsProjection: {\n              value: item[\"$ vs Projection\"]\n            }\n          },\n          periods: [],\n          weeks: []\n        });\n      });\n    }\n    // Check if it's a period (format: \"Period 8\")\n    else if (/^Period \\d+$/.test(divisionInfo)) {\n      // Add period data to the last department in each division\n      // This is a simplified approach - you may need to adjust based on your actual data\n    }\n  });\n  return result;\n};\nconst DashboardTabs = () => {\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [selectedWeek, setSelectedWeek] = useState(null);\n  const [performanceSummaryData, setPerformanceSummaryData] = useState([]);\n  const [forecastVarianceData, setForecastVarianceData] = useState([]);\n  const [dashboardLoading, setDashboardLoading] = useState(true);\n  const {\n    data: worksheetFilters = {}\n  } = useSelectorWrap('workSheetFilterList_rn');\n  const [showMessage, setShowMessage] = useState(false);\n  const [tooltipPosition, setTooltipPosition] = useState(null);\n  const alertIconRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const smicData = worksheetFilters.smicData || [];\n\n  // Safely access displayDate with a fallback\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\n  const displayDate = (displayDateSelector == null ? void 0 : displayDateSelector.data) || {};\n  const {\n    data: appliedFilters\n  } = useSelectorWrap('appliedFilter_rn');\n\n  // const handleTabClick = (tabName: Tabs) => {\n  //   setSelectedTab(tabName);\n  // };\n\n  const handleSyncMonitorClick = () => {\n    setIsDrawerOpen(true);\n  };\n  const handleWeekChange = item => {\n    setSelectedWeek(item);\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\n  };\n  const handlePerformanceSummaryData = data => {\n    setPerformanceSummaryData(data);\n    setDashboardLoading(false);\n  };\n  const handleForecastVarianceData = data => {\n    setForecastVarianceData(data);\n    setDashboardLoading(false);\n  };\n  const renderTabContent = tab => {\n    switch (tab) {\n      case TabsLabels.LEADING_INDICATORS:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Report, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.PERFORMANCE_SUMMARY:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.PERFORMANCE_SUMMARY,\n            onDataLoaded: handlePerformanceSummaryData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 16\n        }, this);\n      case TabsLabels.FORECAST_VARIANCE:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(AllocatrInsights, {\n            selectedTab: TabsLabels.FORECAST_VARIANCE,\n            onDataLoaded: handleForecastVarianceData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const visibleTabs = [TabsLabels.LEADING_INDICATORS, TabsLabels.PERFORMANCE_SUMMARY, TabsLabels.FORECAST_VARIANCE];\n  const classes = 'flex justify-center items-center h-48 text';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-2 py-2 overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\",\n          style: {\n            margin: '5px 10px',\n            padding: '5px',\n            width: '600px',\n            borderColor: 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            initialTab: visibleTabs.indexOf(selectedTab),\n            variant: \"light\",\n            onChange: idx => setSelectedTab(visibleTabs[idx]),\n            className: \"w-full border-transparent dashboard-tab\",\n            children: visibleTabs.map((tab, idx) => /*#__PURE__*/_jsxDEV(Tab, {\n              className: classes,\n              children: /*#__PURE__*/_jsxDEV(Tab.Header, {\n                children: tab === TabsLabels.FORECAST_VARIANCE ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  tabIndex: 2,\n                  onBlur: () => setShowMessage(false),\n                  style: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    gap: '4px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tool-tip-initilizer-top\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      zIndex: 9999,\n                      anchor: \"top\",\n                      variant: \"dark\",\n                      className: 'uds-tooltip-top',\n                      label: ' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.',\n                      children: /*#__PURE__*/_jsxDEV(CircleAlert, {\n                        size: 16,\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        color: \" #1B6EBB\",\n                        onClick: e => {\n                          e.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 19\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this), tab]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this) : tab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          before: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"w-4 h-4 flex items-center text-[#1B6EBB]\",\n            children: [\" \", /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 75\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this),\n          className: \"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\",\n          onClick: async () => {\n            console.log('Download button clicked');\n            console.log('Selected tab:', selectedTab);\n            try {\n              if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\n                console.log('Preparing Performance Summary download...');\n\n                // Try to extract data directly from the DOM\n                // First check if we already have data in state\n                if (performanceSummaryData && performanceSummaryData.length > 0) {\n                  console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\n                  const transformedData = transformDataForExcel(performanceSummaryData);\n                  console.log('Transformed data structure:', transformedData);\n                  handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  // Try to extract data from the table directly\n                  console.log('Attempting to extract data from UI...');\n                  try {\n                    // Find table in the DOM\n                    const tableEl = document.querySelector('.allocatr-insights-table');\n                    if (tableEl) {\n                      console.log('Found table element, extracting data...');\n\n                      // Extracting headers\n                      const headers = Array.from(tableEl.querySelectorAll('thead th')).map(th => th.textContent);\n\n                      // Extracting rows\n                      const rows = Array.from(tableEl.querySelectorAll('tbody tr')).map(tr => Array.from(tr.querySelectorAll('td')).map(td => td.textContent));\n                      console.log(`Extracted ${rows.length} rows from table`);\n                      if (rows.length > 0) {\n                        // Convert to array of objects\n                        const extractedData = rows.map(row => {\n                          const rowData = {};\n                          headers.forEach((header, index) => {\n                            if (header) rowData[header] = row[index];\n                          });\n                          return rowData;\n                        });\n                        console.log('Successfully extracted data from table');\n                        const transformedData = transformDataForExcel(extractedData);\n                        console.log('Transformed data structure:', transformedData);\n                        handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                        return;\n                      }\n                    }\n\n                    // Try global variable as fallback\n                    const dashboardData = window.__ALLOCATR_DATA__ || [];\n                    if (dashboardData && dashboardData.length > 0) {\n                      console.log('Found dashboard data in global variable:', dashboardData.length, 'items');\n                      const transformedData = transformDataForExcel(dashboardData);\n                      console.log('Transformed data structure:', transformedData);\n                      handleDownloadExcel(transformedData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);\n                      return;\n                    }\n\n                    // Force refresh data from AllocatrInsights component\n                    // The DOM element does not have a 'refresh' method; remove this block or implement refresh logic differently.\n                    // If you need to trigger a refresh, consider using state or props to re-fetch data in AllocatrInsights.\n                    // For now, just log that refresh is not available.\n                    console.log('No refresh method available on DOM element. Please refresh data via state or props.');\n                    console.error('Could not find data to download');\n                    alert('No data available to download. The data is visible but not accessible for download. Please try refreshing the page.');\n                  } catch (extractError) {\n                    console.error('Error extracting data:', extractError);\n                    alert('Error extracting data for download. Please try again later.');\n                  }\n                }\n              } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\n                // Similar approach for forecast variance\n                if (forecastVarianceData && forecastVarianceData.length > 0) {\n                  console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\n                  handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);\n                } else {\n                  console.error('No variance data available to download');\n                  alert('No data available to download. Please try refreshing the page.');\n                }\n              }\n            } catch (error) {\n              console.error('Download error:', error);\n              alert('Error preparing download: ' + (error.message || 'Unknown error'));\n            }\n          },\n          children: \"Download as Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-6\",\n          children: /*#__PURE__*/_jsxDEV(SelectWeek, {\n            weekChange: handleWeekChange,\n            selectedTab: selectedTab\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"sync-button\",\n            size: \"xs\",\n            variant: \"secondary\",\n            onClick: handleSyncMonitorClick,\n            children: \"EPBCS Sync Monitor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: renderTabContent(selectedTab)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      isOpen: isDrawerOpen,\n      setOpen: setIsDrawerOpen,\n      hideBackdrop: false,\n      width: \"608px\",\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"EPBCS Sync Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 17\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(EPBCSSyncMonitor, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\nexport default DashboardTabs;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Report", "Drawer", "<PERSON><PERSON>", "useSelectorWrap", "Tabs", "Tab", "EPBCSSyncMonitor", "AllocatrInsights", "SelectWeek", "Circle<PERSON>lert", "<PERSON><PERSON><PERSON>", "Icon", "ReactComponent", "Download", "handleDownloadExcel", "getNowInPST", "format", "jsxDEV", "_jsxDEV", "TabsLabels", "downloadedDate", "transformDataForExcel", "flatData", "length", "divisions", "result", "for<PERSON>ach", "item", "divisionInfo", "test", "divId", "divNameParts", "split", "divName", "join", "division", "id", "name", "quarter", "line1Projection", "lastYear", "actualOrForecast", "idPercentage", "vsLY", "value", "vsProjection", "banners", "departments", "push", "deptId", "deptNameParts", "deptName", "periods", "weeks", "DashboardTabs", "selectedTab", "setSelectedTab", "PERFORMANCE_SUMMARY", "isDrawerOpen", "setIsDrawerOpen", "selectedWeek", "setSelectedWeek", "performanceSummaryData", "setPerformanceSummaryData", "forecastVarianceData", "setForecastVarianceData", "dashboardLoading", "setDashboardLoading", "data", "worksheetFilters", "showMessage", "setShowMessage", "tooltipPosition", "setTooltipPosition", "alertIconRef", "tooltipRef", "smicData", "displayDateSelector", "displayDate", "appliedFilters", "handleSyncMonitorClick", "handleWeekChange", "handlePerformanceSummaryData", "handleForecastVarianceData", "renderTabContent", "tab", "LEADING_INDICATORS", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataLoaded", "FORECAST_VARIANCE", "visibleTabs", "classes", "className", "style", "margin", "padding", "width", "borderColor", "initialTab", "indexOf", "variant", "onChange", "idx", "map", "Header", "tabIndex", "onBlur", "display", "alignItems", "gap", "position", "zIndex", "anchor", "label", "size", "cursor", "color", "onClick", "e", "stopPropagation", "before", "console", "log", "transformedData", "tableEl", "document", "querySelector", "headers", "Array", "from", "querySelectorAll", "th", "textContent", "rows", "tr", "td", "extractedData", "row", "rowData", "header", "index", "dashboardData", "window", "__ALLOCATR_DATA__", "error", "alert", "extractError", "message", "weekChange", "isOpen", "<PERSON><PERSON><PERSON>", "hideBackdrop"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/pages/dashboard-tabs.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\n// import { createPortal } from 'react-dom';\r\nimport Report from './report';\r\nimport LaggingIndicatorPage from './lagging-indicator-page';\r\nimport \"./dashboard-tabs.scss\";\r\nimport Drawer from '@albertsons/uds/molecule/Drawer';\r\nimport Button from '@albertsons/uds/molecule/Button';\r\nimport { useSelectorWrap } from '../rtk/rtk-utilities';\r\nimport Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';\r\nimport Tag from '@albertsons/uds/molecule/Tag';\r\nimport EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';\r\nimport AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';\r\n// Update the import path and casing to match the actual file location\r\nimport {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';\r\nimport { CircleAlert } from 'lucide-react';\r\nimport { useCurrentQuarterNbr } from '../features/calendarServiceUtils';\r\nimport Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';\r\nimport { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; \r\nimport { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';\r\nimport { getNowInPST } from '../util/dateUtils';\r\nimport { format } from 'date-fns-tz';\r\n\r\n// Extend the Window interface to include __ALLOCATR_DATA__\r\ndeclare global {\r\n  interface Window {\r\n    __ALLOCATR_DATA__?: any[];\r\n  }\r\n}\r\n\r\nenum TabsLabels {\r\n  LEADING_INDICATORS = 'Leading Indicators',\r\n  PERFORMANCE_SUMMARY = 'Performance Summary',\r\n  FORECAST_VARIANCE = 'Performance Variance'\r\n}\r\n\r\n// const tabClassNames = {\r\n//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',\r\n//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',\r\n// };\r\n\r\nconst downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');\r\n\r\n// Add this function above your DashboardTabs component\r\nconst transformDataForExcel = (flatData) => {\r\n  if (!flatData || flatData.length === 0) return [];\r\n  \r\n  // Create a hierarchical structure\r\n  const divisions = {};\r\n  const result = [];\r\n  \r\n  flatData.forEach(item => {\r\n    // Extract division info from \"Q3 2025\" or similar field\r\n    const divisionInfo = item[\"Q3 2025\"] || '';\r\n    \r\n    // Check if it's a division (format: \"17 - Southwest\")\r\n    if (/^\\d+ - /.test(divisionInfo)) {\r\n      const [divId, ...divNameParts] = divisionInfo.split(' - ');\r\n      const divName = divNameParts.join(' - ');\r\n      \r\n      if (!divisions[divId]) {\r\n        const division = {\r\n          id: divId,\r\n          name: divName,\r\n          quarter: {\r\n            line1Projection: item[\"$ Projection\"],\r\n            lastYear: item[\"$ Last Year\"],\r\n            actualOrForecast: item[\"$ Actual/Merch. Forecast\"],\r\n            idPercentage: item[\"Keeper% (Includes ID)\"],\r\n            vsLY: { value: item[\"$ vs LY\"] },\r\n            vsProjection: { value: item[\"$ vs Projection\"] }\r\n          },\r\n          banners: [{\r\n            id: \"00\", // Default banner\r\n            name: \"Default\",\r\n            quarter: {},\r\n            departments: []\r\n          }]\r\n        };\r\n        divisions[divId] = division;\r\n        result.push(division);\r\n      }\r\n    }\r\n    // Check if it's a department (format: \"3070000 - Tobacco\")\r\n    else if (/^\\d{7} - /.test(divisionInfo)) {\r\n      const [deptId, ...deptNameParts] = divisionInfo.split(' - ');\r\n      const deptName = deptNameParts.join(' - ');\r\n      \r\n      // Add to each division's default banner\r\n      result.forEach(division => {\r\n        division.banners[0].departments.push({\r\n          id: deptId,\r\n          name: deptName,\r\n          quarter: {\r\n            line1Projection: item[\"$ Projection\"],\r\n            lastYear: item[\"$ Last Year\"],\r\n            actualOrForecast: item[\"$ Actual/Merch. Forecast\"],\r\n            idPercentage: item[\"Keeper% (Includes ID)\"],\r\n            vsLY: { value: item[\"$ vs LY\"] },\r\n            vsProjection: { value: item[\"$ vs Projection\"] }\r\n          },\r\n          periods: [],\r\n          weeks: []\r\n        });\r\n      });\r\n    }\r\n    // Check if it's a period (format: \"Period 8\")\r\n    else if (/^Period \\d+$/.test(divisionInfo)) {\r\n      // Add period data to the last department in each division\r\n      // This is a simplified approach - you may need to adjust based on your actual data\r\n    }\r\n  });\r\n  \r\n  return result;\r\n};\r\n\r\nconst DashboardTabs = () => {\r\n  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);\r\n  const [performanceSummaryData, setPerformanceSummaryData] = useState<any[]>([]);\r\n  const [forecastVarianceData, setForecastVarianceData] = useState<any[]>([]);\r\n  const [dashboardLoading, setDashboardLoading] = useState(true);\r\n  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');\r\n   const [showMessage, setShowMessage] = useState(false);\r\n  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);\r\n  const alertIconRef = useRef<HTMLDivElement>(null);\r\n  const tooltipRef = useRef<HTMLDivElement>(null);\r\n  const smicData = worksheetFilters.smicData || [];\r\n\r\n  // Safely access displayDate with a fallback\r\n  const displayDateSelector = useSelectorWrap('displayDate_rn');\r\n  const displayDate = displayDateSelector?.data || {};\r\n  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');\r\n\r\n  // const handleTabClick = (tabName: Tabs) => {\r\n  //   setSelectedTab(tabName);\r\n  // };\r\n\r\n  const handleSyncMonitorClick = () => {\r\n    setIsDrawerOpen(true);\r\n  };\r\n\r\n  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {\r\n    setSelectedWeek(item);\r\n    // dispatch(setSelectedWeek(item)); // If you want to use redux\r\n  };\r\n\r\n  const handlePerformanceSummaryData = (data: any[]) => {\r\n    setPerformanceSummaryData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n\r\n  const handleForecastVarianceData = (data: any[]) => {\r\n    setForecastVarianceData(data);\r\n    setDashboardLoading(false);\r\n  };\r\n  const renderTabContent = (tab: TabsLabels) => {\r\n    switch (tab) {\r\n      case TabsLabels.LEADING_INDICATORS:\r\n        return <div><Report /></div>;\r\n      case TabsLabels.PERFORMANCE_SUMMARY:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>\r\n      case TabsLabels.FORECAST_VARIANCE:\r\n        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const visibleTabs = [\r\n    TabsLabels.LEADING_INDICATORS,\r\n    TabsLabels.PERFORMANCE_SUMMARY,\r\n    TabsLabels.FORECAST_VARIANCE\r\n  ];\r\n  const classes = 'flex justify-center items-center h-48 text';\r\n\r\n  return (\r\n    <div>\r\n\r\n      <div className=\"flex items-center justify-between px-2 py-2 overflow-x-auto\">\r\n        <div className=\"tabs-container\">\r\n\r\n          <div\r\n            className=\"flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal\"\r\n            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}\r\n          >\r\n\r\n            <Tabs\r\n              initialTab={visibleTabs.indexOf(selectedTab)}\r\n              variant='light'\r\n              onChange={idx => setSelectedTab(visibleTabs[idx])}\r\n              className='w-full border-transparent dashboard-tab'\r\n            >\r\n              {visibleTabs.map((tab, idx) => (\r\n                <Tab className={classes} key={tab}>\r\n                  <Tab.Header>\r\n\r\n                    {tab === TabsLabels.FORECAST_VARIANCE ? (\r\n                      <span\r\n                        tabIndex={2}\r\n                        onBlur={() => setShowMessage(false)}\r\n                        style={{\r\n                          display: 'inline-flex',\r\n                          alignItems: 'center',\r\n                          gap: '4px',\r\n                          position: 'relative'\r\n                        }}>\r\n\r\n                        <div \r\n                          className=\"relative inline-block\"\r\n                        >\r\n                          <span className='tool-tip-initilizer-top'></span>\r\n                        <Tooltip\r\n                  zIndex={9999}\r\n                  anchor='top'\r\n                  variant='dark'\r\n                  className={'uds-tooltip-top'}\r\n                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>\r\n                  <CircleAlert\r\n                    size={16}\r\n                    style={{ cursor: 'pointer' }}\r\n                    color=\" #1B6EBB\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                        </div>\r\n                        \r\n                        {tab}\r\n                      </span>\r\n                    ) : (\r\n                      tab\r\n                    )}\r\n                  </Tab.Header>\r\n              </Tab>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </div>\r\n        <div className=\"flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0\">\r\n          <Icon\r\n            before={\r\n              <span className=\"w-4 h-4 flex items-center text-[#1B6EBB]\"> <Download/> </span>\r\n            }\r\n            className=\"flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer\"\r\n            onClick={async () => {\r\n              console.log('Download button clicked');\r\n              console.log('Selected tab:', selectedTab);\r\n              \r\n              try {\r\n                if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {\r\n                  console.log('Preparing Performance Summary download...');\r\n                  \r\n                  // Try to extract data directly from the DOM\r\n                  // First check if we already have data in state\r\n                  if (performanceSummaryData && performanceSummaryData.length > 0) {\r\n                    console.log('Using existing performance data:', performanceSummaryData.length, 'rows');\r\n                    const transformedData = transformDataForExcel(performanceSummaryData);\r\n                    console.log('Transformed data structure:', transformedData);\r\n                    handleDownloadExcel(\r\n                      transformedData,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    // Try to extract data from the table directly\r\n                    console.log('Attempting to extract data from UI...');\r\n                    \r\n                    try {\r\n                      // Find table in the DOM\r\n                      const tableEl = document.querySelector('.allocatr-insights-table');\r\n                      \r\n                      if (tableEl) {\r\n                        console.log('Found table element, extracting data...');\r\n                        \r\n                        // Extracting headers\r\n                        const headers = Array.from(tableEl.querySelectorAll('thead th')).map(th => th.textContent);\r\n                        \r\n                        // Extracting rows\r\n                        const rows = Array.from(tableEl.querySelectorAll('tbody tr')).map(tr => \r\n                          Array.from(tr.querySelectorAll('td')).map(td => td.textContent)\r\n                        );\r\n                        \r\n                        console.log(`Extracted ${rows.length} rows from table`);\r\n                        \r\n                        if (rows.length > 0) {\r\n                          // Convert to array of objects\r\n                          const extractedData = rows.map(row => {\r\n                            const rowData = {};\r\n                            headers.forEach((header, index) => {\r\n                              if (header) rowData[header] = row[index];\r\n                            });\r\n                            return rowData;\r\n                          });\r\n                          \r\n                          console.log('Successfully extracted data from table');\r\n                          const transformedData = transformDataForExcel(extractedData);\r\n                          console.log('Transformed data structure:', transformedData);\r\n                          handleDownloadExcel(\r\n                            transformedData,\r\n                            smicData,\r\n                            appliedFilters,\r\n                            `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                          );\r\n                          return;\r\n                        }\r\n                      }\r\n                      \r\n                      // Try global variable as fallback\r\n                      const dashboardData = window.__ALLOCATR_DATA__ || [];\r\n                      if (dashboardData && dashboardData.length > 0) {\r\n                        console.log('Found dashboard data in global variable:', dashboardData.length, 'items');\r\n                        const transformedData = transformDataForExcel(dashboardData);\r\n                        console.log('Transformed data structure:', transformedData);\r\n                        handleDownloadExcel(\r\n                          transformedData,\r\n                          smicData,\r\n                          appliedFilters,\r\n                          `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`\r\n                        );\r\n                        return;\r\n                      }\r\n                      \r\n                      // Force refresh data from AllocatrInsights component\r\n                      // The DOM element does not have a 'refresh' method; remove this block or implement refresh logic differently.\r\n                      // If you need to trigger a refresh, consider using state or props to re-fetch data in AllocatrInsights.\r\n                      // For now, just log that refresh is not available.\r\n                      console.log('No refresh method available on DOM element. Please refresh data via state or props.');\r\n                      \r\n                      console.error('Could not find data to download');\r\n                      alert('No data available to download. The data is visible but not accessible for download. Please try refreshing the page.');\r\n                    } catch (extractError) {\r\n                      console.error('Error extracting data:', extractError);\r\n                      alert('Error extracting data for download. Please try again later.');\r\n                    }\r\n                  }\r\n                } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {\r\n                  // Similar approach for forecast variance\r\n                  if (forecastVarianceData && forecastVarianceData.length > 0) {\r\n                    console.log('Using existing variance data:', forecastVarianceData.length, 'rows');\r\n                    handleDownloadExcel(\r\n                      forecastVarianceData,\r\n                      smicData, \r\n                      appliedFilters, \r\n                      `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`\r\n                    );\r\n                  } else {\r\n                    console.error('No variance data available to download');\r\n                    alert('No data available to download. Please try refreshing the page.');\r\n                  }\r\n                }\r\n              } catch (error: any) {\r\n                console.error('Download error:', error);\r\n                alert('Error preparing download: ' + (error.message || 'Unknown error'));\r\n              }\r\n            }}\r\n          >Download as Excel\r\n          </Icon>\r\n        </div>\r\n      <div className='flex items-center gap-4'>\r\n           <div className='mr-6'>\r\n        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>\r\n      </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              className=\"sync-button\"\r\n              size=\"xs\"\r\n              variant=\"secondary\"\r\n              onClick={handleSyncMonitorClick}\r\n            >\r\n              EPBCS Sync Monitor\r\n            </Button>\r\n          </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        {renderTabContent(selectedTab)}\r\n      </div>\r\n\r\n      <Drawer\r\n        anchor=\"right\"\r\n        isOpen={isDrawerOpen}\r\n        setOpen={setIsDrawerOpen}\r\n        hideBackdrop={false}\r\n        width=\"608px\"\r\n        header={<div>EPBCS Sync Monitor</div>}\r\n      >\r\n        <EPBCSSyncMonitor />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D;AACA,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,iCAAiC;AACpD,OAAOC,MAAM,MAAM,iCAAiC;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAOC,IAAI,IAAIC,GAAG,QAAQ,+BAA+B;AAEzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E;AACA,SAAUC,UAAU,QAAQ,sDAAsD;AAClF,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAOC,OAAO,MAAM,kCAAkC;AAAC,OAAOC,IAAI,MAAM,+BAA+B;AACvG,SAASC,cAAc,IAAIC,QAAQ,QAAQ,uCAAuC;AAClF,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,IAOKC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA,EAAVA,UAAU,SAMf;AACA;AACA;AACA;AAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACD,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;;AAE1D;AACA,MAAMM,qBAAqB,GAAIC,QAAQ,IAAK;EAC1C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;EAEjD;EACA,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,MAAMC,MAAM,GAAG,EAAE;EAEjBH,QAAQ,CAACI,OAAO,CAACC,IAAI,IAAI;IACvB;IACA,MAAMC,YAAY,GAAGD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;IAE1C;IACA,IAAI,SAAS,CAACE,IAAI,CAACD,YAAY,CAAC,EAAE;MAChC,MAAM,CAACE,KAAK,EAAE,GAAGC,YAAY,CAAC,GAAGH,YAAY,CAACI,KAAK,CAAC,KAAK,CAAC;MAC1D,MAAMC,OAAO,GAAGF,YAAY,CAACG,IAAI,CAAC,KAAK,CAAC;MAExC,IAAI,CAACV,SAAS,CAACM,KAAK,CAAC,EAAE;QACrB,MAAMK,QAAQ,GAAG;UACfC,EAAE,EAAEN,KAAK;UACTO,IAAI,EAAEJ,OAAO;UACbK,OAAO,EAAE;YACPC,eAAe,EAAEZ,IAAI,CAAC,cAAc,CAAC;YACrCa,QAAQ,EAAEb,IAAI,CAAC,aAAa,CAAC;YAC7Bc,gBAAgB,EAAEd,IAAI,CAAC,0BAA0B,CAAC;YAClDe,YAAY,EAAEf,IAAI,CAAC,uBAAuB,CAAC;YAC3CgB,IAAI,EAAE;cAAEC,KAAK,EAAEjB,IAAI,CAAC,SAAS;YAAE,CAAC;YAChCkB,YAAY,EAAE;cAAED,KAAK,EAAEjB,IAAI,CAAC,iBAAiB;YAAE;UACjD,CAAC;UACDmB,OAAO,EAAE,CAAC;YACRV,EAAE,EAAE,IAAI;YAAE;YACVC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,CAAC,CAAC;YACXS,WAAW,EAAE;UACf,CAAC;QACH,CAAC;QACDvB,SAAS,CAACM,KAAK,CAAC,GAAGK,QAAQ;QAC3BV,MAAM,CAACuB,IAAI,CAACb,QAAQ,CAAC;MACvB;IACF;IACA;IAAA,KACK,IAAI,WAAW,CAACN,IAAI,CAACD,YAAY,CAAC,EAAE;MACvC,MAAM,CAACqB,MAAM,EAAE,GAAGC,aAAa,CAAC,GAAGtB,YAAY,CAACI,KAAK,CAAC,KAAK,CAAC;MAC5D,MAAMmB,QAAQ,GAAGD,aAAa,CAAChB,IAAI,CAAC,KAAK,CAAC;;MAE1C;MACAT,MAAM,CAACC,OAAO,CAACS,QAAQ,IAAI;QACzBA,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,CAACC,WAAW,CAACC,IAAI,CAAC;UACnCZ,EAAE,EAAEa,MAAM;UACVZ,IAAI,EAAEc,QAAQ;UACdb,OAAO,EAAE;YACPC,eAAe,EAAEZ,IAAI,CAAC,cAAc,CAAC;YACrCa,QAAQ,EAAEb,IAAI,CAAC,aAAa,CAAC;YAC7Bc,gBAAgB,EAAEd,IAAI,CAAC,0BAA0B,CAAC;YAClDe,YAAY,EAAEf,IAAI,CAAC,uBAAuB,CAAC;YAC3CgB,IAAI,EAAE;cAAEC,KAAK,EAAEjB,IAAI,CAAC,SAAS;YAAE,CAAC;YAChCkB,YAAY,EAAE;cAAED,KAAK,EAAEjB,IAAI,CAAC,iBAAiB;YAAE;UACjD,CAAC;UACDyB,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAI,cAAc,CAACxB,IAAI,CAACD,YAAY,CAAC,EAAE;MAC1C;MACA;IAAA;EAEJ,CAAC,CAAC;EAEF,OAAOH,MAAM;AACf,CAAC;AAED,MAAM6B,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAACqB,UAAU,CAACsC,mBAAmB,CAAC;EAC9E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAyE,IAAI,CAAC;EAC9H,MAAM,CAACgE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjE,QAAQ,CAAQ,EAAE,CAAC;EAC/E,MAAM,CAACkE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnE,QAAQ,CAAQ,EAAE,CAAC;EAC3E,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM;IAAEsE,IAAI,EAAEC,gBAAgB,GAAG,CAAC;EAAE,CAAC,GAAGlE,eAAe,CAAC,wBAAwB,CAAC;EAChF,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAM4E,YAAY,GAAG3E,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAM4E,UAAU,GAAG5E,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM6E,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,IAAI,EAAE;;EAEhD;EACA,MAAMC,mBAAmB,GAAG1E,eAAe,CAAC,gBAAgB,CAAC;EAC7D,MAAM2E,WAAW,GAAG,CAAAD,mBAAmB,oBAAnBA,mBAAmB,CAAET,IAAI,KAAI,CAAC,CAAC;EACnD,MAAM;IAAEA,IAAI,EAAEW;EAAe,CAAC,GAAG5E,eAAe,CAAC,kBAAkB,CAAC;;EAEpE;EACA;EACA;;EAEA,MAAM6E,sBAAsB,GAAGA,CAAA,KAAM;IACnCrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMsB,gBAAgB,GAAItD,IAAqE,IAAK;IAClGkC,eAAe,CAAClC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMuD,4BAA4B,GAAId,IAAW,IAAK;IACpDL,yBAAyB,CAACK,IAAI,CAAC;IAC/BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMgB,0BAA0B,GAAIf,IAAW,IAAK;IAClDH,uBAAuB,CAACG,IAAI,CAAC;IAC7BD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMiB,gBAAgB,GAAIC,GAAe,IAAK;IAC5C,QAAQA,GAAG;MACT,KAAKlE,UAAU,CAACmE,kBAAkB;QAChC,oBAAOpE,OAAA;UAAAqE,QAAA,eAAKrE,OAAA,CAAClB,MAAM;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9B,KAAKxE,UAAU,CAACsC,mBAAmB;QACjC,oBAAOvC,OAAA;UAAAqE,QAAA,eAAKrE,OAAA,CAACX,gBAAgB;YAACgD,WAAW,EAAEpC,UAAU,CAACsC,mBAAoB;YAACmC,YAAY,EAAEV;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChI,KAAKxE,UAAU,CAAC0E,iBAAiB;QAC/B,oBAAO3E,OAAA;UAAAqE,QAAA,eAAKrE,OAAA,CAACX,gBAAgB;YAACgD,WAAW,EAAEpC,UAAU,CAAC0E,iBAAkB;YAACD,YAAY,EAAET;UAA2B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5H;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMG,WAAW,GAAG,CAClB3E,UAAU,CAACmE,kBAAkB,EAC7BnE,UAAU,CAACsC,mBAAmB,EAC9BtC,UAAU,CAAC0E,iBAAiB,CAC7B;EACD,MAAME,OAAO,GAAG,4CAA4C;EAE5D,oBACE7E,OAAA;IAAAqE,QAAA,gBAEErE,OAAA;MAAK8E,SAAS,EAAC,6DAA6D;MAAAT,QAAA,gBAC1ErE,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAE7BrE,OAAA;UACE8E,SAAS,EAAC,8IAA8I;UACxJC,KAAK,EAAE;YAAEC,MAAM,EAAE,UAAU;YAAEC,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAC,OAAO;YAAEC,WAAW,EAAE;UAAc,CAAE;UAAAd,QAAA,eAGzFrE,OAAA,CAACd,IAAI;YACHkG,UAAU,EAAER,WAAW,CAACS,OAAO,CAAChD,WAAW,CAAE;YAC7CiD,OAAO,EAAC,OAAO;YACfC,QAAQ,EAAEC,GAAG,IAAIlD,cAAc,CAACsC,WAAW,CAACY,GAAG,CAAC,CAAE;YAClDV,SAAS,EAAC,yCAAyC;YAAAT,QAAA,EAElDO,WAAW,CAACa,GAAG,CAAC,CAACtB,GAAG,EAAEqB,GAAG,kBACxBxF,OAAA,CAACb,GAAG;cAAC2F,SAAS,EAAED,OAAQ;cAAAR,QAAA,eACtBrE,OAAA,CAACb,GAAG,CAACuG,MAAM;gBAAArB,QAAA,EAERF,GAAG,KAAKlE,UAAU,CAAC0E,iBAAiB,gBACnC3E,OAAA;kBACE2F,QAAQ,EAAE,CAAE;kBACZC,MAAM,EAAEA,CAAA,KAAMvC,cAAc,CAAC,KAAK,CAAE;kBACpC0B,KAAK,EAAE;oBACLc,OAAO,EAAE,aAAa;oBACtBC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,KAAK;oBACVC,QAAQ,EAAE;kBACZ,CAAE;kBAAA3B,QAAA,gBAEFrE,OAAA;oBACE8E,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,gBAEjCrE,OAAA;sBAAM8E,SAAS,EAAC;oBAAyB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDzE,OAAA,CAACR,OAAO;sBACdyG,MAAM,EAAE,IAAK;sBACbC,MAAM,EAAC,KAAK;sBACZZ,OAAO,EAAC,MAAM;sBACdR,SAAS,EAAE,iBAAkB;sBAC7BqB,KAAK,EAAE,0HAA2H;sBAAA9B,QAAA,eAClIrE,OAAA,CAACT,WAAW;wBACV6G,IAAI,EAAE,EAAG;wBACTrB,KAAK,EAAE;0BAAEsB,MAAM,EAAE;wBAAU,CAAE;wBAC7BC,KAAK,EAAC,UAAU;wBAChBC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACrB;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,EAELN,GAAG;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GAEPN;cACD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC,GAxCeN,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyC9B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJzE,OAAA;QAAK8E,SAAS,EAAC,oEAAoE;QAAAT,QAAA,eACjFrE,OAAA,CAACP,IAAI;UACHiH,MAAM,eACJ1G,OAAA;YAAM8E,SAAS,EAAC,0CAA0C;YAAAT,QAAA,GAAC,GAAC,eAAArE,OAAA,CAACL,QAAQ;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC/E;UACDK,SAAS,EAAC,8FAA8F;UACxGyB,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnBI,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACtCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvE,WAAW,CAAC;YAEzC,IAAI;cACF,IAAIA,WAAW,KAAKpC,UAAU,CAACsC,mBAAmB,EAAE;gBAClDoE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;gBAExD;gBACA;gBACA,IAAIhE,sBAAsB,IAAIA,sBAAsB,CAACvC,MAAM,GAAG,CAAC,EAAE;kBAC/DsG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhE,sBAAsB,CAACvC,MAAM,EAAE,MAAM,CAAC;kBACtF,MAAMwG,eAAe,GAAG1G,qBAAqB,CAACyC,sBAAsB,CAAC;kBACrE+D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,eAAe,CAAC;kBAC3DjH,mBAAmB,CACjBiH,eAAe,EACfnD,QAAQ,EACRG,cAAc,EACd,wDAAwD3D,cAAc,OACxE,CAAC;gBACH,CAAC,MAAM;kBACL;kBACAyG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBAEpD,IAAI;oBACF;oBACA,MAAME,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;oBAElE,IAAIF,OAAO,EAAE;sBACXH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;sBAEtD;sBACA,MAAMK,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC3B,GAAG,CAAC4B,EAAE,IAAIA,EAAE,CAACC,WAAW,CAAC;;sBAE1F;sBACA,MAAMC,IAAI,GAAGL,KAAK,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC3B,GAAG,CAAC+B,EAAE,IAClEN,KAAK,CAACC,IAAI,CAACK,EAAE,CAACJ,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC3B,GAAG,CAACgC,EAAE,IAAIA,EAAE,CAACH,WAAW,CAChE,CAAC;sBAEDX,OAAO,CAACC,GAAG,CAAC,aAAaW,IAAI,CAAClH,MAAM,kBAAkB,CAAC;sBAEvD,IAAIkH,IAAI,CAAClH,MAAM,GAAG,CAAC,EAAE;wBACnB;wBACA,MAAMqH,aAAa,GAAGH,IAAI,CAAC9B,GAAG,CAACkC,GAAG,IAAI;0BACpC,MAAMC,OAAO,GAAG,CAAC,CAAC;0BAClBX,OAAO,CAACzG,OAAO,CAAC,CAACqH,MAAM,EAAEC,KAAK,KAAK;4BACjC,IAAID,MAAM,EAAED,OAAO,CAACC,MAAM,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC;0BAC1C,CAAC,CAAC;0BACF,OAAOF,OAAO;wBAChB,CAAC,CAAC;wBAEFjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;wBACrD,MAAMC,eAAe,GAAG1G,qBAAqB,CAACuH,aAAa,CAAC;wBAC5Df,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,eAAe,CAAC;wBAC3DjH,mBAAmB,CACjBiH,eAAe,EACfnD,QAAQ,EACRG,cAAc,EACd,wDAAwD3D,cAAc,OACxE,CAAC;wBACD;sBACF;oBACF;;oBAEA;oBACA,MAAM6H,aAAa,GAAGC,MAAM,CAACC,iBAAiB,IAAI,EAAE;oBACpD,IAAIF,aAAa,IAAIA,aAAa,CAAC1H,MAAM,GAAG,CAAC,EAAE;sBAC7CsG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmB,aAAa,CAAC1H,MAAM,EAAE,OAAO,CAAC;sBACtF,MAAMwG,eAAe,GAAG1G,qBAAqB,CAAC4H,aAAa,CAAC;sBAC5DpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,eAAe,CAAC;sBAC3DjH,mBAAmB,CACjBiH,eAAe,EACfnD,QAAQ,EACRG,cAAc,EACd,wDAAwD3D,cAAc,OACxE,CAAC;sBACD;oBACF;;oBAEA;oBACA;oBACA;oBACA;oBACAyG,OAAO,CAACC,GAAG,CAAC,qFAAqF,CAAC;oBAElGD,OAAO,CAACuB,KAAK,CAAC,iCAAiC,CAAC;oBAChDC,KAAK,CAAC,qHAAqH,CAAC;kBAC9H,CAAC,CAAC,OAAOC,YAAY,EAAE;oBACrBzB,OAAO,CAACuB,KAAK,CAAC,wBAAwB,EAAEE,YAAY,CAAC;oBACrDD,KAAK,CAAC,6DAA6D,CAAC;kBACtE;gBACF;cACF,CAAC,MAAM,IAAI9F,WAAW,KAAKpC,UAAU,CAAC0E,iBAAiB,EAAE;gBACvD;gBACA,IAAI7B,oBAAoB,IAAIA,oBAAoB,CAACzC,MAAM,GAAG,CAAC,EAAE;kBAC3DsG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE9D,oBAAoB,CAACzC,MAAM,EAAE,MAAM,CAAC;kBACjFT,mBAAmB,CACjBkD,oBAAoB,EACpBY,QAAQ,EACRG,cAAc,EACd,qDAAqD3D,cAAc,OACrE,CAAC;gBACH,CAAC,MAAM;kBACLyG,OAAO,CAACuB,KAAK,CAAC,wCAAwC,CAAC;kBACvDC,KAAK,CAAC,gEAAgE,CAAC;gBACzE;cACF;YACF,CAAC,CAAC,OAAOD,KAAU,EAAE;cACnBvB,OAAO,CAACuB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;cACvCC,KAAK,CAAC,4BAA4B,IAAID,KAAK,CAACG,OAAO,IAAI,eAAe,CAAC,CAAC;YAC1E;UACF,CAAE;UAAAhE,QAAA,EACH;QACD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRzE,OAAA;QAAK8E,SAAS,EAAC,yBAAyB;QAAAT,QAAA,gBACnCrE,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAT,QAAA,eACxBrE,OAAA,CAACV,UAAU;YAACgJ,UAAU,EAAEvE,gBAAiB;YAAC1B,WAAW,EAAEA;UAAY;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACFzE,OAAA;UAAK8E,SAAS,EAAC,yBAAyB;UAAAT,QAAA,eACtCrE,OAAA,CAAChB,MAAM;YACL8F,SAAS,EAAC,aAAa;YACvBsB,IAAI,EAAC,IAAI;YACTd,OAAO,EAAC,WAAW;YACnBiB,OAAO,EAAEzC,sBAAuB;YAAAO,QAAA,EACjC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENzE,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAT,QAAA,EAC7BH,gBAAgB,CAAC7B,WAAW;IAAC;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAENzE,OAAA,CAACjB,MAAM;MACLmH,MAAM,EAAC,OAAO;MACdqC,MAAM,EAAE/F,YAAa;MACrBgG,OAAO,EAAE/F,eAAgB;MACzBgG,YAAY,EAAE,KAAM;MACpBvD,KAAK,EAAC,OAAO;MACb2C,MAAM,eAAE7H,OAAA;QAAAqE,QAAA,EAAK;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eAEtCrE,OAAA,CAACZ,gBAAgB;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAED,eAAerC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}