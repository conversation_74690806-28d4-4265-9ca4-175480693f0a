import React from 'react';

// Info Icon Component
export const InfoIcon = () => (
  <svg width={17} height={17} viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.50065 10.7667V8.50004M8.50065 6.23337H8.50632M14.1673 8.50004C14.1673 11.6297 11.6303 14.1667 8.50065 14.1667C5.37104 14.1667 2.83398 11.6297 2.83398 8.50004C2.83398 5.37043 5.37104 2.83337 8.50065 2.83337C11.6303 2.83337 14.1673 5.37043 14.1673 8.50004Z" stroke="#1B6EBB" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);
export const AddIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
    <path d="M12.5 4V20M4.5 12H20.5" stroke="#1B6EBB" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);
export const CollapseIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M6 9L12 15L18 9" stroke="#1B6EBB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);