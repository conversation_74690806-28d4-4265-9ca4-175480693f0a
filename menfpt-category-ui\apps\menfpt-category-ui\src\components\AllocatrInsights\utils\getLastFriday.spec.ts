import {
  getLastFriday,
  getFridayOfSelectedWeek,
  getLastFridayInQuarter,
  getNextMondayAfterQuarter
} from './getLastFriday';

describe('getLastFriday', () => {
  it('should return a valid Friday date string', () => {
    const lastFriday = new Date(getLastFriday());
    expect(lastFriday.getUTCDay()).toBe(5); // Friday
    expect(typeof getLastFriday()).toBe('string');
  });

  it('should always return a Friday regardless of current day', () => {
    const lastFriday = new Date(getLastFriday());
    expect(lastFriday.getUTCDay()).toBe(5);
  });

  it('should return a Friday even if today is Saturday or Sunday', () => {
    const lastFriday = new Date(getLastFriday());
    expect(lastFriday.getUTCDay()).toBe(5);
  });

  it('should return a Friday if today is Friday', () => {
    const lastFriday = new Date(getLastFriday());
    expect(lastFriday.getUTCDay()).toBe(5);
  });

  it('should return a Friday for year boundary cases', () => {
    const lastFriday = new Date(getLastFriday());
    expect(lastFriday.getUTCDay()).toBe(5);
  });

    it('should handle the branch when today is Friday', () => {
      const RealDate = Date;
      global.Date = class extends RealDate {
        constructor(...args: any[]) {
          if (args.length === 0) {
            return new RealDate('2025-08-22T12:00:00Z'); 
          }
          super(...args);
        }
        static now() {
          return new RealDate('2025-08-22T12:00:00Z').getTime();
        }
        static UTC(...args: [year: number, month: number, date?: number, hours?: number, minutes?: number, seconds?: number, ms?: number]) {
          return RealDate.UTC(...args);
        }
      } as any;
      const lastFriday = new Date(getLastFriday());
      expect(lastFriday.getUTCDay()).toBe(5);
      global.Date = RealDate;
    });

    it('should handle the branch when today is Saturday', () => {
      const RealDate = Date;
      global.Date = class extends RealDate {
        constructor(...args: any[]) {
          if (args.length === 0) {
            return new RealDate('2025-08-23T12:00:00Z');
          }
          super(...args);
        }
        static now() {
          return new RealDate('2025-08-23T12:00:00Z').getTime();
        }
        static UTC(...args: [year: number, month: number, date?: number, hours?: number, minutes?: number, seconds?: number, ms?: number]) {
          return RealDate.UTC(...args);
        }
      } as any;
      const lastFriday = new Date(getLastFriday());
      expect(lastFriday.getUTCDay()).toBe(5);
      global.Date = RealDate;
    });
});
describe('getFridayOfSelectedWeek', () => {
  it('should return the Friday for a given week end date', () => {
    const result = getFridayOfSelectedWeek('08/23/2025');
    const date = new Date(result);
    expect(date.getUTCDay()).toBe(5); 
  });
});

describe('getLastFridayInQuarter', () => {
  it('should return the last Friday before or on the given date', () => {
    const result = getLastFridayInQuarter('08/23/2025'); // Saturday
    const date = new Date(result);
    expect(date.getUTCDay()).toBe(5); // Friday
  });
});

describe('getNextMondayAfterQuarter', () => {
  it('should return the next Monday after the given date', () => {
    const result = getNextMondayAfterQuarter('08/23/2025'); // Saturday
    const date = new Date(result);
    expect(date.getUTCDay()).toBe(1); // Monday
  });

});
