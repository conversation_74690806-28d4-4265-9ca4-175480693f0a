import { useLocation } from 'react-router-dom';
import { useMemo } from 'react';
import { worksheetFilterConfig } from './worksheetFilterConfig';

// Helper function to extract the current route from a path
export const extractCurrentRoute = (pathname: string): string => {
  // Split the path by '/' and get the last segment which is the actual route
  // This handles both '/dashboard' and '/menfpt/dashboard' and '/memsp-ui-shell/menfpt/dashboard'
  const pathSegments = pathname.split('/').filter(segment => segment !== '');
  return pathSegments[pathSegments.length - 1];
};

export const useTimeframeDisplay = () => {
  const location = useLocation();

  return useMemo(() => {
    const currentRoute = extractCurrentRoute(location.pathname);
    return worksheetFilterConfig.isDisplayTimeFrame.includes(currentRoute);
  }, [location.pathname]);
};

export const useMultipleDepartmentsSelection = () => {
  const location = useLocation();

  return useMemo(() => {
    const currentRoute = extractCurrentRoute(location.pathname);
    return worksheetFilterConfig.isAllowMultipleDeptsSelection.includes(currentRoute);
  }, [location.pathname]);
};

export const useDeskDisplay = () => {
  const location = useLocation();

  return useMemo(() => {
    const currentRoute = extractCurrentRoute(location.pathname);
    return worksheetFilterConfig.isDisplayDesk.includes(currentRoute);
  }, [location.pathname]);
};

export const useDisplayDeptRoleCascade = () => {
  const location = useLocation();

  return useMemo(() => {
    const currentRoute = extractCurrentRoute(location.pathname);
    return worksheetFilterConfig.isDisplayDeptRoleCascade.includes(currentRoute);
  }, [location.pathname]);
};