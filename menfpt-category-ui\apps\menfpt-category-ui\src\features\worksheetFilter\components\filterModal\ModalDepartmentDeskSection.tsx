import React from 'react';
import DepartmentDeskSelector from '../departmentDeskSelector';
import { DropdownType } from '../../../../interfaces/worksheetFilter';

interface ModalDepartmentDeskSectionProps {
  filteredDepartments: DropdownType[];
  filteredDesks: DropdownType[];
  selectedDepartment?: DropdownType | DropdownType[];
  selectedDesk?: DropdownType;
  selectedDivision: DropdownType[];
  handleDepartmentChange: (department: DropdownType | DropdownType[]) => void;
  handleDeskChange: (desk: DropdownType) => void;
}

const ModalDepartmentDeskSection: React.FC<ModalDepartmentDeskSectionProps> = ({
  filteredDepartments,
  filteredDesks,
  selectedDepartment,
  selectedDesk,
  selectedDivision,
  handleDepartmentChange,
  handleDeskChange,
}) => (
  <div className="min-h-0 flex flex-col flex-1 grow-[1.5] overflow-hidden min-w-0 border-r border-[#c8daeb] px-4">
    <DepartmentDeskSelector
      desks={filteredDesks}
      selectedDepartment={selectedDepartment}
      selectedDesk={selectedDesk}
      divisionId={
        selectedDivision.length > 0 ? selectedDivision[0].num.toString() : undefined
      }
      onDepartmentChange={handleDepartmentChange}
      onDeskChange={handleDeskChange}
    />
  </div>
);

export default ModalDepartmentDeskSection; 