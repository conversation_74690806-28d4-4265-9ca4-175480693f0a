import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { DeskSelection } from './deskSelection';

const desks = [
  { name: 'Desk 1', num: '1' },
  { name: 'Desk 2', num: '2' },
];

const mockStore = configureStore([]);
const store = mockStore({});

describe('DeskSelection', () => {
  it('renders a list of desks', () => {
    render(
      <Provider store={store}>
        <DeskSelection desks={desks} onDeskChange={jest.fn()} />
      </Provider>
    );
    expect(screen.getByText('Desk 1')).toBeInTheDocument();
    expect(screen.getByText('Desk 2')).toBeInTheDocument();
  });

  it('calls onDeskChange when a desk is selected', () => {
    const onDeskChange = jest.fn();
    render(
      <Provider store={store}>
        <DeskSelection desks={desks} onDeskChange={onDeskChange} />
      </Provider>
    );
    fireEvent.click(screen.getByText('Desk 1'));
    expect(onDeskChange).toHaveBeenCalledWith(desks[0]);
  });

  it('checks the correct desk based on selectedDesk prop', () => {
    render(
      <Provider store={store}>
        <DeskSelection
          desks={desks}
          selectedDesk={desks[1]}
          onDeskChange={jest.fn()}
        />
      </Provider>
    );
    expect(screen.getByLabelText('Desk 2')).toBeChecked();
  });
});