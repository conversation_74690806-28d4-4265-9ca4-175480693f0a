import { DropdownType } from '../../interfaces/worksheetFilter';

export const filterBySearch = (
  items: DropdownType[],
  searchQuery: string
): DropdownType[] => {
  if (searchQuery === '') {
    return items;
  }
  return items.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
};


export const filterByDeptRolesSearch = (
  {departments, searchQueryDeptRole}: {departments: DropdownType[], searchQueryDeptRole: string}
 
) => {
  // Department matches
  const departmentMatches = departments
    .filter((item) => item.name.toLowerCase().includes(searchQueryDeptRole.toLowerCase()))
    .map((item) => ({
      id: String(item.num), // Use department's num as ID
      label: `${item.name}`,
      originalType: 'department'
    }));

  // SM and ASM extraction from deskNameArr
  const smMap = new Map<string, any>();
  const asmList: any[] = [];

  departments.forEach((item) => {
    if (item.deskNameArr) {
      item.deskNameArr.forEach((deskName, idx) => {
        const parts = deskName.split('-');
        // Format: divisionId-deptId-sm-asm
        if (parts.length >= 4) {
          const divisionId = parts[0];
          const deptId = parts[1];
          const sm = parts[2];
          const asm = parts[3];
          // Find department name for this deptId
          const deptName = item.name; // Do not strip department id
          // SM
          if (sm?.toLowerCase().includes(searchQueryDeptRole.toLowerCase())) {
            smMap.set(`${deptId}-${sm}`, {
              id: `${deptId}-${sm}`,
              label: `${deptName} / ${sm}`,
              originalType: 'SM'
            });
          }
          // ASM
          if (asm?.toLowerCase().includes(searchQueryDeptRole.toLowerCase())) {
            asmList.push({
              id: `${deptId}-${sm}-${asm}`,
              label: `${deptName} / ${sm} / ${asm}`,
              originalType: 'ASM'
            });
          }
        }
      });
    }
  });

  const smMatches = Array.from(smMap.values());

  // Only return groups with at least one match
  const result: any[] = [];
  if (departmentMatches.length > 0) {
    result.push({ type: 'Department', items: departmentMatches });
  }
  if (smMatches.length > 0) {
    result.push({ type: 'SM', items: smMatches });
  }
  if (asmList.length > 0) {
    result.push({ type: 'ASM', items: asmList });
  }  
  return result;
};