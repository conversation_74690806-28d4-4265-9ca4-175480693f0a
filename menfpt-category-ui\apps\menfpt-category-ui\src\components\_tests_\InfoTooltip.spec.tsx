import { render, screen } from '@testing-library/react';
import InfoTooltip from '../InfoTooltip';
import '@testing-library/jest-dom';

jest.mock('@albertsons/uds/molecule/Tooltip', () => {
  return ({ label, children, anchor, variant, className, zIndex }: any) => (
    <div
      data-testid="tooltip"
      data-label={label}
      data-anchor={anchor}
      data-variant={variant}
      data-classname={className}
      data-zindex={zIndex}
    >
      {children}
    </div>
  );
});

describe('InfoTooltip', () => {
  it('applies default props when optional props are not provided', () => {
    render(<InfoTooltip label="Tooltip Text" icon={<span>ℹ️</span>} />);

    const tooltip = screen.getByTestId('tooltip');

    expect(tooltip).toHaveAttribute('data-anchor', 'top');
    expect(tooltip).toHaveAttribute('data-variant', 'dark');
    expect(tooltip).toHaveAttribute('data-zindex', '999');
  });

  it('overrides default props when optional props are provided', () => {
    render(
      <InfoTooltip
        label="Custom Tooltip"
        icon={<span>🛈</span>}
        anchor="bottom"
        variant="light"
        className="custom-class"
        zIndex={1234}
      />
    );

    const tooltip = screen.getByTestId('tooltip');

    expect(tooltip).toHaveAttribute('data-anchor', 'bottom');
    expect(tooltip).toHaveAttribute('data-variant', 'light');
    expect(tooltip).toHaveAttribute('data-classname', 'custom-class');
    expect(tooltip).toHaveAttribute('data-zindex', '1234');
  });
});
