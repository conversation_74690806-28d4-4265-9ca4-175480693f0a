import React, { useState, useMemo } from 'react';
import { Column } from '@albertsons/uds/molecule/Table/Table.types';
import InfoTooltip from '../InfoTooltip';
import { Info, ArrowLeft } from 'lucide-react';
import mockData from './MockData.json';
import FacilitiesTableCard from './FacilitiesTableCard';
import { useNavigate } from 'react-router-dom';
import Search from '@albertsons/uds/molecule/Search';
import ExpandedFacilitiesView from './ExpandedFacilitiesView';

type Facility = {
  id: number;
  facility: string;
  assetType: string;
  siteName: string;
  posStartDate: string;
};

const items: Facility[] = mockData.IdFacilities;

const columns: Column<Facility>[] = [
  {
    id: 'facility',
    label: (
      <div className="facility-header">
        <span>Facility</span>
      </div>
    ),
    value: 'facility',
    hideFromMenu: true,
  },
  {
    id: 'assetType',
    label: (
      <div className="facility-header">
        <span>Asset Type</span>
      </div>
    ),
    value: 'assetType',
    hideFromMenu: true,
  },
  {
    id: 'siteName',
    label: (
      <div className="facility-header">
        <span>Site Name and A...</span>
      </div>
    ),
    value: 'siteName',
    hideFromMenu: true,
  },
  {
    id: 'posStartDate',
    label: (
      <div className="facility-header">
        <span>POS Start date</span>
      </div>
    ),
    value: 'posStartDate',
    hideFromMenu: true,
  },
];

const ViewMoreIdFacilities = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredItems = useMemo(() => {
    return searchTerm
      ? items.filter((row) =>
          Object.values(row).some((value) =>
            String(value).toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : items;
  }, [searchTerm]);

  return (
    <ExpandedFacilitiesView
      title="ID Facility"
      columns={columns}
      items={items}
      headerLabel="ID Facility"
      onBack={() => navigate(-1)}
    />
  );
};

export default ViewMoreIdFacilities;