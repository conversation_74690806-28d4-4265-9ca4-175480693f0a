import * as Helper from '../DashboardDownloadExcel/DashboardDownloadExcelHelper';

describe('DashboardDownloadExcelHelper', () => {
  const mockFormatCurrency = (v: any) => v === null || v === undefined || v === '' ? '' : `$${v}`;
  const mockFormatPercentage = (v: any) => {
    if (v === null || v === undefined || v === '' || isNaN(Number(v))) return '';
    return `${(Number(v) * 100).toFixed(2)}%`;
  };
  jest.mock('../../components/AllocatrInsights/utils/insightsFormatters', () => ({
    formatPercentage: jest.fn((v) => v === null || v === undefined || v === '' ? '' : `${(v * 100).toFixed(2)}%`)
  }));

  describe('mapRow', () => {
    it('should map all fields correctly with valid data', () => {
      const data = {
        line1Projection: 100,
        lastYear: 90,
        actualOrForecast: 110,
        idPercentage: 0.5,
        vsLY: { value: 10 },
        vsProjection: { value: 20 },
        bookGrossProfit: { 
          projectionValue: 101, 
          projectionPct: 0.11, 
          actualOrForecast: 102, 
          percentActualOrForecast: 0.12, 
          vsProjection: 0.13 
        },
        markdown: { 
          projectionValue: 103, 
          projectionPct: 0.14, 
          actualOrForecast: 104, 
          percentActualOrForecast: 0.15, 
          vsProjection: 0.16 
        },
        shrink: { 
          projectionValue: 105, 
          projectionPct: 0.17, 
          actualOrForecast: 106, 
          percentActualOrForecast: 0.18, 
          vsProjection: 0.19 
        },
        line5: { 
          projectionValue: 107, 
          projectionPct: 0.20, 
          actualOrForecast: 108, 
          percentActualOrForecast: 0.21, 
          vsProjection: 109, 
          percentVsProjection: 0.22 
        },
        line6: { projection: -140, actualOrForecast: 150, vsProjection: 160 },
        line7: { projection: 170, actualOrForecast: 180, vsProjection: 190 },
        line8: { 
          projectionValue: 200, 
          projectionPct: 0.23, 
          actualOrForecast: 201, 
          percentActualOrForecast: 0.24, 
          vsProjection: 202, 
          percentVsProjection: 0.25 
        }
      };
      const baseRow = { departmentName: 'Dept' };
      const row = Helper.mapRow(baseRow, data, mockFormatCurrency, 'Quarter');
      expect(row['$ Projection']).toBe('$100');
      expect(row['$ Last Year']).toBe('$90');
      expect(row['$Actual/Merch.Forecast']).toBe('$110');
      expect(row['Keeper% (Includes ID)']).toBe('50.00%');
      expect(row['$ vs LY']).toBe('$10');
      expect(row['$ vs Projection']).toBe('$20');
      expect(row['$ Projection (BGP)']).toBe('$101');
      expect(row['% Projection (BGP)']).toBe('11.00%');
      expect(row['$Actual/Merch.Forecast (BGP)']).toBe('$102');
      expect(row['%Actual/Merch.Forecast']).toBe('12.00%');
      expect(row['% vs Projection']).toBe('13.00%');
      expect(row['$ Projection (Markdown)']).toBe('$103');
      expect(row['% Projection (Markdown)']).toBe('14.00%');
      expect(row['$Actual/Merch.Forecast (Markdown)']).toBe('$104');
      expect(row['%Actual/Merch.Forecast (Markdown)']).toBe('15.00%');
      expect(row['% vs Projection (Markdowns)']).toBe('16.00%');
      expect(row['$ Projection (Shrink)']).toBe('$105');
      expect(row['% Projection (Shrink)']).toBe('17.00%');
      expect(row['$Actual/Merch.Forecast (Shrink)']).toBe('$106');
      expect(row['%Actual/Merch.Forecast (Shrink)']).toBe('18.00%');
      expect(row['% Projection (Shrinks)']).toBe('19.00%');
      expect(row['$ Projection (RGP)']).toBe('$107');
      expect(row['% Projection (RGP)']).toBe('20.00%');
      expect(row['$Actual/Merch.Forecast (RGP)']).toBe('$108');
      expect(row['%Actual/Merch.Forecast (RGP)']).toBe('21.00%');
      expect(row['$ vs Projection (RGP)']).toBe('$109');
      expect(row['% vs Projection (RGP)']).toBe('22.00%');
      expect(row['$ Projection (Supplies Packaging)']).toBe('($140)');
      expect(row['$Actual/Merch.Forecast (Supplies Packaging)']).toBe('$150');
      expect(row['$ vs Projection (Supplies Packaging)']).toBe('$160');
      expect(row['$ Projection (Retail Allowance)']).toBe('$170');
      expect(row['$Actual/Merch.Forecast (Retail Allowance)']).toBe('$180');
      expect(row['$ vs Projection (Retail Allowance)']).toBe('$190');
      expect(row['$ Projection(Sales)']).toBe('$200');
      expect(row['% Projection(Sales)']).toBe('23.00%');
      expect(row['$Actual/Merch.Forecast (Sales)']).toBe('$201');
      expect(row['%Actual/Merch.Forecast (Sales)']).toBe('24.00%');
      expect(row['$ vs Projection (Sales)']).toBe('$202');
      expect(row['% vs Projection (Sales)']).toBe('25.00%');
    });

    it('should return empty string for null/undefined/empty values', () => {
      const data = {};
      const baseRow = { departmentName: 'Dept' };
      const row = Helper.mapRow(baseRow, data, mockFormatCurrency, 'Quarter');
      Object.values(row).forEach(val => {
        expect(typeof val === 'string').toBeTruthy();
      });
    });

    it('should handle negative values for line6 fields', () => {
      const data = { line6: { projection: -10, actualOrForecast: -20 } };
      const baseRow = { departmentName: 'Dept' };
      const row = Helper.mapRow(baseRow, data, mockFormatCurrency, 'Quarter');
      expect(row['$ Projection (Supplies Packaging)']).toBe('($10)');
      expect(row['$Actual/Merch.Forecast (Supplies Packaging)']).toBe('($20)');
    });
  });

  describe('handleDownloadClick', () => {
    it('should alert if dashboardData is empty array', () => {
      global.alert = jest.fn();
      Helper.handleDownloadClick([], [], {}, jest.fn());
      expect(global.alert).toHaveBeenCalledWith('No dashboard data to export!');
    });

    it('should alert if dashboardData object has no divisions or id', () => {
      global.alert = jest.fn();
      Helper.handleDownloadClick({}, [], {}, jest.fn());
      expect(global.alert).toHaveBeenCalledWith('No dashboard data to export!');
    });

    it('should call handleDownloadExcel if array data exists', () => {
      const fn = jest.fn();
      Helper.handleDownloadClick([1], [2], { test: 1 }, fn);
      expect(fn).toHaveBeenCalledWith([1], [2], { test: 1 });
    });

    it('should call handleDownloadExcel if object data with divisions exists', () => {
      const fn = jest.fn();
      const data = { divisions: [{ id: '34' }] };
      Helper.handleDownloadClick(data, [2], { test: 1 }, fn);
      expect(fn).toHaveBeenCalledWith(data, [2], { test: 1 });
    });

    it('should call handleDownloadExcel if object data with id exists', () => {
      const fn = jest.fn();
      const data = { id: 'Total' };
      Helper.handleDownloadClick(data, [2], { test: 1 }, fn);
      expect(fn).toHaveBeenCalledWith(data, [2], { test: 1 });
    });
  });

  describe('getParentHeaderRow', () => {
    it('should return correct header row', () => {
      const row = Helper.getParentHeaderRow('Q1 2025');
      expect(Array.isArray(row)).toBe(true);
      expect(row[0]).toBe('Q1 2025');
    });
  });

  describe('header constants', () => {
    it('COMMON_HEADERS should be an array', () => {
      expect(Array.isArray(Helper.COMMON_HEADERS)).toBe(true);
    });
    it('VS_PROJECTION_HEADERS should be an array', () => {
      expect(Array.isArray(Helper.VS_PROJECTION_HEADERS)).toBe(true);
    });
    it('VS_PROJECTION_DOLLAR_HEADERS should be an array', () => {
      expect(Array.isArray(Helper.VS_PROJECTION_DOLLAR_HEADERS)).toBe(true);
    });
  });
});