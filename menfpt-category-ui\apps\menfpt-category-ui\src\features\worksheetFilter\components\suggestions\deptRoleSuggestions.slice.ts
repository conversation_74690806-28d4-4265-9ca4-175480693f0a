import { createGenericSlice } from '../../../../rtk/rtk-slice';

export const deptRoleSuggestionsSlice = createGenericSlice({
  name: 'deptRoleSuggestions_rn',
  initialState: { status: 'loading', data: { suggestions: [], selectedDepartment: null as string | null, cascadeSearchSelectedItemId: null as string | null, cascadeSearchSelectedItemType: null as 'department' | 'sm' | 'ASM' | null } },
})(
  {
    setDeptRoleSuggestions(state, { payload }) {
      if (!state.data) state.data = { suggestions: [], selectedDepartment: null as string | null, cascadeSearchSelectedItemId: null as string | null, cascadeSearchSelectedItemType: null as 'department' | 'sm' | 'ASM' | null };
      state.data.suggestions = payload;
    },
    setSelectedDepartmentFromSuggestion(state, { payload }: { payload: { itemId: string | null, itemType: 'department' | 'sm' | 'ASM', isCascade?: boolean } }) {
      if (!state.data) state.data = { suggestions: [], selectedDepartment: null, cascadeSearchSelectedItemId: null, cascadeSearchSelectedItemType: null };
      if (payload.itemType === 'department') {
        state.data.selectedDepartment = payload.itemId;
      }
      if (payload.isCascade) {
        state.data.cascadeSearchSelectedItemId = payload.itemId;
        state.data.cascadeSearchSelectedItemType = payload.itemType;
      }
    },
    clearCascadeSearchSelectedItem(state) {
      if (state.data) {
        state.data.cascadeSearchSelectedItemId = null;
        state.data.cascadeSearchSelectedItemType = null;
      }
    },
  }
);

export const { setDeptRoleSuggestions, setSelectedDepartmentFromSuggestion, clearCascadeSearchSelectedItem } = deptRoleSuggestionsSlice.actions;